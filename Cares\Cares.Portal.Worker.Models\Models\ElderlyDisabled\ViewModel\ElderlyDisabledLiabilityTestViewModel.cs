﻿using Cares.Api.Infrastructure.Enums;
using Cares.Portal.Worker.Models.Validators.ElderlyDisabled;
using FluentValidation.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel
{
    [Validator(typeof(ElderlyDisabledLiabilityTestViewModelValidator))]
    public class ElderlyDisabledLiabilityTestViewModel
    {
        public long PersonLiabilityTestId { get; set; }
        public long PersonId { get; set; }
        public long OriginatingApplicationId { get; set; }
        public enumLiabilityTestType LiabilityTestTypeId { get; set; }
        public decimal? Amount { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public string Description { get; set; }
        public DateTime? EndDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
    }
}
