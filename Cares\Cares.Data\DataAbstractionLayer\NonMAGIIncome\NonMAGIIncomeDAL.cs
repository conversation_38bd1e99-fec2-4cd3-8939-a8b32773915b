﻿using Cares.Api.Infrastructure.Enums;
using Cares.Models.Application;
using Cares.Portal.Infrastructure;
using Cares.Portal.Worker.Models.Models.Application.Income;
using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects;
using System.Globalization;
using System.Linq;

namespace Cares.Data.DataAbstractionLayer.NonMAGIIncome
{
    public class NonMAGIIncomeDAL
    {
        /// <summary>
        /// Gets the non magi income application details
        /// </summary>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<IncomeListItem> GetApplicationDetails(int appId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var results = context.usp_SELECT_NON_MAGI_APPLICATION_INCOME(appId);

                return results.Select(applicationDetailResult => new IncomeListItem()
                {
                    NonMagiAppIncomeId = applicationDetailResult.NON_MAGI_APPLICATION_INCOME_ID,
                    IncomeTypeId = applicationDetailResult.INCOME_TYPE_ID,
                    OtherIncome = applicationDetailResult.OTHER_INCOME_DESC,
                    IncomeFrequency = applicationDetailResult.INCOME_FREQUENCY_ID,
                    ClaimNumber = applicationDetailResult.CLAIM_NUMBER,
                    ApplicantGrossAmount = applicationDetailResult.APPLICANT_ATTESTED_INCOME,
                    SpouseGrossAmount = applicationDetailResult.SPOUSE_ATTESTED_INCOME,
                    ChildGrossAmount = applicationDetailResult.CHILD_ATTESTED_INCOME,
                    ApplicantVerifiedIncome = applicationDetailResult.APPLICANT_VERIFIED_INCOME,
                    SpouseVerifiedIncome = applicationDetailResult.SPOUSE_VERIFIED_INCOME,
                    ChildVerifiedIncome = applicationDetailResult.CHILD_VERIFIED_INCOME,
                    ApplicationTypeId = applicationDetailResult.APPLICATION_TYPE_ID
                }).ToList();
            }
        }
        /// <summary>
        /// Gets the non magi income total amount details
        /// </summary>
        /// <param name="nonMagiAppIncomeId"></param>
        /// <returns></returns>
        public NonMagiIncomeDetails GetApplicationTotalIncomeDetails(int applicationId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var results = context.usp_SELECT_GROSS_AMOUNT_OF_NON_MAGI_APPLICATION_INCOME(applicationId).ToList();

                return results.Select(applicationTotalIncomeDetailResult => new NonMagiIncomeDetails()
                {
                    ApplicationId = applicationId,
                    ApplicantTotalIncome = applicationTotalIncomeDetailResult.ApplicantGrossAmount,
                    ChildTotalIncome = applicationTotalIncomeDetailResult.ChildGrossAmount,
                    SpouseTotalIncome = applicationTotalIncomeDetailResult.SpouseGrossAmount

                }).FirstOrDefault();
            }
        }
        /// <summary>
        /// Deletes the non magi income details
        /// </summary>
        /// <param name="nonMagiAppIncomeId"></param>
        /// <returns></returns>
        public void DeleteNonMagiIncomeDetails(int nonMagiAppIncomeId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_DELETE_NON_MAGI_APPLICATION_INCOME(nonMagiAppIncomeId);

            }
        }
        /// <summary>
        /// Saves the non magi income details
        /// </summary>
        /// <param name="incomelist"></param>
        /// <param name="appId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public long SaveNonMagiIncomeDetail(IncomeListItem incomelist, int applicationId, string userName)
        {
            var nonMagiAppIncomeIdOutput = new ObjectParameter("AppIncomeId", typeof(Int32));

            using (var context = new CaresApplicationDBEntities())
            {
                if (incomelist.NonMagiAppIncomeId == 0)
                {
                    var result = context.usp_INSERT_NON_MAGI_APPLICATION_INCOME(
                        applicationId,
                        incomelist.IncomeTypeId,
                        incomelist.OtherIncome,
                        incomelist.ApplicationTypeId,
                        incomelist.ClaimNumber,
                        incomelist.ApplicantGrossAmount,
                        incomelist.SpouseGrossAmount,
                        incomelist.ChildGrossAmount,
                        incomelist.ApplicantVerifiedIncome,
                        incomelist.SpouseVerifiedIncome,
                        incomelist.ChildVerifiedIncome,
                        incomelist.IncomeFrequency,
                        userName,
                        nonMagiAppIncomeIdOutput
                        );
                    return result;

                }
                else
                {
                    var result = context.usp_UPDATE_NON_MAGI_APPLICATION_INCOME(
                        incomelist.NonMagiAppIncomeId,
                        incomelist.IncomeTypeId,
                        incomelist.OtherIncome,
                        incomelist.ApplicationTypeId,
                        incomelist.ClaimNumber,
                        incomelist.ApplicantGrossAmount,
                        incomelist.SpouseGrossAmount,
                        incomelist.ChildGrossAmount,
                        incomelist.ApplicantVerifiedIncome,
                        incomelist.SpouseVerifiedIncome,
                        incomelist.ChildVerifiedIncome,
                        incomelist.IncomeFrequency,
                        userName
                        );
                    return result;
                }

            }
        }

        public List<APPLICATION_NON_MAGI_INCOME> GetNonMagiIncomeDataByAppId(int appId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var incomeh = context.APPLICATION_NON_MAGI_INCOME
                    .Where(i => i.APPLICATION_ID == appId && i.INCOME_RELATIONSHIP_TYPE_ID == 1)
                    .ToList();
                return incomeh;
            }
        }

        /// <summary>
        /// Call to the SP to Calculate and update the VA Net amount.
        /// </summary>
        /// <param name="appId"></param>
        /// <param name="applicationNonMagiId"></param>
        /// <returns></returns>
        public bool Calculate_VA_NET_Values(long appId, long applicationNonMagiId, string username, Guid tokenId)
		{
            using (var context = new CaresApplicationDBEntities())
            {
                var incomeh = context.APPLICATION_NON_MAGI_INCOME.Where(i => i.APPLICATION_ID == appId).ToList();

                var detList = new List<APPLICATION_NON_MAGI_INCOME_DETAIL>();

				foreach (var header in incomeh)
				{
                    var det = new List<APPLICATION_NON_MAGI_INCOME_DETAIL>();
                    det.AddRange(context.APPLICATION_NON_MAGI_INCOME_DETAIL
                        .Include("APPLICATION_NON_MAGI_INCOME")
                        .Where(d => d.APPLICATION_NON_MAGI_INCOME_ID == header.APPLICATION_NON_MAGI_INCOME_ID)
                        .ToList());

                    var iterationDate = det.OrderBy(o => o.INCOME_MONTH).First().INCOME_MONTH;
                    var currentDate = new DateTime(iterationDate.Ticks);

                    var single = new APPLICATION_NON_MAGI_INCOME_DETAIL();

                    while (iterationDate <= DateTime.Today)
					{
                        var existDet = det.FirstOrDefault(d => d.INCOME_MONTH == iterationDate);
                        if (existDet != null)
						{
                            if(existDet.DATE_STOP != null && existDet.DATE_STOP > iterationDate)
							{
                                existDet.GROSS_INCOME_AMOUNT = 0;
                                existDet.MONTHLY_COUNTABLE_NET_INCOME_AMOUNT = 0;
							}
                            single = existDet;
						}
						else
						{
                            existDet = single;
						}

                        detList.Add(new APPLICATION_NON_MAGI_INCOME_DETAIL()
                        {
                            INCOME_MONTH = iterationDate,
                            VETERAN_STATUS = existDet.VETERAN_STATUS,
                            GROSS_INCOME_AMOUNT = existDet.GROSS_INCOME_AMOUNT,
                            APPLICATION_NON_MAGI_INCOME_ID = existDet.APPLICATION_NON_MAGI_INCOME_ID,
                            MONTHLY_COUNTABLE_NET_INCOME_AMOUNT = existDet.MONTHLY_COUNTABLE_NET_INCOME_AMOUNT,
                            APPLICATION_NON_MAGI_INCOME_DETAIL_ID = existDet.APPLICATION_NON_MAGI_INCOME_DETAIL_ID,
                            DATE_STOP = existDet.DATE_STOP,
                            APPLICATION_NON_MAGI_INCOME = header
                        });

                        iterationDate = iterationDate.AddMonths(1);
					}
				}

                //Gets the Veteran Details
                var veteranD = context.APPLICATION_NON_MAGI_INCOME_DETAIL.Where(v => v.APPLICATION_NON_MAGI_INCOME_ID == applicationNonMagiId).ToList();

                //Get Factsheet for each year present on income.
                var years = veteranD.Select(d => d.INCOME_MONTH.Year).ToArray();
                var factsheetValues = context.COLA_FACTSHEET_YEAR_VALUE.Where(f =>
                                                        years.Contains(f.FACTSHEET_YEAR)).ToList();

				foreach (var d in veteranD)
				{
                    var fsStandard = new Decimal();
                    var fsAandA = new Decimal();
                    var fsVAIncrement = new Decimal();

                    switch (d.VETERAN_STATUS)
                    {
                        case "Single Survivor":
                            fsStandard = (decimal)factsheetValues.FirstOrDefault(f => f.COLA_FACTSHEET_SUB_CATEGORY_ID == 18 && f.FACTSHEET_YEAR==d.INCOME_MONTH.Year).VALUE1;
                            fsAandA = (decimal)factsheetValues.FirstOrDefault(f => f.COLA_FACTSHEET_SUB_CATEGORY_ID == 21 && f.FACTSHEET_YEAR == d.INCOME_MONTH.Year).VALUE1;
                            break;
                        case "Single":
                            fsStandard = (decimal)factsheetValues.FirstOrDefault(f => f.COLA_FACTSHEET_SUB_CATEGORY_ID == 19 && f.FACTSHEET_YEAR == d.INCOME_MONTH.Year).VALUE1;
                            fsAandA = (decimal)factsheetValues.FirstOrDefault(f => f.COLA_FACTSHEET_SUB_CATEGORY_ID == 22 && f.FACTSHEET_YEAR == d.INCOME_MONTH.Year).VALUE1;
                            break;
                        case "Couple":
                            fsStandard = (decimal)factsheetValues.FirstOrDefault(f => f.COLA_FACTSHEET_SUB_CATEGORY_ID == 20 && f.COLA_FACTSHEET_YEAR.FACTSHEET_YEAR == d.INCOME_MONTH.Year).VALUE1;
                            fsAandA = (decimal)factsheetValues.FirstOrDefault(f => f.COLA_FACTSHEET_SUB_CATEGORY_ID == 23 && f.FACTSHEET_YEAR == d.INCOME_MONTH.Year).VALUE1;
                            fsVAIncrement = (decimal)factsheetValues.FirstOrDefault(f => f.COLA_FACTSHEET_SUB_CATEGORY_ID == 17 && f.FACTSHEET_YEAR == d.INCOME_MONTH.Year).VALUE1;
                            break;
                    }

                    decimal? totalGross;

                    if (d.VETERAN_STATUS == "Couple")
					{
                        totalGross = detList.Where(w => w.INCOME_MONTH == d.INCOME_MONTH && w.APPLICATION_NON_MAGI_INCOME_ID != d.APPLICATION_NON_MAGI_INCOME_ID).Sum(s => s.GROSS_INCOME_AMOUNT);
					}
					else
					{
                        totalGross = detList.Where(w => w.APPLICATION_NON_MAGI_INCOME.INCOME_RELATIONSHIP_TYPE_ID == (byte)enumRelationship.Self && w.INCOME_MONTH == d.INCOME_MONTH && w.APPLICATION_NON_MAGI_INCOME_ID != d.APPLICATION_NON_MAGI_INCOME_ID).Sum(s => s.GROSS_INCOME_AMOUNT);
					}

                    var maxVAPension = fsStandard - fsAandA;

                    d.MONTHLY_COUNTABLE_NET_INCOME_AMOUNT = maxVAPension - totalGross;
                    d.UPDATED_BY = username;
                    d.UPDATED_DATE = DateTime.Today;
                }

                context.SaveChanges();

                UpdateIncomeWithLatestDetailValue(context, appId);
            }

            return true;
		}

        private static void UpdateIncomeWithLatestDetailValue(CaresApplicationDBEntities context, long applicationId)
        {
            var incomes = context.APPLICATION_NON_MAGI_INCOME
                .Where(i => i.APPLICATION_ID == applicationId)
                .ToList();

            foreach (var income in incomes)
            {
                var latestDetail = context.APPLICATION_NON_MAGI_INCOME_DETAIL
                    .Where(d => d.APPLICATION_NON_MAGI_INCOME_ID == income.APPLICATION_NON_MAGI_INCOME_ID)
                    .OrderByDescending(d => d.INCOME_MONTH)
                    .FirstOrDefault();

                if (latestDetail != null)
                {
                    income.GROSS_INCOME_AMOUNT = latestDetail.GROSS_INCOME_AMOUNT ?? 0.00m;
                    income.NET_INCOME_AMOUNT = latestDetail.MONTHLY_COUNTABLE_NET_INCOME_AMOUNT ?? 0.00m;
                    income.INCOME_FREQUENCY_ID = latestDetail.INCOME_FREQUENCY_ID ?? 0;
                    income.DATE_STOP = latestDetail.DATE_STOP != new DateTime(1, 1, 1) ? latestDetail.DATE_STOP : null;
                }
            }

            context.SaveChanges();
        }
        /// <summary>
        /// GEts the Hub verified SSA incomes
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        public List<HubVerifiedSsaIncome> GetHubVerifiedSsaIncome(int applicationId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var results = context.usp_SELECT_SSA_INCOME(applicationId);

                var finalResult = results.Select(i => new HubVerifiedSsaIncome
                {
                    Name = i.FullName,
                    Relationship = i.RELATIONSHIP_TYPE_DESC,
                    IncomeMonth = string.IsNullOrEmpty(i.INCOME_MONTH_YEAR) ? string.Empty:
                                                                             DateTime.ParseExact(i.INCOME_MONTH_YEAR, "yyyyMM", CultureInfo.InvariantCulture).ToString("MM/yyyy"),
                    BenefitCredited = i.BENEFIT_CREDITED_AMT,
                    PaymentSuspension = i.PAYMENT_IN_SUSPENSE_IND,

                }).ToList();

                finalResult = finalResult.GroupBy(k => new { k.Name, k.Relationship })
                    .SelectMany(k =>
                    {
                        var index = 0;
                        foreach (var item in k)
                        {
                            if (index != 0)
                            {
                                item.Name = "";
                                item.Relationship = "";
                            }

                            index++;
                        }
                        return k.ToList();
                    }).ToList();

                return finalResult;
            }
        }

        /// <summary>
        /// Gets the Hub Verified Equifax income
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        public List<HubVerifiedEquifaxIncome> GetHubVerifiedEquifaxIncome(int applicationId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var results = context.usp_SELECT_EQUIFAX_INCOME(applicationId);

                var finalResult = results.Select(i => new HubVerifiedEquifaxIncome
                {
                    Name = i.FullName,
                    Relationship = i.RelationshipToContact,
                    AsOfDate = DateHelper.ConvertDateMMDDYY(i.AsOfDate?.ToString(), "/"),
                    EmployerName = i.EmployerName,
                    PayPeriodFrequency = i.PayPeriodFrequency,
                    IncomeAmount = i.IncomeAmount,
                    PayPeriodEndDate = DateHelper.ConvertDateMMDDYY(i.PayPeriodEndDate?.ToString(), "/"),
                    EmploymentStatus = i.EmploymentStatus
                }).ToList();

                finalResult = finalResult.GroupBy(k => new { k.Name, k.Relationship })
                    .SelectMany(k =>
                    {
                        var index = 0;
                        foreach (var item in k)
                        {
                            if (index != 0)
                            {
                                item.Name = string.Empty;
                                item.Relationship = string.Empty;
                            }

                            index++;
                        }
                        return k.ToList();
                    }).ToList();

                return finalResult;
            }
        }
    }
}
