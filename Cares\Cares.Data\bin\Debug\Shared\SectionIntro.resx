﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="beforeYouGetStarted" xml:space="preserve">
    <value>Before you get started...</value>
  </data>
  <data name="browsers" xml:space="preserve">
    <value>In order to view this site properly, it is recommended that you use one of the following browsers:</value>
  </data>
  <data name="browsersFireFox" xml:space="preserve">
    <value>Mozilla Firefox 22 or greater</value>
  </data>
  <data name="browsersGoogle" xml:space="preserve">
    <value>Google Chrome 29 or greater</value>
  </data>
  <data name="browsersIE" xml:space="preserve">
    <value>Internet Explorer 10 (non-compatibility view) or greater</value>
  </data>
  <data name="comingUp" xml:space="preserve">
    <value>Coming Up In This Section</value>
  </data>
  <data name="comingUpInHouseholdSection" xml:space="preserve">
    <value>In this section, you will be asked for identifying information about who is applying for health coverage and who lives in your household.</value>
  </data>
  <data name="comingUpInIncomeSection" xml:space="preserve">
    <value>In this section, you will be asked about your household income.  In some cases, we may already have some information about your household income from your prior tax filings, regular payroll reports made by an employer, or other sources.  We will show you that information and you will be able to update it or confirm it.  You may also need to enter new information to complete your income calculation.</value>
  </data>
  <data name="comingUpInInsuranceSection" xml:space="preserve">
    <value>In this section, you will be asked for information concerning any current or future insurance coverage for those applying for health coverage.</value>
  </data>
  <data name="comingUpInReviewSection" xml:space="preserve">
    <value>In this section, you will review your application for completeness and accuracy.  Once you finish your review, you can give your e-signature and file your application.  In most cases, you will receive a final eligibility determination within minutes.</value>
  </data>
  <data name="enablingCookieHeaderInfo" xml:space="preserve">
    <value>You will need to have cookies enabled for this web site to function properly.</value>
  </data>
  <data name="enablingCookieInfo" xml:space="preserve">
    <value>Learn more about managing your cookies settings in &lt;a href="https://support.google.com/accounts/answer/61416?hl=en"&gt;Google Chrome&lt;/a&gt;, &lt;a href="http://windows.microsoft.com/en-us/internet-explorer/delete-manage-cookies#ie=ie-10"&gt; Internet Explorer&lt;/a&gt; and &lt;a href="http://support.mozilla.org/en-US/kb/enable-and-disable-cookies-website-preferences"&gt;Mozilla Firefox&lt;/a&gt;. </value>
  </data>
  <data name="enterInformationStatement" xml:space="preserve">
    <value>You will be asked a series of questions to determine your eligibility for Medicaid and ALL Kids.</value>
  </data>
  <data name="enterYourInformation" xml:space="preserve">
    <value>Enter Your Information</value>
  </data>
  <data name="estimatedTime" xml:space="preserve">
    <value>Estimated time needed to complete this section</value>
  </data>
  <data name="findHealthCarePlans" xml:space="preserve">
    <value>Find Health Care Plans</value>
  </data>
  <data name="findPlansStatement" xml:space="preserve">
    <value>Once you are determined eligible, you can see what health plans are available, compare them, and enroll in the health plan you choose.</value>
  </data>
  <data name="headerHousehold" xml:space="preserve">
    <value>Build Your Household</value>
  </data>
  <data name="headerIncome" xml:space="preserve">
    <value>Income Information</value>
  </data>
  <data name="headerInsurance" xml:space="preserve">
    <value>Insurance Information</value>
  </data>
  <data name="headerReview" xml:space="preserve">
    <value>Review and File</value>
  </data>
  <data name="headerStart" xml:space="preserve">
    <value>Start Your Application</value>
  </data>
  <data name="infoToCompleteApp" xml:space="preserve">
    <value>It will be helpful to have the following information with you to complete this application:</value>
  </data>
  <data name="infoToCompleteApp1" xml:space="preserve">
    <value>Social Security numbers and birth dates of household members for whom you are applying</value>
  </data>
  <data name="infoToCompleteApp2" xml:space="preserve">
    <value>Citizenship and immigration documentation of household members for whom you are applying</value>
  </data>
  <data name="infoToCompleteApp3" xml:space="preserve">
    <value>Household income from jobs, self-employment, and any other income sources</value>
  </data>
  <data name="infoToCompleteApp4" xml:space="preserve">
    <value>Information about any current health insurance coverage for all household members</value>
  </data>
  <data name="itemsForProperFunction" xml:space="preserve">
    <value>You will need to have cookies, popups, and scripting enabled for the web site to function properly.</value>
  </data>
  <data name="seeResultsStatement" xml:space="preserve">
    <value>After you enter your information and file your application, you will see your results.  In most cases, you will get a final eligibility determination within minutes.</value>
  </data>
  <data name="seeYourResults" xml:space="preserve">
    <value>See Your Results</value>
  </data>
  <data name="start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="timeToCompleteApp" xml:space="preserve">
    <value>This application should take 30 - 45 minutes to complete. Once finished, you should have a good idea if you and/or your children may be eligible for any of the programs.</value>
  </data>
  <data name="timeToCompleteSection10" xml:space="preserve">
    <value>10 minutes</value>
  </data>
  <data name="welcomeHeader" xml:space="preserve">
    <value>Welcome to Alabamacares.Alabama.gov</value>
  </data>
  <data name="youMayNeed" xml:space="preserve">
    <value>You May Need</value>
  </data>
  <data name="youMayNeedAllPrevDocuments" xml:space="preserve">
    <value>All previously required documents</value>
  </data>
  <data name="youMayNeedBirthDates" xml:space="preserve">
    <value>Birth dates</value>
  </data>
  <data name="youMayNeedGroupNumber" xml:space="preserve">
    <value>Group numbers</value>
  </data>
  <data name="youMayNeedInsCarrier" xml:space="preserve">
    <value>Insurance carrier</value>
  </data>
  <data name="youMayNeedPayStubs" xml:space="preserve">
    <value>Pay stubs</value>
  </data>
  <data name="youMayNeedPolicyNumber" xml:space="preserve">
    <value>Policy numbers</value>
  </data>
  <data name="youMayNeedRecentTaxFiling" xml:space="preserve">
    <value>Most recent tax filing</value>
  </data>
  <data name="youMayNeedSocialSecurityNumbers" xml:space="preserve">
    <value>Social Security numbers</value>
  </data>
</root>