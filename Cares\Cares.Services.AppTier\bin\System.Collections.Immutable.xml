﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections.Immutable</name>
  </assembly>
  <members>
    <member name="T:System.Collections.Immutable.IImmutableDictionary`2">
      <summary>Represents an immutable collection of key/value pairs.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of values in the dictionary.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.Add(`0,`1)">
      <summary>Adds an element with the specified key and value to the dictionary.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
      <returns>A new immutable dictionary that contains the additional key/value pair.</returns>
      <exception cref="T:System.ArgumentException">The given key already exists in the dictionary but has a different value.</exception>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Adds the specified key/value pairs to the dictionary.</summary>
      <param name="pairs">The key/value pairs to add.</param>
      <returns>A new immutable dictionary that contains the additional key/value pairs.</returns>
      <exception cref="T:System.ArgumentException">One of the given keys already exists in the dictionary but has a different value.</exception>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.Clear">
      <summary>Retrieves an empty dictionary that has the same ordering and key/value comparison rules as this dictionary instance.</summary>
      <returns>An empty dictionary with equivalent ordering and key/value comparison rules.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether the immutable dictionary contains the specified key/value pair.</summary>
      <param name="pair">The key/value pair to locate.</param>
      <returns>
        <see langword="true" /> if the specified key/value pair is found in the dictionary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.Remove(`0)">
      <summary>Removes the element with the specified key from the immutable dictionary.</summary>
      <param name="key">The key of the element to remove.</param>
      <returns>A new immutable dictionary with the specified element removed; or this instance if the specified key cannot be found in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the elements with the specified keys from the immutable dictionary.</summary>
      <param name="keys">The keys of the elements to remove.</param>
      <returns>A new immutable dictionary with the specified keys removed; or this instance if the specified keys cannot be found in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.SetItem(`0,`1)">
      <summary>Sets the specified key and value in the immutable dictionary, possibly overwriting an existing value for the key.</summary>
      <param name="key">The key of the entry to add.</param>
      <param name="value">The key value to set.</param>
      <returns>A new immutable dictionary that contains the specified key/value pair.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.SetItems(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Sets the specified key/value pairs in the immutable dictionary, possibly overwriting existing values for the keys.</summary>
      <param name="items">The key/value pairs to set in the dictionary. If any of the keys already exist in the dictionary, this method will overwrite their previous values.</param>
      <returns>A new immutable dictionary that contains the specified key/value pairs.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableDictionary`2.TryGetKey(`0,`0@)">
      <summary>Determines whether this dictionary contains a specified key.</summary>
      <param name="equalKey">The key to search for.</param>
      <param name="actualKey">The matching key located in the dictionary if found, or <c>equalkey</c> if no match is found.</param>
      <returns>
        <see langword="true" /> if a match for <paramref name="equalKey" /> is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Collections.Immutable.IImmutableList`1">
      <summary>Represents a list of elements that cannot be modified.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of elements in the list.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.Add(`0)">
      <summary>Makes a copy of the list, and adds the specified object to the end of the  copied list.</summary>
      <param name="value">The object to add to the list.</param>
      <returns>A new list with the object added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Makes a copy of the list and adds the specified objects to the end of the copied list.</summary>
      <param name="items">The objects to add to the list.</param>
      <returns>A new list with the elements added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.Clear">
      <summary>Creates  a list with all the items removed, but with the same sorting and ordering semantics as this list.</summary>
      <returns>An empty list that has the same sorting and ordering semantics as this instance.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.IndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the <see cref="T:System.Collections.Immutable.IImmutableList`1" /> that starts at the specified index and contains the specified number of elements.</summary>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Immutable.IImmutableList`1" />. This value can be null for reference types.</param>
      <param name="index">The zero-based starting indexes of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="equalityComparer">The equality comparer to use to locate <paramref name="item" />.</param>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the range of elements in the <see cref="T:System.Collections.Immutable.IImmutableList`1" /> that starts at <paramref name="index" /> and contains <paramref name="count" /> number of elements if found; otherwise -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.Insert(System.Int32,`0)">
      <summary>Inserts the specified element at the specified index in the immutable list.</summary>
      <param name="index">The zero-based index at which to insert the value.</param>
      <param name="element">The object to insert.</param>
      <returns>A new immutable list that includes the specified element.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserts the specified elements at the specified index in the immutable list.</summary>
      <param name="index">The zero-based index at which the new elements should be inserted.</param>
      <param name="items">The elements to insert.</param>
      <returns>A new immutable list that includes the specified elements.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.LastIndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the <see cref="T:System.Collections.Immutable.IImmutableList`1" /> that contains the specified number of elements and ends at the specified index.</summary>
      <param name="item">The object to locate in the list. The value can be <see langword="null" /> for reference types.</param>
      <param name="index">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="equalityComparer">The equality comparer to match <paramref name="item" />.</param>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.Remove(`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the first occurrence of a specified object from this immutable list.</summary>
      <param name="value">The object to remove from the list.</param>
      <param name="equalityComparer">The equality comparer to use to locate <paramref name="value" />.</param>
      <returns>A new list with the specified object removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.RemoveAll(System.Predicate{`0})">
      <summary>Removes all the elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
      <returns>A new immutable list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of the immutable list.</summary>
      <param name="index">The index of the element to remove.</param>
      <returns>A new list with the element removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.RemoveRange(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the specified object from the list.</summary>
      <param name="items">The objects to remove from the list.</param>
      <param name="equalityComparer">The equality comparer to use to determine if <paramref name="items" /> match any objects in the list.</param>
      <returns>A new immutable list with the specified objects removed, if <paramref name="items" /> matched objects in the list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Removes a range of elements from the <see cref="T:System.Collections.Immutable.IImmutableList`1" />.</summary>
      <param name="index">The zero-based starting index of the range of elements to remove.</param>
      <param name="count">The number of elements to remove.</param>
      <returns>A new immutable list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.Replace(`0,`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Returns a new list with the first matching element in the list replaced with the specified element.</summary>
      <param name="oldValue">The element to be replaced.</param>
      <param name="newValue">The element to replace the first occurrence of <paramref name="oldValue" /> with</param>
      <param name="equalityComparer">The equality comparer to use for matching <paramref name="oldValue" />.</param>
      <returns>A new list that contains <paramref name="newValue" />, even if <paramref name="oldvalue" /> is the same as <paramref name="newValue" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="oldValue" /> does not exist in the list.</exception>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableList`1.SetItem(System.Int32,`0)">
      <summary>Replaces an element in the list at a given position with the specified element.</summary>
      <param name="index">The position in the list of the element to replace.</param>
      <param name="value">The element to replace the old element with.</param>
      <returns>A new list that contains the new element, even if the element at the specified location is the same as the new element.</returns>
    </member>
    <member name="T:System.Collections.Immutable.IImmutableQueue`1">
      <summary>Represents an immutable first-in, first-out collection of objects.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of elements in the queue.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableQueue`1.Clear">
      <summary>Returns a new queue with all the elements removed.</summary>
      <returns>An empty immutable queue.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableQueue`1.Dequeue">
      <summary>Removes the first element in the immutable queue, and returns the new queue.</summary>
      <returns>The new immutable queue with the first element removed. This value is never <see langword="null" />.</returns>
      <exception cref="T:System.InvalidOperationException">The queue is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableQueue`1.Enqueue(`0)">
      <summary>Adds an element to the end of the immutable queue, and returns the new queue.</summary>
      <param name="value">The element to add.</param>
      <returns>The new immutable queue with the specified element added.</returns>
    </member>
    <member name="P:System.Collections.Immutable.IImmutableQueue`1.IsEmpty">
      <summary>Gets a value that indicates whether this immutable queue is empty.</summary>
      <returns>
        <see langword="true" /> if this queue is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableQueue`1.Peek">
      <summary>Returns the element at the beginning of the immutable queue without removing it.</summary>
      <returns>The element at the beginning of the queue.</returns>
      <exception cref="T:System.InvalidOperationException">The queue is empty.</exception>
    </member>
    <member name="T:System.Collections.Immutable.IImmutableSet`1">
      <summary>Represents a set of elements that can only be modified by creating a new instance of the set.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of element stored in the set.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Add(`0)">
      <summary>Adds the specified element to this immutable set.</summary>
      <param name="value">The element to add.</param>
      <returns>A new set with the element added, or this set if the element is already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Clear">
      <summary>Retrieves an empty immutable set that has the same sorting and ordering semantics as this instance.</summary>
      <returns>An empty set that has the same sorting and ordering semantics as this instance.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Contains(`0)">
      <summary>Determines whether this immutable set contains a specified element.</summary>
      <param name="value">The element to locate in the set.</param>
      <returns>
        <see langword="true" /> if the set contains the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Except(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the elements in the specified collection from the current immutable set.</summary>
      <param name="other">The collection of items to remove from this set.</param>
      <returns>A new set with the items removed; or the original set if none of the items were in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Intersect(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable set that contains only elements that exist in this set and the specified set.</summary>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Immutable.IImmutableSet`1" />.</param>
      <returns>A new immutable set that contains elements that exist in both sets.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable set is a proper (strict) subset of the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper subset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable set is a proper (strict) superset of the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper superset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable set is a subset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a subset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable set is a superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a superset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable set overlaps with the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set and the specified collection share at least one common element; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Remove(`0)">
      <summary>Removes the specified element from this immutable set.</summary>
      <param name="value">The element to remove.</param>
      <returns>A new set with the specified element removed, or the current set if the element cannot be found in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable set and the specified collection contain the same elements.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the sets are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.SymmetricExcept(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable set that contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>A new set that contains the elements that are present only in the current set or in the specified collection, but not both.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.TryGetValue(`0,`0@)">
      <summary>Determines whether the set contains a specified value.</summary>
      <param name="equalValue">The value to search for.</param>
      <param name="actualValue">The matching value from the set, if found, or <c>equalvalue</c> if there are no matches.</param>
      <returns>
        <see langword="true" /> if a matching value was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableSet`1.Union(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates a new immutable set that contains all elements that are present in either the current set or in the specified collection.</summary>
      <param name="other">The collection to add elements from.</param>
      <returns>A new immutable set with the items added; or the original set if all the items were already in the set.</returns>
    </member>
    <member name="T:System.Collections.Immutable.IImmutableStack`1">
      <summary>Represents an immutable last-in-first-out (LIFO) collection.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of elements in the stack.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableStack`1.Clear">
      <summary>Removes all objects from the immutable stack.</summary>
      <returns>An empty immutable stack.</returns>
    </member>
    <member name="P:System.Collections.Immutable.IImmutableStack`1.IsEmpty">
      <summary>Gets a value that indicates whether this immutable stack is empty.</summary>
      <returns>
        <see langword="true" /> if this stack is empty; otherwise,<see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableStack`1.Peek">
      <summary>Returns the element at the top of the immutable stack without removing it.</summary>
      <returns>The element at the top of the stack.</returns>
      <exception cref="T:System.InvalidOperationException">The stack is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableStack`1.Pop">
      <summary>Removes the element at the top of the immutable stack and returns the new stack.</summary>
      <returns>The new stack; never <see langword="null" /></returns>
      <exception cref="T:System.InvalidOperationException">The stack is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.IImmutableStack`1.Push(`0)">
      <summary>Inserts an element at the top of the immutable stack and returns the new stack.</summary>
      <param name="value">The element to push onto the stack.</param>
      <returns>The new stack.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableArray">
      <summary>Provides methods for creating an array that is immutable; meaning it cannot be changed once it is created.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.BinarySearch``1(System.Collections.Immutable.ImmutableArray{``0},``0)">
      <summary>Searches the sorted immutable array for a specified element using the default comparer and returns the zero-based index of the element, if it's found.</summary>
      <param name="array">The sorted array to search.</param>
      <param name="value">The object to search for.</param>
      <typeparam name="T">The type of element stored in the array.</typeparam>
      <returns>The zero-based index of the item in the array, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="value" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.Generic.ICollection`1.Count" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="value" /> does not implement <see cref="T:System.IComparable" /> or the search encounters an element that does not implement <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.BinarySearch``1(System.Collections.Immutable.ImmutableArray{``0},``0,System.Collections.Generic.IComparer{``0})">
      <summary>Searches a sorted immutable array for a specified element and returns the zero-based index of the element, if it's found.</summary>
      <param name="array">The sorted array to search.</param>
      <param name="value">The object to search for.</param>
      <param name="comparer">The comparer implementation to use when comparing elements, or null to use the default comparer.</param>
      <typeparam name="T">The type of element stored in the array.</typeparam>
      <returns>The zero-based index of the item in the array, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="value" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.Generic.ICollection`1.Count" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is null and <paramref name="value" /> does not implement <see cref="T:System.IComparable" /> or the search encounters an element that does not implement <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.BinarySearch``1(System.Collections.Immutable.ImmutableArray{``0},System.Int32,System.Int32,``0)">
      <summary>Searches a sorted immutable array for a specified element and returns the zero-based index of the element, if it's found.</summary>
      <param name="array">The sorted array to search.</param>
      <param name="index">The starting index of the range to search.</param>
      <param name="length">The length of the range to search.</param>
      <param name="value">The object to search for.</param>
      <typeparam name="T">The type of element stored in the array.</typeparam>
      <returns>The zero-based index of the item in the array, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="value" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.Generic.ICollection`1.Count" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="value" /> does not implement <see cref="T:System.IComparable" /> or the search encounters an element that does not implement <see cref="T:System.IComparable" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="length" /> do not specify a valid range in <paramref name="array" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than the lower bound of <paramref name="array" />.
-or-
<paramref name="length" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.BinarySearch``1(System.Collections.Immutable.ImmutableArray{``0},System.Int32,System.Int32,``0,System.Collections.Generic.IComparer{``0})">
      <summary>Searches a sorted immutable array for a specified element and returns the zero-based index of the element.</summary>
      <param name="array">The sorted array to search.</param>
      <param name="index">The starting index of the range to search.</param>
      <param name="length">The length of the range to search.</param>
      <param name="value">The object to search for.</param>
      <param name="comparer">The comparer to use when comparing elements for equality or <see langword="null" /> to use the default comparer.</param>
      <typeparam name="T">The type of element stored in the array.</typeparam>
      <returns>The zero-based index of the item in the array, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="value" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.Generic.ICollection`1.Count" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is null and <paramref name="value" /> does not implement <see cref="T:System.IComparable" /> or the search encounters an element that does not implement <see cref="T:System.IComparable" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="length" /> do not specify a valid range in <paramref name="array" />.
-or-
<paramref name="comparer" /> is <see langword="null" />, and <paramref name="value" /> is of a type that is not compatible with the elements of <paramref name="array" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than the lower bound of <paramref name="array" />.
-or-
<paramref name="length" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1">
      <summary>Creates an empty immutable array.</summary>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An empty immutable array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1(``0)">
      <summary>Creates an immutable array that contains the specified object.</summary>
      <param name="item">The object to store in the array.</param>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An immutable array that contains the specified object.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1(``0,``0)">
      <summary>Creates an immutable array that contains the specified objects.</summary>
      <param name="item1">The first object to store in the array.</param>
      <param name="item2">The second object to store in the array.</param>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An immutable array that contains the specified objects.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1(``0,``0,``0)">
      <summary>Creates an immutable array that contains the specified objects.</summary>
      <param name="item1">The first object to store in the array.</param>
      <param name="item2">The second object to store in the array.</param>
      <param name="item3">The third object to store in the array.</param>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An immutable array that contains the specified objects.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1(``0,``0,``0,``0)">
      <summary>Creates an immutable array that contains the specified objects.</summary>
      <param name="item1">The first object to store in the array.</param>
      <param name="item2">The second object to store in the array.</param>
      <param name="item3">The third object to store in the array.</param>
      <param name="item4">The fourth object to store in the array.</param>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An immutable array that contains the specified objects.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1(``0[])">
      <summary>Creates an immutable array from the specified array of objects.</summary>
      <param name="items">The array of objects to populate the array with.</param>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An immutable array that contains the array of items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1(``0[],System.Int32,System.Int32)">
      <summary>Creates an immutable array with specified objects from another array.</summary>
      <param name="items">The source array of objects.</param>
      <param name="start">The index of the first element to copy from <paramref name="items" />.</param>
      <param name="length">The number of elements from <paramref name="items" /> to include in this immutable array.</param>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An immutable array that contains the specified objects from the source array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.Create``1(System.Collections.Immutable.ImmutableArray{``0},System.Int32,System.Int32)">
      <summary>Creates an immutable array with the specified objects from another immutable array.</summary>
      <param name="items">The source array of objects.</param>
      <param name="start">The index of the first element to copy from <paramref name="items" />.</param>
      <param name="length">The number of elements from <paramref name="items" /> to include in this immutable array.</param>
      <typeparam name="T">The type of elements stored in the array.</typeparam>
      <returns>An immutable array that contains the specified objects from the source array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.CreateBuilder``1">
      <summary>Creates a mutable array that can be converted to an <see cref="T:System.Collections.Immutable.ImmutableArray" /> without allocating new memory.</summary>
      <typeparam name="T">The type of elements stored in the builder.</typeparam>
      <returns>A mutable array of the specified type that can be efficiently converted to an immutable array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.CreateBuilder``1(System.Int32)">
      <summary>Creates a mutable array that can be converted to an <see cref="T:System.Collections.Immutable.ImmutableArray" /> without allocating new memory.</summary>
      <param name="initialCapacity">The initial capacity of the builder.</param>
      <typeparam name="T">The type of elements stored in the builder.</typeparam>
      <returns>A mutable array of the specified type that can be efficiently converted to an immutable array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.CreateRange``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> populated with the specified items.</summary>
      <param name="items">The elements to add to the array.</param>
      <typeparam name="T">The type of element stored in the array.</typeparam>
      <returns>An immutable array that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.CreateRange``2(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,``1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> struct.</summary>
      <param name="items">The source array to initialize the resulting array with.</param>
      <param name="selector">The function to apply to each element from the source array.</param>
      <typeparam name="TSource" />
      <typeparam name="TResult" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.CreateRange``2(System.Collections.Immutable.ImmutableArray{``0},System.Int32,System.Int32,System.Func{``0,``1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> struct.</summary>
      <param name="items">The source array to initialize the resulting array with.</param>
      <param name="start">The index of the first element in the source array to include in the resulting array.</param>
      <param name="length">The number of elements from the source array to include in the resulting array.</param>
      <param name="selector">The function to apply to each element from the source array included in the resulting array.</param>
      <typeparam name="TSource" />
      <typeparam name="TResult" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.CreateRange``3(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,``1,``2},``1)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> struct.</summary>
      <param name="items">The source array to initialize the resulting array with.</param>
      <param name="selector">The function to apply to each element from the source array.</param>
      <param name="arg">An argument to be passed to the selector mapping function.</param>
      <typeparam name="TSource" />
      <typeparam name="TArg" />
      <typeparam name="TResult" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.CreateRange``3(System.Collections.Immutable.ImmutableArray{``0},System.Int32,System.Int32,System.Func{``0,``1,``2},``1)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> struct.</summary>
      <param name="items">The source array to initialize the resulting array with.</param>
      <param name="start">The index of the first element in the source array to include in the resulting array.</param>
      <param name="length">The number of elements from the source array to include in the resulting array.</param>
      <param name="selector">The function to apply to each element from the source array included in the resulting array.</param>
      <param name="arg">An argument to be passed to the selector mapping function.</param>
      <typeparam name="TSource" />
      <typeparam name="TArg" />
      <typeparam name="TResult" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.ToImmutableArray``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates an immutable array from the specified collection.</summary>
      <param name="items">The collection of objects to copy to the immutable array.</param>
      <typeparam name="TSource">The type of elements contained in <paramref name="items" />.</typeparam>
      <returns>An immutable array that contains the specified collection of objects.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray.ToImmutableArray``1(System.Collections.Immutable.ImmutableArray{``0}.Builder)">
      <summary>Creates an immutable array from the current contents of the builder's array.</summary>
      <param name="builder">The builder to create the immutable array from.</param>
      <typeparam name="TSource">The type of elements contained in the immutable array.</typeparam>
      <returns>An immutable array that contains the current contents of the builder's array.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableArray`1">
      <summary>Represents an array that is immutable; meaning it cannot be changed once it is created.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of element stored by the array.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Add(`0)">
      <summary>Returns a copy of the original array with the specified item added to the end.</summary>
      <param name="item">The item to be added to the end of the array.</param>
      <returns>A new array with the specified item added to the end.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Returns a copy of the original array with the specified elements added to the end of the array.</summary>
      <param name="items">The elements to add to the array.</param>
      <returns>A new array with the elements added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.AddRange(System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Returns a copy of the original array with the specified elements added to the end of the array.</summary>
      <param name="items">The elements to add to the array.</param>
      <returns>A new array with the elements added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.As``1">
      <summary>Returns a new immutable array that contains the elements of this array cast to a different type.</summary>
      <typeparam name="TOther">The type of array element to return.</typeparam>
      <returns>An immutable array that contains the elements of this array, cast to a different type. If the cast fails, returns an array whose <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns <see langword="true" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.AsMemory">
      <summary>Creates a new read-only memory region over this immutable array.</summary>
      <returns>The read-only memory representation of this immutable array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.AsSpan">
      <summary>Creates a new read-only span over this immutable array.</summary>
      <returns>The read-only span representation of this immutable array.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableArray`1.Builder">
      <summary>A writable array accessor that can be converted into an <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> instance without allocating extra memory.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Add(`0)">
      <summary>Adds the specified item to the array.</summary>
      <param name="item">The object to add to the array.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange(`0[])">
      <summary>Adds the specified items to the end of the array.</summary>
      <param name="items">The items to add to the array.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange(`0[],System.Int32)">
      <summary>Adds the specified items to the end of the array.</summary>
      <param name="items">The items to add to the array.</param>
      <param name="length">The number of elements from the source array to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Adds the specified items to the end of the array.</summary>
      <param name="items">The items to add to the array.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange(System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Adds the specified items to the end of the array.</summary>
      <param name="items">The items to add to the array.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange(System.Collections.Immutable.ImmutableArray{`0},System.Int32)">
      <summary>Adds the specified items to the end of the array.</summary>
      <param name="items">The items to add to the array.</param>
      <param name="length">The number of elements from the source array to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange(System.Collections.Immutable.ImmutableArray{`0}.Builder)">
      <summary>Adds the specified items to the end of the array.</summary>
      <param name="items">The items to add to the array.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange``1(``0[])">
      <summary>Adds the specified items that derive from the type currently in the array, to the end of the array.</summary>
      <param name="items">The items to add to end of the array.</param>
      <typeparam name="TDerived">The type that derives from the type of item already in the array.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Adds the specified items that derive from the type currently in the array, to the end of the array.</summary>
      <param name="items">The items to add to the end of the array.</param>
      <typeparam name="TDerived">The type that derives from the type of item already in the array.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.AddRange``1(System.Collections.Immutable.ImmutableArray{``0}.Builder)">
      <summary>Adds the specified items that derive from the type currently in the array, to the end of the array.</summary>
      <param name="items">The items to add to the end of the array.</param>
      <typeparam name="TDerived">The type that derives from the type of item already in the array.</typeparam>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.Builder.Capacity">
      <summary>Gets or sets the length of the internal array. When set, the internal array is reallocated to the given capacity if it is not already the specified length.</summary>
      <returns>The length of the internal array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Clear">
      <summary>Removes all items from the array.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Contains(`0)">
      <summary>Determines whether the array contains a specific value.</summary>
      <param name="item">The object to locate in the array.</param>
      <returns>
        <see langword="true" /> if the object is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.CopyTo(`0[],System.Int32)">
      <summary>Copies the current contents to the specified array.</summary>
      <param name="array">The array to copy to.</param>
      <param name="index">The index to start the copy operation.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.Builder.Count">
      <summary>Gets or sets the number of items in the array.</summary>
      <returns>The number of items in the array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.GetEnumerator">
      <summary>Gets an object that can be used to iterate through the collection.</summary>
      <returns>An object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.IndexOf(`0)">
      <summary>Determines the index of a specific item in the array.</summary>
      <param name="item">The item to locate in the array.</param>
      <returns>The index of <paramref name="item" /> if it's found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.IndexOf(`0,System.Int32)">
      <summary>Determines the index of the specified item.</summary>
      <param name="item">The item to locate in the array.</param>
      <param name="startIndex">The starting position of the search.</param>
      <returns>The index of <paramref name="item" /> if it's found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Determines the index of the specified item.</summary>
      <param name="item">The item to locate in the array.</param>
      <param name="startIndex">The starting position of the search.</param>
      <param name="count">The number of elements to search.</param>
      <returns>The index of <paramref name="item" /> if it's found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.IndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Determines the index for the specified item.</summary>
      <param name="item">The item to locate in the array.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <param name="count">The starting position of the search.</param>
      <param name="equalityComparer">The equality comparer to use in the search</param>
      <returns>The index of <paramref name="item" /> if it's found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Insert(System.Int32,`0)">
      <summary>Inserts an item in the array at the specified index.</summary>
      <param name="index">The zero-based index at which to insert the item.</param>
      <param name="item">The object to insert into the array.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.Builder.Item(System.Int32)">
      <summary>Gets or sets the item at the specified index.</summary>
      <param name="index">The index of the item to get or set.</param>
      <returns>The item at the specified index.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The specified index is not in the array.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.ItemRef(System.Int32)">
      <summary>Gets a read-only reference to the element at the specified index.</summary>
      <param name="index">The item index.</param>
      <returns>The read-only reference to the element at the specified index.</returns>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="index" /> is greater or equal to the array count.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.LastIndexOf(`0)">
      <summary>Determines the 0-based index of the last occurrence of the specified item in this array.</summary>
      <param name="item">The item to search for.</param>
      <returns>The 0-based index where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.LastIndexOf(`0,System.Int32)">
      <summary>Determines the 0-based index of the last occurrence of the specified item in this array.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The starting position of the search.</param>
      <returns>The 0-based index into the array where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Determines the 0-based index of the last occurrence of the specified item in this array.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The starting position of the search.</param>
      <param name="count">The number of elements to search.</param>
      <returns>The 0-based index into the array where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.LastIndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Determines the 0-based index of the last occurrence of the specified item in this array.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The starting position of the search.</param>
      <param name="count">The number of elements to search.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>The 0-based index into the array where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.MoveToImmutable">
      <summary>Extracts the internal array as an <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> and replaces it              with a zero length array.</summary>
      <exception cref="T:System.InvalidOperationException">When <see cref="P:System.Collections.Immutable.ImmutableArray`1.Builder.Count" /> doesn't              equal <see cref="P:System.Collections.Immutable.ImmutableArray`1.Builder.Capacity" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Remove(`0)">
      <summary>Removes the specified element.</summary>
      <param name="element">The item to remove.</param>
      <returns>
        <see langword="true" /> if <paramref name="element" /> was found and removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.RemoveAt(System.Int32)">
      <summary>Removes the item at the specified index from the array.</summary>
      <param name="index">The zero-based index of the item to remove.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Reverse">
      <summary>Reverses the order of elements in the collection.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Sort">
      <summary>Sorts the contents of the array.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the contents of the array.</summary>
      <param name="comparer">The comparer to use for sorting. If comparer is <see langword="null" />, the default comparer for the elements type in the array is used.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Sort(System.Comparison{`0})">
      <summary>Sorts the elements in the entire array using the specified <see cref="T:System.Comparison`1" />.</summary>
      <param name="comparison">The <see cref="T:System.Comparison`1" /> to use when comparing elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the contents of the array.</summary>
      <param name="index">The starting index for the sort.</param>
      <param name="count">The number of elements to include in the sort.</param>
      <param name="comparer">The comparer to use for sorting. If comparer is <see langword="null" />, the default comparer for the elements type in the array is used.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.Builder.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the array.</summary>
      <returns>An enumerator that iterates through the array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the array.</summary>
      <returns>An enumerator that iterates through the array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.ToArray">
      <summary>Creates a new array with the current contents of this <see cref="T:System.Collections.Immutable.ImmutableArray`1.Builder" />.</summary>
      <returns>A new array with the contents of this <see cref="T:System.Collections.Immutable.ImmutableArray`1.Builder" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Builder.ToImmutable">
      <summary>Returns an immutable array that contains the current contents of this <see cref="T:System.Collections.Immutable.ImmutableArray`1.Builder" />.</summary>
      <returns>An immutable array that contains the current contents of this <see cref="T:System.Collections.Immutable.ImmutableArray`1.Builder" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.CastArray``1">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> struct by casting the underlying array to an array of type <typeparamref name="TOther" />.</summary>
      <typeparam name="TOther" />
      <exception cref="T:System.InvalidCastException">Thrown if the cast is illegal.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.CastUp``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> struct based on the contents             of an existing instance, allowing a covariant static cast to efficiently reuse the existing array.</summary>
      <param name="items">The array to initialize the array with. No copy is made.</param>
      <typeparam name="TDerived" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Clear">
      <summary>Returns an array with all the elements removed.</summary>
      <returns>An array with all of the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Contains(`0)">
      <summary>Determines whether the specified item exists in the array.</summary>
      <param name="item">The item to search for.</param>
      <returns>
        <see langword="true" /> if the specified item was found in the array; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.CopyTo(`0[])">
      <summary>Copies the contents of this array to the specified array.</summary>
      <param name="destination">The array to copy to.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the contents of this array to the specified array starting at the specified destination index.</summary>
      <param name="destination">The array to copy to.</param>
      <param name="destinationIndex">The index in <paramref name="array" /> where copying begins.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Copies the specified items in this array to the specified array at the specified starting index.</summary>
      <param name="sourceIndex">The index of this array where copying begins.</param>
      <param name="destination">The array to copy to.</param>
      <param name="destinationIndex">The index in <paramref name="array" /> where copying begins.</param>
      <param name="length">The number of elements to copy from this array.</param>
    </member>
    <member name="F:System.Collections.Immutable.ImmutableArray`1.Empty">
      <summary>Gets an empty immutable array.</summary>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableArray`1.Enumerator">
      <summary>An array enumerator.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.Enumerator.Current">
      <summary>Gets the current item.</summary>
      <returns>The current item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Enumerator.MoveNext">
      <summary>Advances to the next value in the array.</summary>
      <returns>
        <see langword="true" /> if another item exists in the array; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Equals(System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Indicates whether specified array is equal to this array.</summary>
      <param name="other">An object to compare with this object.</param>
      <returns>
        <see langword="true" /> if <paramref name="other" /> is equal to this array; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Equals(System.Object)">
      <summary>Determines if this array is equal to the specified object.</summary>
      <param name="obj">The <see cref="T:System.Object" /> to compare with this array.</param>
      <returns>
        <see langword="true" /> if this array is equal to <paramref name="obj" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the contents of the array.</summary>
      <returns>An enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.GetHashCode">
      <summary>Returns a hash code for this instance.</summary>
      <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.IndexOf(`0)">
      <summary>Searches the array for the specified item.</summary>
      <param name="item">The item to search for.</param>
      <returns>The zero-based index position of the item if it is found, or -1 if it is not.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.IndexOf(`0,System.Int32)">
      <summary>Searches the array for the specified item.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <returns>The zero-based index position of the item if it is found, or -1 if it is not.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.IndexOf(`0,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches the array for the specified item.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>The zero-based index position of the item if it is found, or -1 if it is not.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Searches the array for the specified item.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <param name="count">The number of elements to search.</param>
      <returns>The zero-based index position of the item if it is found, or -1 if it is not.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.IndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches the array for the specified item.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <param name="count">The number of elements to search.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>The zero-based index position of the item if it is found, or -1 if it is not.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Insert(System.Int32,`0)">
      <summary>Returns a new array with the specified value inserted at the specified position.</summary>
      <param name="index">The 0-based index into the array at which the new item should be added.</param>
      <param name="item">The item to insert at the start of the array.</param>
      <returns>A new array with the item inserted at the specified index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserts the specified values at the specified index.</summary>
      <param name="index">The index at which to insert the value.</param>
      <param name="items">The elements to insert.</param>
      <returns>A new immutable array with the items inserted at the specified index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.InsertRange(System.Int32,System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Inserts the specified values at the specified index.</summary>
      <param name="index">The index at which to insert the value.</param>
      <param name="items">The elements to insert.</param>
      <returns>A new immutable array with the items inserted at the specified index.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.IsDefault">
      <summary>Gets a value indicating whether this array was declared but not initialized.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> is <see langword="null" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.IsDefaultOrEmpty">
      <summary>Gets a value indicating whether this <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> is empty or is not initialized.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> is <see langword="null" /> or <see cref="F:System.Collections.Immutable.ImmutableArray`1.Empty" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.IsEmpty">
      <summary>Gets a value indicating whether this <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> is empty.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.Item(System.Int32)">
      <summary>Gets the element at the specified index in the immutable array.</summary>
      <param name="index">The zero-based index of the element to get.</param>
      <returns>The element at the specified index in the immutable array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.ItemRef(System.Int32)">
      <summary>Gets a read-only reference to the element at the specified <paramref name="index" /> in the read-only list.</summary>
      <param name="index">The zero-based index of the element to get a reference to.</param>
      <returns>A read-only reference to the element at the specified <paramref name="index" /> in the read-only list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.LastIndexOf(`0)">
      <summary>Searches the array for the specified item; starting at the end of the array.</summary>
      <param name="item">The item to search for.</param>
      <returns>The 0-based index into the array where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.LastIndexOf(`0,System.Int32)">
      <summary>Searches the array for the specified item; starting at the end of the array.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <returns>The 0-based index into the array where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Searches the array for the specified item; starting at the end of the array.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <param name="count">The number of elements to search.</param>
      <returns>The 0-based index into the array where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.LastIndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches the array for the specified item; starting at the end of the array.</summary>
      <param name="item">The item to search for.</param>
      <param name="startIndex">The index at which to begin the search.</param>
      <param name="count">The number of elements to search.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>The 0-based index into the array where the item was found; or -1 if it could not be found.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.Length">
      <summary>Gets the number of elements in the array.</summary>
      <returns>The number of elements in the array</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.OfType``1">
      <summary>Filters the elements of this array to those assignable to the specified type.</summary>
      <typeparam name="TResult">The type to filter the elements of the sequence on.</typeparam>
      <returns>An <see cref="T:System.Collections.IEnumerable" /> that contains elements from the input sequence of type of <paramref name="TResult" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.op_Equality(System.Collections.Immutable.ImmutableArray{`0},System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Returns a value that indicates if two arrays are equal.</summary>
      <param name="left">The array to the left of the operator.</param>
      <param name="right">The array to the right of the operator.</param>
      <returns>
        <see langword="true" /> if the arrays are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.op_Equality(System.Nullable{System.Collections.Immutable.ImmutableArray{`0}},System.Nullable{System.Collections.Immutable.ImmutableArray{`0}})">
      <summary>Returns a value that indicates if two arrays are equal.</summary>
      <param name="left">The array to the left of the operator.</param>
      <param name="right">The array to the right of the operator.</param>
      <returns>
        <see langword="true" /> if the arrays are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.op_Inequality(System.Collections.Immutable.ImmutableArray{`0},System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Returns a value that indicates whether two arrays are not equal.</summary>
      <param name="left">The array to the left of the operator.</param>
      <param name="right">The array to the right of the operator.</param>
      <returns>
        <see langword="true" /> if the arrays are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.op_Inequality(System.Nullable{System.Collections.Immutable.ImmutableArray{`0}},System.Nullable{System.Collections.Immutable.ImmutableArray{`0}})">
      <summary>Checks for inequality between two array.</summary>
      <param name="left">The object to the left of the operator.</param>
      <param name="right">The object to the right of the operator.</param>
      <returns>
        <see langword="true" /> if the two arrays are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Remove(`0)">
      <summary>Returns an array with the first occurrence of the specified element removed from the array. If no match is found, the current array is returned.</summary>
      <param name="item">The item to remove.</param>
      <returns>A new array with the item removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Remove(`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Returns an array with the first occurrence of the specified element removed from the array.
If no match is found, the current array is returned.</summary>
      <param name="item">The item to remove.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>A new array with the specified item removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.RemoveAll(System.Predicate{`0})">
      <summary>Removes all the items from the array that meet the specified condition.</summary>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
      <returns>A new array with items that meet the specified condition removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.RemoveAt(System.Int32)">
      <summary>Returns an array with the element at the specified position removed.</summary>
      <param name="index">The 0-based index of the element to remove from the returned array.</param>
      <returns>A new array with the item at the specified index removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the specified items from this array.</summary>
      <param name="items">The items to remove if matches are found in this list.</param>
      <returns>A new array with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.RemoveRange(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the specified items from this array.</summary>
      <param name="items">The items to remove if matches are found in this list.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>A new array with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.RemoveRange(System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Removes the specified values from this list.</summary>
      <param name="items">The items to remove if matches are found in this list.</param>
      <returns>A new list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.RemoveRange(System.Collections.Immutable.ImmutableArray{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the specified items from this list.</summary>
      <param name="items">The items to remove if matches are found in this list.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>A new array with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Returns an array with the elements at the specified position removed.</summary>
      <param name="index">The 0-based index of the starting element to remove from the array.</param>
      <param name="length">The number of elements to remove from the array.</param>
      <returns>The new array with the specified elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Replace(`0,`0)">
      <summary>Finds the first element in the array equal to the specified value and replaces the value with the specified new value.</summary>
      <param name="oldValue">The value to find and replace in the array.</param>
      <param name="newValue">The value to replace the <c>oldvalue</c> with.</param>
      <returns>A new array that contains <paramref name="newValue" /> even if the new and old values are the same.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="oldValue" /> is not found in the array.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Replace(`0,`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Finds the first element in the array equal to the specified value and replaces the value with the specified new value.</summary>
      <param name="oldValue">The value to find and replace in the array.</param>
      <param name="newValue">The value to replace the <c>oldvalue</c> with.</param>
      <param name="equalityComparer">The equality comparer to use to compare values.</param>
      <returns>A new array that contains <paramref name="newValue" /> even if the new and old values are the same.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="oldValue" /> is not found in the array.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.SetItem(System.Int32,`0)">
      <summary>Replaces the item at the specified index with the specified item.</summary>
      <param name="index">The index of the item to replace.</param>
      <param name="item">The item to add to the list.</param>
      <returns>The new array that contains <paramref name="item" /> at the specified index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Sort">
      <summary>Sorts the elements in the immutable array using the default comparer.</summary>
      <returns>A new immutable array that contains the items in this array, in sorted order.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the elements in the immutable array using the specified comparer.</summary>
      <param name="comparer">The implementation to use when comparing elements, or <see langword="null" /> to use the default comparer</param>
      <returns>A new immutable array that contains the items in this array, in sorted order.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Sort(System.Comparison{`0})">
      <summary>Sorts the elements in the entire <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> using             the specified <see cref="T:System.Comparison`1" />.</summary>
      <param name="comparison">The <see cref="T:System.Comparison`1" /> to use when comparing elements.</param>
      <returns>The sorted list.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the specified elements in the immutable array using the specified comparer.</summary>
      <param name="index">The index of the first element to sort.</param>
      <param name="count">The number of elements to include in the sort.</param>
      <param name="comparer">The implementation to use when comparing elements, or <see langword="null" /> to use the default comparer</param>
      <returns>A new immutable array that contains the items in this array, in sorted order.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="item">The item to add to the end of the array.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#ICollection{T}#Count">
      <summary>Gets the number of array in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">Thrown if the <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns true.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether this instance is read only.</summary>
      <returns>
        <see langword="true" /> if this instance is read only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="item">The object to remove from the array.</param>
      <returns>Throws <see cref="T:System.NotSupportedException" /> in all cases.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the array.</summary>
      <returns>An enumerator that can be used to iterate through the array.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#IList{T}#Insert(System.Int32,`0)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="index">The index of the location to insert the item.</param>
      <param name="item">The item to insert.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#IList{T}#Item(System.Int32)">
      <summary>Gets or sets the element at the specified index in the read-only list.</summary>
      <param name="index">The zero-based index of the element to get.</param>
      <returns>The element at the specified index in the read-only list.</returns>
      <exception cref="T:System.NotSupportedException">Always thrown from the setter.</exception>
      <exception cref="T:System.InvalidOperationException">Thrown if the <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns true.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#IList{T}#RemoveAt(System.Int32)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="index">The index.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#IReadOnlyCollection{T}#Count">
      <summary>Gets the number of array in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">Thrown if the <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns true.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#Generic#IReadOnlyList{T}#Item(System.Int32)">
      <summary>Gets the element at the specified index.</summary>
      <param name="index">The index.</param>
      <returns>The element.</returns>
      <exception cref="T:System.InvalidOperationException">Thrown if the <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns true.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies this array to another array starting at the specified index.</summary>
      <param name="array">The array to copy this array to.</param>
      <param name="index">The index in the destination array to start the copy operation.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#ICollection#Count">
      <summary>Gets the size of the array.</summary>
      <exception cref="T:System.InvalidOperationException">Thrown if the <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns true.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#ICollection#IsSynchronized">
      <summary>See the <see cref="T:System.Collections.ICollection" /> interface.</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#ICollection#SyncRoot">
      <summary>Gets the sync root.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable array.</summary>
      <returns>An enumerator that iterates through the immutable array.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#Add(System.Object)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="value">The value to add to the array.</param>
      <returns>Throws <see cref="T:System.NotSupportedException" /> in all cases.</returns>
      <exception cref="T:System.NotSupportedException">Thrown in all cases.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#Clear">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <exception cref="T:System.NotSupportedException">Thrown in all cases.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#Contains(System.Object)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="value">The value to check for.</param>
      <returns>Throws <see cref="T:System.NotSupportedException" /> in all cases.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Gets the value at the specified index.</summary>
      <param name="value">The value to return the index of.</param>
      <returns>The value of the element at the specified index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="index">Index that indicates where to insert the item.</param>
      <param name="value">The value to insert.</param>
      <exception cref="T:System.NotSupportedException">Thrown in all cases.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#IsFixedSize">
      <summary>Gets a value indicating whether this instance is fixed size.</summary>
      <returns>
        <see langword="true" /> if this instance is fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#IsReadOnly">
      <summary>Gets a value indicating whether this instance is read only.</summary>
      <returns>
        <see langword="true" /> if this instance is read only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Object" /> at the specified index.</summary>
      <param name="index">The index.</param>
      <returns>The object at the specified index.</returns>
      <exception cref="T:System.NotSupportedException">Always thrown from the setter.</exception>
      <exception cref="T:System.InvalidOperationException">Thrown if the <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault" /> property returns true.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#Remove(System.Object)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="value">The value to remove from the array.</param>
      <exception cref="T:System.NotSupportedException">Thrown in all cases.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IList#RemoveAt(System.Int32)">
      <summary>Throws <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <param name="index">The index of the item to remove.</param>
      <exception cref="T:System.NotSupportedException">Thrown in all cases.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#Add(`0)">
      <summary>Returns a copy of the original array with the specified item added to the end.</summary>
      <param name="value">The value to add to the end of the array.</param>
      <returns>A new array with the specified item added to the end.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Returns a copy of the original array with the specified elements added to the end of the array.</summary>
      <param name="items">The elements to add to the end of the array.</param>
      <returns>A new array with the elements added to the end.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#Clear">
      <summary>Returns an array with all the elements removed.</summary>
      <returns>An array with all the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#Insert(System.Int32,`0)">
      <summary>Returns a new array with the specified value inserted at the specified position.</summary>
      <param name="index">The 0-based index into the array at which the new item should be added.</param>
      <param name="element">The item to insert at the start of the array.</param>
      <returns>A new array with the specified value inserted.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserts the specified values at the specified index.</summary>
      <param name="index">The index at which to insert the value.</param>
      <param name="items">The elements to insert.</param>
      <returns>A new array with the specified values inserted.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#Remove(`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Returns an array with the first occurrence of the specified element removed from the array; if no match is found, the current array is returned.</summary>
      <param name="value">The value to remove from the array.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>A new array with the value removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#RemoveAll(System.Predicate{`0})">
      <summary>Removes all the items from the array that meet the specified condition.</summary>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
      <returns>A new array with items that meet the specified condition removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#RemoveAt(System.Int32)">
      <summary>Returns an array with the element at the specified position removed.</summary>
      <param name="index">The 0-based index of the element to remove from the returned array.</param>
      <returns>A new array with the specified item removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#RemoveRange(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the specified items from this array.</summary>
      <param name="items">The items to remove if matches are found in this list.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>A new array with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#RemoveRange(System.Int32,System.Int32)">
      <summary>Returns an array with the elements at the specified position removed.</summary>
      <param name="index">The 0-based index of the starting element to remove from the array.</param>
      <param name="count">The number of elements to remove from the array.</param>
      <returns>The new array with the specified elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#Replace(`0,`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Finds the first element in the array equal to the specified value and replaces the value with the specified new value.</summary>
      <param name="oldValue">The value to find and replace in the array.</param>
      <param name="newValue">The value to replace the <c>oldvalue</c> with.</param>
      <param name="equalityComparer">The equality comparer to use to compare values.</param>
      <returns>A new array that contains <paramref name="newValue" /> even if the new and old values are the same.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="oldValue" /> is not found in the array.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#Immutable#IImmutableList{T}#SetItem(System.Int32,`0)">
      <summary>Replaces the item at the specified index with the specified item.</summary>
      <param name="index">The index of the item to replace.</param>
      <param name="value">The value to add to the list.</param>
      <returns>The new array that contains <paramref name="item" /> at the specified index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Determines whether the current collection element precedes, occurs in the same position as, or follows another element in the sort order.</summary>
      <param name="other">The element to compare with the current instance.</param>
      <param name="comparer">The object used to compare members of the current array with the corresponding members of other array.</param>
      <returns>An integer that indicates whether the current element precedes, is in the same position or follows the other element.</returns>
      <exception cref="T:System.ArgumentException">The arrays are not the same length.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Determines whether this array is structurally equal to the specified array.</summary>
      <param name="other">The array to compare with the current instance.</param>
      <param name="comparer">An object that determines whether the current instance and other are structurally equal.</param>
      <returns>
        <see langword="true" /> if the two arrays are structurally equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Returns a hash code for the current instance.</summary>
      <param name="comparer">An object that computes the hash code of the current object.</param>
      <returns>The hash code for the current instance.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableArray`1.ToBuilder">
      <summary>Creates a mutable array that has the same contents as this array and can be efficiently mutated across multiple operations using standard mutable interfaces.</summary>
      <returns>The new builder with the same contents as this array.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableDictionary">
      <summary>Provides a set of initialization methods for instances of the <see cref="T:System.Collections.Immutable.ImmutableDictionary`2" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.Contains``2(System.Collections.Immutable.IImmutableDictionary{``0,``1},``0,``1)">
      <summary>Determines whether the specified immutable dictionary contains the specified key/value pair.</summary>
      <param name="map">The immutable dictionary to search.</param>
      <param name="key">The key to locate in the immutable dictionary.</param>
      <param name="value">The value to locate on the specified key, if the key is found.</param>
      <typeparam name="TKey">The type of the keys in the immutable dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the immutable dictionary.</typeparam>
      <returns>
        <see langword="true" /> if this map contains the specified key/value pair; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.Create``2">
      <summary>Creates an empty immutable dictionary.</summary>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>An empty immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.Create``2(System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Creates an empty immutable dictionary that uses the specified key comparer.</summary>
      <param name="keyComparer">The implementation to use to determine the equality of keys in the dictionary.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>An empty immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.Create``2(System.Collections.Generic.IEqualityComparer{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates an empty immutable dictionary that uses the specified key and value comparers.</summary>
      <param name="keyComparer">The implementation to use to determine the equality of keys in the dictionary.</param>
      <param name="valueComparer">The implementation to use to determine the equality of values in the dictionary.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>An empty immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.CreateBuilder``2">
      <summary>Creates a new immutable dictionary builder.</summary>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>The new builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.CreateBuilder``2(System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Creates a new immutable dictionary builder.</summary>
      <param name="keyComparer">The key comparer.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>The new builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.CreateBuilder``2(System.Collections.Generic.IEqualityComparer{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a new immutable dictionary builder.</summary>
      <param name="keyComparer">The key comparer.</param>
      <param name="valueComparer">The value comparer.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>The new builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.CreateRange``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Creates a new immutable dictionary that contains the specified items.</summary>
      <param name="items">The items used to populate the dictionary before it's immutable.</param>
      <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of values in the dictionary.</typeparam>
      <returns>A new immutable dictionary that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.CreateRange``2(System.Collections.Generic.IEqualityComparer{``0},System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Creates a new immutable dictionary that contains the specified items and uses the specified key comparer.</summary>
      <param name="keyComparer">The comparer implementation to use to compare keys for equality.</param>
      <param name="items">The items to add to the dictionary before it's immutable.</param>
      <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of values in the dictionary.</typeparam>
      <returns>A new immutable dictionary that contains the specified items and uses the specified comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.CreateRange``2(System.Collections.Generic.IEqualityComparer{``0},System.Collections.Generic.IEqualityComparer{``1},System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Creates a new immutable dictionary that contains the specified items and uses the specified key comparer.</summary>
      <param name="keyComparer">The comparer implementation to use to compare keys for equality.</param>
      <param name="valueComparer">The comparer implementation to use to compare values for equality.</param>
      <param name="items">The items to add to the dictionary before it's immutable.</param>
      <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of values in the dictionary.</typeparam>
      <returns>A new immutable dictionary that contains the specified items and uses the specified comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.GetValueOrDefault``2(System.Collections.Immutable.IImmutableDictionary{``0,``1},``0)">
      <summary>Gets the value for a given key if a matching key exists in the dictionary.</summary>
      <param name="dictionary">The dictionary to retrieve the value from.</param>
      <param name="key">The key to search for.</param>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="TValue">The type of the value.</typeparam>
      <returns>The value for the key, or <c>default(TValue)</c> if no matching key was found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.GetValueOrDefault``2(System.Collections.Immutable.IImmutableDictionary{``0,``1},``0,``1)">
      <summary>Gets the value for a given key if a matching key exists in the dictionary.</summary>
      <param name="dictionary">The dictionary to retrieve the value from.</param>
      <param name="key">The key to search for.</param>
      <param name="defaultValue">The default value to return if no matching key is found in the dictionary.</param>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="TValue">The type of the value.</typeparam>
      <returns>The value for the key, or <paramref name="defaultValue" /> if no matching key was found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Constructs an immutable dictionary from an existing collection of elements, applying a transformation function to the source keys.</summary>
      <param name="source">The source collection used to generate the immutable dictionary.</param>
      <param name="keySelector">The function used to transform keys for the immutable dictionary.</param>
      <typeparam name="TSource">The type of element in the source collection.</typeparam>
      <typeparam name="TKey">The type of key in the resulting immutable dictionary.</typeparam>
      <returns>The immutable dictionary that contains elements from <paramref name="source" />, with keys transformed by applying <paramref name="keySelector" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Constructs an immutable dictionary based on some transformation of a sequence.</summary>
      <param name="source">The source collection used to generate the immutable dictionary.</param>
      <param name="keySelector">The function used to transform keys for the immutable dictionary.</param>
      <param name="keyComparer">The key comparer to use for the dictionary.</param>
      <typeparam name="TSource">The type of element in the source collection.</typeparam>
      <typeparam name="TKey">The type of key in the resulting immutable dictionary.</typeparam>
      <returns>The immutable dictionary that contains elements from <paramref name="source" />, with keys transformed by applying <paramref name="keySelector" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Enumerates a sequence of key/value pairs and produces an immutable dictionary of its contents.</summary>
      <param name="source">The sequence of key/value pairs to enumerate.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable dictionary that contains the key/value pairs in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Enumerates a sequence of key/value pairs and produces an immutable dictionary of its contents by using the specified key comparer.</summary>
      <param name="source">The sequence of key/value pairs to enumerate.</param>
      <param name="keyComparer">The key comparer to use when building the immutable dictionary.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable dictionary that contains the key/value pairs in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}},System.Collections.Generic.IEqualityComparer{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Enumerates a sequence of key/value pairs and produces an immutable dictionary of its contents by using the specified key and value comparers.</summary>
      <param name="source">The sequence of key/value pairs to enumerate.</param>
      <param name="keyComparer">The key comparer to use when building the immutable dictionary.</param>
      <param name="valueComparer">The value comparer to use for the immutable dictionary.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable dictionary that contains the key/value pairs in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}.Builder)">
      <summary>Creates an immutable dictionary from the current contents of the builder's dictionary.</summary>
      <param name="builder">The builder to create the immutable dictionary from.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable dictionary that contains the current contents in the builder's dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Enumerates and transforms a sequence, and produces an immutable dictionary of its contents.</summary>
      <param name="source">The sequence to enumerate to generate the dictionary.</param>
      <param name="keySelector">The function that will produce the key for the dictionary from each sequence element.</param>
      <param name="elementSelector">The function that will produce the value for the dictionary from each sequence element.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <typeparam name="TKey">The type of the keys in the resulting dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the resulting dictionary.</typeparam>
      <returns>An immutable dictionary that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Enumerates and transforms a sequence, and produces an immutable dictionary of its contents by using the specified key comparer.</summary>
      <param name="source">The sequence to enumerate to generate the dictionary.</param>
      <param name="keySelector">The function that will produce the key for the dictionary from each sequence element.</param>
      <param name="elementSelector">The function that will produce the value for the dictionary from each sequence element.</param>
      <param name="keyComparer">The key comparer to use for the dictionary.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <typeparam name="TKey">The type of the keys in the resulting dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the resulting dictionary.</typeparam>
      <returns>An immutable dictionary that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary.ToImmutableDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Enumerates and transforms a sequence, and produces an immutable dictionary of its contents by using the specified key and value comparers.</summary>
      <param name="source">The sequence to enumerate to generate the dictionary.</param>
      <param name="keySelector">The function that will produce the key for the dictionary from each sequence element.</param>
      <param name="elementSelector">The function that will produce the value for the dictionary from each sequence element.</param>
      <param name="keyComparer">The key comparer to use for the dictionary.</param>
      <param name="valueComparer">The value comparer to use for the dictionary.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <typeparam name="TKey">The type of the keys in the resulting dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the resulting dictionary.</typeparam>
      <returns>An immutable dictionary that contains the items in the specified sequence.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableDictionary`2">
      <summary>Represents an immutable, unordered collection of keys and values.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Add(`0,`1)">
      <summary>Adds an element with the specified key and value to the immutable dictionary.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
      <returns>A new immutable dictionary that contains the additional key/value pair.</returns>
      <exception cref="T:System.ArgumentException">The given key already exists in the dictionary but has a different value.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Adds the specified key/value pairs to the immutable dictionary.</summary>
      <param name="pairs">The key/value pairs to add.</param>
      <returns>A new immutable dictionary that contains the additional key/value pairs.</returns>
      <exception cref="T:System.ArgumentException">One of the given keys already exists in the dictionary but has a different value.</exception>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableDictionary`2.Builder">
      <summary>Represents a hash map that mutates with little or no memory allocations and that can produce or build on immutable hash map instances very efficiently.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="TKey" />
      <typeparam name="TValue" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.Add(`0,`1)">
      <summary>Adds an element that has the specified key and value to the immutable dictionary.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">An element with the same key already exists in the dictionary.</exception>
      <exception cref="T:System.NotSupportedException">The dictionary is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Adds the specified item to the immutable dictionary.</summary>
      <param name="item">The object to add to the dictionary.</param>
      <exception cref="T:System.NotSupportedException">The dictionary is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Adds a sequence of values to this collection.</summary>
      <param name="items">The items to add to this collection.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.Clear">
      <summary>Removes all items from the immutable dictionary.</summary>
      <exception cref="T:System.NotSupportedException">The dictionary is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether the immutable dictionary contains a specific value.</summary>
      <param name="item">The object to locate in the dictionary.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is found in the dictionary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.ContainsKey(`0)">
      <summary>Determines whether the immutable dictionary contains an element that has the specified key.</summary>
      <param name="key">The key to locate in the dictionary.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the key; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.ContainsValue(`1)">
      <summary>Determines whether the immutable dictionary contains an element that has the specified value.</summary>
      <param name="value">The value to locate in the immutable dictionary. The value can be <see langword="null" /> for reference types.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.Count">
      <summary>Gets the number of elements contained in the immutable dictionary.</summary>
      <returns>The number of elements contained in the immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable dictionary.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.GetValueOrDefault(`0)">
      <summary>Gets the value for a given key if a matching key exists in the dictionary.</summary>
      <param name="key">The key to search for.</param>
      <returns>The value for the key, or <c>default(TValue)</c> if no matching key was found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.GetValueOrDefault(`0,`1)">
      <summary>Gets the value for a given key if a matching key exists in the dictionary.</summary>
      <param name="key">The key to search for.</param>
      <param name="defaultValue">The default value to return if no matching key is found in the dictionary.</param>
      <returns>The value for the key, or <paramref name="defaultValue" /> if no matching key was found.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.Item(`0)">
      <summary>Gets or sets the element with the specified key.</summary>
      <param name="key">The element to get or set.</param>
      <returns>The element that has the specified key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The property is being retrieved, and <paramref name="key" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">The property is being set, and the <see cref="T:System.Collections.Generic.IDictionary`2" /> is read-only.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.KeyComparer">
      <summary>Gets or sets the key comparer.</summary>
      <returns>The key comparer.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.Keys">
      <summary>Gets a collection that contains the keys of the immutable dictionary.</summary>
      <returns>A collection that contains the keys of the object that implements the immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.Remove(`0)">
      <summary>Removes the element with the specified key from the immutable dictionary.</summary>
      <param name="key">The key of the element to remove.</param>
      <returns>
        <see langword="true" /> if the element is successfully removed; otherwise, <see langword="false" />.  This method also returns <see langword="false" /> if <paramref name="key" /> was not found in the dictionary.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The dictionary is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Removes the first occurrence of a specific object from the immutable dictionary.</summary>
      <param name="item">The object to remove from the dictionary.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> was successfully removed from the dictionary; otherwise, <see langword="false" />. This method also returns false if <paramref name="item" /> is not found in the dictionary.</returns>
      <exception cref="T:System.NotSupportedException">The dictionary is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes any entries with keys that match those found in the specified sequence from the immutable dictionary.</summary>
      <param name="keys">The keys for entries to remove from the dictionary.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <param name="array" />
      <param name="arrayIndex" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#IsReadOnly" />
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#Generic#IDictionary{TKey@TValue}#Keys" />
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#Generic#IDictionary{TKey@TValue}#Values" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{TKey@TValue}}#GetEnumerator" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the dictionary to an array of type <see cref="T:System.Collections.Generic.KeyValuePair`2" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array of type <see cref="T:System.Collections.Generic.KeyValuePair`2" /> that is the destination of the elements copied from the dictionary. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds an element with the provided key and value to the dictionary object.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the dictionary object contains an element with the specified key.</summary>
      <param name="key">The key to locate.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the dictionary.</summary>
      <returns>An <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the dictionary.</returns>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.IDictionary" /> object has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IDictionary" /> object has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the element with the specified key.</summary>
      <param name="key">The key.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the dictionary.</summary>
      <param name="key">The key of the element to remove.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.ToImmutable">
      <summary>Creates an immutable dictionary based on the contents of this instance.</summary>
      <returns>An immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.TryGetKey(`0,`0@)">
      <summary>Determines whether this dictionary contains a specified key.</summary>
      <param name="equalKey">The key to search for.</param>
      <param name="actualKey">The matching key located in the dictionary if found, or <c>equalkey</c> if no match is found.</param>
      <returns>
        <see langword="true" /> if a match for <paramref name="equalKey" /> is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Builder.TryGetValue(`0,`1@)">
      <summary>Returns the value associated with the specified key.</summary>
      <param name="key">The key whose value will be retrieved.</param>
      <param name="value">When this method returns, contains the value associated with the specified key, if the key is found; otherwise, returns the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
      <returns>
        <see langword="true" /> if the object that implements the immutable dictionary contains an element with the specified key; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.ValueComparer">
      <summary>Gets or sets the value comparer.</summary>
      <returns>The value comparer.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Builder.Values">
      <summary>Gets a collection that contains the values of the immutable dictionary.</summary>
      <returns>A collection that contains the values of the object that implements the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Clear">
      <summary>Retrieves an empty immutable dictionary that has the same ordering and key/value comparison rules as this dictionary instance.</summary>
      <returns>An empty dictionary with equivalent ordering and key/value comparison rules.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether this immutable dictionary contains the specified key/value pair.</summary>
      <param name="pair">The key/value pair to locate.</param>
      <returns>
        <see langword="true" /> if the specified key/value pair is found in the dictionary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.ContainsKey(`0)">
      <summary>Determines whether the immutable dictionary contains an element with the specified key.</summary>
      <param name="key">The key to locate.</param>
      <returns>
        <see langword="true" /> if the immutable dictionary contains an element with the specified key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.ContainsValue(`1)">
      <summary>Determines whether the immutable dictionary contains an element with the specified value.</summary>
      <param name="value">The value to locate. The value can be <see langword="null" /> for reference types.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Count">
      <summary>Gets the number of key/value pairs in the immutable dictionary.</summary>
      <returns>The number of key/value pairs in the dictionary.</returns>
    </member>
    <member name="F:System.Collections.Immutable.ImmutableDictionary`2.Empty">
      <summary>Gets an empty immutable dictionary.</summary>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableDictionary`2.Enumerator">
      <summary>Enumerates the contents of the immutable dictionary without allocating any memory.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="TKey" />
      <typeparam name="TValue" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the dictionary at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Enumerator.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Collections.Immutable.ImmutableDictionary`2.Enumerator" /> class.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the immutable dictionary.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the dictionary.</returns>
      <exception cref="T:System.InvalidOperationException">The dictionary was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the dictionary.</summary>
      <exception cref="T:System.InvalidOperationException">The dictionary was modified after the enumerator was created.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current element.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable dictionary.</summary>
      <returns>An enumerator that can be used to iterate through the dictionary.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.IsEmpty">
      <summary>Gets a value that indicates whether this instance of the immutable dictionary is empty.</summary>
      <returns>
        <see langword="true" /> if this instance is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Item(`0)">
      <summary>Gets the <paramref name="TValue" /> associated with the specified key.</summary>
      <param name="key">The type of the key.</param>
      <returns>The value associated with the specified key. If no results are found, the operation throws an exception.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.KeyComparer">
      <summary>Gets the key comparer for the immutable dictionary.</summary>
      <returns>The key comparer.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Keys">
      <summary>Gets the keys in the immutable dictionary.</summary>
      <returns>The keys in the immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.Remove(`0)">
      <summary>Removes the element with the specified key from the immutable dictionary.</summary>
      <param name="key">The key of the element to remove.</param>
      <returns>A new immutable dictionary with the specified element removed; or this instance if the specified key cannot be found in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the elements with the specified keys from the immutable dictionary.</summary>
      <param name="keys">The keys of the elements to remove.</param>
      <returns>A new immutable dictionary with the specified keys removed; or this instance if the specified keys cannot be found in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.SetItem(`0,`1)">
      <summary>Sets the specified key and value in the immutable dictionary, possibly overwriting an existing value for the key.</summary>
      <param name="key">The key of the entry to add.</param>
      <param name="value">The key value to set.</param>
      <returns>A new immutable dictionary that contains the specified key/value pair.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.SetItems(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Sets the specified key/value pairs in the immutable dictionary, possibly overwriting existing values for the keys.</summary>
      <param name="items">The key/value pairs to set in the dictionary. If any of the keys already exist in the dictionary, this method will overwrite their previous values.</param>
      <returns>A new immutable dictionary that contains the specified key/value pairs.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <param name="item" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#Clear" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <param name="array" />
      <param name="arrayIndex" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#IsReadOnly" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <param name="item" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Add(`0,`1)">
      <param name="key" />
      <param name="value" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Item(`0)">
      <param name="key" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(`0)">
      <param name="key" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{TKey@TValue}}#GetEnumerator" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the dictionary to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the dictionary. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds an element with the provided key and value to the immutable dictionary object.</summary>
      <param name="key">The object to use as the key of the element to add.</param>
      <param name="value">The object to use as the value of the element to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#Clear">
      <summary>Clears this instance.</summary>
      <exception cref="T:System.NotSupportedException">The dictionary object is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the immutable dictionary object contains an element with the specified key.</summary>
      <param name="key">The key to locate in the dictionary object.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the immutable dictionary object.</summary>
      <returns>An enumerator object for the dictionary object.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> object has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IDictionary" /> object has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the element with the specified key.</summary>
      <param name="key">The key.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the immutable dictionary object.</summary>
      <param name="key">The key of the element to remove.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#Add(`0,`1)">
      <param name="key" />
      <param name="value" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <param name="pairs" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#Clear" />
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#Remove(`0)">
      <param name="key" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <param name="keys" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#SetItem(`0,`1)">
      <param name="key" />
      <param name="value" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#SetItems(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <param name="items" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.ToBuilder">
      <summary>Creates an immutable dictionary with the same contents as this dictionary that can be efficiently mutated across multiple operations by using standard mutable interfaces.</summary>
      <returns>A collection with the same contents as this dictionary that can be efficiently mutated across multiple operations by using standard mutable interfaces.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.TryGetKey(`0,`0@)">
      <summary>Determines whether this dictionary contains a specified key.</summary>
      <param name="equalKey">The key to search for.</param>
      <param name="actualKey">The matching key located in the dictionary if found, or <c>equalkey</c> if no match is found.</param>
      <returns>
        <see langword="true" /> if a match for <paramref name="equalKey" /> is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.TryGetValue(`0,`1@)">
      <summary>Gets the value associated with the specified key.</summary>
      <param name="key">The key whose value will be retrieved.</param>
      <param name="value">When this method returns, contains the value associated with the specified key, if the key is found; otherwise, contains the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
      <returns>
        <see langword="true" /> if the object that implements the dictionary contains an element with the specified key; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.ValueComparer">
      <summary>Gets the value comparer used to determine whether values are equal.</summary>
      <returns>The value comparer used to determine whether values are equal.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableDictionary`2.Values">
      <summary>Gets the values in the immutable dictionary.</summary>
      <returns>The values in the immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.WithComparers(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Gets an instance of the immutable dictionary that uses the specified key comparer.</summary>
      <param name="keyComparer">The key comparer to use.</param>
      <returns>An instance of the immutable dictionary that uses the given comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableDictionary`2.WithComparers(System.Collections.Generic.IEqualityComparer{`0},System.Collections.Generic.IEqualityComparer{`1})">
      <summary>Gets an instance of the immutable dictionary that uses the specified key and value comparers.</summary>
      <param name="keyComparer">The key comparer to use.</param>
      <param name="valueComparer">The value comparer to use.</param>
      <returns>An instance of the immutable dictionary that uses the given comparers.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableHashSet">
      <summary>Provides a set of initialization methods for instances of the <see cref="T:System.Collections.Immutable.ImmutableHashSet`1" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.Create``1">
      <summary>Creates an empty immutable hash set.</summary>
      <typeparam name="T">The type of items to be stored in the immutable hash set.</typeparam>
      <returns>An empty immutable hash set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.Create``1(``0)">
      <summary>Creates a new immutable hash set that contains the specified item.</summary>
      <param name="item">The item to prepopulate the hash set with.</param>
      <typeparam name="T">The type of items in the immutable hash set.</typeparam>
      <returns>A new immutable hash set that contains the specified item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.Create``1(``0[])">
      <summary>Creates a new immutable hash set that contains the specified array of items.</summary>
      <param name="items">An array that contains the items to prepopulate the hash set with.</param>
      <typeparam name="T">The type of items in the immutable hash set.</typeparam>
      <returns>A new immutable hash set that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.Create``1(System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Creates an empty immutable hash set that uses the specified equality comparer.</summary>
      <param name="equalityComparer">The object to use for comparing objects in the set for equality.</param>
      <typeparam name="T">The type of items in the immutable hash set.</typeparam>
      <returns>An empty immutable hash set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.Create``1(System.Collections.Generic.IEqualityComparer{``0},``0)">
      <summary>Creates a new immutable hash set that contains the specified item and uses the specified equality comparer for the set type.</summary>
      <param name="equalityComparer">The object to use for comparing objects in the set for equality.</param>
      <param name="item">The item to prepopulate the hash set with.</param>
      <typeparam name="T">The type of items in the immutable hash set.</typeparam>
      <returns>A new immutable hash set that contains the specified item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.Create``1(System.Collections.Generic.IEqualityComparer{``0},``0[])">
      <summary>Creates a new immutable hash set that contains the items in the specified collection and uses the specified equality comparer for the set type.</summary>
      <param name="equalityComparer">The object to use for comparing objects in the set for equality.</param>
      <param name="items">An array that contains the items to prepopulate the hash set with.</param>
      <typeparam name="T">The type of items stored in the immutable hash set.</typeparam>
      <returns>A new immutable hash set that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.CreateBuilder``1">
      <summary>Creates a new immutable hash set builder.</summary>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The immutable hash set builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.CreateBuilder``1(System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Creates a new immutable hash set builder.</summary>
      <param name="equalityComparer">The object to use for comparing objects in the set for equality.</param>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The new immutable hash set builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.CreateRange``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new immutable hash set prefilled with the specified items.</summary>
      <param name="items">The items to add to the hash set.</param>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The new immutable hash set that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.CreateRange``1(System.Collections.Generic.IEqualityComparer{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new immutable hash set that contains the specified items and uses the specified equality comparer for the set type.</summary>
      <param name="equalityComparer">The object to use for comparing objects in the set for equality.</param>
      <param name="items">The items add to the collection before immutability is applied.</param>
      <typeparam name="T">The type of items stored in the collection.</typeparam>
      <returns>The new immutable hash set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.ToImmutableHashSet``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Enumerates a sequence and produces an immutable hash set of its contents.</summary>
      <param name="source">The sequence to enumerate.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <returns>An immutable hash set that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.ToImmutableHashSet``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Enumerates a sequence, produces an immutable hash set of its contents, and uses the specified equality comparer for the set type.</summary>
      <param name="source">The sequence to enumerate.</param>
      <param name="equalityComparer">The object to use for comparing objects in the set for equality.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <returns>An immutable hash set that contains the items in the specified sequence and uses the specified equality comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet.ToImmutableHashSet``1(System.Collections.Immutable.ImmutableHashSet{``0}.Builder)">
      <summary>Creates an immutable hash set from the current contents of the builder's set.</summary>
      <param name="builder">The builder to create the immutable hash set from.</param>
      <typeparam name="TSource">The type of the elements in the hash set.</typeparam>
      <returns>An immutable hash set that contains the current contents in the builder's set.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableHashSet`1">
      <summary>Represents an immutable, unordered hash set.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of elements in the hash set.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Add(`0)">
      <summary>Adds the specified element to the hash set.</summary>
      <param name="item">The element to add to the set.</param>
      <returns>A hash set that contains the added value and any values previously held by the  <see cref="T:System.Collections.Immutable.ImmutableHashSet`1" /> object.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableHashSet`1.Builder">
      <summary>Represents a hash set that mutates with little or no memory allocations and that can produce or build on immutable hash set instances very efficiently.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.Add(`0)">
      <summary>Adds the specified item to the immutable hash set.</summary>
      <param name="item">The item to add.</param>
      <returns>
        <see langword="true" /> if the item did not already belong to the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.Clear">
      <summary>Removes all items from the immutable hash set.</summary>
      <exception cref="T:System.NotSupportedException">The hash set is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.Contains(`0)">
      <summary>Determines whether the immutable hash set contains a specific value.</summary>
      <param name="item">The object to locate in the hash set.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is found in the hash set ; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.Builder.Count">
      <summary>Gets the number of elements contained in the immutable hash set.</summary>
      <returns>The number of elements contained in the immutable hash set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes all elements in the specified collection from the current hash set.</summary>
      <param name="other">The collection of items to remove from the set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable hash set.</summary>
      <returns>An enumerator that can be used to iterate through the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are also in a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a proper (strict) subset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper subset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a proper (strict) superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper superset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a subset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a subset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a superset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.Builder.KeyComparer">
      <summary>Gets or sets the key comparer.</summary>
      <returns>The key comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set overlaps with the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set and <paramref name="other" /> share at least one common element; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the immutable hash set.</summary>
      <param name="item">The object to remove from the set.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> was successfully removed from the set ; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if <paramref name="item" /> is not found in the original set.</returns>
      <exception cref="T:System.NotSupportedException">The set is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set and the specified collection contain the same elements.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is equal to <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an item to the hash set.</summary>
      <param name="item">The object to add to the set.</param>
      <exception cref="T:System.NotSupportedException">The set is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.System#Collections#Generic#ICollection{T}#CopyTo(`0[],System.Int32)">
      <summary>Copies the elements of the hash set to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the hash set. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.Builder.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.ToImmutable">
      <summary>Creates an immutable hash set based on the contents of this instance.</summary>
      <returns>An immutable set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Builder.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains all elements that are present in both the current set and in the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Clear">
      <summary>Retrieves an empty immutable hash set that has the same sorting and ordering semantics as this instance.</summary>
      <returns>An empty hash set that has the same sorting and ordering semantics as this instance.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Contains(`0)">
      <summary>Determines whether this immutable hash set contains the specified element.</summary>
      <param name="item">The object to locate in the immutable hash set.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is found in the <see cref="T:System.Collections.Immutable.ImmutableHashSet`1" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.Count">
      <summary>Gets the number of elements in the immutable hash set.</summary>
      <returns>The number of elements in the hash set.</returns>
    </member>
    <member name="F:System.Collections.Immutable.ImmutableHashSet`1.Empty">
      <summary>Gets an immutable hash set for this type that uses the default <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableHashSet`1.Enumerator">
      <summary>Enumerates the contents of the immutable hash set without allocating any memory.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Enumerator.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Collections.Immutable.ImmutableHashSet`1.Enumerator" /> class.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the immutable hash set.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the hash set.</returns>
      <exception cref="T:System.InvalidOperationException">The hash set was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the hash set.</summary>
      <exception cref="T:System.InvalidOperationException">The hash set was modified after the enumerator was created.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current element.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Except(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the elements in the specified collection from the current immutable hash set.</summary>
      <param name="other">The collection of items to remove from this set.</param>
      <returns>A new set with the items removed; or the original set if none of the items were in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Intersect(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable hash set that contains elements that exist in both this set and the specified set.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>A new immutable set that contains any elements that exist in both sets.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.IsEmpty">
      <summary>Gets a value that indicates whether the current immutable hash set is empty.</summary>
      <returns>
        <see langword="true" /> if this instance is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable hash set is a proper (strict) subset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper subset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable hash set is a proper (strict) superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper superset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable hash set is a subset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a subset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable hash set is a superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a superset of the specified collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.KeyComparer">
      <summary>Gets the object that is used to obtain hash codes for the keys and to check the equality of values in the immutable hash set.</summary>
      <returns>The comparer used to obtain hash codes for the keys and check equality.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable hash set overlaps with the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set and the specified collection share at least one common element; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Remove(`0)">
      <summary>Removes the specified element from this immutable hash set.</summary>
      <param name="item">The element to remove.</param>
      <returns>A new set with the specified element removed, or the current set if the element cannot be found in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable hash set and the specified collection contain the same elements.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the sets are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.SymmetricExcept(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable hash set that contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>A new set that contains the elements that are present only in the current set or in the specified collection, but not both.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an item to the set.</summary>
      <param name="item">The object to add to the set.</param>
      <exception cref="T:System.NotSupportedException">The set is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all items from this set.</summary>
      <exception cref="T:System.NotSupportedException">The set is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ICollection{T}#CopyTo(`0[],System.Int32)">
      <summary>Copies the elements of the set to an array, starting at a particular index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the set. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>See the <see cref="T:System.Collections.Generic.ICollection`1" /> interface.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the set.</summary>
      <param name="item">The object to remove from the set.</param>
      <returns>
        <see langword="true" /> if the element is successfully removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that iterates through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ISet{T}#Add(`0)">
      <summary>Adds an element to the current set and returns a value that indicates whether the element was successfully added.</summary>
      <param name="item">The element to add to the collection.</param>
      <returns>
        <see langword="true" /> if the element is added to the set; <see langword="false" /> if the element is already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ISet{T}#ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes all elements in the specified collection from the current set.</summary>
      <param name="other">The collection of items to remove.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ISet{T}#IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are also in a specified collection.</summary>
      <param name="other">The collection to compare to the current collection.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ISet{T}#SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Generic#ISet{T}#UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains all elements that are present in either the current set or in the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the set to an array, starting at a particular index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the set. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>See the <see cref="T:System.Collections.ICollection" /> interface.</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#ICollection#SyncRoot">
      <summary>See <see cref="T:System.Collections.ICollection" />.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a set.</summary>
      <returns>An enumerator that can be used to iterate through the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Immutable#IImmutableSet{T}#Add(`0)">
      <summary>Adds the specified element to this immutable set.</summary>
      <param name="item">The element to add.</param>
      <returns>A new set with the element added, or this set if the element is already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Immutable#IImmutableSet{T}#Clear">
      <summary>Retrieves an empty set that has the same sorting and ordering semantics as this instance.</summary>
      <returns>An empty set that has the same sorting or ordering semantics as this instance.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Immutable#IImmutableSet{T}#Except(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the elements in the specified collection from the current set.</summary>
      <param name="other">The collection of items to remove from this set.</param>
      <returns>A new set with the items removed; or the original set if none of the items were in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Immutable#IImmutableSet{T}#Intersect(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable set that contains elements that exist in both this set and the specified set.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>A new immutable set that contains any elements that exist in both sets.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Immutable#IImmutableSet{T}#Remove(`0)">
      <summary>Removes the specified element from this immutable set.</summary>
      <param name="item">The element to remove.</param>
      <returns>A new set with the specified element removed, or the current set if the element cannot be found in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Immutable#IImmutableSet{T}#SymmetricExcept(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable set that contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>A new set that contains the elements that are present only in the current set or in the specified collection, but not both.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.System#Collections#Immutable#IImmutableSet{T}#Union(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates a new immutable set that contains all elements that are present in either the current set or in the specified collection.</summary>
      <param name="other">The collection to add elements from.</param>
      <returns>A new immutable set with the items added; or the original set if all the items were already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.ToBuilder">
      <summary>Creates an immutable hash set that has the same contents as this set and can be efficiently mutated across multiple operations by using standard mutable interfaces.</summary>
      <returns>A set with the same contents as this set that can be efficiently mutated across multiple operations by using standard mutable interfaces.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.TryGetValue(`0,`0@)">
      <summary>Searches the set for a given value and returns the equal value it finds, if any.</summary>
      <param name="equalValue">The value to search for.</param>
      <param name="actualValue">The value from the set that the search found, or the original value if the search yielded no match.</param>
      <returns>A value indicating whether the search was successful.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.Union(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates a new immutable hash set that contains all elements that are present in either the current set or in the specified collection.</summary>
      <param name="other">The collection to add elements from.</param>
      <returns>A new immutable hash set with the items added; or the original set if all the items were already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableHashSet`1.WithComparer(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Gets an instance of the immutable hash set that uses the specified equality comparer for its search methods.</summary>
      <param name="equalityComparer">The equality comparer to use.</param>
      <returns>An instance of this immutable hash set that uses the given comparer.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableInterlocked">
      <summary>Contains interlocked exchange mechanisms for immutable collections.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.AddOrUpdate``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,``1,System.Func{``0,``1,``1})">
      <summary>Obtains the value from a dictionary after having added it or updated an existing entry.</summary>
      <param name="location">The variable or field to atomically update if the specified  is not in the dictionary.</param>
      <param name="key">The key for the value to add or update.</param>
      <param name="addValue">The value to use if no previous value exists.</param>
      <param name="updateValueFactory">The function that receives the key and prior value and returns the new value with which to update the dictionary.</param>
      <typeparam name="TKey">The type of key stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of value stored by the dictionary.</typeparam>
      <returns>The added or updated value.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.AddOrUpdate``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,System.Func{``0,``1},System.Func{``0,``1,``1})">
      <summary>Obtains the value from a dictionary after having added it or updated an existing entry.</summary>
      <param name="location">The variable or field to atomically update if the specified  is not in the dictionary.</param>
      <param name="key">The key for the value to add or update.</param>
      <param name="addValueFactory">The function that receives the key and returns a new value to add to the dictionary when no value previously exists.</param>
      <param name="updateValueFactory">The function that receives the key and prior value and returns the new value with which to update the dictionary.</param>
      <typeparam name="TKey">The type of key stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of value stored by the dictionary.</typeparam>
      <returns>The added or updated value.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.Enqueue``1(System.Collections.Immutable.ImmutableQueue{``0}@,``0)">
      <summary>Atomically enqueues an element to the end of a queue.</summary>
      <param name="location">The variable or field to atomically update.</param>
      <param name="value">The value to enqueue.</param>
      <typeparam name="T">The type of items contained in the collection</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.GetOrAdd``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,``1)">
      <summary>Gets the value for the specified key from the dictionary, or if the key was not found, adds a new value to the dictionary.</summary>
      <param name="location">The variable or field to atomically update if the specified key is not in the dictionary.</param>
      <param name="key">The key for the value to get or add.</param>
      <param name="value">The value to add to the dictionary the key is not found.</param>
      <typeparam name="TKey">The type of the keys contained in the collection.</typeparam>
      <typeparam name="TValue">The type of the values contained in the collection.</typeparam>
      <returns>The value at the specified key or <paramref name="valueFactory" /> if the key was not present.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.GetOrAdd``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,System.Func{``0,``1})">
      <summary>Gets the value for the specified key from the dictionary, or if the key was not found, adds a new value to the dictionary.</summary>
      <param name="location">The variable or field to atomically update if the specified  is not in the dictionary.</param>
      <param name="key">The key for the value to retrieve or add.</param>
      <param name="valueFactory">The function to execute to obtain the value to insert into the dictionary if the key is not found. This delegate will not be invoked more than once.</param>
      <typeparam name="TKey">The type of the keys contained in the collection.</typeparam>
      <typeparam name="TValue">The type of the values contained in the collection.</typeparam>
      <returns>The value at the specified key or <paramref name="valueFactory" /> if the key was not present.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.GetOrAdd``3(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,System.Func{``0,``2,``1},``2)">
      <summary>Gets the value for the specified key from the dictionary, or if the key was not found, adds a new value to the dictionary.</summary>
      <param name="location">The variable or field to update if the specified is not in the dictionary.</param>
      <param name="key">The key for the value to retrieve or add.</param>
      <param name="valueFactory">The function to execute to obtain the value to insert into the dictionary if the key is not found.</param>
      <param name="factoryArgument">The argument to pass to the value factory.</param>
      <typeparam name="TKey">The type of the keys contained in the collection.</typeparam>
      <typeparam name="TValue">The type of the values contained in the collection.</typeparam>
      <typeparam name="TArg">The type of the argument supplied to the value factory.</typeparam>
      <returns>The value at the specified key or <paramref name="valueFactory" /> if the key was not present.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.InterlockedCompareExchange``1(System.Collections.Immutable.ImmutableArray{``0}@,System.Collections.Immutable.ImmutableArray{``0},System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Compares two immutable arrays for equality and, if they are equal, replaces one of the arrays.</summary>
      <param name="location">The destination, whose value is compared with <paramref name="comparand" /> and possibly replaced.</param>
      <param name="value">The value that replaces the destination value if the comparison results in equality.</param>
      <param name="comparand">The value that is compared to the value at <paramref name="location" />.</param>
      <typeparam name="T">The type of element stored by the array.</typeparam>
      <returns>The original value in <paramref name="location" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.InterlockedExchange``1(System.Collections.Immutable.ImmutableArray{``0}@,System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Sets an array to the specified array and returns a reference to the original array, as an atomic operation.</summary>
      <param name="location">The array to set to the specified value.</param>
      <param name="value">The value to which the <paramref name="location" /> parameter is set.</param>
      <typeparam name="T">The type of element stored by the array.</typeparam>
      <returns>The original value of <paramref name="location" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.InterlockedInitialize``1(System.Collections.Immutable.ImmutableArray{``0}@,System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Sets an array to the specified array if the array has not been initialized.</summary>
      <param name="location">The array to set to the specified value.</param>
      <param name="value">The value to which the <paramref name="location" /> parameter is set, if it's not initialized.</param>
      <typeparam name="T">The type of element stored by the array.</typeparam>
      <returns>
        <see langword="true" /> if the array was assigned the specified value;  otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.Push``1(System.Collections.Immutable.ImmutableStack{``0}@,``0)">
      <summary>Pushes a new element onto the stack.</summary>
      <param name="location">The stack to update.</param>
      <param name="value">The value to push on the stack.</param>
      <typeparam name="T">The type of items in the stack.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.TryAdd``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,``1)">
      <summary>Adds the specified key and value to the dictionary if the key is not in the dictionary.</summary>
      <param name="location">The dictionary to update with the specified key and value.</param>
      <param name="key">The key to add, if is not already defined in the dictionary.</param>
      <param name="value">The value to add.</param>
      <typeparam name="TKey">The type of the keys contained in the collection.</typeparam>
      <typeparam name="TValue">The type of the values contained in the collection.</typeparam>
      <returns>
        <see langword="true" /> if the key is not in the dictionary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.TryDequeue``1(System.Collections.Immutable.ImmutableQueue{``0}@,``0@)">
      <summary>Atomically removes and returns the specified element at the head of the queue, if the queue is not empty.</summary>
      <param name="location">The variable or field to atomically update.</param>
      <param name="value">Set to the value from the head of the queue, if the queue not empty.</param>
      <typeparam name="T">The type of items in the queue.</typeparam>
      <returns>
        <see langword="true" /> if the queue is not empty and the head element is removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.TryPop``1(System.Collections.Immutable.ImmutableStack{``0}@,``0@)">
      <summary>Removes an element from the top of the stack, if there is an element to remove.</summary>
      <param name="location">The stack to update.</param>
      <param name="value">Receives the value removed from the stack, if the stack is not empty.</param>
      <typeparam name="T">The type of items in the stack.</typeparam>
      <returns>
        <see langword="true" /> if an element is removed from the stack; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.TryRemove``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,``1@)">
      <summary>Removes the element with the specified key, if the key exists.</summary>
      <param name="location">The dictionary to update.</param>
      <param name="key">The key to remove.</param>
      <param name="value">Receives the value of the removed item, if the dictionary is not empty.</param>
      <typeparam name="TKey">The type of the keys contained in the collection.</typeparam>
      <typeparam name="TValue">The type of the values contained in the collection.</typeparam>
      <returns>
        <see langword="true" /> if the key was found and removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.TryUpdate``2(System.Collections.Immutable.ImmutableDictionary{``0,``1}@,``0,``1,``1)">
      <summary>Sets the specified key to the specified value if the specified key already is set to a specific value.</summary>
      <param name="location">The dictionary to update.</param>
      <param name="key">The key to update.</param>
      <param name="newValue">The new value to set.</param>
      <param name="comparisonValue">The current value for <paramref name="key" /> in order for the update to succeed.</param>
      <typeparam name="TKey">The type of the keys contained in the collection.</typeparam>
      <typeparam name="TValue">The type of the values contained in the collection.</typeparam>
      <returns>
        <see langword="true" /> if <paramref name="key" /> and <paramref name="comparisonValue" /> are present in the dictionary and comparison was updated to <paramref name="newValue" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.Update``1(``0@,System.Func{``0,``0})">
      <summary>Mutates a value in-place with optimistic locking transaction semantics             via a specified transformation function.             The transformation is retried as many times as necessary to win the optimistic locking race.</summary>
      <param name="location">The variable or field to be changed, which may be accessed by multiple threads.</param>
      <param name="transformer">A function that mutates the value. This function should be side-effect free,              as it may run multiple times when races occur with other threads.</param>
      <typeparam name="T">The type of data.</typeparam>
      <returns>
        <see langword="true" /> if the location's value is changed by applying the result of the <paramref name="transformer" /> function; <see langword="false" /> if the location's value remained the same because the last invocation of <paramref name="transformer" /> returned the existing value.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableInterlocked.Update``2(``0@,System.Func{``0,``1,``0},``1)">
      <summary>Mutates a value in-place with optimistic locking transaction semantics             via a specified transformation function.             The transformation is retried as many times as necessary to win the optimistic locking race.</summary>
      <param name="location">The variable or field to be changed, which may be accessed by multiple threads.</param>
      <param name="transformer">A function that mutates the value. This function should be side-effect free,              as it may run multiple times when races occur with other threads.</param>
      <param name="transformerArgument">The argument to pass to <paramref name="transformer" />.</param>
      <typeparam name="T">The type of data.</typeparam>
      <typeparam name="TArg">The type of argument passed to the <paramref name="transformer" />.</typeparam>
      <returns>
        <see langword="true" /> if the location's value is changed by applying the result of the <paramref name="transformer" /> function; <see langword="false" /> if the location's value remained the same because the last invocation of <paramref name="transformer" /> returned the existing value.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableList">
      <summary>Provides a set of initialization methods for instances of the <see cref="T:System.Collections.Immutable.ImmutableList`1" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.Create``1">
      <summary>Creates an empty immutable list.</summary>
      <typeparam name="T">The type of items to be stored in the .</typeparam>
      <returns>An empty immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.Create``1(``0)">
      <summary>Creates a new immutable list that contains the specified item.</summary>
      <param name="item">The item to prepopulate the list with.</param>
      <typeparam name="T">The type of items in the .</typeparam>
      <returns>A new  that contains the specified item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.Create``1(``0[])">
      <summary>Creates a new immutable list that contains the specified array of items.</summary>
      <param name="items">An array that contains the items to prepopulate the list with.</param>
      <typeparam name="T">The type of items in the .</typeparam>
      <returns>A new immutable list that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.CreateBuilder``1">
      <summary>Creates a new immutable list builder.</summary>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The immutable collection builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.CreateRange``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new immutable list that contains the specified items.</summary>
      <param name="items">The items to add to the list.</param>
      <typeparam name="T">The type of items in the .</typeparam>
      <returns>An immutable list that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.IndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the list.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the list. The value can be null for reference types.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the list that extends from index to the last element, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.IndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the list.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the Immutable list. The value can be null for reference types.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the immutable list that extends from index to the last element, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.IndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the immutable list that extends from the specified index to the last element.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the Immutable list. The value can be null for reference types.</param>
      <param name="startIndex">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the Immutable list that extends from index to the last element, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.IndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0,System.Int32,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the immutable list that extends from the specified index to the last element.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the Immutable list. The value can be null for reference types.</param>
      <param name="startIndex">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements in the section to search.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the Immutable list that extends from index to the last element, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.LastIndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the entire immutable list.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the Immutable list. The value can be null for reference types.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the last occurrence of item within the entire the Immutable list, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.LastIndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the entire immutable list.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the Immutable list. The value can be null for reference types.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the last occurrence of item within the entire the Immutable list, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.LastIndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the immutable list that extends from the first element to the specified index.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the Immutable list. The value can be null for reference types.</param>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the last occurrence of item within the range of elements in the Immutable list that extends from the first element to index, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.LastIndexOf``1(System.Collections.Immutable.IImmutableList{``0},``0,System.Int32,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the immutable list that extends from the first element to the specified index.</summary>
      <param name="list">The list to search.</param>
      <param name="item">The object to locate in the Immutable list. The value can be null for reference types.</param>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The zero-based index of the last occurrence of item within the range of elements in the Immutable list that extends from the first element to index, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.Remove``1(System.Collections.Immutable.IImmutableList{``0},``0)">
      <summary>Removes the specified value from this list.</summary>
      <param name="list">The list to search.</param>
      <param name="value">The value to remove.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>A new immutable list with the element removed, or this list if the element is not in this list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.RemoveRange``1(System.Collections.Immutable.IImmutableList{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Removes the specified values from this list.</summary>
      <param name="list">The list to search.</param>
      <param name="items">The items to remove if matches are found in this list.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>A new immutable list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.Replace``1(System.Collections.Immutable.IImmutableList{``0},``0,``0)">
      <summary>Replaces the first equal element in the list with the specified element.</summary>
      <param name="list">The list to search.</param>
      <param name="oldValue">The element to replace.</param>
      <param name="newValue">The element to replace the old element with.</param>
      <typeparam name="T">The type of items in the list.</typeparam>
      <returns>The new list -- even if the value being replaced is equal to the new value for that position.</returns>
      <exception cref="T:System.ArgumentException">Thrown when the old value does not exist in the list.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.ToImmutableList``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Enumerates a sequence and produces an immutable list of its contents.</summary>
      <param name="source">The sequence to enumerate.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <returns>An immutable list that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList.ToImmutableList``1(System.Collections.Immutable.ImmutableList{``0}.Builder)">
      <summary>Creates an immutable list from the current contents of the builder's collection.</summary>
      <param name="builder">The builder to create the immutable list from.</param>
      <typeparam name="TSource">The type of the elements in the list.</typeparam>
      <returns>An immutable list that contains the current contents in the builder's collection.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableList`1">
      <summary>Represents an immutable list, which is a strongly typed list of objects that can be accessed by index.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of elements in the list.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Add(`0)">
      <summary>Adds the specified object to the end of the immutable list.</summary>
      <param name="value">The object to add.</param>
      <returns>A new immutable list with the object added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Adds the elements of the specified collection to the end of the immutable list.</summary>
      <param name="items">The collection whose elements will be added to the end of the list.</param>
      <returns>A new immutable list with the elements added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.BinarySearch(`0)">
      <summary>Searches the entire sorted list for an element using the default comparer and returns the zero-based index of the element.</summary>
      <param name="item">The object to locate. The value can be <see langword="null" /> for reference types.</param>
      <returns>The zero-based index of item in the sorted List, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than item or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.ICollection.Count" />.</returns>
      <exception cref="T:System.InvalidOperationException">The default comparer cannot find a comparer implementation of the for type T.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Searches the entire sorted list for an element using the specified comparer and returns the zero-based index of the element.</summary>
      <param name="item">The object to locate. The value can be null for reference types.</param>
      <param name="comparer">The  comparer implementation to use when comparing elements or null to use the default comparer.</param>
      <returns>The zero-based index of item in the sorted List, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than item or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.ICollection.Count" />.</returns>
      <exception cref="T:System.InvalidOperationException">comparer is <see langword="null" />, and the default comparer cannot find an comparer implementation for type T.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Searches a range of elements in the sorted list for an element using the specified comparer and returns the zero-based index of the element.</summary>
      <param name="index">The zero-based starting index of the range to search.</param>
      <param name="count">The length of the range to search.</param>
      <param name="item">The object to locate. The value can be null for reference types.</param>
      <param name="comparer">The comparer implementation to use when comparing elements, or <see langword="null" /> to use the default comparer.</param>
      <returns>The zero-based index of item in the sorted list, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than item or, if there is no larger element, the bitwise complement of <paramref name="count" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">index is less than 0 or <paramref name="count" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">index and <paramref name="count" /> do not denote a valid range in the list.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is <see langword="null" />, and the default comparer cannot find an comparer implementation for type T.</exception>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableList`1.Builder">
      <summary>Represents a list that mutates with little or no memory allocations and that can produce or build on immutable list instances very efficiently.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Add(`0)">
      <summary>Adds an item to the immutable list.</summary>
      <param name="item">The item to add to the list.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Adds a series of elements to the end of this list.</summary>
      <param name="items">The elements to add to the end of the list.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.BinarySearch(`0)">
      <summary>Searches the entire <see cref="T:System.Collections.Immutable.ImmutableList`1.Builder" /> for an element using the default comparer and returns the zero-based index of the element.</summary>
      <param name="item">The object to locate. The value can be null for reference types.</param>
      <returns>The zero-based index of item in the <see cref="T:System.Collections.Immutable.ImmutableList`1.Builder" />, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="item" />.</returns>
      <exception cref="T:System.InvalidOperationException">The default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find an implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type T.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Searches the entire <see cref="T:System.Collections.Immutable.ImmutableList`1.Builder" /> for an element using the specified comparer and returns the zero-based index of the element.</summary>
      <param name="item">The object to locate. This value can be null for reference types.</param>
      <param name="comparer">The implementation to use when comparing elements, or <see langword="null" /> for the default comparer.</param>
      <returns>The zero-based index of item in the <see cref="T:System.Collections.Immutable.ImmutableList`1.Builder" />, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="item" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is <see langword="null" />, and the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find an implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type T.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Searches the specified range of the <see cref="T:System.Collections.Immutable.ImmutableList`1.Builder" /> for an element using the specified comparer and returns the zero-based index of the element.</summary>
      <param name="index">The zero-based starting index of the range to search.</param>
      <param name="count">The length of the range to search.</param>
      <param name="item">The object to locate. This value can be null for reference types.</param>
      <param name="comparer">The implementation to use when comparing elements, or <see langword="null" /> for the default comparer.</param>
      <returns>The zero-based index of item in the <see cref="T:System.Collections.Immutable.ImmutableList`1.Builder" />, if item is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="item" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.
-or-
<paramref name="count" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in the <see cref="T:System.Collections.Generic.List`1" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is <see langword="null" />, and the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find an implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type T.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Clear">
      <summary>Removes all items from the immutable list.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Contains(`0)">
      <summary>Determines whether the immutable list contains a specific value.</summary>
      <param name="item">The object to locate in the list.</param>
      <returns>
        <see langword="true" /> if item is found in the list; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.ConvertAll``1(System.Func{`0,``0})">
      <summary>Creates a new immutable list from the list represented by this builder by using the converter function.</summary>
      <param name="converter">The converter function.</param>
      <typeparam name="TOutput">The type of the output of the delegate converter function.</typeparam>
      <returns>A new immutable list from the list represented by this builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.CopyTo(`0[])">
      <summary>Copies the entire immutable list to a compatible one-dimensional array, starting at the beginning of the target array.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the immutable list. The array must have zero-based indexing.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.CopyTo(`0[],System.Int32)">
      <summary>Copies the entire immutable list to a compatible one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the immutable list. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Copies the entire immutable list to a compatible one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="index">The zero-based index in the source immutable list at which copying begins.</param>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the immutable list. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <param name="count">The number of elements to copy.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.Count">
      <summary>Gets the number of elements in this immutable list.</summary>
      <returns>The number of elements in this list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Exists(System.Predicate{`0})">
      <summary>Determines whether the immutable list contains elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to search for.</param>
      <returns>
        <see langword="true" /> if the immutable list contains one or more elements that match the conditions defined by the specified predicate; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Find(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the first occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The first element that matches the conditions defined by the specified predicate, if found; otherwise, the default value for type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindAll(System.Predicate{`0})">
      <summary>Retrieves all the elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to search for.</param>
      <returns>An immutable list containing all the elements that match the conditions defined by the specified predicate, if found; otherwise, an empty immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the range of elements in the immutable list that starts at the specified index and contains the specified number of elements.</summary>
      <param name="startIndex">The zero-based starting index of the search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the range of elements in the immutable list that extends from the specified index to the last element.</summary>
      <param name="startIndex">The zero-based starting index of the search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindIndex(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindLast(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the last occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The last element that matches the conditions defined by the specified predicate, found; otherwise, the default value for type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the range of elements in the immutable list that contains the specified number of elements and ends at the specified index.</summary>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the range of elements in the immutable list that extends from the first element to the specified index.</summary>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.FindLastIndex(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.ForEach(System.Action{`0})">
      <summary>Performs the specified action on each element of the list.</summary>
      <param name="action">The delegate to perform on each element of the list.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.GetRange(System.Int32,System.Int32)">
      <summary>Creates a shallow copy of a range of elements in the source immutable list.</summary>
      <param name="index">The zero-based index at which the range starts.</param>
      <param name="count">The number of elements in the range.</param>
      <returns>A shallow copy of a range of elements in the source immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.IndexOf(`0)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the immutable list.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the range of elements in the immutable list, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.IndexOf(`0,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the immutable list that extends from the specified index to the last element.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <param name="index">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the immutable list that extends from <paramref name="index" /> to the last element, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the immutable list that starts at the specified index and contains the specified number of elements.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <param name="index">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements in the section to search.</param>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the immutable list that starts at <paramref name="index" /> and contains <paramref name="count" /> number of elements, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.IndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the <see cref="T:System.Collections.Immutable.ImmutableList`1.Builder" /> that starts at the specified index and contains the specified number of elements.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <param name="index">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements to search.</param>
      <param name="equalityComparer">The value comparer to use for comparing elements for equality.</param>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the immutable list that starts at <paramref name="index" /> and contains <paramref name="count" /> number of elements, if found; otherwise, -1</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Insert(System.Int32,`0)">
      <summary>Inserts an item to the immutable list at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The object to insert into the immutable list.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserts the elements of a collection into the immutable list at the specified index.</summary>
      <param name="index">The zero-based index at which the new elements should be inserted.</param>
      <param name="items">The collection whose elements should be inserted into the immutable list. The collection itself cannot be <see langword="null" />, but it can contain elements that are null, if type <c>T</c> is a reference type.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.Item(System.Int32)">
      <summary>Gets or sets the value for a given index in the list.</summary>
      <param name="index">The index of the item to get or set.</param>
      <returns>The value at the specified index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.ItemRef(System.Int32)">
      <summary>Gets a read-only reference to the value for a given <paramref name="index" /> into the list.</summary>
      <param name="index">The index of the desired element.</param>
      <returns>A read-only reference to the value at the specified <paramref name="index" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.LastIndexOf(`0)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the entire immutable list.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <returns>The zero-based index of the last occurrence of <paramref name="item" /> within the entire immutable list, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.LastIndexOf(`0,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the immutable list that extends from the first element to the specified index.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <returns>The zero-based index of the last occurrence of <paramref name="item" /> within the range of elements in the immutable list that extends from the first element to <paramref name="index" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the immutable list that contains the specified number of elements and ends at the specified index.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <returns>The zero-based index of the last occurrence of <paramref name="item" /> within the range of elements in the immutable list that contains <paramref name="count" /> number of elements and ends at <paramref name="index" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.LastIndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the immutable list that contains the specified number of elements and ends at the specified index.</summary>
      <param name="item">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <param name="startIndex">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements to search.</param>
      <param name="equalityComparer">The value comparer to use for comparing elements for equality.</param>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the immutable list that starts at <paramref name="index" /> and contains <paramref name="count" /> number of elements, if found; otherwise, -1</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the immutable list.</summary>
      <param name="item">The object to remove from the list.</param>
      <returns>
        <see langword="true" /> if item was successfully removed from the list; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if item is not found in the list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.RemoveAll(System.Predicate{`0})">
      <summary>Removes all the elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
      <returns>The number of elements removed from the immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.RemoveAt(System.Int32)">
      <summary>Removes the item at the specified index of the immutable list.</summary>
      <param name="index">The zero-based index of the item to remove from the list.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Reverse">
      <summary>Reverses the order of the elements in the entire immutable list.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Reverse(System.Int32,System.Int32)">
      <summary>Reverses the order of the elements in the specified range of the immutable list.</summary>
      <param name="index">The zero-based starting index of the range to reverse.</param>
      <param name="count">The number of elements in the range to reverse.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Sort">
      <summary>Sorts the elements in the entire immutable list by using the default comparer.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the elements in the entire immutable list by using the specified comparer.</summary>
      <param name="comparer">The implementation to use when comparing elements, or <see langword="null" /> to use the default comparer (<see cref="P:System.Collections.Generic.Comparer`1.Default" />).</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Sort(System.Comparison{`0})">
      <summary>Sorts the elements in the entire immutable list by using the specified comparison object.</summary>
      <param name="comparison">The object to use when comparing elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the elements in a range of elements in the immutable list  by using the specified comparer.</summary>
      <param name="index">The zero-based starting index of the range to sort.</param>
      <param name="count">The length of the range to sort.</param>
      <param name="comparer">The implementation to use when comparing elements, or <see langword="null" /> to use the default comparer (<see cref="P:System.Collections.Generic.Comparer`1.Default" />).</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value that indicates whether this instance is read-only.</summary>
      <returns>Always <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the list to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the list. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#Add(System.Object)">
      <summary>Adds an item to the list.</summary>
      <param name="value">The object to add to the list.</param>
      <returns>The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the collection.</returns>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#Clear">
      <summary>Removes all items from the list.</summary>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the list contains a specific value.</summary>
      <param name="value">The object to locate in the list.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Object" /> is found in the list; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of a specific item in the list.</summary>
      <param name="value">The object to locate in the list.</param>
      <returns>The index of <paramref name="value" /> if found in the list; otherwise, -1.</returns>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an item to the list at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The object to insert into the list.</param>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.IList" /> has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IList" /> has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Object" /> at the specified index.</summary>
      <param name="index">The index.</param>
      <returns>The object at the specified index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of a specific object from the list.</summary>
      <param name="value">The object to remove from the list.</param>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.ToImmutable">
      <summary>Creates an immutable list based on the contents of this instance.</summary>
      <returns>An immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Builder.TrueForAll(System.Predicate{`0})">
      <summary>Determines whether every element in the immutable list matches the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions to check against the elements.</param>
      <returns>
        <see langword="true" /> if every element in the immutable list matches the conditions defined by the specified predicate; otherwise, <see langword="false" />. If the list has no elements, the return value is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Clear">
      <summary>Removes all elements from the immutable list.</summary>
      <returns>An empty list that retains the same sort or unordered semantics that this instance has.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Contains(`0)">
      <summary>Determines whether this immutable list contains the specified value.</summary>
      <param name="value">The value to locate.</param>
      <returns>
        <see langword="true" /> if the list contains the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.ConvertAll``1(System.Func{`0,``0})">
      <summary>Converts the elements in the current immutable list to another type, and returns a list containing the converted elements.</summary>
      <param name="converter">A delegate that converts each element from one type to another type.</param>
      <typeparam name="TOutput">The type of the elements of the target array.</typeparam>
      <returns>A list of the target type containing the converted elements from the current <see cref="T:System.Collections.Immutable.ImmutableList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.CopyTo(`0[])">
      <summary>Copies the entire immutable list to a compatible one-dimensional array, starting at the beginning of the target array.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the immutable list. The array must have zero-based indexing.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the entire immutable list to a compatible one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the immutable list. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Copies a range of elements from the immutable list to a compatible one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="index">The zero-based index in the source immutable list at which copying begins.</param>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the immutable list. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
      <param name="count">The number of elements to copy.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Count">
      <summary>Gets the number of elements contained in the list.</summary>
      <returns>The number of elements in the list.</returns>
    </member>
    <member name="F:System.Collections.Immutable.ImmutableList`1.Empty">
      <summary>Gets an empty set with the default sort comparer.</summary>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableList`1.Enumerator">
      <summary>Enumerates the contents of a binary tree.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Enumerator.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Collections.Immutable.ImmutableList`1.Enumerator" /> class.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Enumerator.MoveNext">
      <summary>Advances enumeration to the next element of the immutable list.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the immutable list.</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>The current element.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Exists(System.Predicate{`0})">
      <summary>Determines whether the immutable list contains elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to search for.</param>
      <returns>
        <see langword="true" /> if the immutable list contains one or more elements that match the conditions defined by the specified predicate; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Find(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the first occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The first element that matches the conditions defined by the specified predicate, if found; otherwise, the default value for type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindAll(System.Predicate{`0})">
      <summary>Retrieves all the elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to search for.</param>
      <returns>An immutable list that contains all the elements that match the conditions defined by the specified predicate, if found; otherwise, an empty immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the range of elements in the immutable list that starts at the specified index and contains the specified number of elements.</summary>
      <param name="startIndex">The zero-based starting index of the search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by match, if found; otherwise, ?1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the range of elements in the immutable list that extends from the specified index to the last element.</summary>
      <param name="startIndex">The zero-based starting index of the search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by match, if found; otherwise, ?1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindIndex(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by match, if found; otherwise, ?1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindLast(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the last occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The last element that matches the conditions defined by the specified predicate, if found; otherwise, the default value for type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the range of elements in the immutable list that contains the specified number of elements and ends at the specified index.</summary>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, ?1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the range of elements in the immutable list that extends from the first element to the specified index.</summary>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, ?1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.FindLastIndex(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the entire immutable list.</summary>
      <param name="match">The delegate that defines the conditions of the element to search for.</param>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, ?1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.ForEach(System.Action{`0})">
      <summary>Performs the specified action on each element of the immutable list.</summary>
      <param name="action">The delegate to perform on each element of the immutable list.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable list.</summary>
      <returns>An enumerator  that can be used to iterate through the immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.GetRange(System.Int32,System.Int32)">
      <summary>Creates a shallow copy of a range of elements in the source immutable list.</summary>
      <param name="index">The zero-based index at which the range starts.</param>
      <param name="count">The number of elements in the range.</param>
      <returns>A shallow copy of a range of elements in the source immutable list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.IndexOf(`0)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the entire immutable list.</summary>
      <param name="value">The object to locate in the immutable list. The value can be <see langword="null" /> for reference types.</param>
      <returns>The zero-based index of the first occurrence of <paramref name="value" /> within the entire immutable list, if found; otherwise, ?1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.IndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the list that starts at the specified index and contains the specified number of elements.</summary>
      <param name="item">The object to locate in the list The value can be null for reference types.</param>
      <param name="index">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>The zero-based index of the first occurrence of item within the range of elements in the list that starts at index and contains count number of elements, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Insert(System.Int32,`0)">
      <summary>Inserts the specified object into the immutable list at the specified index.</summary>
      <param name="index">The zero-based index at which to insert the object.</param>
      <param name="item">The object to insert.</param>
      <returns>The new immutable list after the object is inserted.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserts the elements of a collection into the immutable list at the specified index.</summary>
      <param name="index">The zero-based index at which to insert the elements.</param>
      <param name="items">The collection whose elements should be inserted.</param>
      <returns>The new immutable list after the elements are inserted.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.IsEmpty">
      <summary>Gets a value that indicates whether this list is empty.</summary>
      <returns>
        <see langword="true" /> if the list is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.Item(System.Int32)">
      <summary>Gets the element at the specified index of the list.</summary>
      <param name="index">The index of the element to retrieve.</param>
      <returns>The element at the specified index.</returns>
      <exception cref="T:System.IndexOutOfRangeException">In a get operation, <paramref name="index" /> is negative or not less than <see cref="P:System.Collections.Immutable.ImmutableList`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.ItemRef(System.Int32)">
      <summary>Gets a read-only reference to the element of the set at the given <paramref name="index" />.</summary>
      <param name="index">The 0-based index of the element in the set to return.</param>
      <returns>A read-only reference to the element at the given position.</returns>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="index" /> is negative or not less than <see cref="P:System.Collections.Immutable.ImmutableList`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.LastIndexOf(`0,System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the list that contains the specified number of elements and ends at the specified index.</summary>
      <param name="item">The object to locate in the list. The value can be null for reference types.</param>
      <param name="index">The zero-based starting index of the backward search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>The zero-based index of the last occurrence of item within the range of elements in the list that contains count number of elements and ends at index, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Remove(`0)">
      <summary>Removes the first occurrence of the specified object from this immutable list.</summary>
      <param name="value">The object to remove.</param>
      <returns>A new list with the object removed, or this list if the specified object is not in this list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Remove(`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the first occurrence of the object that matches the specified value from this immutable list.</summary>
      <param name="value">The value of the element to remove from the list.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>A new list with the object removed, or this list if the specified object is not in this list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.RemoveAll(System.Predicate{`0})">
      <summary>Removes all the elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
      <returns>The new list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index.</summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <returns>A new list with the element removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes a range of elements from this immutable list.</summary>
      <param name="items">The collection whose elements should be removed if matches are found in this list.</param>
      <returns>A new list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.RemoveRange(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the specified values from this list.</summary>
      <param name="items">The items to remove if matches are found in this list.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>A new list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Removes a range of elements, starting from the specified index and containing the specified number of elements, from this immutable list.</summary>
      <param name="index">The starting index to begin removal.</param>
      <param name="count">The number of elements to remove.</param>
      <returns>A new list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Replace(`0,`0)">
      <summary>Replaces the specified element in the immutable list with a new element.</summary>
      <param name="oldValue">The element to replace.</param>
      <param name="newValue">The element to replace <paramref name="oldValue" /> with.</param>
      <returns>The new list with the replaced element, even if it is equal to the old element.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="oldValue" /> does not exist in the immutable list.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Replace(`0,`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Replaces the specified element in the immutable list with a new element.</summary>
      <param name="oldValue">The element to replace in the list.</param>
      <param name="newValue">The element to replace <paramref name="oldValue" /> with.</param>
      <param name="equalityComparer">The comparer to use to check for equality.</param>
      <returns>A new list with the object replaced, or this list if the specified object is not in this list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Reverse">
      <summary>Reverses the order of the elements in the entire immutable list.</summary>
      <returns>The reversed list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Reverse(System.Int32,System.Int32)">
      <summary>Reverses the order of the elements in the specified range of the immutable list.</summary>
      <param name="index">The zero-based starting index of the range to reverse.</param>
      <param name="count">The number of elements in the range to reverse.</param>
      <returns>The reversed list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.SetItem(System.Int32,`0)">
      <summary>Replaces an element at a given position in the immutable list with the specified element.</summary>
      <param name="index">The position in the list of the element to replace.</param>
      <param name="value">The element to replace the old element with.</param>
      <returns>The new list with the replaced element, even if it is equal to the old element at that position.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Sort">
      <summary>Sorts the elements in the entire immutable list using the default comparer.</summary>
      <returns>The sorted list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the elements in the entire immutable list using the specified comparer.</summary>
      <param name="comparer">The  implementation to use when comparing elements, or <see langword="null" /> to use the default comparer (<see cref="P:System.Collections.Generic.Comparer`1.Default" />).</param>
      <returns>The sorted list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Sort(System.Comparison{`0})">
      <summary>Sorts the elements in the entire immutable list using the specified comparer.</summary>
      <param name="comparison">The delegate to use when comparing elements.</param>
      <returns>The sorted list.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Sorts a range of elements in the immutable list using the specified comparer.</summary>
      <param name="index">The zero-based starting index of the range to sort.</param>
      <param name="count">The length of the range to sort.</param>
      <param name="comparer">The implementation to use when comparing elements, or <see langword="null" /> to use the default comparer (<see cref="P:System.Collections.Generic.Comparer`1.Default" />).</param>
      <returns>The sorted list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds the specified item to the immutable list.</summary>
      <param name="item">The item to add.</param>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all items from the immutable list.</summary>
      <exception cref="T:System.NotSupportedException" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the immutable list.</summary>
      <param name="item">The object to remove.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> was successfully removed from the list; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if <paramref name="item" /> is not found in the original list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable list.</summary>
      <returns>An enumerator that can be used to iterate through the list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#IList{T}#Insert(System.Int32,`0)">
      <summary>Inserts an object in the immutable list at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The object to insert.</param>
      <exception cref="T:System.NotSupportedException" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#IList{T}#Item(System.Int32)">
      <summary>Gets or sets the value at the specified index.</summary>
      <param name="index" />
      <exception cref="T:System.IndexOutOfRangeException">Thrown from getter when <paramref name="index" /> is negative or not less than <see cref="P:System.Collections.Immutable.ImmutableList`1.Count" />.</exception>
      <exception cref="T:System.NotSupportedException">Always thrown from the setter.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Generic#IList{T}#RemoveAt(System.Int32)">
      <summary>Removes the value at the specified index.</summary>
      <param name="index">The zero-based index of the item to remove.</param>
      <exception cref="T:System.NotSupportedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the entire immutable list to a compatible one-dimensional array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from immutable list.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.System#Collections#ICollection#IsSynchronized">
      <summary>See the <see cref="T:System.Collections.ICollection" /> interface.</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.System#Collections#ICollection#SyncRoot">
      <summary>See <see cref="T:System.Collections.ICollection" />.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable list.</summary>
      <returns>An enumerator that can be used to iterate through the list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#Add(System.Object)">
      <summary>Adds an item to the immutable list.</summary>
      <param name="value">The object to add to the list.</param>
      <returns>The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the list.</returns>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#Clear">
      <summary>Removes all items from the immutable list.</summary>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the immutable list contains a specific value.</summary>
      <param name="value">The object to locate in the list.</param>
      <returns>
        <see langword="true" /> if the object is found in the list; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of a specific item in the immutable list.</summary>
      <param name="value">The object to locate in the list.</param>
      <returns>The index of <paramref name="value" /> if found in the list; otherwise, -1.</returns>
      <exception cref="T:System.NotImplementedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an item into the immutable list at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The object to insert into the list.</param>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IList" /> has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IList" /> has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Object" /> at the specified index.</summary>
      <param name="index">The index.</param>
      <returns>The value at the specified index.</returns>
      <exception cref="T:System.IndexOutOfRangeException">Thrown from getter when <paramref name="index" /> is negative or not less than <see cref="P:System.Collections.Immutable.ImmutableList`1.Count" />.</exception>
      <exception cref="T:System.NotSupportedException">Always thrown from the setter.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of a specific object from the immutable list.</summary>
      <param name="value">The object to remove from the list.</param>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#IList#RemoveAt(System.Int32)">
      <summary>Removes the item at the specified index of the immutable list.</summary>
      <param name="index">The zero-based index of the item to remove.</param>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#Add(`0)">
      <summary>Adds the specified value to this immutable list.</summary>
      <param name="value">The value to add.</param>
      <returns>A new list with the element added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Adds the specified values to this immutable list.</summary>
      <param name="items">The values to add.</param>
      <returns>A new list with the elements added.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#Clear">
      <summary>Retrieves an empty list that has the same sorting and ordering semantics as this instance.</summary>
      <returns>An empty list that has the same sorting and ordering semantics as this instance.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#Insert(System.Int32,`0)">
      <summary>Inserts the specified element at the specified index in the immutable list.</summary>
      <param name="index">The index at which to insert the value.</param>
      <param name="item">The element to insert.</param>
      <returns>A new immutable list that includes the specified element.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserts the specified elements at the specified index in the immutable list.</summary>
      <param name="index">The index at which to insert the elements.</param>
      <param name="items">The elements to insert.</param>
      <returns>A new immutable list that includes the specified elements.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#Remove(`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes the element with the specified value from the list.</summary>
      <param name="value">The value of the element to remove from the list.</param>
      <param name="equalityComparer">The comparer to use to compare elements for equality.</param>
      <returns>A new <see cref="T:System.Collections.Immutable.ImmutableList`1" /> with the specified element removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#RemoveAll(System.Predicate{`0})">
      <summary>Removes all the elements that match the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
      <returns>A new immutable list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of the immutable list.</summary>
      <param name="index">The index of the element to remove.</param>
      <returns>A new list with the element removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#RemoveRange(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Removes a range of elements from this immutable list that match the items specified.</summary>
      <param name="items">The range of items to remove from the list, if found.</param>
      <param name="equalityComparer">The equality comparer to use to compare elements.</param>
      <returns>An immutable list with the items removed.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="items" /> or <paramref name="equalityComparer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#RemoveRange(System.Int32,System.Int32)">
      <summary>Removes the specified number of elements at the specified location from this list.</summary>
      <param name="index">The starting index of the range of elements to remove.</param>
      <param name="count">The number of elements to remove.</param>
      <returns>A new list with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#Replace(`0,`0,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Replaces an element in the list with the specified element.</summary>
      <param name="oldValue">The element to replace.</param>
      <param name="newValue">The element to replace the old element with.</param>
      <param name="equalityComparer">The equality comparer to use in the search.</param>
      <returns>The new list.</returns>
      <exception cref="T:System.ArgumentException">Thrown when the old value does not exist in the list.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.System#Collections#Immutable#IImmutableList{T}#SetItem(System.Int32,`0)">
      <summary>Replaces an element in the list at a given position with the specified element.</summary>
      <param name="index">The position in the list of the element to replace.</param>
      <param name="value">The element to replace the old element with.</param>
      <returns>The new list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.ToBuilder">
      <summary>Creates a list that has the same contents as this list and can be efficiently mutated across multiple operations using standard mutable interfaces.</summary>
      <returns>The created list with the same contents as this list.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableList`1.TrueForAll(System.Predicate{`0})">
      <summary>Determines whether every element in the immutable list matches the conditions defined by the specified predicate.</summary>
      <param name="match">The delegate that defines the conditions to check against the elements.</param>
      <returns>
        <see langword="true" /> if every element in the immutable list matches the conditions defined by the specified predicate; otherwise, <see langword="false" />. If the list has no elements, the return value is <see langword="true" />.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableQueue">
      <summary>Provides a set of initialization methods for instances of the <see cref="T:System.Collections.Immutable.ImmutableQueue`1" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue.Create``1">
      <summary>Creates an empty immutable queue.</summary>
      <typeparam name="T">The type of items to be stored in the immutable queue.</typeparam>
      <returns>An empty immutable queue.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue.Create``1(``0)">
      <summary>Creates a new immutable queue that contains the specified item.</summary>
      <param name="item">The item to prepopulate the queue with.</param>
      <typeparam name="T">The type of items in the immutable queue.</typeparam>
      <returns>A new immutable queue that contains the specified item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue.Create``1(``0[])">
      <summary>Creates a new immutable queue that contains the specified array of items.</summary>
      <param name="items">An array that contains the items to prepopulate the queue with.</param>
      <typeparam name="T">The type of items in the immutable queue.</typeparam>
      <returns>A new immutable queue that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue.CreateRange``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new immutable queue that contains the specified items.</summary>
      <param name="items">The items to add to the queue before immutability is applied.</param>
      <typeparam name="T">The type of elements in the queue.</typeparam>
      <returns>An immutable queue that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue.Dequeue``1(System.Collections.Immutable.IImmutableQueue{``0},``0@)">
      <summary>Removes the item at the beginning of the immutable queue, and returns the new queue.</summary>
      <param name="queue">The queue to remove the item from.</param>
      <param name="value">When this method returns, contains the item from the beginning of the queue.</param>
      <typeparam name="T">The type of elements in the immutable queue.</typeparam>
      <returns>The new queue with the item removed.</returns>
      <exception cref="T:System.InvalidOperationException">The stack is empty.</exception>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableQueue`1">
      <summary>Represents an immutable queue.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of elements in the queue.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.Clear">
      <summary>Removes all objects from the immutable queue.</summary>
      <returns>The empty immutable queue.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.Dequeue">
      <summary>Removes the element at the beginning of the immutable queue, and returns the new queue.</summary>
      <returns>The new immutable queue; never <see langword="null" />.</returns>
      <exception cref="T:System.InvalidOperationException">The queue is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.Dequeue(`0@)">
      <summary>Removes the item at the beginning of the immutable queue, and returns the new queue.</summary>
      <param name="value">When this method returns, contains the element from the beginning of the queue.</param>
      <returns>The new immutable queue with the beginning element removed.</returns>
      <exception cref="T:System.InvalidOperationException">The queue is empty.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableQueue`1.Empty">
      <summary>Gets an empty immutable queue.</summary>
      <returns>An empty immutable queue.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.Enqueue(`0)">
      <summary>Adds an element to the end of the immutable queue, and returns the new queue.</summary>
      <param name="value">The element to add.</param>
      <returns>The new immutable queue.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableQueue`1.Enumerator">
      <summary>Enumerates the contents of an immutable queue without allocating any memory.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableQueue`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the immutable queue.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the queue.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable queue.</summary>
      <returns>An enumerator that can be used to iterate through the queue.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableQueue`1.IsEmpty">
      <summary>Gets a value that indicates whether this immutable queue is empty.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <returns>
        <see langword="true" /> if this queue is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.Peek">
      <summary>Returns the element at the beginning of the immutable queue without removing it.</summary>
      <returns>The element at the beginning of the queue.</returns>
      <exception cref="T:System.InvalidOperationException">The queue is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.PeekRef">
      <summary>Gets a read-only reference to the element at the front of the queue.</summary>
      <exception cref="T:System.InvalidOperationException">The queue is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator  that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.System#Collections#Immutable#IImmutableQueue{T}#Clear">
      <summary>Removes all elements from the immutable queue.</summary>
      <returns>The empty immutable queue.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.System#Collections#Immutable#IImmutableQueue{T}#Dequeue">
      <summary>Removes the element at the beginning of the immutable queue, and returns the new queue.</summary>
      <returns>The new immutable queue; never <see langword="null" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableQueue`1.System#Collections#Immutable#IImmutableQueue{T}#Enqueue(`0)">
      <summary>Adds an element to the end of the immutable queue, and returns the new queue.</summary>
      <param name="value">The element to add.</param>
      <returns>The new immutable queue.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedDictionary">
      <summary>Provides a set of initialization methods for instances of the <see cref="T:System.Collections.Immutable.ImmutableSortedDictionary`2" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.Create``2">
      <summary>Creates an empty immutable sorted dictionary.</summary>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>An empty immutable sorted dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.Create``2(System.Collections.Generic.IComparer{``0})">
      <summary>Creates an empty immutable sorted dictionary that uses the specified key comparer.</summary>
      <param name="keyComparer">The implementation to use to determine the equality of keys in the dictionary.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>An empty immutable sorted dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.Create``2(System.Collections.Generic.IComparer{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates an empty immutable sorted dictionary that uses the specified key and value comparers.</summary>
      <param name="keyComparer">The implementation to use to determine the equality of keys in the dictionary.</param>
      <param name="valueComparer">The implementation to use to determine the equality of values in the dictionary.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>An empty immutable sorted dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.CreateBuilder``2">
      <summary>Creates a new immutable sorted dictionary builder.</summary>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>The immutable collection builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.CreateBuilder``2(System.Collections.Generic.IComparer{``0})">
      <summary>Creates a new immutable sorted dictionary builder.</summary>
      <param name="keyComparer">The key comparer.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>The immutable collection builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.CreateBuilder``2(System.Collections.Generic.IComparer{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a new immutable sorted dictionary builder.</summary>
      <param name="keyComparer">The key comparer.</param>
      <param name="valueComparer">The value comparer.</param>
      <typeparam name="TKey">The type of keys stored by the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored by the dictionary.</typeparam>
      <returns>The immutable collection builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.CreateRange``2(System.Collections.Generic.IComparer{``0},System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Creates a new immutable sorted dictionary from the specified range of items with the specified key comparer.</summary>
      <param name="keyComparer">The comparer implementation to use to evaluate keys for equality and sorting.</param>
      <param name="items">The items to add to the sorted dictionary.</param>
      <typeparam name="TKey">The type of keys stored in the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored in the dictionary.</typeparam>
      <returns>The new immutable sorted dictionary that contains the specified items and uses the specified key comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.CreateRange``2(System.Collections.Generic.IComparer{``0},System.Collections.Generic.IEqualityComparer{``1},System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Creates a new immutable sorted dictionary from the specified range of items with the specified key and value comparers.</summary>
      <param name="keyComparer">The comparer implementation to use to compare keys for equality and sorting.</param>
      <param name="valueComparer">The comparer implementation to use to compare values for equality and sorting.</param>
      <param name="items">The items to add to the sorted dictionary before it's immutable.</param>
      <typeparam name="TKey">The type of keys stored in the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored in the dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the specified items and uses the specified comparers.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.CreateRange``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Creates an immutable sorted dictionary that contains the specified items and uses the default comparer.</summary>
      <param name="items">The items to add to the sorted dictionary before it's immutable.</param>
      <typeparam name="TKey">The type of keys stored in the dictionary.</typeparam>
      <typeparam name="TValue">The type of values stored in the dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.ToImmutableSortedDictionary``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}})">
      <summary>Enumerates a sequence of key/value pairs and produces an immutable sorted dictionary of its contents.</summary>
      <param name="source">The sequence of key/value pairs to enumerate.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the key/value pairs in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.ToImmutableSortedDictionary``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}},System.Collections.Generic.IComparer{``0})">
      <summary>Enumerates a sequence of key/value pairs and produces an immutable dictionary of its contents by using the specified key comparer.</summary>
      <param name="source">The sequence of key/value pairs to enumerate.</param>
      <param name="keyComparer">The key comparer to use when building the immutable dictionary.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the key/value pairs in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.ToImmutableSortedDictionary``2(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``0,``1}},System.Collections.Generic.IComparer{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Enumerates a sequence of key/value pairs and produces an immutable sorted dictionary of its contents by using the specified key and value comparers.</summary>
      <param name="source">The sequence of key/value pairs to enumerate.</param>
      <param name="keyComparer">The key comparer to use when building the immutable dictionary.</param>
      <param name="valueComparer">The value comparer to use for the immutable dictionary.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the key/value pairs in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.ToImmutableSortedDictionary``2(System.Collections.Immutable.ImmutableSortedDictionary{``0,``1}.Builder)">
      <summary>Creates an immutable sorted dictionary from the current contents of the builder's dictionary.</summary>
      <param name="builder">The builder to create the immutable sorted dictionary from.</param>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the current contents in the builder's dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.ToImmutableSortedDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Enumerates and transforms a sequence, and produces an immutable sorted dictionary of its contents.</summary>
      <param name="source">The sequence to enumerate to generate the dictionary.</param>
      <param name="keySelector">The function that will produce the key for the dictionary from each sequence element.</param>
      <param name="elementSelector">The function that will produce the value for the dictionary from each sequence element.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <typeparam name="TKey">The type of the keys in the resulting dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the resulting dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.ToImmutableSortedDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IComparer{``1})">
      <summary>Enumerates and transforms a sequence, and produces an immutable sorted dictionary of its contents by using the specified key comparer.</summary>
      <param name="source">The sequence to enumerate to generate the dictionary.</param>
      <param name="keySelector">The function that will produce the key for the dictionary from each sequence element.</param>
      <param name="elementSelector">The function that will produce the value for the dictionary from each sequence element.</param>
      <param name="keyComparer">The key comparer to use for the dictionary.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <typeparam name="TKey">The type of the keys in the resulting dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the resulting dictionary.</typeparam>
      <returns>An immutable dictionary that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary.ToImmutableSortedDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IComparer{``1},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Enumerates and transforms a sequence, and produces an immutable sorted dictionary of its contents by using the specified key and value comparers.</summary>
      <param name="source">The sequence to enumerate to generate the dictionary.</param>
      <param name="keySelector">The function that will produce the key for the dictionary from each sequence element.</param>
      <param name="elementSelector">The function that will produce the value for the dictionary from each sequence element.</param>
      <param name="keyComparer">The key comparer to use for the dictionary.</param>
      <param name="valueComparer">The value comparer to use for the dictionary.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <typeparam name="TKey">The type of the keys in the resulting dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the resulting dictionary.</typeparam>
      <returns>An immutable sorted dictionary that contains the items in the specified sequence.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedDictionary`2">
      <summary>Represents an immutable sorted dictionary.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="TKey">The type of the key contained in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the value contained in the dictionary.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Add(`0,`1)">
      <summary>Adds an element with the specified key and value to the immutable sorted dictionary.</summary>
      <param name="key">The key of the entry to add.</param>
      <param name="value">The value of entry to add.</param>
      <returns>A new immutable sorted dictionary that contains the additional key/value pair.</returns>
      <exception cref="T:System.ArgumentException">The given key already exists in the dictionary but has a different value.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Adds the specific key/value pairs to the immutable sorted dictionary.</summary>
      <param name="items">The key/value pairs to add.</param>
      <returns>A new immutable dictionary that contains the additional key/value pairs.</returns>
      <exception cref="T:System.ArgumentException">One of the given keys already exists in the dictionary but has a different value.</exception>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder">
      <summary>Represents a sorted dictionary that mutates with little or no memory allocations and that can produce or build on immutable sorted dictionary instances very efficiently.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="TKey" />
      <typeparam name="TValue" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Add(`0,`1)">
      <summary>Adds an element that has the specified key and value to the immutable sorted dictionary.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Adds the specified item to the immutable sorted dictionary.</summary>
      <param name="item">The object to add to the dictionary.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Adds a sequence of values to the immutable sorted dictionary.</summary>
      <param name="items">The items to add to the dictionary.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Clear">
      <summary>Removes all items from the immutable sorted dictionary.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether the immutable sorted dictionary contains a specific value.</summary>
      <param name="item">The object to locate in the dictionary.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is found in the dictionary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.ContainsKey(`0)">
      <summary>Determines whether the immutable sorted dictionary contains an element with the specified key.</summary>
      <param name="key">The key to locate in the dictionary.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.ContainsValue(`1)">
      <summary>Determines whether the immutable sorted dictionary contains an element with the specified value.</summary>
      <param name="value">The value to locate in the dictionary. The value can be <see langword="null" /> for reference types.</param>
      <returns>
        <see langword="true" /> if the immutable sorted dictionary contains an element with the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Count">
      <summary>Gets the number of elements in this immutable sorted dictionary.</summary>
      <returns>The number of elements in this dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable sorted dictionary.</summary>
      <returns>An enumerator that can be used to iterate through the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.GetValueOrDefault(`0)">
      <summary>Gets the value for a given key if a matching key exists in the dictionary; otherwise the default value.</summary>
      <param name="key">The key to search for.</param>
      <returns>The value for the key, or <c>default(TValue)</c> if no matching key was found.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.GetValueOrDefault(`0,`1)">
      <summary>Gets the value for a given key if a matching key exists in the dictionary; otherwise the default value.</summary>
      <param name="key">The key to search for.</param>
      <param name="defaultValue">The default value to return if no matching key is found in the dictionary.</param>
      <returns>The value for the key, or <paramref name="defaultValue" /> if no matching key was found.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Item(`0)">
      <summary>Gets or sets the value for a specified key in the immutable sorted dictionary.</summary>
      <param name="key">The key to retrieve the value for.</param>
      <returns>The value associated with the given key.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.KeyComparer">
      <summary>Gets or sets the key comparer.</summary>
      <returns>The key comparer.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Keys">
      <summary>Gets a strongly typed, read-only collection of elements.</summary>
      <returns>A strongly typed, read-only collection of elements.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Remove(`0)">
      <summary>Removes the element with the specified key from the immutable sorted dictionary.</summary>
      <param name="key">The key of the element to remove.</param>
      <returns>
        <see langword="true" /> if the element is successfully removed; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if <paramref name="key" /> was not found in the original dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Removes the first occurrence of a specific object from the immutable sorted dictionary.</summary>
      <param name="item">The object to remove from the dictionary.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> was successfully removed from the dictionary; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if <paramref name="item" /> is not found in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes any entries with keys that match those found in the specified sequence from the immutable sorted dictionary.</summary>
      <param name="keys">The keys for entries to remove from the dictionary.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <param name="array" />
      <param name="arrayIndex" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#IsReadOnly" />
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#Generic#IDictionary{TKey@TValue}#Keys" />
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#Generic#IDictionary{TKey@TValue}#Values" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{TKey@TValue}}#GetEnumerator" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the dictionary to an array, starting at a particular array index.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the dictionary. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds an element with the provided key and value to the dictionary object.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the dictionary object contains an element with the specified key.</summary>
      <param name="key">The key to locate.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the dictionary.</summary>
      <returns>An <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the dictionary.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.IDictionary" /> object has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IDictionary" /> object has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the element with the specified key.</summary>
      <param name="key">The key.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the dictionary.</summary>
      <param name="key">The key of the element to remove.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.ToImmutable">
      <summary>Creates an immutable sorted dictionary based on the contents of this instance.</summary>
      <returns>An immutable sorted dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.TryGetKey(`0,`0@)">
      <summary>Determines whether this dictionary contains a specified key.</summary>
      <param name="equalKey">The key to search for.</param>
      <param name="actualKey">The matching key located in the dictionary if found, or <c>equalkey</c> if no match is found.</param>
      <returns>
        <see langword="true" /> if a match for <paramref name="equalKey" /> is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.TryGetValue(`0,`1@)">
      <summary>Gets the value associated with the specified key.</summary>
      <param name="key">The key whose value will be retrieved.</param>
      <param name="value">When this method returns, contains the value associated with the specified key, if the key is found; otherwise, contains the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
      <returns>
        <see langword="true" /> if the object that implements the dictionary contains an element with the specified key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.ValueComparer">
      <summary>Gets or sets the value comparer.</summary>
      <returns>The value comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.ValueRef(`0)">
      <summary>Returns a read-only reference to the value associated with the provided <paramref name="key" />.</summary>
      <param name="key" />
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The <paramref name="key" /> is not present.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Builder.Values">
      <summary>Gets a collection that contains the values of the immutable sorted dictionary.</summary>
      <returns>A collection that contains the values of the object that implements the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Clear">
      <summary>Retrieves an empty immutable sorted dictionary that has the same ordering and key/value comparison rules as this dictionary instance.</summary>
      <returns>An empty dictionary with equivalent ordering and key/value comparison rules.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether this immutable sorted dictionary contains the specified key/value pair.</summary>
      <param name="pair">The key/value pair to locate.</param>
      <returns>
        <see langword="true" /> if the specified key/value pair is found in the dictionary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.ContainsKey(`0)">
      <summary>Determines whether this immutable sorted map contains the specified key.</summary>
      <param name="key">The key to locate.</param>
      <returns>
        <see langword="true" /> if the immutable dictionary contains the specified key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.ContainsValue(`1)">
      <summary>Determines whether the immutable sorted dictionary contains an element with the specified value.</summary>
      <param name="value">The value to locate. The value can be <see langword="null" /> for reference types.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Count">
      <summary>Gets the number of key/value pairs in the immutable sorted dictionary.</summary>
      <returns>The number of key/value pairs in the dictionary.</returns>
    </member>
    <member name="F:System.Collections.Immutable.ImmutableSortedDictionary`2.Empty">
      <summary>Gets an empty immutable sorted dictionary.</summary>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedDictionary`2.Enumerator">
      <summary>Enumerates the contents of a binary tree.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="TKey" />
      <typeparam name="TValue" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Enumerator.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Collections.Immutable.ImmutableSortedDictionary`2.Enumerator" /> class.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the immutable sorted dictionary.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the sorted dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the immutable sorted dictionary.</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>The current element.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable sorted dictionary.</summary>
      <returns>An enumerator that can be used to iterate through the dictionary.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.IsEmpty">
      <summary>Gets a value that indicates whether this instance of the immutable sorted dictionary is empty.</summary>
      <returns>
        <see langword="true" /> if this instance is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Item(`0)">
      <summary>Gets the <paramref name="TValue" /> associated with the specified key.</summary>
      <param name="key">The key to retrieve the value for.</param>
      <returns>The value associated with the specified key. If no results are found, the operation throws an exception.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.KeyComparer">
      <summary>Gets the key comparer for the immutable sorted dictionary.</summary>
      <returns>The key comparer for the dictionary.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Keys">
      <summary>Gets the keys in the immutable sorted dictionary.</summary>
      <returns>The keys in the immutable dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.Remove(`0)">
      <summary>Removes the element with the specified value from the immutable sorted dictionary.</summary>
      <param name="value">The value of the element to remove.</param>
      <returns>A new immutable dictionary with the specified element removed; or this instance if the specified value cannot be found in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the elements with the specified keys from the immutable sorted dictionary.</summary>
      <param name="keys">The keys of the elements to remove.</param>
      <returns>A new immutable dictionary with the specified keys removed; or this instance if the specified keys cannot be found in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.SetItem(`0,`1)">
      <summary>Sets the specified key and value in the immutable sorted dictionary, possibly overwriting an existing value for the given key.</summary>
      <param name="key">The key of the entry to add.</param>
      <param name="value">The key value to set.</param>
      <returns>A new immutable sorted dictionary that contains the specified key/value pair.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.SetItems(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Sets the specified key/value pairs in the immutable sorted dictionary, possibly overwriting existing values for the keys.</summary>
      <param name="items">The key/value pairs to set in the dictionary. If any of the keys already exist in the dictionary, this method will overwrite their previous values.</param>
      <returns>An immutable dictionary that contains the specified key/value pairs.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <param name="item" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#Clear" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <param name="array" />
      <param name="arrayIndex" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#IsReadOnly" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey@TValue}}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <param name="item" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Add(`0,`1)">
      <param name="key" />
      <param name="value" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Item(`0)">
      <param name="key" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(`0)">
      <param name="key" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{TKey@TValue}}#GetEnumerator" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the dictionary to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the dictionary. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread-safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds an element with the provided key and value to the dictionary object.</summary>
      <param name="key">The object to use as the key of the element to add.</param>
      <param name="value">The object to use as the value of the element to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#Clear">
      <summary>Clears this instance.</summary>
      <exception cref="T:System.NotSupportedException">The dictionary object is read-only.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the immutable dictionary object contains an element with the specified key.</summary>
      <param name="key">The key to locate in the dictionary object.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the immutable dictionary object.</summary>
      <returns>An enumerator object for the dictionary object.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> object has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IDictionary" /> object has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the element with the specified key.</summary>
      <param name="key">The key.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the immutable dictionary object.</summary>
      <param name="key">The key of the element to remove.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#Add(`0,`1)">
      <param name="key" />
      <param name="value" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <param name="pairs" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#Clear" />
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#Remove(`0)">
      <param name="key" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#RemoveRange(System.Collections.Generic.IEnumerable{`0})">
      <param name="keys" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#SetItem(`0,`1)">
      <param name="key" />
      <param name="value" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.System#Collections#Immutable#IImmutableDictionary{TKey@TValue}#SetItems(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <param name="items" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.ToBuilder">
      <summary>Creates an immutable sorted dictionary with the same contents as this dictionary that can be efficiently mutated across multiple operations by using standard mutable interfaces.</summary>
      <returns>A collection with the same contents as this dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.TryGetKey(`0,`0@)">
      <summary>Determines whether this dictionary contains a specified key.</summary>
      <param name="equalKey">The key to search for.</param>
      <param name="actualKey">The matching key located in the dictionary if found, or <c>equalkey</c> if no match is found.</param>
      <returns>
        <see langword="true" /> if a match for <paramref name="equalKey" /> is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>Gets the value associated with the specified key.</summary>
      <param name="key">The key whose value will be retrieved.</param>
      <param name="value">When this method returns, contains the value associated with the specified key, if the key is found; otherwise, contains the default value for the type of the <paramref name="value" /> parameter.</param>
      <returns>
        <see langword="true" /> if the dictionary contains an element with the specified key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.ValueComparer">
      <summary>Gets the value comparer used to determine whether values are equal.</summary>
      <returns>The value comparer used to determine whether values are equal.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.ValueRef(`0)">
      <summary>Returns a read-only reference to the value associated with the provided <paramref name="key" />.</summary>
      <param name="key" />
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The <paramref name="key" /> is not present.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedDictionary`2.Values">
      <summary>Gets the values in the immutable sorted dictionary.</summary>
      <returns>The values in the dictionary.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.WithComparers(System.Collections.Generic.IComparer{`0})">
      <summary>Gets an instance of the immutable sorted dictionary that uses the specified key comparer.</summary>
      <param name="keyComparer">The key comparer to use.</param>
      <returns>An instance of the immutable dictionary that uses the given comparer.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedDictionary`2.WithComparers(System.Collections.Generic.IComparer{`0},System.Collections.Generic.IEqualityComparer{`1})">
      <summary>Gets an instance of the immutable sorted dictionary that uses the specified key and value comparers.</summary>
      <param name="keyComparer">The key comparer to use.</param>
      <param name="valueComparer">The value comparer to use.</param>
      <returns>An instance of the immutable dictionary that uses the given comparers.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedSet">
      <summary>Provides a set of initialization methods for instances of the <see cref="T:System.Collections.Immutable.ImmutableSortedSet`1" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.Create``1">
      <summary>Creates an empty immutable sorted set.</summary>
      <typeparam name="T">The type of items to be stored in the immutable set.</typeparam>
      <returns>An empty immutable sorted set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.Create``1(``0)">
      <summary>Creates a new immutable sorted set that contains the specified item.</summary>
      <param name="item">The item to prepopulate the set with.</param>
      <typeparam name="T">The type of items in the immutable set.</typeparam>
      <returns>A new immutable set that contains the specified item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.Create``1(``0[])">
      <summary>Creates a new immutable sorted set that contains the specified array of items.</summary>
      <param name="items">An array that contains the items to prepopulate the set with.</param>
      <typeparam name="T">The type of items in the immutable set.</typeparam>
      <returns>A new immutable set that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.Create``1(System.Collections.Generic.IComparer{``0})">
      <summary>Creates an empty immutable sorted set that uses the specified comparer.</summary>
      <param name="comparer">The implementation to use when comparing items in the set.</param>
      <typeparam name="T">The type of items in the immutable set.</typeparam>
      <returns>An empty immutable set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.Create``1(System.Collections.Generic.IComparer{``0},``0)">
      <summary>Creates a new immutable sorted set that contains the specified item and uses the specified comparer.</summary>
      <param name="comparer">The implementation to use when comparing items in the set.</param>
      <param name="item">The item to prepopulate the set with.</param>
      <typeparam name="T">The type of items stored in the immutable set.</typeparam>
      <returns>A new immutable set that contains the specified item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.Create``1(System.Collections.Generic.IComparer{``0},``0[])">
      <summary>Creates a new immutable sorted set that contains the specified array of items and uses the specified comparer.</summary>
      <param name="comparer">The implementation to use when comparing items in the set.</param>
      <param name="items">An array that contains the items to prepopulate the set with.</param>
      <typeparam name="T">The type of items in the immutable set.</typeparam>
      <returns>A new immutable set that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.CreateBuilder``1">
      <summary>Returns a collection that can be used to build an immutable sorted set.</summary>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The immutable collection builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.CreateBuilder``1(System.Collections.Generic.IComparer{``0})">
      <summary>Returns a collection that can be used to build an immutable sorted set.</summary>
      <param name="comparer">The comparer used to compare items in the set for equality.</param>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The immutable collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.CreateRange``1(System.Collections.Generic.IComparer{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new immutable collection that contains the specified items.</summary>
      <param name="comparer">The comparer to use to compare elements in this set.</param>
      <param name="items">The items to add to the set before it's immutable.</param>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The new immutable set that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.CreateRange``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new immutable collection that contains the specified items.</summary>
      <param name="items">The items to add to the set with before it's immutable.</param>
      <typeparam name="T">The type of items stored by the collection.</typeparam>
      <returns>The new immutable set that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.ToImmutableSortedSet``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Enumerates a sequence and produces an immutable sorted set of its contents.</summary>
      <param name="source">The sequence to enumerate.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <returns>An immutable sorted set that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.ToImmutableSortedSet``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IComparer{``0})">
      <summary>Enumerates a sequence, produces an immutable sorted set of its contents, and uses the specified comparer.</summary>
      <param name="source">The sequence to enumerate.</param>
      <param name="comparer">The comparer to use for initializing and adding members to the sorted set.</param>
      <typeparam name="TSource">The type of the elements in the sequence.</typeparam>
      <returns>An immutable sorted set that contains the items in the specified sequence.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet.ToImmutableSortedSet``1(System.Collections.Immutable.ImmutableSortedSet{``0}.Builder)">
      <summary>Creates an immutable sorted set from the current contents of the builder's set.</summary>
      <param name="builder">The builder to create the immutable sorted set from.</param>
      <typeparam name="TSource">The type of the elements in the immutable sorted set.</typeparam>
      <returns>An immutable sorted set that contains the current contents in the builder's set.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedSet`1">
      <summary>Represents an immutable sorted set implementation.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of elements in the set.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Add(`0)">
      <summary>Adds the specified value to this immutable sorted set.</summary>
      <param name="value">The value to add.</param>
      <returns>A new set with the element added, or this set if the element is already in this set.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedSet`1.Builder">
      <summary>Represents a sorted set that enables changes with little or no memory allocations, and efficiently manipulates or builds immutable sorted sets.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Add(`0)">
      <summary>Adds an element to the current set and returns a value to indicate whether the element was successfully added.</summary>
      <param name="item">The element to add to the set.</param>
      <returns>
        <see langword="true" /> if the element is added to the set; <see langword="false" /> if the element is already in the set</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Clear">
      <summary>Removes all elements from this set.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Contains(`0)">
      <summary>Determines whether the set contains the specified object.</summary>
      <param name="item">The object to locate in the set.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is found in the set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Count">
      <summary>Gets the number of elements in the immutable sorted set.</summary>
      <returns>The number of elements in this set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the specified set of items from the current set.</summary>
      <param name="other">The collection of items to remove from the set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.GetEnumerator">
      <summary>Returns an enumerator that iterates through the set.</summary>
      <returns>A enumerator that can be used to iterate through the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are also in a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a proper (strict) subset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper subset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a proper (strict) superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper superset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a subset of a specified collection.</summary>
      <param name="other">The collection is compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a subset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set is a superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a superset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Item(System.Int32)">
      <summary>Gets the element of the set at the given index.</summary>
      <param name="index">The 0-based index of the element in the set to return.</param>
      <returns>The element at the given position.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.ItemRef(System.Int32)">
      <summary>Gets a read-only reference to the element of the set at the given <paramref name="index" />.</summary>
      <param name="index">The 0-based index of the element in the set to return.</param>
      <returns>A read-only reference to the element at the given position.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.KeyComparer">
      <summary>Gets or sets the object that is used to determine equality for the values in the immutable sorted set.</summary>
      <returns>The comparer that is used to determine equality for the values in the set.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Max">
      <summary>Gets the maximum value in the immutable sorted set, as defined by the comparer.</summary>
      <returns>The maximum value in the set.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Min">
      <summary>Gets the minimum value in the immutable sorted set, as defined by the comparer.</summary>
      <returns>The minimum value in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set overlaps with the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set and <paramref name="other" /> share at least one common element; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Remove(`0)">
      <summary>Removes the first occurrence of the specified object from the set.</summary>
      <param name="item">The object to remove from the set.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> was removed from the set; <see langword="false" /> if <paramref name="item" /> was not found in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.Reverse">
      <summary>Returns an enumerator that iterates over the immutable sorted set in reverse order.</summary>
      <returns>An enumerator that iterates over the set in reverse order.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current set and the specified collection contain the same elements.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is equal to <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an element to the current set and returns a value to indicate whether the element was successfully added.</summary>
      <param name="item">The element to add to the set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#Generic#ICollection{T}#CopyTo(`0[],System.Int32)">
      <summary>Copies the elements of the collection to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from collection. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value that indicates whether this instance is read-only.</summary>
      <returns>Always <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>A enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the set to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the set. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread-safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread-safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>A enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.ToImmutable">
      <summary>Creates an immutable sorted set based on the contents of this instance.</summary>
      <returns>An immutable set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Builder.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains all elements that are present in both the current set and in the specified collection.</summary>
      <param name="other">The collection to compare to the current state.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Clear">
      <summary>Removes all elements from the immutable sorted set.</summary>
      <returns>An empty set with the elements removed.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Contains(`0)">
      <summary>Determines whether this immutable sorted set contains the specified value.</summary>
      <param name="value">The value to check for.</param>
      <returns>
        <see langword="true" /> if the set contains the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Count">
      <summary>Gets the number of elements in the immutable sorted set.</summary>
      <returns>The number of elements in the immutable sorted set.</returns>
    </member>
    <member name="F:System.Collections.Immutable.ImmutableSortedSet`1.Empty">
      <summary>Gets an empty immutable sorted set.</summary>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableSortedSet`1.Enumerator">
      <summary>Enumerates the contents of a binary tree.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <returns>The element at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Enumerator.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Collections.Immutable.ImmutableSortedSet`1.Enumerator" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the immutable sorted set.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the sorted set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the immutable sorted set.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>The current element.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Except(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes a specified set of items from this immutable sorted set.</summary>
      <param name="other">The items to remove from this set.</param>
      <returns>A new set with the items removed; or the original set if none of the items were in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable sorted set.</summary>
      <returns>An enumerator that can be used to iterate through the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.IndexOf(`0)">
      <summary>Gets the position within this immutable sorted set that the specified value appears in.</summary>
      <param name="item">The value whose position is being sought.</param>
      <returns>The index of the specified <paramref name="item" /> in the sorted set, if <paramref name="item" /> is found. If <paramref name="item" /> is not found and is less than one or more elements in this set, this method returns a negative number that is the bitwise complement of the index of the first element that is larger than value. If <paramref name="item" /> is not found and is greater than any of the elements in the set, this method returns a negative number that is the bitwise complement of the index of the last element plus 1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Intersect(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable sorted set that contains elements that exist both in this set and in the specified set.</summary>
      <param name="other">The set to intersect with this one.</param>
      <returns>A new immutable sorted set that contains any elements that exist in both sets.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.IsEmpty">
      <summary>Gets a value that indicates whether this immutable sorted set is empty.</summary>
      <returns>
        <see langword="true" /> if this set is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable sorted set is a proper (strict) subset of the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper subset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable sorted set is a proper superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a proper superset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable sorted set is a subset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a subset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable sorted set is a superset of a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set is a superset of <paramref name="other" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Item(System.Int32)">
      <summary>Gets the element of the immutable sorted set at the given index.</summary>
      <param name="index">The index of the element to retrieve from the sorted set.</param>
      <returns>The element at the given index.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.ItemRef(System.Int32)">
      <summary>Gets a read-only reference of the element of the set at the given <paramref name="index" />.</summary>
      <param name="index">The 0-based index of the element in the set to return.</param>
      <returns>A read-only reference of the element at the given position.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.KeyComparer">
      <summary>Gets the comparer used to sort keys in the immutable sorted set.</summary>
      <returns>The comparer used to sort keys.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Max">
      <summary>Gets the maximum value in the immutable sorted set, as defined by the comparer.</summary>
      <returns>The maximum value in the set.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.Min">
      <summary>Gets the minimum value in the immutable sorted set, as defined by the comparer.</summary>
      <returns>The minimum value in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable sorted set and a specified collection share common elements.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the current set and <paramref name="other" /> share at least one common element; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Remove(`0)">
      <summary>Removes the specified value from this immutable sorted set.</summary>
      <param name="value">The element to remove.</param>
      <returns>A new immutable sorted set with the element removed, or this set if the element was not found in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Reverse">
      <summary>Returns an <see cref="T:System.Collections.Generic.IEnumerable`1" /> that iterates over this immutable sorted set in reverse order.</summary>
      <returns>An enumerator that iterates over the immutable sorted set in reverse order.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current immutable sorted set and the specified collection contain the same elements.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>
        <see langword="true" /> if the sets are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.SymmetricExcept(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable sorted set that contains elements that exist either in this set or in a given sequence, but not both.</summary>
      <param name="other">The other sequence of items.</param>
      <returns>The new immutable sorted set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds the specified value to the collection.</summary>
      <param name="item">The value to add.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all the items from the collection.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ICollection{T}#CopyTo(`0[],System.Int32)">
      <summary>Copies the elements of the collection to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from collection. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>See the <see cref="T:System.Collections.Generic.ICollection`1" /> interface.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the collection.</summary>
      <param name="item">The object to remove from the collection.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> was successfully removed from the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#IList{T}#Insert(System.Int32,`0)">
      <summary>Inserts an item in the set at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The object to insert into the set.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#IList{T}#Item(System.Int32)">
      <summary>See the <see cref="T:System.Collections.Generic.IList`1" /> interface.</summary>
      <param name="index" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#IList{T}#RemoveAt(System.Int32)">
      <summary>Removes the  item at the specified index.</summary>
      <param name="index">The zero-based index of the item to remove.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ISet{T}#Add(`0)">
      <summary>Adds an element to the current set and returns a value to indicate if the element was successfully added.</summary>
      <param name="item">The element to add to the set.</param>
      <returns>
        <see langword="true" /> if the element is added to the set; <see langword="false" /> if the element is already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ISet{T}#ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes all elements in the specified collection from the current set.</summary>
      <param name="other">The collection of items to remove from the set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ISet{T}#IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are also in a specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ISet{T}#SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Generic#ISet{T}#UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current set so that it contains all elements that are present in either the current set or the specified collection.</summary>
      <param name="other">The collection to compare to the current set.</param>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the set to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the set. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>See the <see cref="T:System.Collections.ICollection" /> interface.</summary>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>See <see cref="T:System.Collections.ICollection" />.</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#Add(System.Object)">
      <summary>Adds an item to the set.</summary>
      <param name="value">The object to add to the set.</param>
      <returns>The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the collection.</returns>
      <exception cref="T:System.NotSupportedException">The set is read-only or has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#Clear">
      <summary>Removes all items from the set.</summary>
      <exception cref="T:System.NotSupportedException">Thrown in all cases.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the set contains a specific value.</summary>
      <param name="value">The object to locate in the set.</param>
      <returns>
        <see langword="true" /> if the object is found in the set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of a specific item in the set.</summary>
      <param name="value">The object to locate in the set.</param>
      <returns>The index of <paramref name="value" /> if found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an item into the set at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The object to insert into the set.</param>
      <exception cref="T:System.NotSupportedException">The set is read-only or has a fixed size.</exception>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.IList" /> has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IList" /> has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Object" /> at the specified index.</summary>
      <param name="index">The index.</param>
      <returns>The <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.NotSupportedException" />
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of a specific object from the set.</summary>
      <param name="value">The object to remove from the set.</param>
      <exception cref="T:System.NotSupportedException">The set is read-only or has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#IList#RemoveAt(System.Int32)">
      <summary>Removes the item at the specified index of the set.</summary>
      <param name="index">The zero-based index of the item to remove.</param>
      <exception cref="T:System.NotSupportedException">The set is read-only or has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Immutable#IImmutableSet{T}#Add(`0)">
      <summary>Adds the specified element to this immutable set.</summary>
      <param name="value">The element to add.</param>
      <returns>A new set with the element added, or this set if the element is already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Immutable#IImmutableSet{T}#Clear">
      <summary>Retrieves an empty immutable set that has the same sorting and ordering semantics as this instance.</summary>
      <returns>An empty set that has the same sorting and ordering semantics as this instance.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Immutable#IImmutableSet{T}#Except(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes the elements in the specified collection from the current immutable set.</summary>
      <param name="other">The items to remove from this set.</param>
      <returns>The new set with the items removed; or the original set if none of the items were in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Immutable#IImmutableSet{T}#Intersect(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable set that contains elements that exist in both this set and the specified set.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>A new immutable set that contains any elements that exist in both sets.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Immutable#IImmutableSet{T}#Remove(`0)">
      <summary>Removes the specified element from this immutable set.</summary>
      <param name="value">The element to remove.</param>
      <returns>A new set with the specified element removed, or the current set if the element cannot be found in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Immutable#IImmutableSet{T}#SymmetricExcept(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates an immutable set that contains only elements that are present either in the current set or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current set.</param>
      <returns>A new set that contains the elements that are present only in the current set or in the specified collection, but not both.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.System#Collections#Immutable#IImmutableSet{T}#Union(System.Collections.Generic.IEnumerable{`0})">
      <summary>Creates a new immutable set that contains all elements that are present in either the current set or in the specified collection.</summary>
      <param name="other">The collection to add elements from.</param>
      <returns>A new immutable set with the items added; or the original set if all the items were already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.ToBuilder">
      <summary>Creates a collection that has the same contents as this immutable sorted set that can be efficiently manipulated by using standard mutable interfaces.</summary>
      <returns>The sorted set builder.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.TryGetValue(`0,`0@)">
      <summary>Searches the set for a given value and returns the equal value it finds, if any.</summary>
      <param name="equalValue">The value to search for.</param>
      <param name="actualValue">The value from the set that the search found, or the original value if the search yielded no match.</param>
      <returns>A value indicating whether the search was successful.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.Union(System.Collections.Generic.IEnumerable{`0})">
      <summary>Adds a given set of items to this immutable sorted set.</summary>
      <param name="other">The items to add.</param>
      <returns>The new set with the items added; or the original set if all the items were already in the set.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableSortedSet`1.WithComparer(System.Collections.Generic.IComparer{`0})">
      <summary>Returns the immutable sorted set that has the specified key comparer.</summary>
      <param name="comparer">The comparer to check for.</param>
      <returns>The immutable sorted set that has the specified key comparer.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableStack">
      <summary>Provides a set of initialization methods for instances of the <see cref="T:System.Collections.Immutable.ImmutableStack`1" /> class.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack.Create``1">
      <summary>Creates an empty immutable stack.</summary>
      <typeparam name="T">The type of items to be stored in the immutable stack.</typeparam>
      <returns>An empty immutable stack.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack.Create``1(``0)">
      <summary>Creates a new immutable stack that contains the specified item.</summary>
      <param name="item">The item to prepopulate the stack with.</param>
      <typeparam name="T">The type of items in the immutable stack.</typeparam>
      <returns>A new immutable collection that contains the specified item.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack.Create``1(``0[])">
      <summary>Creates a new immutable stack that contains the specified array of items.</summary>
      <param name="items">An array that contains the items to prepopulate the stack with.</param>
      <typeparam name="T">The type of items in the immutable stack.</typeparam>
      <returns>A new immutable stack that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack.CreateRange``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a new immutable stack that contains the specified items.</summary>
      <param name="items">The items to add to the stack before it's immutable.</param>
      <typeparam name="T">The type of items in the stack.</typeparam>
      <returns>An immutable stack that contains the specified items.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack.Pop``1(System.Collections.Immutable.IImmutableStack{``0},``0@)">
      <summary>Removes the specified item from an immutable stack.</summary>
      <param name="stack">The stack to modify.</param>
      <param name="value">The item to remove from the stack.</param>
      <typeparam name="T">The type of items contained in the stack.</typeparam>
      <returns>A stack; never <see langword="null" />.</returns>
      <exception cref="T:System.InvalidOperationException">The stack is empty.</exception>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableStack`1">
      <summary>Represents an immutable stack.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T">The type of element on the stack.</typeparam>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.Clear">
      <summary>Removes all objects from the immutable stack.</summary>
      <returns>An empty immutable stack.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableStack`1.Empty">
      <summary>Gets an empty immutable stack.</summary>
      <returns>An empty immutable stack.</returns>
    </member>
    <member name="T:System.Collections.Immutable.ImmutableStack`1.Enumerator">
      <summary>Enumerates the contents of an immutable stack without allocating any memory.
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
      <typeparam name="T" />
    </member>
    <member name="P:System.Collections.Immutable.ImmutableStack`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the immutable stack.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the stack.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the immutable stack.</summary>
      <returns>An enumerator that can be used to iterate through the stack.</returns>
    </member>
    <member name="P:System.Collections.Immutable.ImmutableStack`1.IsEmpty">
      <summary>Gets a value that indicates whether this instance of the immutable stack is empty.</summary>
      <returns>
        <see langword="true" /> if this instance is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.Peek">
      <summary>Returns the object at the top of the stack without removing it.</summary>
      <returns>The object at the top of the stack.</returns>
      <exception cref="T:System.InvalidOperationException">The stack is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.PeekRef">
      <summary>Gets a read-only reference to the element on the top of the stack.</summary>
      <returns>A read-only reference to the element on the top of the stack.</returns>
      <exception cref="T:System.InvalidOperationException">Thrown when the stack is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.Pop">
      <summary>Removes the element at the top of the immutable stack and returns the stack after the removal.</summary>
      <returns>A stack; never <see langword="null" />.</returns>
      <exception cref="T:System.InvalidOperationException">The stack is empty.</exception>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.Pop(`0@)">
      <summary>Removes the specified element from the immutable stack and returns the stack after the removal.</summary>
      <param name="value">The value to remove from the stack.</param>
      <returns>A stack; never <see langword="null" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.Push(`0)">
      <summary>Inserts an object at the top of the immutable stack and returns the new stack.</summary>
      <param name="value">The object to push onto the stack.</param>
      <returns>The new stack.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator  that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.System#Collections#Immutable#IImmutableStack{T}#Clear">
      <summary>Removes all elements from the immutable stack.</summary>
      <returns>The empty immutable stack.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.System#Collections#Immutable#IImmutableStack{T}#Pop">
      <summary>Removes the element at the top of the immutable stack and returns the new stack.</summary>
      <returns>The new stack; never <see langword="null" />.</returns>
    </member>
    <member name="M:System.Collections.Immutable.ImmutableStack`1.System#Collections#Immutable#IImmutableStack{T}#Push(`0)">
      <summary>Inserts an element at the top of the immutable stack and returns the new stack.</summary>
      <param name="value">The element to push onto the stack.</param>
      <returns>The new stack.</returns>
    </member>
    <member name="T:System.Linq.ImmutableArrayExtensions">
      <summary>LINQ extension method overrides that offer greater efficiency for <see cref="T:System.Collections.Immutable.ImmutableArray`1" /> than the standard LINQ methods
NuGet package: System.Collections.Immutable (about immutable collections and how to install)</summary>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Aggregate``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,``0,``0})">
      <summary>Applies a function to a sequence of elements in a cumulative way.</summary>
      <param name="immutableArray">The collection to apply the function to.</param>
      <param name="func">A function to be invoked on each element, in a cumulative way.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The final value after the cumulative function has been applied to all elements.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Aggregate``2(System.Collections.Immutable.ImmutableArray{``1},``0,System.Func{``0,``1,``0})">
      <summary>Applies a function to a sequence of elements in a cumulative way.</summary>
      <param name="immutableArray">The collection to apply the function to.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">A function to be invoked on each element, in a cumulative way.</param>
      <typeparam name="TAccumulate">The type of the accumulated value.</typeparam>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The final accumulator value.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Aggregate``3(System.Collections.Immutable.ImmutableArray{``2},``0,System.Func{``0,``2,``0},System.Func{``0,``1})">
      <summary>Applies a function to a sequence of elements in a cumulative way.</summary>
      <param name="immutableArray">The collection to apply the function to.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">A function to be invoked on each element, in a cumulative way.</param>
      <param name="resultSelector" />
      <typeparam name="TAccumulate">The type of the accumulated value.</typeparam>
      <typeparam name="TResult">The type of result returned by the result selector.</typeparam>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The final accumulator value.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.All``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Gets a value indicating whether all elements in this array match a given condition.</summary>
      <param name="immutableArray">The array to check for matches.</param>
      <param name="predicate">The predicate.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>
        <see langword="true" /> if every element of the source sequence passes the test in the specified predicate; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Any``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Gets a value indicating whether the array contains any elements.</summary>
      <param name="immutableArray">The array to check for elements.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>
        <see langword="true" /> if the array contains an elements; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Any``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Gets a value indicating whether the array contains any elements that match a specified condition.</summary>
      <param name="immutableArray">The array to check for elements.</param>
      <param name="predicate">The delegate that defines the condition to match to an element.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>
        <see langword="true" /> if an element matches the specified condition; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Any``1(System.Collections.Immutable.ImmutableArray{``0}.Builder)">
      <summary>Returns a value indicating whether this collection contains any elements.</summary>
      <param name="builder">The builder to check for matches.</param>
      <typeparam name="T">The type of elements in the array.</typeparam>
      <returns>
        <see langword="true" /> if the array builder contains any elements; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.ElementAt``1(System.Collections.Immutable.ImmutableArray{``0},System.Int32)">
      <summary>Returns the element at a specified index in the array.</summary>
      <param name="immutableArray">The array to find an element in.</param>
      <param name="index">The index for the element to retrieve.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The item at the specified index.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.ElementAtOrDefault``1(System.Collections.Immutable.ImmutableArray{``0},System.Int32)">
      <summary>Returns the element at a specified index in a sequence or a default value if the index is out of range.</summary>
      <param name="immutableArray">The array to find an element in.</param>
      <param name="index">The index for the element to retrieve.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The item at the specified index, or the default value if the index is not found.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.First``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Returns the first element in an array.</summary>
      <param name="immutableArray">The array to get an item from.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The first item in the array.</returns>
      <exception cref="T:System.InvalidOperationException">If the array is empty.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.First``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the first element in a sequence that satisfies a specified condition.</summary>
      <param name="immutableArray">The array to get an item from.</param>
      <param name="predicate">The delegate that defines the conditions of the element to search for.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The first item in the list if it meets the condition specified by <paramref name="predicate" />.</returns>
      <exception cref="T:System.InvalidOperationException">If the array is empty.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.First``1(System.Collections.Immutable.ImmutableArray{``0}.Builder)">
      <summary>Returns the first element in the collection.</summary>
      <param name="builder">The builder to retrieve an item from.</param>
      <typeparam name="T">The type of items in the array.</typeparam>
      <returns>The first item in the list.</returns>
      <exception cref="T:System.InvalidOperationException">If the array is empty.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.FirstOrDefault``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Returns the first element of a sequence, or a default value if the sequence contains no elements.</summary>
      <param name="immutableArray">The array to retrieve items from.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The first item in the list, if found; otherwise the default value for the item type.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.FirstOrDefault``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the first element of the sequence that satisfies a condition or a default value if no such element is found.</summary>
      <param name="immutableArray">The array to retrieve elements from.</param>
      <param name="predicate">The delegate that defines the conditions of the element to search for.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The first item in the list, if found; otherwise the default value for the item type.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.FirstOrDefault``1(System.Collections.Immutable.ImmutableArray{``0}.Builder)">
      <summary>Returns the first element in the collection, or the default value if the collection is empty.</summary>
      <param name="builder">The builder to retrieve an element from.</param>
      <typeparam name="T">The type of item in the builder.</typeparam>
      <returns>The first item in the list, if found; otherwise the default value for the item type.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Last``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Returns the last element of the array.</summary>
      <param name="immutableArray">The array to retrieve items from.</param>
      <typeparam name="T">The type of element contained by the array.</typeparam>
      <returns>The last element in the array.</returns>
      <exception cref="T:System.InvalidOperationException">Thrown if the collection is empty.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Last``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the last element of a sequence that satisfies a specified condition.</summary>
      <param name="immutableArray">The array to retrieve elements from.</param>
      <param name="predicate">The delegate that defines the conditions of the element to retrieve.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The last element of the array that satisfies the <paramref name="predicate" /> condition.</returns>
      <exception cref="T:System.InvalidOperationException">Thrown if the collection is empty.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Last``1(System.Collections.Immutable.ImmutableArray{``0}.Builder)">
      <summary>Returns the last element in the collection.</summary>
      <param name="builder">The builder to retrieve elements from.</param>
      <typeparam name="T">The type of item in the builder.</typeparam>
      <returns>The last element in the builder.</returns>
      <exception cref="T:System.InvalidOperationException">Thrown if the collection is empty.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.LastOrDefault``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Returns the last element of a sequence, or a default value if the sequence contains no elements.</summary>
      <param name="immutableArray">The array to retrieve items from.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The last element of a sequence, or a default value if the sequence contains no elements.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.LastOrDefault``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the last element of a sequence that satisfies a condition or a default value if no such element is found.</summary>
      <param name="immutableArray">The array to retrieve an element from.</param>
      <param name="predicate">The delegate that defines the conditions of the element to search for.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The last element of a sequence, or a default value if the sequence contains no elements.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.LastOrDefault``1(System.Collections.Immutable.ImmutableArray{``0}.Builder)">
      <summary>Returns the last element in the collection, or the default value if the collection is empty.</summary>
      <param name="builder">The builder to retrieve an element from.</param>
      <typeparam name="T">The type of item in the builder.</typeparam>
      <returns>The last element of a sequence, or a default value if the sequence contains no elements.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Select``2(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,``1})">
      <summary>Projects each element of a sequence into a new form.</summary>
      <param name="immutableArray">The immutable array to select items from.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <typeparam name="TResult">The type of the result element.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result of invoking the transform function on each element of source.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.SelectMany``3(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Collections.Generic.IEnumerable{``1}},System.Func{``0,``1,``2})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" />,             flattens the resulting sequences into one sequence, and invokes a result             selector function on each element therein.</summary>
      <param name="immutableArray">The immutable array.</param>
      <param name="collectionSelector">A transform function to apply to each element of the input sequence.</param>
      <param name="resultSelector">A transform function to apply to each element of the intermediate sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="immutableArray" />.</typeparam>
      <typeparam name="TCollection">The type of the intermediate elements collected by <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the resulting sequence.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result             of invoking the one-to-many transform function <paramref name="collectionSelector" /> on each             element of <paramref name="immutableArray" /> and then mapping each of those sequence elements and their             corresponding source element to a result element.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.SequenceEqual``2(System.Collections.Immutable.ImmutableArray{``1},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Determines whether two sequences are equal according to an equality comparer.</summary>
      <param name="immutableArray">The array to use for comparison.</param>
      <param name="items">The items to use for comparison.</param>
      <param name="comparer">The comparer to use to check for equality.</param>
      <typeparam name="TDerived">The type of element in the compared array.</typeparam>
      <typeparam name="TBase">The type of element contained by the collection.</typeparam>
      <returns>
        <see langword="true" /> to indicate the sequences are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.SequenceEqual``2(System.Collections.Immutable.ImmutableArray{``1},System.Collections.Immutable.ImmutableArray{``0},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Determines whether two sequences are equal according to an equality comparer.</summary>
      <param name="immutableArray">The array to use for comparison.</param>
      <param name="items">The items to use for comparison.</param>
      <param name="comparer">The comparer to use to check for equality.</param>
      <typeparam name="TDerived">The type of element in the compared array.</typeparam>
      <typeparam name="TBase">The type of element contained by the collection.</typeparam>
      <returns>
        <see langword="true" /> to indicate the sequences are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.SequenceEqual``2(System.Collections.Immutable.ImmutableArray{``1},System.Collections.Immutable.ImmutableArray{``0},System.Func{``1,``1,System.Boolean})">
      <summary>Determines whether two sequences are equal according to an equality comparer.</summary>
      <param name="immutableArray">The array to use for comparison.</param>
      <param name="items">The items to use for comparison.</param>
      <param name="predicate">The comparer to use to check for equality.</param>
      <typeparam name="TDerived">The type of element in the compared array.</typeparam>
      <typeparam name="TBase">The type of element contained by the collection.</typeparam>
      <returns>
        <see langword="true" /> to indicate the sequences are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Single``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Returns the only element of a sequence, and throws an exception if there is not exactly one element in the sequence.</summary>
      <param name="immutableArray">The array to retrieve the element from.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The element in the sequence.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Single``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the only element of a sequence that satisfies a specified condition, and throws an exception if more than one such element exists.</summary>
      <param name="immutableArray" />
      <param name="predicate" />
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.SingleOrDefault``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Returns the only element of the array, or a default value if the sequence is empty; this method throws an exception if there is more than one element in the sequence.</summary>
      <param name="immutableArray">The array.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The element in the array, or the default value if the array is empty.</returns>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains more than one element.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.SingleOrDefault``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the only element of a sequence that satisfies a specified condition or a default value if no such element exists; this method throws an exception if more than one element satisfies the condition.</summary>
      <param name="immutableArray">The array to get the element from.</param>
      <param name="predicate">The condition the element must satisfy.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The element if it satisfies the specified condition; otherwise the default element.</returns>
      <exception cref="T:System.InvalidOperationException">More than one element satisfies the condition in <paramref name="predicate" />.</exception>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.ToArray``1(System.Collections.Immutable.ImmutableArray{``0})">
      <summary>Copies the contents of this array to a mutable array.</summary>
      <param name="immutableArray" />
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The newly instantiated array.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.ToDictionary``2(System.Collections.Immutable.ImmutableArray{``1},System.Func{``1,``0})">
      <summary>Creates a dictionary based on the contents of this array.</summary>
      <param name="immutableArray">The array to create a dictionary from.</param>
      <param name="keySelector">The key selector.</param>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The newly initialized dictionary.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.ToDictionary``2(System.Collections.Immutable.ImmutableArray{``1},System.Func{``1,``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Creates a dictionary based on the contents of this array.</summary>
      <param name="immutableArray">The array to create a dictionary from.</param>
      <param name="keySelector">The key selector.</param>
      <param name="comparer">The comparer to initialize the dictionary with.</param>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The newly initialized dictionary.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.ToDictionary``3(System.Collections.Immutable.ImmutableArray{``2},System.Func{``2,``0},System.Func{``2,``1})">
      <summary>Creates a dictionary based on the contents of this array.</summary>
      <param name="immutableArray">The array to create a dictionary from.</param>
      <param name="keySelector">The key selector.</param>
      <param name="elementSelector">The element selector.</param>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="TElement">The type of the element.</typeparam>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The newly initialized dictionary.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.ToDictionary``3(System.Collections.Immutable.ImmutableArray{``2},System.Func{``2,``0},System.Func{``2,``1},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Creates a dictionary based on the contents of this array.</summary>
      <param name="immutableArray">The array to create a dictionary from.</param>
      <param name="keySelector">The key selector.</param>
      <param name="elementSelector">The element selector.</param>
      <param name="comparer">The comparer to initialize the dictionary with.</param>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="TElement">The type of the element.</typeparam>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>The newly initialized dictionary.</returns>
    </member>
    <member name="M:System.Linq.ImmutableArrayExtensions.Where``1(System.Collections.Immutable.ImmutableArray{``0},System.Func{``0,System.Boolean})">
      <summary>Filters a sequence of values based on a predicate.</summary>
      <param name="immutableArray">The array to filter.</param>
      <param name="predicate">The condition to use for filtering the array content.</param>
      <typeparam name="T">The type of element contained by the collection.</typeparam>
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements that meet the condition.</returns>
    </member>
  </members>
</doc>