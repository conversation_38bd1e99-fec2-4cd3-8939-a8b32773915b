﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.Application.Insurance {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class APTCSpecific {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal APTCSpecific() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.Application.Insurance.APTCSpecific", typeof(APTCSpecific).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Another Employer.
        /// </summary>
        public static string addAnotherEmployer {
            get {
                return ResourceManager.GetString("addAnotherEmployer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When was [NAME] adopted or placed for adoption?.
        /// </summary>
        public static string adoptionDate {
            get {
                return ResourceManager.GetString("adoptionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Have any of these people been adopted or placed for adoption in the last 60 days?.
        /// </summary>
        public static string adoptionLast60Days {
            get {
                return ResourceManager.GetString("adoptionLast60Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to American Indian/Alaska Native Information.
        /// </summary>
        public static string aianInformation {
            get {
                return ResourceManager.GetString("aianInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Credit Program Questions.
        /// </summary>
        public static string aptcProgramQuestions {
            get {
                return ResourceManager.GetString("aptcProgramQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer that offers health coverage is required.
        /// </summary>
        public static string atleastOneEmployerOffersHealthCoverage {
            get {
                return ResourceManager.GetString("atleastOneEmployerOffersHealthCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME] expect [EMPLOYER] to make any of these changes to the coverage offered to [NAME] in [COVERAGEYEAR]?.
        /// </summary>
        public static string changesToCoverage {
            get {
                return ResourceManager.GetString("changesToCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [EMPLOYER] will no longer offer health coverage.
        /// </summary>
        public static string changesToCoverageA {
            get {
                return ResourceManager.GetString("changesToCoverageA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What will be the last day [EMPLOYER] offers coverage?.
        /// </summary>
        public static string changesToCoverageA1 {
            get {
                return ResourceManager.GetString("changesToCoverageA1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [EMPLOYER] will change the cost of premiums for the lowest-cost plan available to the employee that meets minimum value.(Only tell us about plans that aren&apos;t family plans.).
        /// </summary>
        public static string changesToCoverageB {
            get {
                return ResourceManager.GetString("changesToCoverageB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much will the employee have to pay in premiums for this plan?  If the employer has wellness programs, provide the premium that the employee would pay if [HE/SHE] received the maximum discount for any tobacco cessation programs, and didn&apos;t receive any other discounts based on wellness programs..
        /// </summary>
        public static string changesToCoverageB1 {
            get {
                return ResourceManager.GetString("changesToCoverageB1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Email.
        /// </summary>
        public static string contactEmail {
            get {
                return ResourceManager.GetString("contactEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Name.
        /// </summary>
        public static string contactName {
            get {
                return ResourceManager.GetString("contactName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telephone number must contain 10 digits..
        /// </summary>
        public static string contactNumber {
            get {
                return ResourceManager.GetString("contactNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Phone.
        /// </summary>
        public static string contactPhone {
            get {
                return ResourceManager.GetString("contactPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date when coverage will change.
        /// </summary>
        public static string CoverageChangeDate {
            get {
                return ResourceManager.GetString("CoverageChangeDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is the coverage from [EMPLOYER] COBRA coverage?.
        /// </summary>
        public static string coverageIsCobra {
            get {
                return ResourceManager.GetString("coverageIsCobra", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME]&apos;s coverage from [EMPLOYER] a retiree health plan?.
        /// </summary>
        public static string coverageIsRetiree {
            get {
                return ResourceManager.GetString("coverageIsRetiree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coverage Start Date.
        /// </summary>
        public static string coverageStartDate {
            get {
                return ResourceManager.GetString("coverageStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coverage start date is required.
        /// </summary>
        public static string coverageStartDateRequired {
            get {
                return ResourceManager.GetString("coverageStartDateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME] currently enrolled in this employer&apos;s health coverage?.
        /// </summary>
        public static string currentlyEnrolled {
            get {
                return ResourceManager.GetString("currentlyEnrolled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date [NAME]&apos;s coverage could start.
        /// </summary>
        public static string dateCoverageCouldStart {
            get {
                return ResourceManager.GetString("dateCoverageCouldStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did [NAME] lose health coverage because [HE/SHE] didn&apos;t pay premiums?.
        /// </summary>
        public static string didNotPayPremiums {
            get {
                return ResourceManager.GetString("didNotPayPremiums", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When did [NAME] gain eligible immigration status?.
        /// </summary>
        public static string eligibleImmStatusDate {
            get {
                return ResourceManager.GetString("eligibleImmStatusDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did any of these people gain eligible immigration status in the last 60 days?.
        /// </summary>
        public static string eligibleImmStatusLast60Days {
            get {
                return ResourceManager.GetString("eligibleImmStatusLast60Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tell us about [EMPLOYER]&apos;s Health Coverage for [COVERAGEYEAR].First,print out and take the [LINK] to [EMPLOYER] to collect the information you need for this section for using the tool to fill out the application. Instructions on the employer coverage form provide step-by-step instructions for using the tool to answer the questions in this section..
        /// </summary>
        public static string empCoverageToolInfo {
            get {
                return ResourceManager.GetString("empCoverageToolInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer CoverageTool.
        /// </summary>
        public static string empCoverageToolLinkText {
            get {
                return ResourceManager.GetString("empCoverageToolLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s [NAME]&apos;s current work status at this employer?.
        /// </summary>
        public static string employeeStatus {
            get {
                return ResourceManager.GetString("employeeStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string employerAddress {
            get {
                return ResourceManager.GetString("employerAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Contact Information.
        /// </summary>
        public static string employerContactInformation {
            get {
                return ResourceManager.GetString("employerContactInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identification Number(EIN).
        /// </summary>
        public static string employerEIN {
            get {
                return ResourceManager.GetString("employerEIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Email.
        /// </summary>
        public static string employerEmail {
            get {
                return ResourceManager.GetString("employerEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Health Coverage.
        /// </summary>
        public static string employerHealthCoverage {
            get {
                return ResourceManager.GetString("employerHealthCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Health Coverage Detail.
        /// </summary>
        public static string employerHealthCoverageDetail {
            get {
                return ResourceManager.GetString("employerHealthCoverageDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Information.
        /// </summary>
        public static string employerInformation {
            get {
                return ResourceManager.GetString("employerInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Name is required..
        /// </summary>
        public static string employerNameRequired {
            get {
                return ResourceManager.GetString("employerNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number.
        /// </summary>
        public static string employerPhone {
            get {
                return ResourceManager.GetString("employerPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer phone number is required..
        /// </summary>
        public static string employerPhoneRequired {
            get {
                return ResourceManager.GetString("employerPhoneRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who can we contact about this employer&apos;s health coverage?  If you&apos;re not sure, ask your employer..
        /// </summary>
        public static string employersContact {
            get {
                return ResourceManager.GetString("employersContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When could [NAME] enroll in coverage?.
        /// </summary>
        public static string enrollDate {
            get {
                return ResourceManager.GetString("enrollDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enrollment Date.
        /// </summary>
        public static string enrollmentDate {
            get {
                return ResourceManager.GetString("enrollmentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME] expect to drop [EMPLOYER]&apos;s health coverage in [COVERAGEYEAR]?.
        /// </summary>
        public static string expectToDropCoverage {
            get {
                return ResourceManager.GetString("expectToDropCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s [NAME]&apos;s last day of coverage through [EMPLOYER]&apos;s health plan?.
        /// </summary>
        public static string expectToDropCoverageDate {
            get {
                return ResourceManager.GetString("expectToDropCoverageDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When did [NAME] get married?.
        /// </summary>
        public static string getMarriedDate {
            get {
                return ResourceManager.GetString("getMarriedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did any of these people get married in the last 60 days?.
        /// </summary>
        public static string getMarriedLast60Days {
            get {
                return ResourceManager.GetString("getMarriedLast60Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Health coverage program is required.
        /// </summary>
        public static string healthCoverageSelectionRequired {
            get {
                return ResourceManager.GetString("healthCoverageSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How often would [NAME] pay this amount?.
        /// </summary>
        public static string howOftenPayAmt {
            get {
                return ResourceManager.GetString("howOftenPayAmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tell us about [EMPLOYER]&apos;s health coverage for [COVERAGEYEAR].  First, print out and take the Employer coverage tool to [EMPLOYER] to collect the information you need for this section for using the tool to fill our the application.  Instructions on the employer coverage form provide step-by-step instructions for using the tool to answer the questions in this section..
        /// </summary>
        public static string instructions {
            get {
                return ResourceManager.GetString("instructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME] currently eligible for health coverage through a job(even if it&apos;s from another person&apos;s job, like a spouse[UNDER26])?.
        /// </summary>
        public static string isEligibleThroughJob {
            get {
                return ResourceManager.GetString("isEligibleThroughJob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last day of coverage.
        /// </summary>
        public static string lastCoverageDate {
            get {
                return ResourceManager.GetString("lastCoverageDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When will [NAME]&apos;s health coverage end?.
        /// </summary>
        public static string loseCoverageDate {
            get {
                return ResourceManager.GetString("loseCoverageDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are any of these people going to lose their health coverage in the next 60 days?.
        /// </summary>
        public static string loseCoverageNext60Days {
            get {
                return ResourceManager.GetString("loseCoverageNext60Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When did [NAME] lose health coverage?.
        /// </summary>
        public static string lostCoverageDate {
            get {
                return ResourceManager.GetString("lostCoverageDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did any of these people lose health coverage in the last 60 days?.
        /// </summary>
        public static string lostCoverageLast60Days {
            get {
                return ResourceManager.GetString("lostCoverageLast60Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For the lowest-cost plan available only to the employee that meets the minimum value standard:(Only tell us about plans that aren&apos;t family plans)..
        /// </summary>
        public static string lowestCostPlan {
            get {
                return ResourceManager.GetString("lowestCostPlan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much would the employee have to pay in premiums for this plan?  If the employer has wellness programs, provide the premium that the employee would pay if [HE/SHE] received the maximum discount for any tobacco cessation programs and didn&apos;t receive any other discounts based on wellness programs..
        /// </summary>
        public static string lowestCostPlanA {
            get {
                return ResourceManager.GetString("lowestCostPlanA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [EMPLOYER] offer a health plan that meets the minimum value standard?.
        /// </summary>
        public static string meetsMinValueStandard {
            get {
                return ResourceManager.GetString("meetsMinValueStandard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are any of these people a member of a federally recognized tribe?.
        /// </summary>
        public static string memberOfAnyTribe {
            get {
                return ResourceManager.GetString("memberOfAnyTribe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who is a member of the [TRIBE] tribe?.
        /// </summary>
        public static string memberOfThisTribe {
            get {
                return ResourceManager.GetString("memberOfThisTribe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When did [HE/SHE] move?.
        /// </summary>
        public static string moveDate {
            get {
                return ResourceManager.GetString("moveDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What was the Zip code and county of [NAME]&apos;s last address?.
        /// </summary>
        public static string moveFromZipCounty {
            get {
                return ResourceManager.GetString("moveFromZipCounty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did any of these people move in the last 60 days?.
        /// </summary>
        public static string moveLast60Days {
            get {
                return ResourceManager.GetString("moveLast60Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to * An employer-sponsored health plan meets the “minimum value standard” if the plan’s share of the total allowed benefit costs covered by the plan is no less than 60 percent of such costs (Section 36B(c)(2)(C)(ii) of the Internal Revenue Code of 1986).
        /// </summary>
        public static string mvsHelpNote {
            get {
                return ResourceManager.GetString("mvsHelpNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For [NAME] to get help paying for health coverage, [HE/SHE] must file a joint federal income tax return with [HIS/HER] spouse.  Do you want to change your answers about how [NAME] will file taxes for [COVERAGEYEAR]?.
        /// </summary>
        public static string notFileJointlyChangeAnswers {
            get {
                return ResourceManager.GetString("notFileJointlyChangeAnswers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For [NAME] to get help paying for health coverage, each person must file a tax return or be claimed as a dependent on some else&apos;s tax return.  Do you want to change your answers about how [NAME] will file taxes for [COVERAGEYEAR]?.
        /// </summary>
        public static string notTaxFilerOrDepChangeAnswers {
            get {
                return ResourceManager.GetString("notTaxFilerOrDepChangeAnswers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You selected &quot;other relative&quot; or &quot;other unrelated&quot; for relationship of [NAME] to [CONTACT].  Select one of these options to describe the relationship of [NAME] to [CONTACT].
        /// </summary>
        public static string otherRelationship {
            get {
                return ResourceManager.GetString("otherRelationship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        public static string phone {
            get {
                return ResourceManager.GetString("phone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME] planning to enroll in [EMPLOYER]&apos;s health coverage in [COVERAGEYEAR]?.
        /// </summary>
        public static string planningToEnroll {
            get {
                return ResourceManager.GetString("planningToEnroll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s the first day [NAME] will be covered by [EMPLOYER]&apos;s health plan?.
        /// </summary>
        public static string planningToEnrollDate {
            get {
                return ResourceManager.GetString("planningToEnrollDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Premium Amount.
        /// </summary>
        public static string premiumAmount {
            get {
                return ResourceManager.GetString("premiumAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [FILER] indicated [HE/SHE] is the claiming tax filer for [NAMES], but a Social Security number(SSN) hasn&apos;t been entered for [FILER].  Providing an SSN may help [NAMES] pay for health coverage.  The SSN you provide won&apos;t be used to verify citizenship or immigration status.  Does [FILER] want to provide one now?.
        /// </summary>
        public static string provideSSNNow {
            get {
                return ResourceManager.GetString("provideSSNNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When was [NAME] released from incarceration?.
        /// </summary>
        public static string releasedFromJailDate {
            get {
                return ResourceManager.GetString("releasedFromJailDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did any of these people get released from incarceration(detention or jail) in the last 60 days?.
        /// </summary>
        public static string releasedFromJailLast60Days {
            get {
                return ResourceManager.GetString("releasedFromJailLast60Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME] enrolled in health coverage from any of the following?.
        /// </summary>
        public static string selectCoverageEnrolledIn {
            get {
                return ResourceManager.GetString("selectCoverageEnrolledIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select current work status..
        /// </summary>
        public static string selectPeopleAtEmployee {
            get {
                return ResourceManager.GetString("selectPeopleAtEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a state and tribe..
        /// </summary>
        public static string stateAndTribe {
            get {
                return ResourceManager.GetString("stateAndTribe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to plan is no less than 60 percent of such costs (Section 36B(c)(2)(C)(ii) of the Internal Revenue Code of 1986).
        /// </summary>
        public static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tell us about [EMPLOYER]..
        /// </summary>
        public static string tellUsAboutEmployer {
            get {
                return ResourceManager.GetString("tellUsAboutEmployer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tribe name.
        /// </summary>
        public static string tribeName {
            get {
                return ResourceManager.GetString("tribeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  or parent/guardian.
        /// </summary>
        public static string under26Text {
            get {
                return ResourceManager.GetString("under26Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME] currently in a waiting or probationary period?.
        /// </summary>
        public static string waitingPeriod {
            get {
                return ResourceManager.GetString("waitingPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When will [EMPLOYER] make this change?.
        /// </summary>
        public static string whenWillChangeTakeEffect {
            get {
                return ResourceManager.GetString("whenWillChangeTakeEffect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Where will [NAME] live in Alabama?.
        /// </summary>
        public static string whereLiveInState {
            get {
                return ResourceManager.GetString("whereLiveInState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tell us which employer(s) offer(s) health coverage to [NAME].
        /// </summary>
        public static string whichEmployerOffersCoverage {
            get {
                return ResourceManager.GetString("whichEmployerOffersCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Which of these people is the employee at this Employer?.
        /// </summary>
        public static string whichIsEmployee {
            get {
                return ResourceManager.GetString("whichIsEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will [NAME] be eligible for health coverage from a job during [COVERAGEYEAR](even if it&apos;s from another person&apos;s job, like a spouse[UNDER26])?.
        /// </summary>
        public static string willBeEligibleThroughJob {
            get {
                return ResourceManager.GetString("willBeEligibleThroughJob", resourceCulture);
            }
        }
    }
}
