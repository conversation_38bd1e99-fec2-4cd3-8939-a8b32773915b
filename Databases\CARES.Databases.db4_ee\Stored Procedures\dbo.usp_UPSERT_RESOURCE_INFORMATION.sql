﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_UPSERT_RESOURCE_INFORMATION]
(
	@ApplicantResourceInfoJson	NVARCHAR(MAX)	--<PERSON>son String
)
AS
-- =============================================
-- Author:	 <PERSON><PERSON>
-- Create date: 02/23/2021
-- Description: Updates Elderly and Disabled application resource tables.
-- Modified By: <PERSON><PERSON> on 02/26/2021 - Listed all the column names when selecting from #temp tables as per coding standards.
--				<PERSON><PERSON>utta on 02/26/2021 - Added filter on the DELETE clause in MERGE statement when target is not matched by source.
--				<PERSON><PERSON> on 04/12/2021 - Fix remarks field text won't show the updated text.
--										   - Fix for rounding of numbers should not happen on amount field.
--				<PERSON><PERSON> on 04/15/2021 - Added logic to check for whether record exists in the tables, if not return error.
--										   - Fix for rounding of numbers on Current Balance field.
--				<PERSON> on 04/27/2021 - Modified the length of the out of state county name field to 25. Also updated the sproc name.
--				<PERSON><PERSON> on 05/03/2021 - Update the scale/precision on the amount fields to (9,2).
--				Brian Wilson on 07/01/2021 - Updated the size of the Receiver Name in the Transfer Resource section to 200 and updated
--										the size of Other Account Type in the Bank section to 50.
--				Vinith Golla on 07/22/2021 - Added - APPLICANT_EXCLUDED_AMOUNT, SPOUSE_EXCLUDED_AMOUNT, PROTECTED_RESOURCE_IND to APPLICATION_RESOURCE_DETAIL table.
--				Vinith Golla on 07/26/2021 - Added - Correct the PROTECTED_RESOURCE_IND data type.
--				Vinith Golla on 08/06/2021 - Match the sproc alias names with DTOs alias names.
--				Vinith Golla on 05/17/2022 - Added - HAS_PENALTY, PENALTY_REASON to APPLICATION_RESOURCE_TRANSFER table.
--				Divya Arockiam on 10/25/2022 - Removed logic to upsert address details into dbo.APPLICATION_RESOURCE_BANK table.
--				Divya Arockiam on 10/27/2022 - Save BANK_ID to dbo.APPLICATION_RESOURCE_BANK table.
--				Divya Arockiam on 10/28/2022 - Removed BankName from ResourceBankDetails.
--				Emmanuel Lara on 05/12/2025 - Added data for Bank month details into APPLICATION_RESOURCE_BANK_DETAILS.
--				Emmanuel Lara on 05/13/2025 - Logic adjustments to delete deleted records on APPLICATION_RESOURCE_BANK_DETAILS.
--				Divya Arockiam on 06/13/2025 - Upserting dbo.APPLICATION_RESOURCE_MONTH_DETAIL required for budget calculations.
--				Jeff Bush on 06/20/2025 - Updates on dbo.APPLICATION_RESOURCE_TRANSFER and APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL required for budget calculations.
--				Jeff Bush on 06/26/2025 - Fixes to APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL logic
-- =============================================
BEGIN
	SET NOCOUNT ON;

	DECLARE	@CurrentDate	DATETIME2 = GETDATE(),
			@UpsertedBy		VARCHAR(50),
			@AppResourceId	BIGINT;

	DROP TABLE IF EXISTS dbo.#AppResource;
	DROP TABLE IF EXISTS dbo.#AppResourceDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceDetailWithMonthWiseDetails;
	DROP TABLE IF EXISTS dbo.#InsertNewMonthWiseResourceDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceBankDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceBankIDs;
	DROP TABLE IF EXISTS dbo.#AppResourceBankSubDetails;
	DROP TABLE IF EXISTS dbo.#BankDetailsNewToInsert;
	DROP TABLE IF EXISTS dbo.#AppResourceTransferDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceTransferMonthDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceTranferIDs;
	DROP TABLE IF EXISTS dbo.#AppResourceTransferSubDetails;
	DROP TABLE IF EXISTS dbo.#TransferNewToInsert;

/* Start Parsing and Transforming JSON data */
	SELECT	@UpsertedBy = UpsertedBy
    FROM	OPENJSON(@ApplicantResourceInfoJson)
	WITH	(UpsertedBy VARCHAR(50) '$.UpdatedBy');

	--	Parse the Json for the APPLICATION_RESOURCE table
	SELECT	AppResource.ApplicationId,
			AppResource.ApplicationResourceId,
			AppResource.HasNameOnAccount,
			AppResource.HasNameOnSafeDeposit,
			AppResource.HasTransferredResources
	INTO	dbo.#AppResource
	FROM	OPENJSON(@ApplicantResourceInfoJson)
	WITH	(
				ApplicationId		    BIGINT	'$.ApplicationId',
				ApplicationResourceId   BIGINT	'$.ApplicationResourceId',
				HasNameOnAccount	    BIT		'$.HasNameOnAccount',
				HasNameOnSafeDeposit    BIT		'$.HasNameOnSafeDeposit',
				HasTransferredResources BIT		'$.HasTransferredResources'
			) AS AppResource;

	--	Parse the Json for the APPLICATION_RESOURCE_DETAIL table
	SELECT	AppResourceDetails.ApplicationResourceDetailId,
			AppResourceDetails.ResourceTypeId,
			AppResourceDetails.ApplicantAmount,
			AppResourceDetails.ApplicantExcludedAmount,
			AppResourceDetails.SpouseAmount,
			AppResourceDetails.SpouseExcludedAmount,
			AppResourceDetails.ProtectedResourceInd,
			AppResourceDetails.Remarks,
			AppResourceDetails.DateDisposed
	INTO	dbo.#AppResourceDetails
	FROM	OPENJSON(@ApplicantResourceInfoJson, '$.ResourceDetails')
	WITH	(
				ApplicationResourceDetailId	BIGINT		    '$.ApplicationResourceDetailId',
				ResourceTypeId				TINYINT			'$.ResourceTypeId',
				ApplicantAmount				DECIMAL(9, 2)   '$.ApplicantAmount',
				ApplicantExcludedAmount		DECIMAL(9, 2)	'$.ApplicantExcludedAmount',
				SpouseAmount				DECIMAL(9, 2)   '$.SpouseAmount',
				SpouseExcludedAmount		DECIMAL(9, 2)	'$.SpouseExcludedAmount',
				ProtectedResourceInd		CHAR(1)			'$.ProtectedResourceInd',
				Remarks						VARCHAR(200)    '$.Remarks',
				DateDisposed				DATE			'$.DateDisposed'
			) AS AppResourceDetails;

	--  Get the JSON for the APPLICATION_RESOURCE_DETAIL and APPLICATION_RESOURCE_MONTH_DETAIL combined
	SELECT	AppResourceDetails.ApplicationResourceDetailId,
			AppResourceDetails.ResourceTypeId,
			AppResourceDetails.ApplicantAmount,
			AppResourceDetails.ApplicantExcludedAmount,
			AppResourceDetails.SpouseAmount,
			AppResourceDetails.SpouseExcludedAmount,
			AppResourceDetails.ProtectedResourceInd,
			AppResourceDetails.Remarks,
			AppResourceDetails.DateDisposed,
			AppResourceMonthDetails.ApplicationResourceMonthDetailId,
			AppResourceMonthDetails.ResourceMonthDetail,
			AppResourceMonthDetails.ResourceMonthDetailApplicantAmount,
			AppResourceMonthDetails.ResourceMonthDetailDateDisposed
	INTO	dbo.#AppResourceDetailWithMonthWiseDetails
	FROM	OPENJSON(@ApplicantResourceInfoJson, '$.ResourceDetails')
	WITH	(
				ApplicationResourceDetailId	BIGINT		    '$.ApplicationResourceDetailId',
				ResourceTypeId				TINYINT			'$.ResourceTypeId',
				ApplicantAmount				DECIMAL(9, 2)   '$.ApplicantAmount',
				ApplicantExcludedAmount		DECIMAL(9, 2)	'$.ApplicantExcludedAmount',
				SpouseAmount				DECIMAL(9, 2)   '$.SpouseAmount',
				SpouseExcludedAmount		DECIMAL(9, 2)	'$.SpouseExcludedAmount',
				ProtectedResourceInd		CHAR(1)			'$.ProtectedResourceInd',
				Remarks						VARCHAR(200)    '$.Remarks',
				DateDisposed				DATE			'$.DateDisposed',
				EDResourceMonthDetail		NVARCHAR(MAX) AS JSON
			) AS AppResourceDetails
	CROSS APPLY OPENJSON(AppResourceDetails.EDResourceMonthDetail)
	WITH (
			ApplicationResourceMonthDetailId		BIGINT			'$.ApplicationResourceMonthDetailId',
			ResourceMonthDetail						DATE			'$.ResourceMonthDetail',
			ResourceMonthDetailApplicantAmount		DECIMAL(9, 2)	'$.ResourceMonthDetailApplicantAmount',
			ResourceMonthDetailDateDisposed			DATE			'$.ResourceMonthDetailDateDisposed'
	) AS AppResourceMonthDetails;

	--	Parse the Json for the APPLICATION_RESOURCE_BANK ONLY table
	SELECT	AppResourceBankDetails.ApplicationResourceBankId,
			AppResourceBankDetails.AccountNumber,
			AppResourceBankDetails.BankAccountTypeId,
			AppResourceBankDetails.BankId,
			AppResourceBankDetails.OtherAccountType,
			AppResourceBankDetails.AccountName,
			AppResourceBankDetails.DateClosed,
			AppResourceBankDetails.CurrentBalance
	INTO	dbo.#AppResourceBankDetails
	FROM	OPENJSON(@ApplicantResourceInfoJson, '$.ResourceBankDetails')
	WITH	(
				ApplicationResourceBankId	BIGINT		    '$.ApplicationResourceBankId',
				AccountNumber				VARCHAR(20)	    '$.AccountNumber',
				BankAccountTypeId			TINYINT			'$.BankAccountTypeId',
				BankId						INT				'$.BankId',
				OtherAccountType			VARCHAR(50)	    '$.OtherAccountType',
				AccountName					VARCHAR(200)    '$.AccountName',
				DateClosed					DATE			'$.DateClosed',
				CurrentBalance				DECIMAL(9, 2)	'$.CurrentBalance'
			) AS AppResourceBankDetails;

	--	Parse the Json for the APPLICATION_RESOURCE_BANK AND DETAIL table
	SELECT	AppResourceBankDetails.ApplicationResourceBankId,
			AppResourceBankDetails.AccountNumber,
			AppResourceBankDetails.BankAccountTypeId,
			AppResourceBankDetails.BankId,
			AppResourceBankDetails.OtherAccountType,
			AppResourceBankDetails.AccountName,
			AppResourceBankDetails.DateClosed,
			AppResourceBankDetails.CurrentBalance,
			CAST(AppResourceBankSubDetails.ApplicationResourceBankDetailId AS bigint) AS ApplicationResourceBankDetailId,
			CAST(AppResourceBankSubDetails.ResourceMonth AS DATE) AS ResourceMonth,
			CAST(AppResourceBankSubDetails.MonthCurrentBalance AS decimal) AS MonthCurrentBalance,
			CAST(AppResourceBankSubDetails.MonthCountableAmount AS decimal) AS MonthCountableAmount
	INTO	dbo.#AppResourceBankSubDetails
	FROM	OPENJSON(@ApplicantResourceInfoJson, '$.ResourceBankDetails')
	WITH	(
				ApplicationResourceBankId	BIGINT		    '$.ApplicationResourceBankId',
				AccountNumber				VARCHAR(20)	    '$.AccountNumber',
				BankAccountTypeId			TINYINT			'$.BankAccountTypeId',
				BankId						INT				'$.BankId',
				OtherAccountType			VARCHAR(50)	    '$.OtherAccountType',
				AccountName					VARCHAR(200)    '$.AccountName',
				DateClosed					DATE			'$.DateClosed',
				CurrentBalance				DECIMAL(9, 2)	'$.CurrentBalance',
				BankDetails					NVARCHAR(max) as JSON
			) AS AppResourceBankDetails
	CROSS APPLY OPENJSON(AppResourceBankDetails.BankDetails)
			WITH	(
						ApplicationResourceBankDetailId	BIGINT			'$.ApplicationResourceBankDetailId',
						ResourceMonth					DATE			'$.ResourceMonth',
						MonthCurrentBalance				DECIMAL(9, 2)	'$.CurrentBalance',
						MonthCountableAmount				DECIMAL(9, 2)	'$.CountableAmount'
					) AS AppResourceBankSubDetails;

	--	Parse the Json for ONLY the APPLICATION_RESOURCE_TRANSFER table (Month details will be below)
	SELECT	AppResourceTransferDetails.ApplicationResourceTransferId,
			AppResourceTransferDetails.ReceiverName,
			AppResourceTransferDetails.ItemDescription,
			AppResourceTransferDetails.SoldGivenDate,
			AppResourceTransferDetails.SoldGivenAmount,
			AppResourceTransferDetails.HasPenalty,
			AppResourceTransferDetails.PenaltyReason,
			AppResourceTransferDetails.DateDisposed,
			AppResourceTransferDetails.TransferPenaltyAmount
	INTO	dbo.#AppResourceTransferDetails
	FROM	OPENJSON(@ApplicantResourceInfoJson, '$.ResourceTransferDetails')
	WITH	(
				ApplicationResourceTransferId	BIGINT			'$.ApplicationResourceTransferId',
				ReceiverName					VARCHAR(200)	'$.ReceiverName',
				ItemDescription					VARCHAR(100)	'$.ItemDescription',
				SoldGivenDate					DATE			'$.SoldGivenDate',
				SoldGivenAmount					DECIMAL(9, 2)	'$.SoldGivenAmount',
				HasPenalty						BIT				'$.HasPenalty',
				PenaltyReason					VARCHAR(300)	'$.PenaltyReason',
				DateDisposed					DATE			'$.DateDisposed',
				TransferPenaltyAmount			DECIMAL(9, 2)   '$.TransferPenaltyAmount'
			) AS AppResourceTransferDetails;

	--	Parse the Json for the APPLICATION_RESOURCE_TRANSFER AND APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL table
	SELECT	AppResourceTransferDetails.ApplicationResourceTransferId,
			AppResourceTransferDetails.ReceiverName,
			AppResourceTransferDetails.ItemDescription,
			AppResourceTransferDetails.SoldGivenDate,
			AppResourceTransferDetails.SoldGivenAmount,
			AppResourceTransferDetails.HasPenalty,
			AppResourceTransferDetails.PenaltyReason,
			AppResourceTransferDetails.DateDisposed,
			AppResourceTransferDetails.TransferPenaltyAmount,
			CAST(AppResourceTransferMonthDetails.ElderlyDisabledResourceTransferMonthDetailId AS bigint) AS ElderlyDisabledResourceTransferMonthDetailId,
			CAST(AppResourceTransferMonthDetails.ResourceMonth AS DATE) AS MonthDetail_ResourceMonth,
			CAST(AppResourceTransferMonthDetails.AmountGiven AS DECIMAL) AS MonthDetail_AmountGiven,
			CAST(AppResourceTransferMonthDetails.CountableAmount AS DECIMAL) AS MonthDetail_CountableAmount,
			CAST(AppResourceTransferMonthDetails.AmountReturned AS DECIMAL) AS MonthDetail_AmountReturned,
			CAST(AppResourceTransferMonthDetails.HasPenalty AS BIT) AS MonthDetail_HasPenalty,
			CAST(AppResourceTransferMonthDetails.DateDisposed AS DATE) AS MonthDetail_DateDisposed
	INTO	dbo.#AppResourceTransferMonthDetails
	FROM	OPENJSON(@ApplicantResourceInfoJson, '$.ResourceTransferDetails')
	WITH	(
				ApplicationResourceTransferId	BIGINT			'$.ApplicationResourceTransferId',
				ReceiverName					VARCHAR(200)	'$.ReceiverName',
				ItemDescription					VARCHAR(100)	'$.ItemDescription',
				SoldGivenDate					DATE			'$.SoldGivenDate',
				SoldGivenAmount					DECIMAL(9, 2)	'$.SoldGivenAmount',
				HasPenalty						BIT				'$.HasPenalty',
				PenaltyReason					VARCHAR(300)	'$.PenaltyReason',
				DateDisposed					DATE			'$.DateDisposed',
				TransferPenaltyAmount			DECIMAL(9, 2)   '$.TransferPenaltyAmount',
				EDTransferMonthDetail			NVARCHAR(max) as JSON
			) AS AppResourceTransferDetails
	CROSS APPLY OPENJSON(AppResourceTransferDetails.EDTransferMonthDetail)
			WITH	(
						ElderlyDisabledResourceTransferMonthDetailId	BIGINT			'$.ElderlyDisabledResourceTransferMonthDetailId',
						ResourceMonth									DATE			'$.ResourceMonth',
						AmountGiven										DECIMAL(9, 2)	'$.AmountGiven',
						CountableAmount									DECIMAL(9, 2)	'$.CountableAmount',
						AmountReturned									DECIMAL(9, 2)	'$.AmountReturned',
						HasPenalty										BIT				'$.HasPenalty',
						DateDisposed									DATE			'$.DateDisposed'
					) AS AppResourceTransferMonthDetails;

/* End Parsing and Transforming JSON data */

	-- Check for whether passed in ApplicationResourceId exists in APPLICATION_RESOURCE table, if doesn't return error.
	IF EXISTS
			(
				SELECT	sourceRecord.ApplicationResourceId
				FROM	dbo.#AppResource AS sourceRecord WITH (NOLOCK)
						LEFT JOIN dbo.APPLICATION_RESOURCE AS targetRecord WITH (NOLOCK) ON sourceRecord.ApplicationResourceId = targetRecord.APPLICATION_RESOURCE_ID
				WHERE	targetRecord.APPLICATION_RESOURCE_ID IS NULL
						AND ISNULL(sourceRecord.ApplicationResourceId, 0) > 0
			)
		BEGIN
		    ;THROW	50300, 'APPLICATION_RESOURCE Input Error - The ApplicationResourceId specified is not valid.', 1;
		END;

	-- Check for whether passed in ApplicationResourceBankId exists in APPLICATION_RESOURCE_BANK table, if doesn't return error.
	IF EXISTS
			(
				SELECT	DISTINCT resourceBankDetailsRecord.ApplicationResourceBankId
				FROM	dbo.#AppResourceBankDetails AS resourceBankDetailsRecord WITH (NOLOCK)
						LEFT JOIN dbo.APPLICATION_RESOURCE_BANK AS targetRecord WITH (NOLOCK) ON resourceBankDetailsRecord.ApplicationResourceBankId = targetRecord.APPLICATION_RESOURCE_BANK_ID
				WHERE	targetRecord.APPLICATION_RESOURCE_BANK_ID IS NULL
						AND ISNULL(resourceBankDetailsRecord.ApplicationResourceBankId, 0) > 0
			)
		BEGIN
		    ;THROW	50400, 'APPLICATION_RESOURCE_BANK Input Error - The ApplicationResourceBankId specified is not valid.', 1;
		END;

	-- Check for whether passed in ApplicationResourceDetailId exists in APPLICATION_RESOURCE_DETAIL table, if doesn't return error.
	IF EXISTS
			(
				SELECT	DISTINCT resourceDetailsRecord.ApplicationResourceDetailId
				FROM	dbo.#AppResourceDetails AS resourceDetailsRecord WITH (NOLOCK)
						LEFT JOIN dbo.APPLICATION_RESOURCE_DETAIL AS targetRecord WITH (NOLOCK) ON resourceDetailsRecord.ApplicationResourceDetailId = targetRecord.APPLICATION_RESOURCE_DETAIL_ID
				WHERE	targetRecord.APPLICATION_RESOURCE_DETAIL_ID IS NULL
						AND ISNULL(resourceDetailsRecord.ApplicationResourceDetailId, 0) > 0
			)
		BEGIN
		    ;THROW	50500, 'APPLICATION_RESOURCE_DETAIL Input Error - The ApplicationResourceDetailId specified is not valid.', 1;
		END;

	-- Check for whether passed in ApplicationResourceTransferId exists in APPLICATION_RESOURCE_TRANSFER table, if doesn't return error.
	IF EXISTS
			(
				SELECT	DISTINCT resourceTransferRecord.ApplicationResourceTransferId
				FROM	dbo.#AppResourceTransferDetails AS resourceTransferRecord WITH (NOLOCK)
						LEFT JOIN dbo.APPLICATION_RESOURCE_TRANSFER AS targetRecord WITH (NOLOCK) ON resourceTransferRecord.ApplicationResourceTransferId = targetRecord.APPLICATION_RESOURCE_TRANSFER_ID
				WHERE	targetRecord.APPLICATION_RESOURCE_TRANSFER_ID IS NULL
						AND ISNULL(resourceTransferRecord.ApplicationResourceTransferId, 0) > 0
			)
		BEGIN
		    ;THROW	50600, 'APPLICATION_RESOURCE_TRANSFER Input Error - The ApplicationResourceTransferId specified is not valid.', 1;
		END;

/* Start doing necessary updates for the E&D Resource tables */
	CREATE TABLE dbo.#ApplicationResourceId (ApplicationResourceId BIGINT);	--	Create #temp table to store the Application_Resource_Id
	CREATE TABLE dbo.#ApplicationResourceBankId (ApplicationResourceBankId BIGINT);	--	Create #temp table to store the Application_Resource_Bank_Id
	CREATE TABLE dbo.#ApplicationResourceTransferId (ApplicationResourceTransferId BIGINT);	--	Create #temp table to store the APPLICATION_RESOURCE_TRANSFER_ID

	--	Merge records for APPLICATION_RESOURCE table
	--	Synchronize the trgtResource table with refreshed data from srcResource table
	MERGE INTO dbo.APPLICATION_RESOURCE AS trgtResource
	USING	(
				SELECT	ApplicationId,
						ApplicationResourceId,
						HasNameOnAccount,
						HasNameOnSafeDeposit,
						HasTransferredResources
				FROM	dbo.#AppResource
			) AS srcResource
			(
				ApplicationId,
				ApplicationResourceId,
				HasNameOnAccount,
				HasNameOnSafeDeposit,
				HasTransferredResources
			)
	ON trgtResource.APPLICATION_RESOURCE_ID = srcResource.ApplicationResourceId
	--	When records are matched, update the records if there is any change
	WHEN MATCHED THEN
	UPDATE
	SET	trgtResource.HAS_NAME_ON_ACCOUNT			=	srcResource.HasNameOnAccount,
		trgtResource.HAS_NAME_ON_SAFE_DEPOSIT_BOX	=	srcResource.HasNameOnSafeDeposit,
		trgtResource.HAS_TRANSFERRED_RESOURCES		=	srcResource.HasTransferredResources,
		trgtResource.UPDATED_BY						=	@UpsertedBy,
		trgtResource.UPDATED_DATE					=	@CurrentDate
	--	When no records are matched, insert the incoming records from srcResource table to trgtResource table
	WHEN NOT MATCHED THEN
	INSERT	(
				APPLICATION_ID,
				HAS_NAME_ON_ACCOUNT,
				HAS_NAME_ON_SAFE_DEPOSIT_BOX,
				HAS_TRANSFERRED_RESOURCES,
				CREATED_BY,
				UPDATED_BY
			)
	VALUES	(
				srcResource.ApplicationId,
				srcResource.HasNameOnAccount,
				srcResource.HasNameOnSafeDeposit,
				srcResource.HasTransferredResources,
				@UpsertedBy,
				@UpsertedBy
			)
	--	OUTPUT clause that returns the specific action (Insert/Update) performed on that row
	OUTPUT INSERTED.APPLICATION_RESOURCE_ID INTO dbo.#ApplicationResourceId;	--Stores the APPLICATION_RESOURCE_ID in to dbo.#ApplicationResourceId table.

	--	Selects the APPLICATION_RESOURCE_ID value after inserted for new resource record and map to a variable(@AppResourceId)
    SELECT	@AppResourceId = ISNULL(AR.ApplicationResourceId, ARID.ApplicationResourceId)
    FROM	dbo.#AppResource AS AR, dbo.#ApplicationResourceId AS ARID;

    DROP TABLE dbo.#ApplicationResourceId;	--Drop the table, since there is no dependency on this table.


	IF (@AppResourceId IS NOT NULL)
	BEGIN
		--	Merge records for APPLICATION_RESOURCE_DETAIL table
		--	Synchronize the trgtResourceDetails table with refreshed data from srcResourceDetails table
		MERGE INTO dbo.APPLICATION_RESOURCE_DETAIL AS trgtResourceDetails
		USING	(
					SELECT	DISTINCT ApplicationResourceDetailId,
							ResourceTypeId,
							ApplicantAmount,
							ApplicantExcludedAmount,
							SpouseAmount,
							SpouseExcludedAmount,
							ProtectedResourceInd,
							Remarks,
							DateDisposed
					FROM	dbo.#AppResourceDetails
				) AS srcResourceDetails
				(
					ApplicationResourceDetailId,
					ResourceTypeId,
					ApplicantAmount,
					ApplicantExcludedAmount,
					SpouseAmount,
					SpouseExcludedAmount,
					ProtectedResourceInd,
					Remarks,
					DateDisposed
				)
		ON trgtResourceDetails.APPLICATION_RESOURCE_DETAIL_ID = srcResourceDetails.ApplicationResourceDetailId
		--	When records are matched, update the records if there is any change
		WHEN MATCHED THEN
		UPDATE
		SET	trgtResourceDetails.RESOURCE_TYPE_ID		    =	srcResourceDetails.ResourceTypeId,
			trgtResourceDetails.APPLICANT_RESOURCE_AMOUNT	=	srcResourceDetails.ApplicantAmount,
			trgtResourceDetails.APPLICANT_EXCLUDED_AMOUNT   =	srcResourceDetails.ApplicantExcludedAmount,
			trgtResourceDetails.SPOUSE_RESOURCE_AMOUNT	    =	srcResourceDetails.SpouseAmount,
			trgtResourceDetails.SPOUSE_EXCLUDED_AMOUNT		=	srcResourceDetails.SpouseExcludedAmount,
			trgtResourceDetails.PROTECTED_RESOURCE_IND		=	srcResourceDetails.ProtectedResourceInd,
			trgtResourceDetails.RESOURCE_REMARKS		    =	srcResourceDetails.Remarks,
			trgtResourceDetails.DATE_RESOURCE_DISPOSED	    =	srcResourceDetails.DateDisposed,
			trgtResourceDetails.UPDATED_BY					=	@UpsertedBy,
			trgtResourceDetails.UPDATED_DATE			    =	@CurrentDate
		--	When no records are matched, insert the incoming records from srcResourceDetails table to trgtResourceDetails table
		WHEN NOT MATCHED THEN
		INSERT	(
					APPLICATION_RESOURCE_ID,
					RESOURCE_TYPE_ID,
					APPLICANT_RESOURCE_AMOUNT,
					APPLICANT_EXCLUDED_AMOUNT,
					SPOUSE_RESOURCE_AMOUNT,
					SPOUSE_EXCLUDED_AMOUNT,
					PROTECTED_RESOURCE_IND,
					RESOURCE_REMARKS,
					DATE_RESOURCE_DISPOSED,
					CREATED_BY,
					UPDATED_BY
				)
		VALUES	(
					@AppResourceId,
					srcResourceDetails.ResourceTypeId,
					srcResourceDetails.ApplicantAmount,
					srcResourceDetails.ApplicantExcludedAmount,
					srcResourceDetails.SpouseAmount,
					srcResourceDetails.SpouseExcludedAmount,
					srcResourceDetails.ProtectedResourceInd,
					srcResourceDetails.Remarks,
					srcResourceDetails.DateDisposed,
					@UpsertedBy,
					@UpsertedBy
				)
		--	When there is a row that exists in trgtResourceDetails and same record does not exist in srcResourceDetails then delete this record target
		WHEN NOT MATCHED BY SOURCE AND trgtResourceDetails.APPLICATION_RESOURCE_ID = @AppResourceId
		THEN DELETE;

		--  /* Merge the APPLICATION_RESOURCE_MONTH_DETAIL records */
		MERGE INTO dbo.APPLICATION_RESOURCE_MONTH_DETAIL AS trgtResourceDetails
		USING	(
					SELECT	DISTINCT ApplicationResourceMonthDetailId,
							ApplicationResourceDetailId,
							ResourceMonthDetail,
							ResourceMonthDetailApplicantAmount,
							ResourceMonthDetailDateDisposed
					FROM	dbo.#AppResourceDetailWithMonthWiseDetails
					WHERE	ApplicationResourceMonthDetailId > 0
				) AS srcResourceDetails
				(
					ApplicationResourceMonthDetailId,
					ApplicationResourceDetailId,
					ResourceMonthDetail,
					ResourceMonthDetailApplicantAmount,
					ResourceMonthDetailDateDisposed
				)
		ON trgtResourceDetails.APPLICATION_RESOURCE_MONTH_DETAIL_ID = srcResourceDetails.ApplicationResourceMonthDetailId
		--	When records are matched, update the records if there is any change
		WHEN MATCHED THEN
		UPDATE
		SET	trgtResourceDetails.RESOURCE_MONTH				=	srcResourceDetails.ResourceMonthDetail,
			trgtResourceDetails.APPLICANT_RESOURCE_AMOUNT	=	srcResourceDetails.ResourceMonthDetailApplicantAmount,
			trgtResourceDetails.DATE_RESOURCE_DISPOSED		=	srcResourceDetails.ResourceMonthDetailDateDisposed,
			trgtResourceDetails.UPDATED_BY					=	@UpsertedBy,
			trgtResourceDetails.UPDATED_DATE			    =	@CurrentDate
		--	When no records are matched, insert the incoming records from srcResourceDetails table to trgtResourceDetails table
		WHEN NOT MATCHED THEN
		INSERT	(
					APPLICATION_RESOURCE_DETAIL_ID,
					RESOURCE_MONTH,
					APPLICANT_RESOURCE_AMOUNT,
					DATE_RESOURCE_DISPOSED,
					CREATED_BY,
					UPDATED_BY
				)
		VALUES	(
					srcResourceDetails.ApplicationResourceDetailId,
					srcResourceDetails.ResourceMonthDetail,
					srcResourceDetails.ResourceMonthDetailApplicantAmount,
					srcResourceDetails.ResourceMonthDetailDateDisposed,
					@UpsertedBy,
					@UpsertedBy
				)
		--	When there is a row that exists in trgtResourceDetails and same record does not exist in srcResourceDetails then delete this record target
		WHEN NOT MATCHED BY SOURCE
		THEN DELETE;

		--	Merge records for APPLICATION_RESOURCE_BANK table
		--	Synchronize the trgtResourceBankDetails table with refreshed data from srcResourceBankDetails table
		MERGE INTO dbo.APPLICATION_RESOURCE_BANK AS trgtResourceBankDetails
		USING	(
					SELECT	DISTINCT ApplicationResourceBankId,
							AccountNumber,
							BankAccountTypeId,
							BankId,
							OtherAccountType,
							AccountName,
							DateClosed,
							CurrentBalance
					FROM	dbo.#AppResourceBankDetails
				) AS srcResourceBankDetails
				(
					ApplicationResourceBankId,
					AccountNumber,
					BankAccountTypeId,
					BankId,
					OtherAccountType,
					AccountName,
					DateClosed,
					CurrentBalance
				)
		ON trgtResourceBankDetails.APPLICATION_RESOURCE_BANK_ID = srcResourceBankDetails.ApplicationResourceBankId
		--	When records are matched, update the records if there is any change
		WHEN MATCHED THEN
		UPDATE
		SET	trgtResourceBankDetails.ACCOUNT_NUMBER				=	srcResourceBankDetails.AccountNumber,
			trgtResourceBankDetails.BANK_ACCOUNT_TYPE_ID		=	srcResourceBankDetails.BankAccountTypeId,
			trgtResourceBankDetails.BANK_ID						=	srcResourceBankDetails.BankId,
			trgtResourceBankDetails.OTHER_ACCOUNT_TYPE			=	srcResourceBankDetails.OtherAccountType,
			trgtResourceBankDetails.NAME_ON_ACCOUNT				=	srcResourceBankDetails.AccountName,
			trgtResourceBankDetails.ACCOUNT_CLOSE_DATE			=	srcResourceBankDetails.DateClosed,
			trgtResourceBankDetails.OPEN_ACCOUNT_BALANCE		=	srcResourceBankDetails.CurrentBalance,
			trgtResourceBankDetails.UPDATED_BY					=	@UpsertedBy,
			trgtResourceBankDetails.UPDATED_DATE				=	@CurrentDate
		--	When no records are matched, insert the incoming records from srcResourceBankDetails table to trgtResourceBankDetails table
		WHEN NOT MATCHED THEN
		INSERT	(
					APPLICATION_RESOURCE_ID,
					ACCOUNT_NUMBER,
					BANK_ACCOUNT_TYPE_ID,
					BANK_ID,
					OTHER_ACCOUNT_TYPE,
					NAME_ON_ACCOUNT,
					ACCOUNT_CLOSE_DATE,
					OPEN_ACCOUNT_BALANCE,
					CREATED_BY,
					UPDATED_BY
				)
		VALUES	(
					@AppResourceId,
					srcResourceBankDetails.AccountNumber,
					srcResourceBankDetails.BankAccountTypeId,
					srcResourceBankDetails.BankId,
					srcResourceBankDetails.OtherAccountType,
					srcResourceBankDetails.AccountName,
					srcResourceBankDetails.DateClosed,
					srcResourceBankDetails.CurrentBalance,
					@UpsertedBy,
					@UpsertedBy
				)
		--	When there is a row that exists in trgtResourceBankDetails and same record does not exist in srcResourceBankDetails then delete this record target
		WHEN NOT MATCHED BY SOURCE AND trgtResourceBankDetails.APPLICATION_RESOURCE_ID = @AppResourceId
		THEN DELETE

		--	OUTPUT clause that returns the specific action (Insert/Update) performed on that row
		OUTPUT INSERTED.APPLICATION_RESOURCE_BANK_ID INTO dbo.#ApplicationResourceBankId;	--Stores the APPLICATION_RESOURCE_BANK_ID in to dbo.#ApplicationResourceBankId table.

		--	Selects the APPLICATION_RESOURCE_ID value after inserted for new resource record and map to a variable(@AppResourceId)
		SELECT ISNULL(AR.ApplicationResourceBankId, ARID.ApplicationResourceBankId) AS ApplicationResourceBankId
		INTO #AppResourceBankIDs
		FROM	dbo.#AppResourceBankDetails AS AR, dbo.#ApplicationResourceBankId AS ARID;

		DROP TABLE dbo.#ApplicationResourceBankId;	--Drop the table, since there is no dependency on this table.


		--	Merge records for APPLICATION_RESOURCE_BANK_DETAIL table
		--	Synchronize the trgtResourceBankSubDetails table with refreshed data from srcResourceBankSubDetails table
		MERGE INTO dbo.APPLICATION_RESOURCE_BANK_DETAIL AS trgtResourceBankSubDetails
		USING	(
					SELECT	DISTINCT
							ApplicationResourceBankId,
							ApplicationResourceBankDetailId,
							ResourceMonth,
							MonthCurrentBalance,
							MonthCountableAmount
					FROM	dbo.#AppResourceBankSubDetails
					WHERE ApplicationResourceBankDetailId > 0
				) AS srcResourceBankSubDetails
				(
					ApplicationResourceBankId,
					ApplicationResourceBankDetailId,
					ResourceMonth,
					CurrentBalance,
					CountableAmount
				)
		ON trgtResourceBankSubDetails.APPLICATION_RESOURCE_BANK_DETAIL_ID = srcResourceBankSubDetails.ApplicationResourceBankDetailId
		--	When records are matched, update the records if there is any change
		WHEN MATCHED THEN
		UPDATE
		SET	trgtResourceBankSubDetails.RESOURCE_MONTH			=	srcResourceBankSubDetails.ResourceMonth,
			trgtResourceBankSubDetails.CURRENT_BALANCE			=	srcResourceBankSubDetails.CurrentBalance,
			trgtResourceBankSubDetails.COUNTABLE_AMOUNT			=	srcResourceBankSubDetails.CountableAmount,
			trgtResourceBankSubDetails.UPDATED_BY				=	@UpsertedBy,
			trgtResourceBankSubDetails.UPDATED_DATE				=	@CurrentDate
		--	When no records are matched, insert the incoming records from srcResourceBankSubDetails table to trgtResourceBankSubDetails table
		WHEN NOT MATCHED THEN
		INSERT	(
					APPLICATION_RESOURCE_BANK_ID,
					RESOURCE_MONTH,
					CURRENT_BALANCE,
					COUNTABLE_AMOUNT,
					CREATED_BY,
					UPDATED_BY
				)
		VALUES	(
					srcResourceBankSubDetails.ApplicationResourceBankId,
					srcResourceBankSubDetails.ResourceMonth,
					srcResourceBankSubDetails.CurrentBalance,
					srcResourceBankSubDetails.CountableAmount,
					@UpsertedBy,
					@UpsertedBy
				)

		--	When there is a row that exists in trgtResourceBankSubDetails and same record does not exist in srcResourceBankSubDetails then delete this record target
		WHEN NOT MATCHED BY SOURCE AND trgtResourceBankSubDetails.APPLICATION_RESOURCE_BANK_ID IN (SELECT ApplicationResourceBankId FROM #AppResourceBankIDs)
		THEN DELETE;

		--	Merge records for APPLICATION_RESOURCE_TRANSFER table
		--	Synchronize the trgtResourceTransferDetails table with refreshed data from srcResourceTransferDetails table
		MERGE INTO dbo.APPLICATION_RESOURCE_TRANSFER AS trgtResourceTransferDetails
		USING	(
					SELECT	DISTINCT ApplicationResourceTransferId,
							ReceiverName,
							ItemDescription,
							SoldGivenDate,
							SoldGivenAmount,
							HasPenalty,
							PenaltyReason,
							DateDisposed,
							TransferPenaltyAmount
					FROM	dbo.#AppResourceTransferDetails
				) AS srcResourceTransferDetails
				(
					ApplicationResourceTransferId,
					ReceiverName,
					ItemDescription,
					SoldGivenDate,
					SoldGivenAmount,
					HasPenalty,
					PenaltyReason,
					DateDisposed,
					TransferPenaltyAmount
				)
		ON trgtResourceTransferDetails.APPLICATION_RESOURCE_TRANSFER_ID = srcResourceTransferDetails.ApplicationResourceTransferId
		--	When records are matched, update the records if there is any change
		WHEN MATCHED THEN
		UPDATE
		SET	trgtResourceTransferDetails.RECEIVER_NAME			=   srcResourceTransferDetails.ReceiverName,
			trgtResourceTransferDetails.ITEM_DESCRIPTION		=   srcResourceTransferDetails.ItemDescription,
			trgtResourceTransferDetails.SOLD_GIVEN_DATE			=   srcResourceTransferDetails.SoldGivenDate,
			trgtResourceTransferDetails.SOLD_GIVEN_AMOUNT		=   srcResourceTransferDetails.SoldGivenAmount,
			trgtResourceTransferDetails.HAS_PENALTY				=   srcResourceTransferDetails.HasPenalty,
			trgtResourceTransferDetails.PENALTY_REASON			=   srcResourceTransferDetails.PenaltyReason,
			trgtResourceTransferDetails.DATE_DISPOSED			=   srcResourceTransferDetails.DateDisposed,
			trgtResourceTransferDetails.TRANSFER_PENALTY_AMOUNT	=   srcResourceTransferDetails.TransferPenaltyAmount,
			trgtResourceTransferDetails.UPDATED_BY				=   @UpsertedBy,
			trgtResourceTransferDetails.UPDATED_DATE			=   @CurrentDate
		--	When no records are matched, insert the incoming records from srcResourceTransferDetails table to trgtResourceTransferDetails table
		WHEN NOT MATCHED THEN
		INSERT	(
					APPLICATION_RESOURCE_ID,
					RECEIVER_NAME,
					ITEM_DESCRIPTION,
					SOLD_GIVEN_DATE,
					SOLD_GIVEN_AMOUNT,
					HAS_PENALTY,
					PENALTY_REASON,
					DATE_DISPOSED,
					TRANSFER_PENALTY_AMOUNT,
					CREATED_BY,
					UPDATED_BY
				)
		VALUES	(
					@AppResourceId,
					srcResourceTransferDetails.ReceiverName,
					srcResourceTransferDetails.ItemDescription,
					srcResourceTransferDetails.SoldGivenDate,
					srcResourceTransferDetails.SoldGivenAmount,
					srcResourceTransferDetails.HasPenalty,
					srcResourceTransferDetails.PenaltyReason,
					srcResourceTransferDetails.DateDisposed,
					srcResourceTransferDetails.TransferPenaltyAmount,
					@UpsertedBy,
					@UpsertedBy
				)
		--	When there is a row that exists in trgtResourceTransferDetails and same record does not exist in srcResourceTransferDetails then delete this record target
		WHEN NOT MATCHED BY SOURCE AND trgtResourceTransferDetails.APPLICATION_RESOURCE_ID = @AppResourceId
		THEN DELETE

		--	OUTPUT clause that returns the specific action (Insert/Update) performed on that row
		OUTPUT INSERTED.APPLICATION_RESOURCE_TRANSFER_ID INTO dbo.#ApplicationResourceTransferId;	--Stores the APPLICATION_RESOURCE_TRANSFER_ID in to dbo.##ApplicationResourceTransferId table.

		--	Selects the APPLICATION_RESOURCE_TRANSFER_ID value after inserted for new resource record and map to a variable
		SELECT ISNULL(AR.ApplicationResourceTransferId, ARID.ApplicationResourceTransferId) AS ApplicationResourceTransferId
		INTO dbo.#AppResourceTranferIDs
		FROM	dbo.#AppResourceTransferDetails AS AR, dbo.#ApplicationResourceTransferId AS ARID;

		DROP TABLE dbo.#ApplicationResourceTransferId;	--Drop the table, since there is no dependency on this table.


		--	Merge records for APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL table
		--	Synchronize the trgtResourceTransferMonthlyDetails table with refreshed data from srcResourceBankSubDetails table
		MERGE INTO dbo.APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL AS trgtResourceTransferMonthlyDetails
		USING	(
					SELECT	DISTINCT
							ApplicationResourceTransferId,
							ElderlyDisabledResourceTransferMonthDetailId,
							MonthDetail_ResourceMonth,
							MonthDetail_AmountGiven,
							MonthDetail_CountableAmount,
							MonthDetail_AmountReturned,
							MonthDetail_HasPenalty,
							MonthDetail_DateDisposed
					FROM	dbo.#AppResourceTransferMonthDetails
					WHERE ElderlyDisabledResourceTransferMonthDetailId > 0
				) AS srcResourceTransferMonthlyDetails
				(
					ApplicationResourceTransferId,
					ElderlyDisabledResourceTransferMonthDetailId,
					ResourceMonth,
					AmountGiven,
					CountableAmount,
					AmountReturned,
					HasPenalty,
					DateDisposed
				)
		ON trgtResourceTransferMonthlyDetails.APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL_ID = srcResourceTransferMonthlyDetails.ElderlyDisabledResourceTransferMonthDetailId
		--	When records are matched, update the records if there is any change
		WHEN MATCHED THEN
		UPDATE
		SET	trgtResourceTransferMonthlyDetails.RESOURCE_MONTH		=	srcResourceTransferMonthlyDetails.ResourceMonth,
			trgtResourceTransferMonthlyDetails.AMOUNT_GIVEN			=	srcResourceTransferMonthlyDetails.AmountGiven,
			trgtResourceTransferMonthlyDetails.AMOUNT_RETURNED		=	srcResourceTransferMonthlyDetails.AmountReturned,
			trgtResourceTransferMonthlyDetails.COUNTABLE_AMOUNT     =   srcResourceTransferMonthlyDetails.CountableAmount,
			trgtResourceTransferMonthlyDetails.HAS_PENALTY			=	srcResourceTransferMonthlyDetails.HasPenalty,
			trgtResourceTransferMonthlyDetails.DATE_DISPOSED		=	srcResourceTransferMonthlyDetails.DateDisposed,
			trgtResourceTransferMonthlyDetails.UPDATED_BY			=	@UpsertedBy,
			trgtResourceTransferMonthlyDetails.UPDATED_DATE			=	@CurrentDate
		--	When no records are matched, insert the incoming records from srcResourceBankSubDetails table to trgtResourceTransferMonthlyDetails table
		WHEN NOT MATCHED THEN
		INSERT	(
					APPLICATION_RESOURCE_TRANSFER_ID,
					RESOURCE_MONTH,
					AMOUNT_GIVEN,	
					COUNTABLE_AMOUNT,
					AMOUNT_RETURNED,
					HAS_PENALTY,
					DATE_DISPOSED,
					CREATED_BY,
					UPDATED_BY
				)
		VALUES	(
					srcResourceTransferMonthlyDetails.ApplicationResourceTransferId,
					srcResourceTransferMonthlyDetails.ResourceMonth,
					srcResourceTransferMonthlyDetails.AmountGiven,
					srcResourceTransferMonthlyDetails.CountableAmount,
					srcResourceTransferMonthlyDetails.AmountReturned,
					srcResourceTransferMonthlyDetails.HasPenalty,
					srcResourceTransferMonthlyDetails.DateDisposed,
					@UpsertedBy,
					@UpsertedBy
				)

		--	When there is a row that exists in trgtResourceTransferMonthlyDetails and same record does not exist in srcResourceTransferMonthlyDetails then delete this record target
		WHEN NOT MATCHED BY SOURCE AND trgtResourceTransferMonthlyDetails.APPLICATION_RESOURCE_TRANSFER_ID IN (SELECT ApplicationResourceTransferId FROM dbo.#AppResourceTranferIDs)
		THEN DELETE;

		--Get ID's newly inserted ABOVE by MATCHING all other fields to get the APPLICATION_RESOURCE_BANK_ID
		SELECT BANK.APPLICATION_RESOURCE_BANK_ID, DET.ResourceMonth, DET.MonthCurrentBalance, DET.MonthCountableAmount

		INTO #BankDetailsNewToInsert
		FROM dbo.[APPLICATION_RESOURCE_BANK] AS BANK
		INNER JOIN #AppResourceBankSubDetails AS DET ON ApplicationResourceBankDetailId = 0
			AND BANK.BANK_ID = DET.BankId
			AND	ACCOUNT_NUMBER = DET.AccountNumber
			AND NAME_ON_ACCOUNT = DET.AccountName
			AND BANK_ACCOUNT_TYPE_ID = DET.BankAccountTypeId
			AND APPLICATION_RESOURCE_ID = @AppResourceId
			AND iif(BANK.ACCOUNT_CLOSE_DATE is null, '01-01-1901', ACCOUNT_CLOSE_DATE) = iif(DET.DateClosed is null, '01-01-1901', DET.DateClosed)
			AND OPEN_ACCOUNT_BALANCE = DET.CurrentBalance

		--Insert New Nested Detail Bank info into BankDetail table,
		INSERT INTO APPLICATION_RESOURCE_BANK_DETAIL
		(APPLICATION_RESOURCE_BANK_ID,	RESOURCE_MONTH,	CURRENT_BALANCE, COUNTABLE_AMOUNT)
		SELECT APPLICATION_RESOURCE_BANK_ID, ResourceMonth,	MonthCurrentBalance, MonthCountableAmount

		FROM #BankDetailsNewToInsert

		-- Logic to insert the application resource month detail table
	   SELECT	ARD.APPLICATION_RESOURCE_DETAIL_ID,
				ARMD.ResourceMonthDetail,
				ARMD.ResourceMonthDetailApplicantAmount,
				ARMD.ResourceMonthDetailDateDisposed
	   INTO		dbo.#InsertNewMonthWiseResourceDetails
	   FROM		dbo.APPLICATION_RESOURCE_DETAIL AS ARD
				INNER JOIN dbo.#AppResourceDetailWithMonthWiseDetails AS ARMD ON ARMD.ApplicationResourceMonthDetailId							= 0
														   AND ARD.APPLICATION_RESOURCE_ID														= @AppResourceId
														   AND COALESCE(ARD.RESOURCE_TYPE_ID, '')												= COALESCE(ARMD.ResourceTypeId, '')
														   AND COALESCE(ARD.APPLICANT_RESOURCE_AMOUNT, '')										= COALESCE(ARMD.ApplicantAmount, '')
														   AND COALESCE(ARD.APPLICANT_EXCLUDED_AMOUNT, '')										= COALESCE(ARMD.ApplicantExcludedAmount, '')
														   AND COALESCE(ARD.SPOUSE_RESOURCE_AMOUNT, '')											= COALESCE(ARMD.SpouseAmount, '')
														   AND COALESCE(ARD.SPOUSE_EXCLUDED_AMOUNT, '')											= COALESCE(ARMD.SpouseExcludedAmount, '')
														   AND COALESCE(ARD.PROTECTED_RESOURCE_IND, '')											= COALESCE(ARMD.ProtectedResourceInd, '')
														   AND COALESCE(ARD.RESOURCE_REMARKS, '')												= COALESCE(ARMD.Remarks, '')
														   AND IIF(ARD.DATE_RESOURCE_DISPOSED IS NULL, '01-01-1901', ARD.DATE_RESOURCE_DISPOSED) = IIF(ARMD.DateDisposed IS NULL, '01-01-1901', ARMD.DateDisposed)
	   INSERT	INTO dbo.APPLICATION_RESOURCE_MONTH_DETAIL
				(
					APPLICATION_RESOURCE_DETAIL_ID,
					RESOURCE_MONTH,
					APPLICANT_RESOURCE_AMOUNT,
					DATE_RESOURCE_DISPOSED,
					CREATED_BY,
					UPDATED_BY
				)
	   SELECT	APPLICATION_RESOURCE_DETAIL_ID,
				ResourceMonthDetail,
				ResourceMonthDetailApplicantAmount,
				ResourceMonthDetailDateDisposed,
				@UpsertedBy,
				@UpsertedBy
	   FROM		dbo.#InsertNewMonthWiseResourceDetails;

		-- Get ID's newly inserted ABOVE by MATCHING all other fields to get the APPLICATION_RESOURCE_TRANSFER_ID
		SELECT	ART.APPLICATION_RESOURCE_TRANSFER_ID, 
				TMD.MonthDetail_ResourceMonth,	
				TMD.MonthDetail_AmountGiven,
				TMD.MonthDetail_CountableAmount,
				TMD.MonthDetail_AmountReturned,
				TMD.MonthDetail_HasPenalty,
				TMD.MonthDetail_DateDisposed
		INTO	dbo.#TransferNewToInsert
		FROM	dbo.[APPLICATION_RESOURCE_TRANSFER] AS ART
				INNER JOIN #AppResourceTransferMonthDetails AS TMD ON ElderlyDisabledResourceTransferMonthDetailId = 0
				AND COALESCE(ART.RECEIVER_NAME, '')			=   COALESCE(TMD.ReceiverName, '')
				AND COALESCE(ART.ITEM_DESCRIPTION, '')		=   COALESCE(TMD.ItemDescription, '')
				AND iif(ART.SOLD_GIVEN_DATE is null, '01-01-1901', ART.SOLD_GIVEN_DATE)			=   iif(TMD.SoldGivenDate is null, '01-01-1901', TMD.SoldGivenDate)
				AND COALESCE(ART.SOLD_GIVEN_AMOUNT, 0)		=   COALESCE(TMD.SoldGivenAmount, 0)
				AND COALESCE(ART.HAS_PENALTY, 0)			=   COALESCE(TMD.HasPenalty, 0)
				AND COALESCE(ART.PENALTY_REASON, '')		=   COALESCE(TMD.PenaltyReason, '');

		--Insert New Nested Transfer info 
		INSERT INTO dbo.APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL
				(
					APPLICATION_RESOURCE_TRANSFER_ID,
					RESOURCE_MONTH,
					AMOUNT_GIVEN,
					COUNTABLE_AMOUNT,
					AMOUNT_RETURNED,
					HAS_PENALTY,
					DATE_DISPOSED,
					CREATED_BY,
					UPDATED_BY
				)
		SELECT	APPLICATION_RESOURCE_TRANSFER_ID, 
				MonthDetail_ResourceMonth,	
				MonthDetail_AmountGiven,
				MonthDetail_CountableAmount,
				MonthDetail_AmountReturned,
				MonthDetail_HasPenalty,
				MonthDetail_DateDisposed,
				@UpsertedBy,
				@UpsertedBy
		FROM	dbo.#TransferNewToInsert;

	END

/* End doing necessary updates for the E&D Resource tables */

	-- Clean up
	DROP TABLE IF EXISTS dbo.#AppResource;
	DROP TABLE IF EXISTS dbo.#AppResourceDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceDetailWithMonthWiseDetails;
	DROP TABLE IF EXISTS dbo.#InsertNewMonthWiseResourceDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceBankDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceBankIDs;
	DROP TABLE IF EXISTS dbo.#AppResourceBankSubDetails;
	DROP TABLE IF EXISTS dbo.#BankDetailsNewToInsert;
	DROP TABLE IF EXISTS dbo.#AppResourceTransferDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceTransferMonthDetails;
	DROP TABLE IF EXISTS dbo.#AppResourceTranferIDs;
	DROP TABLE IF EXISTS dbo.#AppResourceTransferSubDetails;
	DROP TABLE IF EXISTS dbo.#TransferNewToInsert;

END
GO