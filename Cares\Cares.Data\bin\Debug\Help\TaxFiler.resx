﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="claimedAsDependent" xml:space="preserve">
    <value>&lt;p&gt;
For example, you might be claimed as a dependent if you live with someone who pays for most of your costs, like housing, food and clothing. Even if you don’t live with the person who pays these costs, they may be able to claim you as a dependent if they’re related to you and your income is less than a certain amount.&lt;/p&gt;</value>
  </data>
  <data name="dependent" xml:space="preserve">
    <value>&lt;p&gt;Most tax filers claim their own children as their dependents if the children are 19 or younger, full-time students younger than 25, or are disabled. Tax filers also might claim other people as dependents when they pay for most of their costs, like housing, food, and clothing.&lt;/p&gt;</value>
  </data>
  <data name="differentDependent" xml:space="preserve">
    <value>&lt;p&gt;If you and/or your family want health coverage for next year, tell us about any expected differences between who you claim as dependents now, and why you’ll claim on the tax return for 2015.&lt;br&gt;
Sometimes dependents change from one year to another if:&lt;br&gt;
• Parents alternate claiming children as dependents.&lt;/p&gt;&lt;br&gt;
• A child turns 19 or 25 next year and won’t be claimed.&lt;br&gt;
• A dependent moved out of the home recently and won’t live with the tax filer next year.&lt;br&gt;
• A dependent has a new job and will support his or herself next year.</value>
  </data>
  <data name="fileJointly" xml:space="preserve">
    <value>&lt;p&gt;If you’re a married couple and you agree to file a joint federal income tax return, you and your spouse report combined income information on one tax return. To be eligible for a tax credit to help pay for health coverage for your family, you and your spouse must file a joint federal income tax return for the year you want health coverage.&lt;br&gt;
If you’re a married couple, but you haven’t filed joint returns in the past, but you plan to file jointly, you can select “Yes.” Spouses may file a joint return even if just one of them had income for the year.&lt;/p&gt;</value>
  </data>
  <data name="fileTaxNextYear" xml:space="preserve">
    <value>&lt;p&gt;If you’ll file a federal income tax return for&amp;nbsp;[COVERAGEYEAR], you may be able to get help paying for health coverage through a new tax credit. If you don’t file a tax return, you or your family may be eligible for other types of free or low-cost health benefits. Tell us if you plan to file so we know what you’re eligible for.&lt;/p&gt;</value>
  </data>
  <data name="isMarried" xml:space="preserve">
    <value>• If you’re separated but not divorced, select “Yes.”&lt;br&gt;
• If you live with your partner, but aren’t legally married, select “No.”&lt;br&gt;
• If you have a same-sex spouse, select “Yes.” Each state has its own rules about same-sex marriage. </value>
  </data>
  <data name="liveWithSpouse" xml:space="preserve">
    <value>&lt;p&gt;You live with your spouse if you spend most nights in the same household. You can still count as living with your spouse if you’re away temporarily, like for school or a short-term job, if you’re expected to return.&lt;/p&gt;</value>
  </data>
  <data name="provideTaxFilerInfo" xml:space="preserve">
    <value>&lt;p&gt;The claiming tax filer (or tax filer) is the main person filing a household’s federal income tax return. If a spouse files a joint return with this person, he or she is a tax filer, too. Anyone claimed as a dependent on this person’s federal income tax return is a “tax dependent.”&lt;br&gt;
Select “Yes” if you can get the tax filer’s information, for example, if he or she lives with you. This will give the tax dependent the best chance of getting help paying for coverage.&lt;/p&gt;</value>
  </data>
  <data name="relationToTaxFiler" xml:space="preserve">
    <value>&lt;p&gt;If the dependent is this person’s legally adopted child, choose “son/daughter” from the relationship choices, even if another relationship also applies.&lt;/p&gt;&lt;br&gt;
&lt;b&gt;Who’s a dependent?&lt;/b&gt;
When you file a tax return, your child, stepchild, foster child, or sibling (if younger) is likely to be&lt;br&gt;

your dependent if he or she lives with you, doesn’t provide more than half of his or her own support for the year, and is younger than 19 or a full-time student younger than 25.</value>
  </data>
</root>