﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="arrival_I94" xml:space="preserve">
    <value>• Arrival/Departure Record (I-94, I-94A):* I-94 Arrival/Departure Records are issued to people when they enter the U.S. The bottom portion of the I-94 should be stapled to the passport. Enter the I-94 number, which is usually found at the top, left-hand side of the form. The I-94 paper form will no longer be provided upon arrival to the U.S., except in limited circumstances. If a person doesn’t have a paper version of the I-94, they can get a copy at cbp.gov/I94.</value>
  </data>
  <data name="docFieldsHelp" xml:space="preserve">
    <value>&lt;b&gt;When you enter your document type, you may need to enter one or more of these fields:&lt;hr&gt;&lt;/b&gt;
•&lt;b&gt; Alien number:&lt;/b&gt; The alien number (also called the alien registration number) can be found on the immigration document. It starts with an “A” and ends with 8-9 numbers.&lt;br&gt;
•&lt;b&gt; I-94 number:&lt;/b&gt; The I-94 number (also called the admission number) is printed on the I-94 or I-94A. This is an 11-digit number and is usually found at the top, left-hand side of the document.&lt;br&gt;
•&lt;b&gt; Passport or document number:&lt;/b&gt; The passport or document number can be found on the passport.&lt;br&gt;
•&lt;b&gt; Country of issuance:&lt;/b&gt; Select the country where the passport was issued.&lt;br&gt;
•&lt;b&gt; Passport expiration date:&lt;/b&gt; Enter the date the passport will expire. The expiration date should be listed on the document.&lt;br&gt;
•&lt;b&gt; SEVIS ID number:&lt;/b&gt; The SEVIS ID is located at the top, right-hand corner of the document.&lt;br&gt;
•&lt;b&gt; Document expiration date:&lt;/b&gt; Enter the expiration date listed on the document.&lt;br&gt;
•&lt;b&gt; Category code:&lt;/b&gt; Enter the 3-digit code listed on the employment authorization document. This code starts with an “A,” “B,” or “C.”&lt;br&gt;
If you need help finding information on your document or help completing this section, call the ALL Kids toll-free number at 1-888-373-KIDS(5437).</value>
  </data>
  <data name="eligiExhagne_J1Status" xml:space="preserve">
    <value>•&lt;b&gt;Certificate of Eligibility for Exchange Visitor (J-1) Status (DS2019):&lt;/b&gt; Certificates of Eligibility for Exchange Visitor Status (DS-2019s) are the documents that support applications for exchange visitor visa statuses (J-1s or J-2s). Enter the SEVIS ID number, which is located at the top, right-hand side of the document.</value>
  </data>
  <data name="eligiNonImmi_F1_StudentSatatus" xml:space="preserve">
    <value>•&lt;b&gt; Certificate of Eligibility for Nonimmigrant (F-1) Student Status (I-20):&lt;/b&gt; I-20 Certificates of Eligibility for Non-immigrant Student Status are the documents that support applications for student visa statuses (F-1s or F-2s). Enter the SEVIS ID number, which is located at the top, right-hand side of the document.</value>
  </data>
  <data name="employmentAuthoCard" xml:space="preserve">
    <value>• &lt;b&gt;Employment Authorization Card (EAD, I-766):&lt;/b&gt;* Employment Authorization Cards (or I-766s) are issued to people who are authorized to work temporarily in the U.S. Enter the Alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. Also enter the care expiration date, as listed on the card.</value>
  </data>
  <data name="foreignPassport" xml:space="preserve">
    <value>•&lt;b&gt; Foreign passport:&lt;/b&gt; Passports from foreign countries are used when non-immigrants enter the U.S. Enter the passport number and passport expiration date.</value>
  </data>
  <data name="foreignPassport_I94" xml:space="preserve">
    <value>• &lt;b&gt;Arrival/Departure Record in foreign passport (I-94):&lt;/b&gt;* I-94 Arrival/Departure Records are issued to non-immigrants when they enter the U.S. The bottom portion of the I-94 should be stapled to the foreign passport. Enter the I-94 number, which is usually found at the top, left-hand side of the form. Also enter the passport number and expiration date.</value>
  </data>
  <data name="greenCard" xml:space="preserve">
    <value>•&lt;b&gt; Permanent Resident Card (“Green Card,” I-551):&lt;/b&gt;* I-551 Permanent Resident Cards (or “Green Cards”) are issued to eligible immigrants who enter the U. S. to permanently live. To verify your eligible immigration status, enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. Also enter the card number, which is listed on the card. On the I-551, this number is listed under the heading “A#” or “USCIS#.”</value>
  </data>
  <data name="greenCardImgHelp" xml:space="preserve">
    <value />
  </data>
  <data name="machineReadableImmiVisa" xml:space="preserve">
    <value>• &lt;b&gt;Machine Readable Immigrant Visa (with temporary I-551 language):&lt;b&gt;* Machine-readable immigrant visas (MRIVs) with temporary I-551 language are documents indicating permanent resident status. Enter the Alien number (also known as alien registration number), which may start with an “A” and end with 8-9 numbers. Some MRIVs may not have an “A” before the number. Also enter the passport number.</value>
  </data>
  <data name="noticeOfAction_I797" xml:space="preserve">
    <value>• &lt;b&gt;Notice of Action (I-797):&lt;/b&gt;* Notices of Action (I-797s) are communication from U.S. Citizenship and Immigration Service about immigration benefits. I-797s can be used for different purposes, like an approval notice, receipt notice, or a replacement for an I-94. Sometimes these notices have other documents attached to them, like I-360s (petitions for Amerasian, widow(er), or special immigrant statuses).</value>
  </data>
  <data name="reentryPermit" xml:space="preserve">
    <value>•&lt;b&gt; Reentry Permit (I-327):&lt;/b&gt; Re-entry permits (or I-327s), when valid, allow permanent residents to leave and re-enter the U.S. These permits are located in multi-purpose booklets called “U.S. Travel Documents.” Enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. This number is located at the top, right-hand side of the document.</value>
  </data>
  <data name="refugee" xml:space="preserve">
    <value>•&lt;b&gt; Refugee Travel Document (I-571):&lt;/b&gt; Refugee Travel Documents (or I-571s) may be issued to refugees and asylees for travel purposes. These permits should be located in multi-purpose booklets called “U.S. Travel Documents.” Enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. This number is located at the top, right-hand side of the document.</value>
  </data>
  <data name="temporary551Stamp" xml:space="preserve">
    <value>•&lt;b&gt; Temporary I-551 Stamp (on passport or I-94, I-94A):&lt;/b&gt;* Temporary I-551 stamps can be used to attest to permanent resident status. A temporary I-551 stamp will have a handwritten or stamped issue date and a “valid until” date. This stamp can be found on the front of an I-94 form or in the foreign passport. Enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers.</value>
  </data>
</root>