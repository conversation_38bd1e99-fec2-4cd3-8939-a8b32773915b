CREATE PROCEDURE [dbo].[usp_CALCULATE_VA_PENSION_NET]
(
	@appNonMagiIncomeId BIGINT = 0,
	@UpdatedBy VARCHAR(50) = ''
)
AS
-- ==============================================================================================
-- Author:		<PERSON>
-- Create date:	09/10/2025
-- Description:	Updates the Net Amount for VA based on SSA, Factsheet and income.
-- Modified by: <PERSON>:	9/11/25		Update logic to set Updated by for the records that were updated.
--              <PERSON>:	9/15/25		Update logic to update the correct income detail id.
--              <PERSON>:	9/17/25		Update logic to handle Null values.
-- ==============================================================================================
BEGIN
	SET NOCOUNT ON;

	DROP TABLE IF EXISTS #incomeData, #veteranRows, #grossIncomes, #factsheetYear, #veteranRowsByYear

	-- DECLARATIONS
	DECLARE @appId AS INT = 0;
	DECLARE @countableVAPension AS DECIMAL = 0;
	DECLARE @maxVaPensionYear AS DECIMAL = 0;
	DECLARE @grossIncome AS DECIMAL = 0;


	CREATE TABLE #factsheetYear
	(
		RowN INT,
		RecordYear INT,
		StandardValue DECIMAL(8,2),
		AandAValue DECIMAL(8,2),
		MaxVAPension DECIMAL(8,2)
	)

	--************* GETTING APP_ID  ***************************
	SELECT @appId = APPLICATION_ID
	FROM application_non_magi_income
	WHERE APPLICATION_NON_MAGI_INCOME_ID = @appNonMagiIncomeId


	DROP TABLE IF EXISTS #myIncomeSplit;

	CREATE TABLE #myIncomeSplit
	(
		RowN INT,
		APPLICATION_NON_MAGI_INCOME_ID BIGINT,
		APPLICATION_NON_MAGI_INCOME_DETAIL_ID BIGINT,
		INCOME_TYPE_ID INT,
		INCOME_MONTH DATE,
		INCOME_DESC	NVARCHAR(100),
		VETERAN_STATUS NVARCHAR(50),
		VA_INDICATOR_ID INT,
		DETAIL_GROSS DECIMAL(8,2),
		DETAIL_NET  DECIMAL(8,2)
	)

	--*************  Get all INCOME Splitted  ***************************
	INSERT INTO #myIncomeSplit
	EXEC usp_GET_APPLICATION_INCOME_DATA_SPLITTED_BY_MONTH @appId

	SELECT *
	INTO #incomeData
	FROM #myIncomeSplit
	ORDER BY 1 desc

	--select * from #incomeData;

	--************************   Get the Total SS Gross Incomes  *********************
	SELECT
		APPLICATION_NON_MAGI_INCOME_ID,
		INCOME_TYPE_ID, INCOME_DESC,
		VETERAN_STATUS,
		VA_INDICATOR_ID,
		INCOME_MONTH,
		DETAIL_GROSS AS DetailGrossIncome,
		DETAIL_NET AS DetailNetIncome
	INTO #grossIncomes
	FROM #incomeData
	WHERE INCOME_TYPE_ID != 15



	--******************* Gathering "SINGLE SURVIVOR OF A VETERAN" Records  ***********************
	SELECT
		ROW_NUMBER () OVER(ORDER BY det.INCOME_MONTH) AS RowN,
		inc.APPLICATION_NON_MAGI_INCOME_ID,
		APPLICATION_NON_MAGI_INCOME_DETAIL_ID,
		INCOME_TYPE_ID,
		VETERAN_STATUS,
		VA_INDICATOR_ID,
		INCOME_MONTH,
		det.GROSS_INCOME_AMOUNT  AS DetailGrossIncome,
		det.MONTHLY_COUNTABLE_NET_INCOME_AMOUNT
	INTO #veteranRows
	FROM APPLICATION_NON_MAGI_INCOME_DETAIL as det
		INNER JOIN  APPLICATION_NON_MAGI_INCOME as inc ON Det.APPLICATION_NON_MAGI_INCOME_ID = inc.APPLICATION_NON_MAGI_INCOME_ID
	WHERE INCOME_TYPE_ID = 15
		AND VETERAN_STATUS = 'Single Survivor'
		AND APPLICATION_ID = @appId
	ORDER BY 1 DESC


	--**************** Gather Factsheet values. ******************************
		INSERT INTO #factsheetYear (RowN, RecordYear, StandardValue)
		(
			SELECT
				ROW_NUMBER () OVER(ORDER BY cola.FACTSHEET_YEAR) AS RowN,
				cola.FACTSHEET_YEAR,
				VALUE1
			FROM COLA_FACTSHEET_YEAR_VALUE as cola
			WHERE cola.FACTSHEET_YEAR IN (SELECT YEAR(INCOME_MONTH) FROM #veteranRows)
			AND COLA_FACTSHEET_SUB_CATEGORY_ID IN (18)
		)

		UPDATE f
		SET AandAValue = VALUE1, MaxVAPension = f.StandardValue - VALUE1
		FROM  #factsheetYear AS F
		INNER JOIN COLA_FACTSHEET_YEAR_VALUE AS COLA ON F.RecordYear = COLA.FACTSHEET_YEAR AND COLA_FACTSHEET_SUB_CATEGORY_ID in (21)


	--******************************************************************
	--************* Get factsheet values split by year *****************
	DECLARE @yearcount INT = 1;
	DECLARE @TotalYears INT = 0;

	SELECT @TotalYears = COUNT(*) FROM #factsheetYear

	WHILE @yearcount <= @TotalYears
	BEGIN

		DECLARE @year AS INT;
		DECLARE @standardVal AS DECIMAL;
		DECLARE @aAVal AS DECIMAL;
		DECLARE @netVal AS DECIMAL;
		DECLARE @priorGross AS DECIMAL = 0;

		--**************** GET MAX VA PENSION  **********************
		SELECT
			@year = fs.[RecordYear],
			@standardVal = fs.standardValue,
			@aAVal = fs.AandAValue,
			@maxVaPensionYear = fs.MaxVAPension
		FROM #factsheetYear AS fs
		WHERE fs.RowN = @yearCount;

		--****************************************************************************************************
		--********* MAIN LOGIC GATHERING INCOME GROSS AND CALCULATE THE NET VA AMOUNT PER MONTH. *************

		DECLARE @rowcount INT = 1;
		DECLARE @TotalRows INT = 0;

		DROP TABLE IF EXISTS #veteranRowsByYear;

		--*************** SELECT VETERAN PER YEAR
		SELECT
			ROW_NUMBER () OVER(ORDER BY v.INCOME_MONTH) AS RowN,
			v.INCOME_MONTH,
			v.APPLICATION_NON_MAGI_INCOME_DETAIL_ID,
			v.DetailGrossIncome
		INTO #veteranRowsByYear
		FROM #veteranRows AS v
		WHERE YEAR(v.INCOME_MONTH) = @year;

		SELECT @TotalRows = COUNT(*) FROM #veteranRowsByYear


		--********************* ITERATING RECORDS FOR VETERAN MONTHS  ************************
		WHILE @RowCount <= @TotalRows
		BEGIN

			DECLARE @currentGross AS DECIMAL;
			DECLARE @incomeDetailId AS INT;
			DECLARE @vaCheckVal AS DECIMAL;
			DECLARE @incomeDate AS DATE;

			SELECT @incomeDate = V.INCOME_MONTH
			FROM #veteranRowsByYear AS V
			WHERE v.RowN = @RowCount;

			-- *****************     GET GROSS TOTAL FOR MONTH.     ******************

				SELECT
					@currentGross = ISNULL(SUM(ISNULL(DetailGrossIncome, CAST(0 AS DECIMAL))), CAST(0 AS DECIMAL))
				FROM #grossIncomes AS G
				WHERE G.INCOME_MONTH = @incomeDate

			--**********************************************************
			--****************  GATHERING VETERAN RECORDS  *************

			SELECT
				@incomeDetailId  = vet.APPLICATION_NON_MAGI_INCOME_DETAIL_ID,
				@vaCheckVal = vet.DetailGrossIncome
			FROM #veteranRowsByYear as vet
			WHERE
				RowN = @rowcount;

			--***********************************************************
			--*************** GET CONTABLE VA PENSION *******************

			SET @countableVAPension = iif(@maxVaPensionYear - @currentGross < 0, 0, @maxVAPensionYear - @currentGross);

			--*******************************************************
			-- ********** INSERTING NET VALUE  *********************
			UPDATE D
			SET MONTHLY_COUNTABLE_NET_INCOME_AMOUNT = IIF(MONTHLY_COUNTABLE_NET_INCOME_AMOUNT != @countableVAPension, @countableVAPension, MONTHLY_COUNTABLE_NET_INCOME_AMOUNT),
				UPDATED_BY = IIF(MONTHLY_COUNTABLE_NET_INCOME_AMOUNT != @countableVAPension,  @UpdatedBy, UPDATED_BY)
			FROM APPLICATION_NON_MAGI_INCOME_DETAIL AS D
			WHERE APPLICATION_NON_MAGI_INCOME_DETAIL_ID = @incomeDetailId

			SET @RowCount = @RowCount + 1;
		END;

		SET @yearcount = @yearcount + 1;
	END;

END