
CREATE TABLE [dbo].[RENEWAL_NORESPONSE_RECIPIENTS](
	[RECIPIENT_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[DISTRICT_OFFICE_NUMBER] [tinyint]  NULL,
	[DISTRICT_OFFICE_NAME] [varchar](100) NULL,
	[REVIEWER_NUMBER] [char](2) NULL,
	[REVIEWER_NAME] [varchar](100) NULL,
	[PROGRAM_CODE] [char](2) NULL,
	[MEDICAID_ID] [bigint] NULL,
	[SSN] [varchar](9) NULL,
	[APPLICATION_ID] [bigint] NOT NULL,
	[PERSON_ID] [bigint] NOT NULL,
	[LAST_NAME] [varchar](50) NULL,
	[FIRST_NAME] [varchar](50) NULL,
	[MIDDLE_INITIAL] [char](1) NULL,
	[BUY_IN_STATUS] [char](1) NULL,
	[LIABILITY_BLOCK_INDICATOR] [char](1) NULL,
	[WORKER_ALERT_CODE] [varchar](50) NULL,
	[WORKER_ALERT_DATE] [DATE] NULL,
	[REVIEW_DATE] [date] NULL,
	[TERMINATION_DATE] [char](8) NULL,
	[REVIEW_RECEIVED_IND] [char](1) NULL,
	[NOTICE_TO_SPONSOR] [char](1) NULL,
	[NOTICE_TO_FACILITY] [char](1) NULL,
	[REVIEW_TYPE] [varchar](10) NULL,
	[PROGRAM_CATEGORY] [varchar](15) NOT NULL,
	[AID_CATEGORY] [varchar](100) NULL,
	[PROGRAM_NAME] [varchar](80) NULL,
	[FORM_214R_SENT] [char](1) NULL,
	[FORM_240R_SENT] [char](1) NULL,
	[FORM_225R_SENT] [char](1) NULL,
	[NVRA] [char](1) NULL,
	[REVIEW_SENT_DATE] [date] NULL,
	[TERMINATION_SENT_DATE] [date] NULL,
	[CREATED_DATE] [date]  NULL,
	[UPDATED_DATE] [date]  NULL,
 CONSTRAINT [PK_RecipientsRenewalDueNoResponse] PRIMARY KEY CLUSTERED 
(
	[RECIPIENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RecipientsRenewalDueNoResponse] ADD  CONSTRAINT [DF_RecipientsRenewalDueNoResponse_CREATED_DATE]  DEFAULT (getdate()) FOR [CREATED_DATE]
GO

ALTER TABLE [dbo].[RecipientsRenewalDueNoResponse] ADD  CONSTRAINT [DF_RecipientsRenewalDueNoResponse_UPDATED_DATE]  DEFAULT (getdate()) FOR [UPDATED_DATE]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'RECIPIENT ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'RECIPIENT_ID'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'District Office Number' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'DISTRICT_OFFICE_NUMBER'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'District Office Name' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'DISTRICT_OFFICE_NAME'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Worker Number' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'REVIEWER_NUMBER'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Worker Name' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'REVIEWER_NAME'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Program Code' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'PRGRAM_CODE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Medicaid ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'MEDICAID_ID'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SSN' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'SSN'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'A unique identifier for an  Application' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'APPLICATION_ID'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Person ID  -  Unique identifier for  applicants' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'PERSON_ID'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Last Name' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'LASTNAME'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'First Name' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'FIRSTNAME'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Middle Initial' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'MIDDLE_INITIAL'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Buy In Status' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'BUY_IN_STATUS'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Liability Block Inidicator' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'LIABILITY_BLOCK_INDICATOR'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Worker Alert Date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'WORKER_ALERT_CODE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Worker Alert Code' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'WORKER_ALERT_DATE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Review Date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'REVIEW_DATE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Termination Date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'TERMINATION_DATE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Review Recieved Indicator' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'REVIEW_RECEIVED_IND'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Notice To Sponsor' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'NOTICE_TO_SPONSOR'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Notice To Facility' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'NOTICE_TO_FACILITY'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Review Type' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'REVIEW_TYPE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Program Category' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'PROGRAM_CATEGORY'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Aid Category' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'AID_CATEGORY'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Program Name' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'PROGRAM_NAME'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Form 214R Sent - Intent to Return Home' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'FORM_214R_SENT'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Form 240R Sent - Statement of dependent relative' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'FORM_240R_SENT'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Form 225 R sent - Joint Owner' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'FORM_255R_SENT'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'NVRA' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'NVRA'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Review Sent date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'REVIEW_SENT_DATE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Termination Sent Date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'TERMINATION_SENT_DATE'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SSN' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'Created_Date'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SSN' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RecipientsRenewalDueNoResponse', @level2type=N'COLUMN',@level2name=N'Updated_Date'
GO
