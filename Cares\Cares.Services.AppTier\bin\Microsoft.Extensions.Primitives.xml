<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Primitives</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Primitives.CancellationChangeToken">
            <summary>
            A <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> implementation using <see cref="T:System.Threading.CancellationToken"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.CancellationChangeToken.#ctor(System.Threading.CancellationToken)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.Primitives.CancellationChangeToken"/>.
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/>.</param>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.CancellationChangeToken.ActiveChangeCallbacks">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.Primitives.CancellationChangeToken.HasChanged">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Primitives.CancellationChangeToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Primitives.ChangeToken">
            <summary>
            Propagates notifications that a change has occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.ChangeToken.OnChange(System.Func{Microsoft.Extensions.Primitives.IChangeToken},System.Action)">
            <summary>
            Registers the <paramref name="changeTokenConsumer"/> action to be called whenever the token produced changes.
            </summary>
            <param name="changeTokenProducer">Produces the change token.</param>
            <param name="changeTokenConsumer">Action called when the token changes.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.ChangeToken.OnChange``1(System.Func{Microsoft.Extensions.Primitives.IChangeToken},System.Action{``0},``0)">
            <summary>
            Registers the <paramref name="changeTokenConsumer"/> action to be called whenever the token produced changes.
            </summary>
            <param name="changeTokenProducer">Produces the change token.</param>
            <param name="changeTokenConsumer">Action called when the token changes.</param>
            <param name="state">state for the consumer.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Extensions.Primitives.CompositeChangeToken">
            <summary>
            An <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> which represents one or more <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.CompositeChangeToken.#ctor(System.Collections.Generic.IReadOnlyList{Microsoft.Extensions.Primitives.IChangeToken})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Extensions.Primitives.CompositeChangeToken"/>.
            </summary>
            <param name="changeTokens">The list of <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> to compose.</param>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.CompositeChangeToken.ChangeTokens">
            <summary>
            Returns the list of <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> which compose the current <see cref="T:Microsoft.Extensions.Primitives.CompositeChangeToken"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.CompositeChangeToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.Primitives.CompositeChangeToken.HasChanged">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.Primitives.CompositeChangeToken.ActiveChangeCallbacks">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Primitives.Extensions.Append(System.Text.StringBuilder,Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Add the given <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to the <see cref="T:System.Text.StringBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:System.Text.StringBuilder"/> to add to.</param>
            <param name="segment">The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to add.</param>
            <returns>The original <see cref="T:System.Text.StringBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Primitives.IChangeToken">
            <summary>
            Propagates notifications that a change has occurred.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.IChangeToken.HasChanged">
            <summary>
            Gets a value that indicates if a change has occurred.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.IChangeToken.ActiveChangeCallbacks">
            <summary>
            Indicates if this token will pro-actively raise callbacks. If <c>false</c>, the token consumer must
            poll <see cref="P:Microsoft.Extensions.Primitives.IChangeToken.HasChanged" /> to detect changes.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.IChangeToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <summary>
            Registers for a callback that will be invoked when the entry has changed.
            <see cref="P:Microsoft.Extensions.Primitives.IChangeToken.HasChanged"/> MUST be set before the callback is invoked.
            </summary>
            <param name="callback">The <see cref="T:System.Action`1"/> to invoke.</param>
            <param name="state">State to be passed into the callback.</param>
            <returns>An <see cref="T:System.IDisposable"/> that is used to unregister the callback.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Primitives.StringSegment">
            <summary>
            An optimized representation of a substring.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Primitives.StringSegment.Empty">
            <summary>
            A <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> for <see cref="F:System.String.Empty"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.#ctor(System.String)">
            <summary>
            Initializes an instance of the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> struct.
            </summary>
            <param name="buffer">
            The original <see cref="T:System.String"/>. The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> includes the whole <see cref="T:System.String"/>.
            </param>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Initializes an instance of the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> struct.
            </summary>
            <param name="buffer">The original <see cref="T:System.String"/> used as buffer.</param>
            <param name="offset">The offset of the segment within the <paramref name="buffer"/>.</param>
            <param name="length">The length of the segment.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="buffer"/> is <code>null</code>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> or <paramref name="length"/> is less than zero, or <paramref name="offset"/> +
            <paramref name="length"/> is greater than the number of characters in <paramref name="buffer"/>.
            </exception>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.StringSegment.Buffer">
            <summary>
            Gets the <see cref="T:System.String"/> buffer for this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.StringSegment.Offset">
            <summary>
            Gets the offset within the buffer for this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.StringSegment.Length">
            <summary>
            Gets the length of this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.StringSegment.Value">
            <summary>
            Gets the value of this segment as a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.StringSegment.HasValue">
            <summary>
            Gets whether this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> contains a valid value.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.StringSegment.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:System.Char"/> at a specified position in the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <param name="index">The offset into the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/></param>
            <returns>The <see cref="T:System.Char"/> at a specified position.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index"/> is greater than or equal to <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/> or less than zero.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.AsSpan">
            <summary>
            Gets a <see cref="T:System.ReadOnlySpan`1"/> from the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <returns>The <see cref="T:System.ReadOnlySpan`1"/> from this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.AsMemory">
            <summary>
            Gets a <see cref="T:System.ReadOnlyMemory`1"/> from the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <returns>The <see cref="T:System.ReadOnlyMemory`1"/> from this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Compare(Microsoft.Extensions.Primitives.StringSegment,Microsoft.Extensions.Primitives.StringSegment,System.StringComparison)">
            <summary>
            Compares substrings of two specified <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> objects using the specified rules,
            and returns an integer that indicates their relative position in the sort order.
            </summary>
            <param name="a">The first <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare.</param>
            <param name="b">The second <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare.</param>
            <param name="comparisonType">One of the enumeration values that specifies the rules for the comparison.</param>
            <returns>
            A 32-bit signed integer indicating the lexical relationship between the two comparands.
            The value is negative if <paramref name="a"/> is less than <paramref name="b"/>, 0 if the two comparands are equal,
            and positive if <paramref name="a"/> is greater than <paramref name="b"/>.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Equals(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns><code>true</code> if the current object is equal to the other parameter; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Equals(Microsoft.Extensions.Primitives.StringSegment,System.StringComparison)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <param name="comparisonType">One of the enumeration values that specifies the rules to use in the comparison.</param>
            <returns><code>true</code> if the current object is equal to the other parameter; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Equals(Microsoft.Extensions.Primitives.StringSegment,Microsoft.Extensions.Primitives.StringSegment,System.StringComparison)">
            <summary>
            Determines whether two specified <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> objects have the same value. A parameter specifies the culture, case, and
            sort rules used in the comparison.
            </summary>
            <param name="a">The first <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare.</param>
            <param name="b">The second <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare.</param>
            <param name="comparisonType">One of the enumeration values that specifies the rules for the comparison.</param>
            <returns><code>true</code> if the objects are equal; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Equals(System.String)">
            <summary>
            Checks if the specified <see cref="T:System.String"/> is equal to the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <param name="text">The <see cref="T:System.String"/> to compare with the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</param>
            <returns><code>true</code> if the specified <see cref="T:System.String"/> is equal to the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Equals(System.String,System.StringComparison)">
            <summary>
            Checks if the specified <see cref="T:System.String"/> is equal to the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <param name="text">The <see cref="T:System.String"/> to compare with the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</param>
            <param name="comparisonType">One of the enumeration values that specifies the rules to use in the comparison.</param>
            <returns><code>true</code> if the specified <see cref="T:System.String"/> is equal to the current <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>; otherwise, <code>false</code>.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="text"/> is <code>null</code>.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.op_Equality(Microsoft.Extensions.Primitives.StringSegment,Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Checks if two specified <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> have the same value.
            </summary>
            <param name="left">The first <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare, or <code>null</code>.</param>
            <param name="right">The second <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare, or <code>null</code>.</param>
            <returns><code>true</code> if the value of <paramref name="left"/> is the same as the value of <paramref name="right"/>; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.op_Inequality(Microsoft.Extensions.Primitives.StringSegment,Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Checks if two specified <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> have different values.
            </summary>
            <param name="left">The first <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare, or <code>null</code>.</param>
            <param name="right">The second <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to compare, or <code>null</code>.</param>
            <returns><code>true</code> if the value of <paramref name="left"/> is different from the value of <paramref name="right"/>; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.op_Implicit(System.String)~Microsoft.Extensions.Primitives.StringSegment">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> from the given <see cref="T:System.String"/>.
            </summary>
            <param name="value">The <see cref="T:System.String"/> to convert to a <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/></param>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.op_Implicit(Microsoft.Extensions.Primitives.StringSegment)~System.ReadOnlySpan{System.Char}">
            <summary>
            Creates a see <see cref="T:System.ReadOnlySpan`1"/> from the given <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <param name="segment">The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to convert to a <see cref="T:System.ReadOnlySpan`1"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.op_Implicit(Microsoft.Extensions.Primitives.StringSegment)~System.ReadOnlyMemory{System.Char}">
            <summary>
            Creates a see <see cref="T:System.ReadOnlyMemory`1"/> from the given <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <param name="segment">The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to convert to a <see cref="T:System.ReadOnlyMemory`1"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.StartsWith(System.String,System.StringComparison)">
            <summary>
            Checks if the beginning of this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> matches the specified <see cref="T:System.String"/> when compared using the specified <paramref name="comparisonType"/>.
            </summary>
            <param name="text">The <see cref="T:System.String"/>to compare.</param>
            <param name="comparisonType">One of the enumeration values that specifies the rules to use in the comparison.</param>
            <returns><code>true</code> if <paramref name="text"/> matches the beginning of this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>; otherwise, <code>false</code>.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="text"/> is <code>null</code>.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.EndsWith(System.String,System.StringComparison)">
            <summary>
            Checks if the end of this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> matches the specified <see cref="T:System.String"/> when compared using the specified <paramref name="comparisonType"/>.
            </summary>
            <param name="text">The <see cref="T:System.String"/>to compare.</param>
            <param name="comparisonType">One of the enumeration values that specifies the rules to use in the comparison.</param>
            <returns><code>true</code> if <paramref name="text"/> matches the end of this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>; otherwise, <code>false</code>.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="text"/> is <code>null</code>.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Substring(System.Int32)">
            <summary>
            Retrieves a substring from this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            The substring starts at the position specified by <paramref name="offset"/> and has the remaining length.
            </summary>
            <param name="offset">The zero-based starting character position of a substring in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</param>
            <returns>A <see cref="T:System.String"/> that is equivalent to the substring of remaining length that begins at
            <paramref name="offset"/> in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/></returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> is greater than or equal to <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/> or less than zero.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Substring(System.Int32,System.Int32)">
            <summary>
            Retrieves a substring from this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            The substring starts at the position specified by <paramref name="offset"/> and has the specified <paramref name="length"/>.
            </summary>
            <param name="offset">The zero-based starting character position of a substring in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</param>
            <param name="length">The number of characters in the substring.</param>
            <returns>A <see cref="T:System.String"/> that is equivalent to the substring of length <paramref name="length"/> that begins at
            <paramref name="offset"/> in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/></returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> or <paramref name="length"/> is less than zero, or <paramref name="offset"/> + <paramref name="length"/> is
            greater than <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/>.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Subsegment(System.Int32)">
            <summary>
            Retrieves a <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> that represents a substring from this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> starts at the position specified by <paramref name="offset"/>.
            </summary>
            <param name="offset">The zero-based starting character position of a substring in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</param>
            <returns>A <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> that begins at <paramref name="offset"/> in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>
            whose length is the remainder.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> is greater than or equal to <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/> or less than zero.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Subsegment(System.Int32,System.Int32)">
            <summary>
            Retrieves a <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> that represents a substring from this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> starts at the position specified by <paramref name="offset"/> and has the specified <paramref name="length"/>.
            </summary>
            <param name="offset">The zero-based starting character position of a substring in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</param>
            <param name="length">The number of characters in the substring.</param>
            <returns>A <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> that is equivalent to the substring of length <paramref name="length"/> that begins at <paramref name="offset"/> in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/></returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> or <paramref name="length"/> is less than zero, or <paramref name="offset"/> + <paramref name="length"/> is
            greater than <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/>.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.IndexOf(System.Char,System.Int32,System.Int32)">
            <summary>
            Gets the zero-based index of the first occurrence of the character <paramref name="c"/> in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            The search starts at <paramref name="start"/> and examines a specified number of <paramref name="count"/> character positions.
            </summary>
            <param name="c">The Unicode character to seek.</param>
            <param name="start">The zero-based index position at which the search starts. </param>
            <param name="count">The number of characters to examine.</param>
            <returns>The zero-based index position of <paramref name="c"/> from the beginning of the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> if that character is found, or -1 if it is not.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="start"/> or <paramref name="count"/> is less than zero, or <paramref name="start"/> + <paramref name="count"/> is
            greater than <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/>.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.IndexOf(System.Char,System.Int32)">
            <summary>
            Gets the zero-based index of the first occurrence of the character <paramref name="c"/> in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            The search starts at <paramref name="start"/>.
            </summary>
            <param name="c">The Unicode character to seek.</param>
            <param name="start">The zero-based index position at which the search starts. </param>
            <returns>The zero-based index position of <paramref name="c"/> from the beginning of the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> if that character is found, or -1 if it is not.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="start"/> is greater than or equal to <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/> or less than zero.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.IndexOf(System.Char)">
            <summary>
            Gets the zero-based index of the first occurrence of the character <paramref name="c"/> in this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.
            </summary>
            <param name="c">The Unicode character to seek.</param>
            <returns>The zero-based index position of <paramref name="c"/> from the beginning of the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> if that character is found, or -1 if it is not.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.IndexOfAny(System.Char[],System.Int32,System.Int32)">
            <summary>
            Reports the zero-based index of the first occurrence in this instance of any character in a specified array
            of Unicode characters. The search starts at a specified character position and examines a specified number
            of character positions.
            </summary>
            <param name="anyOf">A Unicode character array containing one or more characters to seek.</param>
            <param name="startIndex">The search starting position.</param>
            <param name="count">The number of character positions to examine.</param>
            <returns>The zero-based index position of the first occurrence in this instance where any character in <paramref name="anyOf"/>
            was found; -1 if no character in <paramref name="anyOf"/> was found.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="anyOf"/> is <code>null</code>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="startIndex"/> or <paramref name="count"/> is less than zero, or <paramref name="startIndex"/> + <paramref name="count"/> is
            greater than <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/>.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.IndexOfAny(System.Char[],System.Int32)">
            <summary>
            Reports the zero-based index of the first occurrence in this instance of any character in a specified array
            of Unicode characters. The search starts at a specified character position.
            </summary>
            <param name="anyOf">A Unicode character array containing one or more characters to seek.</param>
            <param name="startIndex">The search starting position.</param>
            <returns>The zero-based index position of the first occurrence in this instance where any character in <paramref name="anyOf"/>
            was found; -1 if no character in <paramref name="anyOf"/> was found.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="startIndex"/> is greater than or equal to <see cref="P:Microsoft.Extensions.Primitives.StringSegment.Length"/> or less than zero.
            </exception>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.IndexOfAny(System.Char[])">
            <summary>
            Reports the zero-based index of the first occurrence in this instance of any character in a specified array
            of Unicode characters.
            </summary>
            <param name="anyOf">A Unicode character array containing one or more characters to seek.</param>
            <returns>The zero-based index position of the first occurrence in this instance where any character in <paramref name="anyOf"/>
            was found; -1 if no character in <paramref name="anyOf"/> was found.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.LastIndexOf(System.Char)">
            <summary>
            Reports the zero-based index position of the last occurrence of a specified Unicode character within this instance.
            </summary>
            <param name="value">The Unicode character to seek.</param>
            <returns>The zero-based index position of value if that character is found, or -1 if it is not.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Trim">
            <summary>
            Removes all leading and trailing whitespaces.
            </summary>
            <returns>The trimmed <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.TrimStart">
            <summary>
            Removes all leading whitespaces.
            </summary>
            <returns>The trimmed <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.TrimEnd">
            <summary>
            Removes all trailing whitespaces.
            </summary>
            <returns>The trimmed <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.Split(System.Char[])">
            <summary>
            Splits a string into <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>s that are based on the characters in an array.
            </summary>
            <param name="chars">A character array that delimits the substrings in this string, an empty array that
            contains no delimiters, or null.</param>
            <returns>An <see cref="T:Microsoft.Extensions.Primitives.StringTokenizer"/> whose elements contain the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>s from this instance
            that are delimited by one or more characters in <paramref name="chars"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.IsNullOrEmpty(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Indicates whether the specified <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> is null or an Empty string.
            </summary>
            <param name="value">The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to test.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringSegment.ToString">
            <summary>
            Returns the <see cref="T:System.String"/> represented by this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> or <code>String.Empty</code> if the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> does not contain a value.
            </summary>
            <returns>The <see cref="T:System.String"/> represented by this <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> or <code>String.Empty</code> if the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> does not contain a value.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Primitives.StringTokenizer">
            <summary>
            Tokenizes a <see cref="T:System.String"/> into <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringTokenizer.#ctor(System.String,System.Char[])">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.Primitives.StringTokenizer"/>.
            </summary>
            <param name="value">The <see cref="T:System.String"/> to tokenize.</param>
            <param name="separators">The characters to tokenize by.</param>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.StringTokenizer.#ctor(Microsoft.Extensions.Primitives.StringSegment,System.Char[])">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.Primitives.StringTokenizer"/>.
            </summary>
            <param name="value">The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> to tokenize.</param>
            <param name="separators">The characters to tokenize by.</param>
        </member>
        <member name="T:Microsoft.Extensions.Primitives.StringValues">
            <summary>
            Represents zero/null, one, or many strings in an efficient way.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.Resources.Argument_InvalidOffsetLength">
            <summary>Offset and length are out of bounds for the string or length is greater than the number of characters from index to the end of the string.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.Resources.Argument_InvalidOffsetLengthStringSegment">
            <summary>Offset and length are out of bounds for this StringSegment or length is greater than the number of characters to the end of this StringSegment.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.Resources.Capacity_CannotChangeAfterWriteStarted">
            <summary>Cannot change capacity after write started.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.Resources.Capacity_NotEnough">
            <summary>Not enough capacity to write '{0}' characters, only '{1}' left.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.Resources.FormatCapacity_NotEnough(System.Object,System.Object)">
            <summary>Not enough capacity to write '{0}' characters, only '{1}' left.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Primitives.Resources.Capacity_NotUsedEntirely">
            <summary>Entire reserved capacity was not used. Capacity: '{0}', written '{1}'.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Primitives.Resources.FormatCapacity_NotUsedEntirely(System.Object,System.Object)">
            <summary>Entire reserved capacity was not used. Capacity: '{0}', written '{1}'.</summary>
        </member>
    </members>
</doc>
