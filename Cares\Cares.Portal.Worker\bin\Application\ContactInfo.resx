﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="contact" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="contactAndAllFamily" xml:space="preserve">
    <value>[NAME] and other family members</value>
  </data>
  <data name="contactBy" xml:space="preserve">
    <value>You'll be contacted when a notice is ready for you on this website. How can we contact you?</value>
  </data>
  <data name="contactHeader" xml:space="preserve">
    <value>If you are seeking coverage for yourself, or others in your household, please enter your contact information.</value>
  </data>
  <data name="contactOnly" xml:space="preserve">
    <value>[NAME] only</value>
  </data>
  <data name="contactPerson" xml:space="preserve">
    <value>Contact Person</value>
  </data>
  <data name="contactPhone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="ContactPreferenceEmailRequired" xml:space="preserve">
    <value>Email Address is required if Email option is selected.</value>
  </data>
  <data name="ContactPreferencePhoneRequired" xml:space="preserve">
    <value>Phone Number is required if Text Message is selected.</value>
  </data>
  <data name="ContactPreferenceRequired" xml:space="preserve">
    <value>Text Message or Email is required to receive an online notice.</value>
  </data>
  <data name="contactPreferences" xml:space="preserve">
    <value>Preferences</value>
  </data>
  <data name="electronicNotice" xml:space="preserve">
    <value>We need to know the best way to contact you about this application and your health coverage if you're eligible. Do you want to read your notices about your application on your electronic "Dashboard" on this website?</value>
  </data>
  <data name="ElectronicNoticeRadioRequired" xml:space="preserve">
    <value>Please select a Contact Preference.</value>
  </data>
  <data name="email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="familyMembersButNoContact" xml:space="preserve">
    <value>Other family members but not [NAME]</value>
  </data>
  <data name="howToContactMe" xml:space="preserve">
    <value>I would like to receive information about this application by:</value>
  </data>
  <data name="mail" xml:space="preserve">
    <value>Standard Mail</value>
  </data>
  <data name="msgRateApply" xml:space="preserve">
    <value>Messaging rates may apply.</value>
  </data>
  <data name="noOnline" xml:space="preserve">
    <value>No. I want to get paper notices sent to me in the mail.</value>
  </data>
  <data name="onlineNotices" xml:space="preserve">
    <value>Online Notices</value>
  </data>
  <data name="paperCopyMail" xml:space="preserve">
    <value>We'll contact you by [TYPE] to let you know there's a message for you to read on your "Dashboard". &lt;br/&gt;Do you also want to get paper copies in the mail?</value>
  </data>
  <data name="phoneTypeRequired" xml:space="preserve">
    <value>Phone Type is required for Primary Phone.</value>
  </data>
  <data name="RelationshipRequired" xml:space="preserve">
    <value>Relationship is required.</value>
  </data>
  <data name="sameAsHomeAddress" xml:space="preserve">
    <value>Is your mailing address the same as your home address?</value>
  </data>
  <data name="spokenLanguage" xml:space="preserve">
    <value>Preferred Spoken Language</value>
  </data>
  <data name="spokenLanguageRequired" xml:space="preserve">
    <value>Please select one preferred spoken language.</value>
  </data>
  <data name="text" xml:space="preserve">
    <value>Text Message</value>
  </data>
  <data name="whoApplying" xml:space="preserve">
    <value>Who are you applying for health coverage for?</value>
  </data>
  <data name="whoNeedsCoverageHeader" xml:space="preserve">
    <value>Who needs health coverage?</value>
  </data>
  <data name="writtenLanguage" xml:space="preserve">
    <value>Preferred Written Language</value>
  </data>
  <data name="writtenLanguageRequired" xml:space="preserve">
    <value>Please select one preferred written language.</value>
  </data>
  <data name="yesOnline" xml:space="preserve">
    <value>Yes. I want to read my notices online.</value>
  </data>
  <data name="ConfirmSSN" xml:space="preserve">
    <value>Confirm Social Security Number</value>
  </data>
  <data name="ssn" xml:space="preserve">
    <value>Social Security Number</value>
  </data>
  <data name="ErrorApplicationCompletedDateMissing" xml:space="preserve">
    <value>Application does not have a completed date. Please contact administrator.</value>
  </data>
  <data name="ReopenAdministrator" xml:space="preserve">
    <value>Only administrators can reopen applications older than 1 year old.  Please contact a system administrator.</value>
  </data>
  <data name="ReopenSupervisor" xml:space="preserve">
    <value>Only supervisors can reopen an application.  Please contact your supervisor for assistance.</value>
  </data>
  <data name="workerNumber" xml:space="preserve">
    <value>The worker number you are trying to assign is not associated with any user.</value>
  </data>
</root>