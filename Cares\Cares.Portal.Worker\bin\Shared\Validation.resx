<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="acceptTerms" xml:space="preserve">
    <value>You must accept the terms.</value>
  </data>
  <data name="accountNotCreated" xml:space="preserve">
    <value>Internal Error. Could not create an account.</value>
  </data>
  <data name="answerAllRequired" xml:space="preserve">
    <value>Please answer all required fields marked with *</value>
  </data>
  <data name="compareFields" xml:space="preserve">
    <value>'{PropertyName}' and '{0}' fields do not match.</value>
  </data>
  <data name="dateRange" xml:space="preserve">
    <value>{PropertyName} must be between {1} and {2}</value>
  </data>
  <data name="duplicateEmail" xml:space="preserve">
    <value>Email is already registered.</value>
  </data>
  <data name="duplicateUsername" xml:space="preserve">
    <value>UserName already exists. Please enter another.</value>
  </data>
  <data name="errorSummaryHeader" xml:space="preserve">
    <value>Please fix the following</value>
  </data>
  <data name="exactLength" xml:space="preserve">
    <value>{PropertyName} must be {0} characters.</value>
    <comment>maxlength value in this case is also minlength</comment>
  </data>
  <data name="InformationalMessage" xml:space="preserve">
    <value>&lt;div id='InfoHeader'&gt;&lt;img src='../Content/images/info.png' alt='information' style='padding-bottom: 5px;padding-right: 10px;'&gt;&lt;span&gt;[HeaderMessage]&lt;/span&gt;&lt;/div&gt;&lt;br&gt;&lt;ul style='margin-left: 25px;'&gt;</value>
  </data>
  <data name="internalErrorOnAccountCreation" xml:space="preserve">
    <value>Internal error. Please contact administrator !!!</value>
  </data>
  <data name="invalidEmail" xml:space="preserve">
    <value>{PropertyName} must be a valid email.</value>
  </data>
  <data name="maxLength" xml:space="preserve">
    <value>{PropertyName} must be between 5 and  {MaxLength} characters.</value>
  </data>
  <data name="messageLi" xml:space="preserve">
    <value>&lt;li&gt;&lt;a style='cursor: pointer'data-error-field-name='[Element]'&gt;[message]&lt;/a&gt;&lt;/li&gt;</value>
  </data>
  <data name="minLength" xml:space="preserve">
    <value>{PropertyName} must be a minimum of {MinLength} characters.</value>
  </data>
  <data name="minMaxLength" xml:space="preserve">
    <value>{PropertyName} must be between {MinLength} and {MaxLength} characters.</value>
  </data>
  <data name="mustBeTrueAttribute" xml:space="preserve">
    <value>{PropertyName} must be selected.</value>
  </data>
  <data name="noFutureDate" xml:space="preserve">
    <value>{PropertyName} cannot be future date.</value>
  </data>
  <data name="noPastDate" xml:space="preserve">
    <value>{PropertyName} cannot be past date.</value>
  </data>
  <data name="patternAttribute" xml:space="preserve">
    <value>{PropertyName} is invalid.</value>
  </data>
  <data name="radioButtonSelectionRequired" xml:space="preserve">
    <value>Please select either "Yes" or "No" for all options.</value>
  </data>
  <data name="range" xml:space="preserve">
    <value>Valid range is between {1} and {2}.</value>
  </data>
  <data name="requiredAttribute" xml:space="preserve">
    <value>{PropertyName} is required.</value>
  </data>
  <data name="selectionRequired" xml:space="preserve">
    <value>Please select at least one option in fields marked as required.</value>
  </data>
  <data name="ssnPattern" xml:space="preserve">
    <value>(?:\d{3})-(?:\d{2})-(\d{4})</value>
    <comment>DO NOT TRANSLATE</comment>
  </data>
  <data name="termsAndCondition" xml:space="preserve">
    <value>You must accept terms and conditions to proceed.</value>
  </data>
  <data name="tryLater" xml:space="preserve">
    <value>Please try again later.</value>
  </data>
  <data name="userRejected" xml:space="preserve">
    <value>Unexpected error occurred. Please try again later !!!</value>
  </data>
  <data name="validDate" xml:space="preserve">
    <value>{PropertyName} must be a valid date.</value>
  </data>
  <data name="vmGenericInvalidError" xml:space="preserve">
    <value>{PropertyName} is invalid.</value>
  </data>
  <data name="vmLengthError" xml:space="preserve">
    <value> does not meet the number of characters required.</value>
  </data>
  <data name="vmMaxLengthError" xml:space="preserve">
    <value>{PropertyName} is greater than the max length.</value>
  </data>
  <data name="vmMaxValueError" xml:space="preserve">
    <value> is greater than the max value.</value>
  </data>
  <data name="vmMinLengthError" xml:space="preserve">
    <value> is less than the min length.</value>
  </data>
  <data name="vmMinValueError" xml:space="preserve">
    <value> is less than the min value.</value>
  </data>
  <data name="vmPatternError" xml:space="preserve">
    <value>{PropertyName} has invalid characters or is improperly formatted.</value>
  </data>
  <data name="vmSchemaError" xml:space="preserve">
    <value>Server-Side Schema error.  Please call support center.</value>
  </data>
  <data name="atPaymentAppliedAmt" xml:space="preserve">
    <value>Payment amount is not match with total applied amount.</value>
  </data>
  <data name="compareFieldsPI" xml:space="preserve">
    <value>Confirm Social Security Number and Social Security Number fields do not match.</value>
  </data>
  <data name="atCurrentBalanceAmt" xml:space="preserve">
    <value>Payment should not exceed amount</value>
  </data>
  <data name="invalidZipCode" xml:space="preserve">
    <value>ZipCode must be 5 or 9 characters.</value>
  </data>
  <data name="alienNumber" xml:space="preserve">
    <value>Alien number is required.</value>
  </data>
  <data name="cardReceiptptNumber" xml:space="preserve">
    <value>Card/receipt number is required.</value>
  </data>
  <data name="countryofIssuance" xml:space="preserve">
    <value>Country of Issuance is required.</value>
  </data>
  <data name="documentDescription" xml:space="preserve">
    <value>Document description is required.</value>
  </data>
  <data name="documentExpiryDate" xml:space="preserve">
    <value>Document expiry date is required.</value>
  </data>
  <data name="i94" xml:space="preserve">
    <value>I94 number is required.</value>
  </data>
  <data name="nonCitizenDocumentInformation" xml:space="preserve">
    <value>Please select at least one option.</value>
  </data>
  <data name="passportExpirydate" xml:space="preserve">
    <value>Passport expiry date is required.</value>
  </data>
  <data name="passportNumber" xml:space="preserve">
    <value>Passport Number is required.</value>
  </data>
  <data name="sevisId" xml:space="preserve">
    <value>Sevis Id is required.</value>
  </data>
  <data name="dodBeforeDob" xml:space="preserve">
    <value>Date of death cannot be prior to date of birth.</value>
  </data>
  <data name="firstNameUnborn" xml:space="preserve">
    <value>First Name should not start with Unborn.</value>
  </data>
  <data name="needDoD" xml:space="preserve">
    <value>If you provide a Death verification, you must include a DOD.</value>
  </data>
  <data name="otherEthnicityRequired" xml:space="preserve">
    <value>Other Ethnicity description is required.</value>
  </data>
  <data name="otherRaceRequired" xml:space="preserve">
    <value>Other Race description is required.</value>
  </data>
  <data name="validFirstName" xml:space="preserve">
    <value>Please enter a valid first name.</value>
  </data>
  <data name="validLastName" xml:space="preserve">
    <value>Please enter a valid last name.</value>
  </data>
  <data name="validMiddleName" xml:space="preserve">
    <value>Please enter a valid middle name.</value>
  </data>
  <data name="sexUnknown" xml:space="preserve">
    <value>Sex cannot be "Unknown".</value>
  </data>
  <data name="personNotFound" xml:space="preserve">
    <value>That person was not Found.</value>
  </data>
  <data name="cannotFutureDate" xml:space="preserve">
    <value>Date of Birth must be a valid date that is not in the future.</value>
  </data>
  <data name="cannotBeforeCreated" xml:space="preserve">
    <value>Date of Birth for unborn cannot be set before the unborn was created.</value>
  </data>
  <data name="mustNewbornForSSN" xml:space="preserve">
    <value>Please make unborn a newborn before providing a valid SSN.</value>
  </data>
  <data name="sexRequired" xml:space="preserve">
    <value>Sex is a required field.</value>
  </data>
  <data name="validMaidenName" xml:space="preserve">
    <value>Please enter a valid maiden name.</value>
  </data>
  <data name="validEmployerName" xml:space="preserve">
    <value>Please enter a valid employer name.</value>
  </data>
  <data name="newBornDOB" xml:space="preserve">
    <value>{PropertyName} must be within 3 months of received date</value>
  </data>
  <data name="lessThanDob" xml:space="preserve">
    <value>{PropertyName} cannot be less than Date of Birth.</value>
  </data>
  <data name="noLessthanTwoYears" xml:space="preserve">
    <value>{PropertyName} cannot be less than 2 years from the current date.</value>
  </data>
  <data name="PhoneNumberRequired" xml:space="preserve">
    <value>Telephone number must contain 10 digits.</value>
  </data>
  <data name="ValidPhoneExtension" xml:space="preserve">
    <value>Please enter valid phone extension.</value>
  </data>
  <data name="invalidOtherRace" xml:space="preserve">
    <value>Please enter a valid other race.</value>
  </data>
  <data name="invalidOtherPhoneOwner" xml:space="preserve">
    <value>Please enter a valid owner of the other phone number</value>
  </data>
  <data name="invalidOtherDescription" xml:space="preserve">
    <value>Please enter a valid other description</value>
  </data>
  <data name="invalidRentAmount" xml:space="preserve">
    <value>Please enter a valid rent amount</value>
  </data>
  <data name="invalidFaxNumber" xml:space="preserve">
    <value>Please enter a valid fax number.</value>
  </data>
  <data name="phoneTypeIdRequired" xml:space="preserve">
    <value>Phone Type Id is required.</value>
  </data>
  <data name="OnePrimarySponsor" xml:space="preserve">
    <value>Please select one Primary Sponsor</value>
  </data>
  <data name="ssnAndConfirmSsnMissMatchError" xml:space="preserve">
    <value>SSN and Confirm SSN must match.</value>
  </data>
  <data name="invalidAttestedAmount" xml:space="preserve">
    <value>Please enter a valid attested income amount</value>
  </data>
  <data name="invalidClaimNumber" xml:space="preserve">
    <value>Please enter a valid claim number</value>
  </data>
  <data name="invalidVerifiedAmount" xml:space="preserve">
    <value>Please enter a valid verified income amount</value>
  </data>
  <data name="marriageEndDateAfterBeginDate" xml:space="preserve">
    <value>Marriage End Date must be after Begin Date.</value>
  </data>
  <data name="mailingAddressIsRequired" xml:space="preserve">
    <value>Mailing address is required.</value>
  </data>
  <data name="ageLessThanOrEqual19" xml:space="preserve">
    <value>Age must be less than or equal to 19 years.</value>
  </data>
  <data name="addressIsRequired" xml:space="preserve">
    <value>Address is required.</value>
  </data>
  <data name="homeAddressIsRequired" xml:space="preserve">
    <value>Home address is required.</value>
  </data>
  <data name="invalidDeathVerificationSource" xml:space="preserve">
    <value>Please enter a valid death verification source</value>
  </data>
  <data name="invalidOtherSpokenLanguage" xml:space="preserve">
    <value>Please enter a valid other spoken language</value>
  </data>
  <data name="validName" xml:space="preserve">
    <value>Please enter a valid name.</value>
  </data>
  <data name="whoOwnsProperty" xml:space="preserve">
    <value>Please enter a valid who owns the property</value>
  </data>
  <data name="invalidBankOrCreditUnionOrBrokerageFirm" xml:space="preserve">
    <value>Please enter a valid name of bank/credit union/brokerage firm.</value>
  </data>
  <data name="invalidAccountNumber" xml:space="preserve">
    <value>Please enter valid account number.</value>
  </data>
  <data name="invalidCurrentBalance" xml:space="preserve">
    <value>Please enter a valid current balance amount.</value>
  </data>
  <data name="invalidOtherAccountType" xml:space="preserve">
    <value>Please enter a valid other type of account.</value>
  </data>
  <data name="amountMustBeGreaterThanOrEqualToZero" xml:space="preserve">
    <value>{PropertyName} amount must be greater than or equal to 0.</value>
  </data>
  <data name="invalidAmount" xml:space="preserve">
    <value>Please enter a valid {PropertyName} amount.</value>
  </data>
  <data name="invalidAmountReceivedOrGiven" xml:space="preserve">
    <value>Please enter a valid amount received or given.</value>
  </data>
  <data name="invalidDateClosed" xml:space="preserve">
    <value>Please enter a valid date closed.</value>
  </data>
  <data name="invalidDateSoldOrGiven" xml:space="preserve">
    <value>Please enter a valid date given or sold.</value>
  </data>
  <data name="invalidItemDescription" xml:space="preserve">
    <value>Please enter valid Item description.</value>
  </data>
  <data name="invalidRemarks" xml:space="preserve">
    <value>Please enter a valid remarks.</value>
  </data>
  <data name="invalidToWhomSoldOrGiven" xml:space="preserve">
    <value>Please enter a valid name of person to whom it was sold or given.</value>
  </data>
  <data name="receivedOrGivenAmountGreaterThanOrEqualToZero" xml:space="preserve">
    <value>Amount received or given must be greater than or equal to 0.</value>
  </data>
  <data name="ageBetween0And19" xml:space="preserve">
    <value>Age must be between 0 and 19 years.</value>
  </data>
  <data name="genericValidationError" xml:space="preserve">
    <value>Please enter a valid {PropertyName}.</value>
  </data>
  <data name="edResourceDetailDuplicateType" xml:space="preserve">
    <value>Multiple resource types of the same type specified. Please submit only one (1) resource type of each type.</value>
  </data>
  <data name="edResourceDetailRecordIncomplete" xml:space="preserve">
    <value>Please complete at least one additional field.</value>
  </data>
  <data name="edRepresentativeDuplicatePhoneType" xml:space="preserve">
    <value>Multiple phones of the same type specified. Please submit only one (1) phone of each type.</value>
  </data>
  <data name="edLifeInsuranceDetailRecordIncomplete" xml:space="preserve">
    <value>Please enter Name of Company, Policy #, Policy Value, Cash Surrender Value or Address.</value>
  </data>
  <data name="vmGenericMaxLengthError" xml:space="preserve">
    <value>{PropertyName} is greater than the max length of {MaxLength}.</value>
  </data>
  <data name="cannotBeAFutureYear" xml:space="preserve">
    <value>{PropertyName} cannot be a future year.</value>
  </data>
  <data name="cannotBeLessThanYear1950" xml:space="preserve">
    <value>Please enter a year greater or equal to 1950.</value>
  </data>
  <data name="veteranSsnRequiredForClaimNumber" xml:space="preserve">
    <value>An SSN is required when "Is VA Claim# same as SSN?" is answered "Yes".</value>
  </data>
  <data name="drasticAlertSSSN" xml:space="preserve">
    <value>That SSN has already been used by a different person. Please resolve before continuing.</value>
  </data>
  <data name="liabilityToDateMustBeGreaterThanFromDate" xml:space="preserve">
    <value>Liability Through Date must be greater than or equal to the From Date.</value>
  </data>
  <data name="mustBeGreaterThanOrEqualToZero" xml:space="preserve">
    <value>{PropertyName} must be greater than or equal to 0.</value>
  </data>
  <data name="invalidReason" xml:space="preserve">
    <value>Please enter a valid {PropertyName}.</value>
  </data>
  <data name="invalidOtherLivingArrangement" xml:space="preserve">
    <value>Please enter a valid other living arrangement.</value>
  </data>
  <data name="startDateLessThanCancelDate" xml:space="preserve">
    <value>Start Date must be less than Cancel Date</value>
  </data>
  <data name="firstOfTheMonth" xml:space="preserve">
    <value>{PropertyName} must be on the first of the month.</value>
  </data>
  <data name="onlyAwardOrDenial" xml:space="preserve">
    <value>Select either Award or Denial.</value>
  </data>
  <data name="startDateNotPriorToRetroDate" xml:space="preserve">
    <value>Start Date must be equal or greater than the retro date. ({RETRODATE})</value>
  </data>
  <data name="ageLessThanEqual120" xml:space="preserve">
    <value>Age cannot be more than 120 years.</value>
  </data>
  <data name="validVAOtherInfo" xml:space="preserve">
    <value>Please enter valid other VA information</value>
  </data>
  <data name="confirmOnlyOneProvider" xml:space="preserve">
    <value>Please confirm only one provider.</value>
  </data>
  <data name="dateGreaterThan12MonthsInTheFuture" xml:space="preserve">
    <value>From date cannot be beyond 12 months in the future from the current date.</value>
  </data>
  <data name="dateGreaterThan36MonthsInThePast" xml:space="preserve">
    <value>From date cannot be beyond 36 months in the past</value>
  </data>
  <data name="dateGreaterThan12MonthsInTheFutureGeneral" xml:space="preserve">
    <value>{PropertyName} cannot be beyond 12 months in the future from the current date.</value>
  </data>
  <data name="isFutureDate" xml:space="preserve">
    <value>{PropertyName} must be a valid future date.</value>
  </data>
  <data name="reminderOtherDescription" xml:space="preserve">
    <value>Please enter a valid Other description.</value>
  </data>
  <data name="reminderOtherDescriptionLength" xml:space="preserve">
    <value>Other description must be between 0 and 250 characters.</value>
  </data>
  <data name="reminderAppIdOrReminderId" xml:space="preserve">
    <value>Provide either an Application Id or a Worker Reminder Id</value>
  </data>
  <data name="dateMustBeGreaterThanTheDateOfBirth" xml:space="preserve">
    <value>Date must be greater than the Date of Birth.</value>
  </data>
  <data name="cancelDateWithinOnYearOfStartDate" xml:space="preserve">
    <value>Cancel Date must be within one year of the Start Date</value>
  </data>
  <data name="overlappingSegmentsNotAllowed" xml:space="preserve">
    <value>Overlapping segments are not allowed</value>
  </data>
  <data name="noGaps" xml:space="preserve">
    <value>A gap in coverage is not allowed</value>
  </data>
  <data name="startDatePriorToRetro" xml:space="preserve">
    <value>Start Date cannot be prior to Retro</value>
  </data>
  <data name="qmbMustBeFirstOfNextMonth" xml:space="preserve">
    <value>QMB Start Date must be first of next month after award month.</value>
  </data>
  <data name="minLengthWithOutSpecialCharacters" xml:space="preserve">
    <value>{PropertyName} must be a minimum of 3 characters, special characters not counted.</value>
  </data>
  <data name="oneYearOfCoverage" xml:space="preserve">
    <value>Enrollment coverage must be up to one year past the Award Date.</value>
  </data>
  <data name="noLessThanOneYear" xml:space="preserve">
    <value>The worker must specify the termination reason.</value>
  </data>
  <data name="noAnySegmentsAfterTerminatedSegment" xml:space="preserve">
    <value>Segment cannot be created after terminating.</value>
  </data>
  <data name="noMultipleTerminatedSegments" xml:space="preserve">
    <value>Terminated segment cannot have multiple segment.</value>
  </data>
  <data name="cannotBeLessThanYear1900" xml:space="preserve">
    <value>Please enter a year greater or equal to 1900.</value>
  </data>
  <data name="cannotExceedOneFromCurrentYear" xml:space="preserve">
    <value>{PropertyName} cannot exceed one year from the current year.</value>
  </data>
  <data name="invalidAlienNumber" xml:space="preserve">
    <value>Please enter a valid Alien number.</value>
  </data>
  <data name="otherInsuranceName" xml:space="preserve">
    <value>Please enter valid other insurance name.</value>
  </data>
  <data name="validateQuarterlyFrequency" xml:space="preserve">
    <value>For the Quarterly frequency the months should be selected as either 'March, June, September, or December'</value>
  </data>
</root>