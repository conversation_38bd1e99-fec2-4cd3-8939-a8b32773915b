﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class MoreAboutHousehold {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MoreAboutHousehold() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.MoreAboutHousehold", typeof(MoreAboutHousehold).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How old was [NAME], (DOB:[DOB]) when [HE/SHE] left the Foster Care system?.
        /// </summary>
        public static string ageWhenLeftFosterCare {
            get {
                return ResourceManager.GetString("ageWhenLeftFosterCare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age when left Foster Care is required..
        /// </summary>
        public static string ageWhenLeftFosterCareRequired {
            get {
                return ResourceManager.GetString("ageWhenLeftFosterCareRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        public static string dueDate {
            get {
                return ResourceManager.GetString("dueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who were ever in Foster Care.
        /// </summary>
        public static string everInFosterCare {
            get {
                return ResourceManager.GetString("everInFosterCare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who want to apply for, or continue to receive, Family Planning services.
        /// </summary>
        public static string familyPlanning {
            get {
                return ResourceManager.GetString("familyPlanning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Females Ages 19-55 or Males Ages 21 and over, may be eligible for Family Planning (Birth Control or Sterilization for males only) Services. (NOTE: You are not eligible for this program if you have had your tubes tied, been sterilized or on Medicare)..
        /// </summary>
        public static string familyPlanningNote {
            get {
                return ResourceManager.GetString("familyPlanningNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Was [NAME], (DOB:[DOB]) getting health care through the Alabama Medicaid Agency?.
        /// </summary>
        public static string fosterCarePersonUsedMedicaid {
            get {
                return ResourceManager.GetString("fosterCarePersonUsedMedicaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who are full-time students.
        /// </summary>
        public static string fullTimeStudents {
            get {
                return ResourceManager.GetString("fullTimeStudents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full-time students selection is required..
        /// </summary>
        public static string fullTimeStudentsSelectionRequired {
            get {
                return ResourceManager.GetString("fullTimeStudentsSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who have a physical disability or mental health condition that limits their ability to work, attend school, or take care of their daily needs.
        /// </summary>
        public static string havePhysicalDisability {
            get {
                return ResourceManager.GetString("havePhysicalDisability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More About This Household.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How many babies is [NAME], (DOB:[DOB]) expecting during this pregnancy?.
        /// </summary>
        public static string howManyBabies {
            get {
                return ResourceManager.GetString("howManyBabies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who are American Indian or Alaska Native.
        /// </summary>
        public static string IndianOrAlaskan {
            get {
                return ResourceManager.GetString("IndianOrAlaskan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB:[DOB]) sterile?.
        /// </summary>
        public static string isSterileOrNot {
            get {
                return ResourceManager.GetString("isSterileOrNot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who have had a miscarriage within the past three months.
        /// </summary>
        public static string Miscarriage {
            get {
                return ResourceManager.GetString("Miscarriage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Miscarriage Date.
        /// </summary>
        public static string MiscarriageDate {
            get {
                return ResourceManager.GetString("MiscarriageDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of babies selection is required..
        /// </summary>
        public static string nbrOfBabiesSelectionRequired {
            get {
                return ResourceManager.GetString("nbrOfBabiesSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who need help with daily living activities(bathing, dressing, using the bathroom), or live in a medical facility or nursing home.
        /// </summary>
        public static string needHelpWithDailyActivities {
            get {
                return ResourceManager.GetString("needHelpWithDailyActivities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None of these people.
        /// </summary>
        public static string noneOfThese {
            get {
                return ResourceManager.GetString("noneOfThese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do one or both of [NAME]&apos;s, (DOB:[DOB]) parents live in Alabama?.
        /// </summary>
        public static string parentLivingInAlabama {
            get {
                return ResourceManager.GetString("parentLivingInAlabama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME], (DOB:[DOB]) have a parent living in the same state where [NAME], (DOB:[DOB]) goes to school?.
        /// </summary>
        public static string parentLivingSameStateAsSchool {
            get {
                return ResourceManager.GetString("parentLivingSameStateAsSchool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Have any of these people had a baby in the past 3 months?.
        /// </summary>
        public static string pastPregnancy {
            get {
                return ResourceManager.GetString("pastPregnancy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Newborn DOB.
        /// </summary>
        public static string pastPregnancyDate {
            get {
                return ResourceManager.GetString("pastPregnancyDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must be an Alabama resident at the time of delivery to be eligible for the 12 months postpartum extension.
        /// </summary>
        public static string pastPregnancyNote {
            get {
                return ResourceManager.GetString("pastPregnancyNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who are pregnant.
        /// </summary>
        public static string pregnant {
            get {
                return ResourceManager.GetString("pregnant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have selected a pregnant person to be sterile. Please fix your selection, a pregnant person cannot be sterile..
        /// </summary>
        public static string pregnantCannotBeSterile {
            get {
                return ResourceManager.GetString("pregnantCannotBeSterile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State in Foster Care selection is required..
        /// </summary>
        public static string stateInFosterCareRequired {
            get {
                return ResourceManager.GetString("stateInFosterCareRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who are veteran or dependent of veteran.
        /// </summary>
        public static string VeteranQuestion {
            get {
                return ResourceManager.GetString("VeteranQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In what state was [NAME], (DOB:[DOB]) in the Foster Care program?.
        /// </summary>
        public static string whatStateInFosterCare {
            get {
                return ResourceManager.GetString("whatStateInFosterCare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to if you are interested in applying for WIC (for pregnant or breast-feeding women and children under age five) you can apply at your local County  Health Department..
        /// </summary>
        public static string WICText {
            get {
                return ResourceManager.GetString("WICText", resourceCulture);
            }
        }
    }
}
