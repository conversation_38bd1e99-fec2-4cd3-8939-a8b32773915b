﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="addAnotherJob" xml:space="preserve">
    <value>Add another job</value>
  </data>
  <data name="addAnotherSocialSecurity" xml:space="preserve">
    <value>Add another Social Security benefit amount</value>
  </data>
  <data name="addAnotherUnemployment" xml:space="preserve">
    <value>Add another unemployment source</value>
  </data>
  <data name="alimonyQuestion" xml:space="preserve">
    <value>How much does [NAME] receive from alimony?</value>
  </data>
  <data name="amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="amountIncomeTypeChangeRequired" xml:space="preserve">
    <value>Please enter amount you expect to change for selected income type.</value>
  </data>
  <data name="capitalGainsCurrentMonth" xml:space="preserve">
    <value>Capital Gains - Current Month</value>
  </data>
  <data name="capitalGainsCurrentYear" xml:space="preserve">
    <value>Capital Gains - Current Year</value>
  </data>
  <data name="capitalGainsMonthQuestion" xml:space="preserve">
    <value>How much does [NAME] expect to receive from net capital gains (the profit after subtracting capital losses) this month?</value>
  </data>
  <data name="capitalGainsYearQuestion" xml:space="preserve">
    <value>How much does [NAME] expect to receive from net capital gains (the profit after subtracting capital losses) this year?</value>
  </data>
  <data name="daysWeek" xml:space="preserve">
    <value>Days per week</value>
  </data>
  <data name="deductionAmountRequired" xml:space="preserve">
    <value>Please enter deduction amount.</value>
  </data>
  <data name="deductionFrequencyRequired" xml:space="preserve">
    <value>Deduction payment frequency is required</value>
  </data>
  <data name="deductionTypeRequired" xml:space="preserve">
    <value>Deduction type is required</value>
  </data>
  <data name="employerNameRequired" xml:space="preserve">
    <value>Employer name is required</value>
  </data>
  <data name="expiryDate" xml:space="preserve">
    <value>Expiry Date</value>
  </data>
  <data name="farmingFishingQuestion" xml:space="preserve">
    <value>How much does [NAME] receive from net farming or fishing income (the profit after subtracting costs)?</value>
  </data>
  <data name="futureIncomeAmount" xml:space="preserve">
    <value>How much will this income be in [COVERAGEYEAR]?</value>
  </data>
  <data name="futureIncomeAmountRequired" xml:space="preserve">
    <value>Future income type amount is required</value>
  </data>
  <data name="futureIncomeType" xml:space="preserve">
    <value>You've told us that [NAME]'s current monthly income doesn't include any of the following.  Is there another type of income that may start in a future month?</value>
  </data>
  <data name="futureIncomeTypeSelectionRequired" xml:space="preserve">
    <value>Future income type is required</value>
  </data>
  <data name="haveAnyIncome" xml:space="preserve">
    <value>Does [NAME] have any type of income?</value>
  </data>
  <data name="hoursWeek" xml:space="preserve">
    <value>Hours per week</value>
  </data>
  <data name="howMuchDoYouGetPaid" xml:space="preserve">
    <value>How much does [NAME] get paid (before taxes or anything is taken out)?  Tell us about the regular pay from this job that [NAME] receives as well as any one-time amounts this month, like a bonus or a severage payment.</value>
  </data>
  <data name="howMuchWorkPerWeekAtJobD" xml:space="preserve">
    <value>How many days does [NAME] usually work per week at this job?</value>
  </data>
  <data name="howMuchWorkPerWeekAtJobH" xml:space="preserve">
    <value>How many hours does [NAME] usually work per week at this job?</value>
  </data>
  <data name="howOftenDoYouPayThisAmt" xml:space="preserve">
    <value>How often does [NAME] pay this amount?</value>
  </data>
  <data name="howOftenDoYouReceiveThisAmt" xml:space="preserve">
    <value>How often does [NAME] receive this amount?</value>
  </data>
  <data name="incomeAmountRequired" xml:space="preserve">
    <value>Amount is required</value>
  </data>
  <data name="incomeFrequencyRequired" xml:space="preserve">
    <value>Amount received frequency is required</value>
  </data>
  <data name="incomeInfoHeader" xml:space="preserve">
    <value>Income Information</value>
  </data>
  <data name="incomeSourceRequired" xml:space="preserve">
    <value>Income source is required</value>
  </data>
  <data name="incomeSummaryHeader" xml:space="preserve">
    <value>[NAME]'s Income Summary</value>
  </data>
  <data name="incomeSummaryHowOften" xml:space="preserve">
    <value>How Often</value>
  </data>
  <data name="incomeSummaryMonthlyAmt" xml:space="preserve">
    <value>Monthly Amount</value>
  </data>
  <data name="incomeSummarySource" xml:space="preserve">
    <value>Income Source</value>
  </data>
  <data name="incomeSummaryYearlyAmt" xml:space="preserve">
    <value>Yearly Amount</value>
  </data>
  <data name="IncomeTypeExpectedToChangeRequired" xml:space="preserve">
    <value>Income type expected to change is required</value>
  </data>
  <data name="incomeTypeOfSelectedSourceRequired" xml:space="preserve">
    <value>Income type of selected sources is required</value>
  </data>
  <data name="incomeTypes" xml:space="preserve">
    <value>Please include income from these sources: Job, Self-employment, Social Security benefits, Unemployment, Retirement, Pension, Capital gains, Investment income, Rental or Royalty income, Farming or Fishing income, Alimony received and Other income.</value>
  </data>
  <data name="incomeTypeSelection" xml:space="preserve">
    <value>Income Type Selection</value>
  </data>
  <data name="incomeTypeSelectionRequired" xml:space="preserve">
    <value>Selection of a type of income is required</value>
  </data>
  <data name="incomeWorkHoursRequired" xml:space="preserve">
    <value>Number of hours worked is required</value>
  </data>
  <data name="indianAlaskanQuestion" xml:space="preserve">
    <value>Is any of the above income from these sources?</value>
  </data>
  <data name="invalidWeeklyLoadforDaily" xml:space="preserve">
    <value>Please select a value between 1 and 7.</value>
  </data>
  <data name="invalidWeeklyLoadforWeekly" xml:space="preserve">
    <value>Please select a value between 1 and 168.</value>
  </data>
  <data name="investmentQuestion" xml:space="preserve">
    <value>How much does [NAME] receive from investment income, like interest and dividends?</value>
  </data>
  <data name="itWillBeAboutThisMuch" xml:space="preserve">
    <value>In [COVERAGEYEAR] it will be about this much</value>
  </data>
  <data name="nameOfEmployer" xml:space="preserve">
    <value>Name of employer</value>
  </data>
  <data name="noZeroIncomeAllowed" xml:space="preserve">
    <value>Income Amount must be greater than 0.</value>
  </data>
  <data name="otherIncomeDetail" xml:space="preserve">
    <value>You don’t need to tell us about child support, veteran’s payments, or Supplemental Security Income (SSI). Click here to learn more about what not to list.</value>
  </data>
  <data name="otherIncomeTypeRequired" xml:space="preserve">
    <value>A selection from Other Income is required</value>
  </data>
  <data name="otherTaxDeduction" xml:space="preserve">
    <value>Other deduction [NAME] could take on [HIS/HER] [COVERAGEYEAR] tax return</value>
  </data>
  <data name="pensionQuestion" xml:space="preserve">
    <value>How much does [NAME] receive from this pension account?</value>
  </data>
  <data name="profitLossRequired" xml:space="preserve">
    <value>Please select either 'Profit' or 'Loss'</value>
  </data>
  <data name="rentalQuestion" xml:space="preserve">
    <value>How much does [NAME] receive from net rental or royalty income (the profit after subtracting costs)?</value>
  </data>
  <data name="retirementQuestion" xml:space="preserve">
    <value>How much does [NAME] receive from retirement account(s)?  Include amounts received as a distribution from a retirement investment even if [NAME] isn't retired.</value>
  </data>
  <data name="schoolGrantQuestion" xml:space="preserve">
    <value>Is any of this income from a scholarship or grant used to pay for education expenses?</value>
  </data>
  <data name="selectOtherIncomeTypes" xml:space="preserve">
    <value>Select any other type of income that [NAME] receives</value>
  </data>
  <data name="selfEmploymentQuestion" xml:space="preserve">
    <value>How much net income (profits once business expenses are paid) will [NAME] receive from this self-employment this month? (If the costs for this self-employment are more than the amount [NAME] expects to earn, you may enter a negative number.)</value>
  </data>
  <data name="socialSecurityQuestion" xml:space="preserve">
    <value>How much does [NAME] receive from Social Security?  Do not include Supplemental Security Income(SSI).</value>
  </data>
  <data name="taxDeductionsQuestion" xml:space="preserve">
    <value>Does [NAME] pay alimony, student loan interest, or any other deductions that get reported on the front page of a federal income tax return form 1040?  This could make the cost of coverage a little lower.</value>
  </data>
  <data name="typeOfWork" xml:space="preserve">
    <value>Type of work</value>
  </data>
  <data name="typeOfWorkRequired" xml:space="preserve">
    <value>Type of work is required</value>
  </data>
  <data name="unemploymentExpireQuestion" xml:space="preserve">
    <value>Is there is a date that the unemployment benefits are set to expire?</value>
  </data>
  <data name="unemploymentQuestion" xml:space="preserve">
    <value>From what state government or former employer does [NAME] receive unemployment benefits?</value>
  </data>
  <data name="unemploymentSource" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="unemploymentSourceRequired" xml:space="preserve">
    <value>Unemployment Income Source is required</value>
  </data>
  <data name="unEmplyExpDate" xml:space="preserve">
    <value>Please enter a date at which the unemployment benefits are going to expire.</value>
  </data>
  <data name="whichFutureIncomeType" xml:space="preserve">
    <value>Which income type do you expect to begin in a future month?</value>
  </data>
  <data name="whichIncomeFromSource" xml:space="preserve">
    <value>Which income type is from this source?</value>
  </data>
  <data name="whichIncomeTypeExpectedToChange" xml:space="preserve">
    <value>Which income type do you expect to change?</value>
  </data>
  <data name="whichIncomeWillChange" xml:space="preserve">
    <value>Which income type do you expect will change?</value>
  </data>
  <data name="workLoad" xml:space="preserve">
    <value>Work Load</value>
  </data>
  <data name="yearlyAmountCalculated" xml:space="preserve">
    <value>Based on what you told us, if [NAME]'s income is steady each month, then it's about [AMOUNT] per year.  Is this how much you think [NAME] will receive in [COVERAGEYEAR]?</value>
  </data>
  <data name="yearlyAmountManual" xml:space="preserve">
    <value>Based on what you know today, how much do you think [NAME] will receive in [COVERAGEYEAR]?</value>
  </data>
  <data name="employerPhone" xml:space="preserve">
    <value>Telephone number must contain 10 digits.</value>
  </data>
</root>