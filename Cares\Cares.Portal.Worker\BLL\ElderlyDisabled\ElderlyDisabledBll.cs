﻿using AlabamaConnectExpress.Controllers.Letters;
using AutoMapper;
using Cares.Api.Application;
using Cares.Api.ElderlyDisabled;
using Cares.Api.Hub;
using Cares.Api.Infrastructure;
using Cares.Api.Infrastructure.Helper;
using Cares.Api.Infrastructure.WebAPI;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Api.Messages.Mapper;
using Cares.Api.Messages.Representatives;
using Cares.Controllers.Application;
using Cares.Data.DataAbstractionLayer;
using Cares.Data.DataAbstractionLayer.NonMAGIIncome;
using Cares.Models.Application.Representative;
using Cares.Portal.Infrastructure;
using Cares.Portal.Infrastructure.VGSecurity;
using Cares.Portal.Worker.Models.Models;
using Cares.Portal.Worker.Models.Models.Application.ViewModel;
using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel;
using Cares.Portal.Worker.Models.Models.Enrollment.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Web.Mvc;
using static Cares.Api.Infrastructure.Constants;
using ENUMS = Cares.Api.Infrastructure.Enums;

namespace AlabamaConnectExpress.BLL.ElderlyDisabled
{
    public class ElderlyDisabledBll : BaseBll
    {
        private const string NotFoundOrNotEandD = "The application was either not found, or is not an Elderly and Disabled app.";
        public const string ExpediteImportProcessingMessage = "This application is being imported from Expedite. If you wish to cancel this import process, please contact the CARES help desk with this message and provide the Expedite Import Id: {0}";
        public const string OverlappingSegmentsErrorMsg = "This segment's dates create an overlap with existing segment(s).";
        public const string StartDatePriorToRetroValidationError = "Start Date cannot be prior to retro date";

        // SecurityComponent, used for updating the LandingNavBarViewModel
        private ISecurityComponent _securityComponent = null;

        public ElderlyDisabledBll(WpBaseController controller) : base(controller)
        {
            _securityComponent = new BLL.Landing.LandingSecurityComponent();
        }

        /// <summary>
        /// Given an application ID, calls AppTier and returns a filled in view-model
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledApplicationViewModel> GetApplicationViewModel(long applicationId, Guid tokenId)
        {
            ElderlyDisabledApplicationViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledApplicationDto appDto = await edApi.GetApplication(applicationId, CallingUsername, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledApplicationViewModel>(appDto);

                // Populate the various E&D Detail ProgramId's for drop-downs based on the ElderlyDisabledProgramId & ProgramOrCategory values:
                switch (viewModel.ApplicationElderlyDisabledDetail.ProgramOrCategory)
                {
                    case ENUMS.enumElderlyDisabledMedicaidPrograms.HomeAndCommunityBasedWaiverProgram:
                        viewModel.ApplicationElderlyDisabledDetail.WaiverProgramId = viewModel.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId;
                        break;
                    case ENUMS.enumElderlyDisabledMedicaidPrograms.SSIRelatedPrograms:
                        viewModel.ApplicationElderlyDisabledDetail.SsiRelatedProgramId = viewModel.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId;
                        break;
                    case ENUMS.enumElderlyDisabledMedicaidPrograms.OtherPrograms:
                        viewModel.ApplicationElderlyDisabledDetail.OtherProgramId = viewModel.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId;
                        break;
                }
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledApplicationViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves an E&D application
        /// </summary>
        /// <param name="app">The app.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SaveApplication(ElderlyDisabledApplicationViewModel app, Guid tokenId)
        {
            // Based on the user's selection stored in ProgramOrCategory, set the ElderlyDisabledProgramId
            switch (app.ApplicationElderlyDisabledDetail.ProgramOrCategory)
            {
                case ENUMS.enumElderlyDisabledMedicaidPrograms.HomeAndCommunityBasedWaiverProgram:
                    app.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId = app.ApplicationElderlyDisabledDetail.WaiverProgramId;
                    break;
                case ENUMS.enumElderlyDisabledMedicaidPrograms.SSIRelatedPrograms:
                    app.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId = app.ApplicationElderlyDisabledDetail.SsiRelatedProgramId;
                    break;
                case ENUMS.enumElderlyDisabledMedicaidPrograms.OtherPrograms:
                    app.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId = app.ApplicationElderlyDisabledDetail.OtherProgramId;
                    break;
                default:
                    app.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId = (byte)app.ApplicationElderlyDisabledDetail.ProgramOrCategory;
                    break;
            }

            // Map this to a data transfer object
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledApplicationDto application = mapper.Map<ElderlyDisabledApplicationDto>(app);
            application.UserName = CallingUsername;

            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var result = await edApi.SaveApplication(application, CallingUsername, tokenId);
            return result;
        }

        /// <summary>
        /// Get rep info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledRepresentativesViewModel> GetRepresentativeInfo(long applicationId, string username, Guid tokenId)
        {
            // Check if found and is an E&D app.
            // Unfortunately AuthorizedUserAppTierApi.GetRepresentativeInfo() doesn't give us this
            // information, so we check it this way:
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();

            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            var result = await edApi.GetRepresentativeInfo(applicationId, username, tokenId);

            if (okToDisplay(result))
            {
                // Remove all empty sponsor details from the result.
                result.SponsorData.HouseholdMembers[0].SponsorDetail.RemoveAll(s => s.SponsorId == null);

                // Delete extra empty phones because the E&D representative screen's phone sections use
                // a list with Add/Remove buttons
                // The MPS page fills in four PhoneNumbers, some of which are empty
                foreach (var hhm in result.SponsorData.HouseholdMembers)
                {
                    foreach (var sponsorDetail in hhm.SponsorDetail)
                    {
                        sponsorDetail.PhoneList.RemoveAll(p => string.IsNullOrEmpty(p.PhoneNumber) && string.IsNullOrEmpty(p.PhoneID));
                    }
                }

                // Create the view model
                ElderlyDisabledRepresentativesViewModel viewModel = ElderlyDisabledMapper.MapToElderlyDisableRepresentativesViewModel(applicationId, result);

                return viewModel;
            }

            // An error occurred.
            return await generateErroredViewModel<ElderlyDisabledRepresentativesViewModel>(result, tokenId);
        }

        /// <summary>
        /// Save rep info
        /// </summary>
        /// <param name="representativeData">The representative data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveRepresentativeInfo(ElderlyDisabledRepresentativesViewModel representativeData, string username, Guid tokenId)
        {
            BaseApiMessage result = new BaseApiMessage();

            // First, perform Deletes.
            // E&D Representative code relies on MSP Representative implementation, which performs realtime deletes.
            // All E&D forms do not use realtime deletes, but rely on an IsDeleted flag.
            // During this save, we will first implement the real-time deletes, then continue with the saves
            foreach (var sponsor in representativeData.Sponsors)
            {
                // If marked IsDeleted, and has a SponsorId (wasn't a newly created row):
                if (sponsor.IsDeleted && (sponsor.SponsorId ?? 0) > 0)
                {
                    var deleteResult = await DeleteRepresentativeInfo(sponsor.SponsorId ?? 0, (int)representativeData.ApplicationId, username, tokenId);
                    if (!deleteResult.IsSuccessful)
                    {
                        return deleteResult;
                    }
                }
            }
            // Remove these from the list
            representativeData.Sponsors = representativeData.Sponsors.Where(s => !s.IsDeleted).ToList();

            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();

            RepresentativeInfo repDto = new RepresentativeInfo()
            {
                ApplicationId = (int)representativeData.ApplicationId,
                SponsorData = new Sponsor()
                {
                    HasSponsor = true,
                    HouseholdMembers = new List<Cares.Models.Application.Representative.HouseholdMember>()
                },
                UpdatedBy = username
            };

            Cares.Models.Application.Representative.HouseholdMember onlyMember = new Cares.Models.Application.Representative.HouseholdMember()
            {
                PersonId = representativeData.ContactPersonId,
                IsSelected = true,
                SponsorDetail = new List<SponsorDetail>()
            };

            if (representativeData.Sponsors != null)
            {
                foreach (var sponsor in representativeData.Sponsors)
                {
                    // Sanity check.  If the user didn't add any, this ends up null:
                    if (sponsor.PhoneList.Phones == null)
                    {
                        sponsor.PhoneList.Phones = new List<Cares.Portal.Worker.Models.Models.Person.ViewModel.PhoneAndPersonPhoneViewModel>();
                    }

                    // Delete any empty phone rows.  Rows the user accidentally added then delete
                    sponsor.PhoneList.Phones.RemoveAll(phone => phone.PhoneNo == null);

                    // The sponsor
                    SponsorDetail nextSponsor = mapper.Map<SponsorDetail>(sponsor);
                    nextSponsor.Addresses = new List<Cares.Models.Shared.Address>();

                    // The address
                    if (sponsor.Address != null)
                    {
                        Cares.Models.Shared.Address nextSponsorAddress = mapper.Map<Cares.Models.Shared.Address>(sponsor.Address);
                        nextSponsor.Addresses.Add(nextSponsorAddress);
                    }

                    // Check for phone deletes
                    foreach (var phone in nextSponsor.PhoneList)
                    {
                        if (phone.isDeleted)
                        {
                            // The way phones are currently deleted is by clearing out their number
                            phone.PhoneNumber = string.Empty;
                        }
                    }

                    onlyMember.SponsorDetail.Add(nextSponsor);
                }
            }

            repDto.SponsorData.HouseholdMembers.Add(onlyMember);

            return await edApi.SaveRepresentativeInfo(repDto, username, tokenId);
        }

        /// <summary>
        /// Deletes Representative Info
        /// </summary>
        /// <param name="sponsorId">The sponsor identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> DeleteRepresentativeInfo(long sponsorId, int applicationId, string username, Guid tokenId)
        {
            AuthorizedUserAppTierApi auApi = new AuthorizedUserAppTierApi();
            return await auApi.DeleteRepresentativeInfo(sponsorId, applicationId, username, tokenId);
        }

        /// <summary>
        /// Gets Household Members
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledHouseholdMembersViewModel> GetHouseholdMembers(long applicationId, Guid tokenId)
        {
            ElderlyDisabledHouseholdMembersViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledHouseholdMembersDto appDto = await edApi.GetHouseholdMembers(applicationId, CallingUsername, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();

                viewModel = mapper.Map<ElderlyDisabledHouseholdMembersViewModel>(appDto);

                foreach (var hhMember in viewModel.HouseholdMembers)
                {
                    if (hhMember.Age == -1)
                    {
                        hhMember.AgeNotProvided = true;
                        hhMember.Age = 0;   //  This is needed if record had no age provided and is saved with no modifications, which triggers validator
                    }
                    else
                    {
                        hhMember.AgeForDisplay = hhMember.Age;
                    }
                }
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledHouseholdMembersViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves household info
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveHouseholdMembers(ElderlyDisabledHouseholdMembersViewModel formData, string username, Guid tokenId)
        {
            // First, perform Deletes.
            // E&D Household code relies on MSP Household implementation, which performs realtime deletes.
            // All E&D forms do not use realtime deletes, but rely on an IsDeleted flag.
            // During this save, we will first implement the real-time deletes, then continue with the saves
            foreach (var householdMember in formData.HouseholdMembers)
            {
                // If marked IsDeleted, and has a ApplicationNonMagiNoSsnPersonId (wasn't a newly created row):
                if (householdMember.IsDeleted && householdMember.ApplicationNonMagiNoSsnPersonId > 0)
                {
                    var deleteResult = await DeleteHouseholdMember(householdMember.ApplicationNonMagiNoSsnPersonId, username, tokenId);

                    if (!deleteResult.IsSuccessful)
                    {
                        return deleteResult;
                    }
                }
            }

            // Remove these from the list
            formData.HouseholdMembers = formData.HouseholdMembers.Where(s => !s.IsDeleted).ToList();

            foreach (var hhMember in formData.HouseholdMembers)
            {
                //  If age not provided, set age to -1, else insert the value from age for display
                hhMember.Age = hhMember.AgeNotProvided ? -1 : (int)hhMember.AgeForDisplay;
            }

            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledHouseholdMembersDto householdData = mapper.Map<ElderlyDisabledHouseholdMembersDto>(formData);

            householdData.UpdatedBy = username;

            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var result = await edApi.SaveHouseholdMembers(householdData, username, tokenId);

            return result;
        }

        /// <summary>
        /// Deletes household member
        /// </summary>
        /// <param name="applicationNonMagiNoSsnPersonId">The application non-magi no ssn person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param
        /// <returns></returns>
        public async Task<BaseApiMessage> DeleteHouseholdMember(int applicationNonMagiNoSsnPersonId, string username, Guid tokenId)
        {
            return await new ElderlyDisabledApi().DeleteHouseholdMember(applicationNonMagiNoSsnPersonId, username, tokenId);
        }

        /// <summary>
        /// Gets Property info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledPropertyViewModel> GetPropertyInformation(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledPropertyViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledPropertyDto appDto = await edApi.GetPropertyInformation(applicationId, username, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledPropertyViewModel>(appDto);

                // Sanity check:  Ensure the lists exist even if no data was there:
                viewModel.PropertyParcels = viewModel.PropertyParcels ?? new List<ElderlyDisabledPropertyParcelViewModel>();
                viewModel.PreviousProperties = viewModel.PreviousProperties ?? new List<ElderlyDisabledPropertyPreviousViewModel>();
                viewModel.PropertyMobileHomes = viewModel.PropertyMobileHomes ?? new List<ElderlyDisabledPropertyMobileHomeViewModel>();
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledPropertyViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves property info
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SavePropertyInformation(ElderlyDisabledPropertyViewModel viewModel, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledPropertyDto propertyData = mapper.Map<ElderlyDisabledPropertyDto>(viewModel);
            propertyData.UpdatedBy = username;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var result = await edApi.SavePropertyInformation(propertyData, username, tokenId);
            return result;
        }

        /// <summary>
        /// Gets spouse info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledSpouseViewModel> GetSpouseInfo(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledSpouseDto spouseDto = await edApi.GetSpouseInfo(applicationId, username, tokenId);
            ElderlyDisabledSpouseViewModel viewModel;

            if (okToDisplay(spouseDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();

                viewModel = mapper.Map<ElderlyDisabledSpouseViewModel>(spouseDto);

                // For Divorce or Widowed, make sure there is at least one Former Spouse row
                if (viewModel.MustHaveOneFormerSpouse)
                {
                    if (viewModel.FormerSpouses.Count() == 0)
                    {
                        viewModel.FormerSpouses.Add(new ElderlyDisabledFormerSpouseViewModel() { ApplicationId = viewModel.ApplicationId });
                    }
                }
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledSpouseViewModel>(spouseDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves Spouse data
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SaveSpouseInfo(ElderlyDisabledSpouseViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledSpouseDto spouseData = mapper.Map<ElderlyDisabledSpouseDto>(formData);

            spouseData.UpdatedBy = username;

            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var result = await edApi.SaveSpouseInfo(spouseData, username, tokenId);
            return result;
        }

        /// <summary>
        /// Gets resource info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledResourceViewModel> GetResourceInformation(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledResourceViewModel viewModel;

            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledResourceDto appDto = await edApi.GetResourceInformation(applicationId, username, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledResourceViewModel>(appDto);

                // Sanity check:  Ensure the lists exist even if no data was there:
                viewModel.ResourceBankDetails = viewModel.ResourceBankDetails ?? new List<ElderlyDisabledResourceBankDetailViewModel>();
                viewModel.ResourceDetails = viewModel.ResourceDetails ?? new List<ElderlyDisabledResourceDetailViewModel>();
                viewModel.ResourceTransfers = viewModel.ResourceTransfers ?? new List<ElderlyDisabledResourceTransferViewModel>();

                // Calculate TotalTransferPenaltyAmount
                foreach (var transfer in viewModel.ResourceTransfers)
                {
                    foreach (var transferDetail in transfer.EDTransferMonthDetail)
                    {
                        transferDetail.TotalTransferPenaltyAmount = (transferDetail.AmountGiven ?? 0) - (transferDetail.AmountReturned ?? 0);
                    }
                }
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledResourceViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves Resource info.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveResourceInformation(ElderlyDisabledResourceViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledResourceDto resourceData = mapper.Map<ElderlyDisabledResourceDto>(formData);
            resourceData.UpdatedBy = username;

            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var result = await edApi.SaveResourceInformation(resourceData, username, tokenId);

            return result;
        }

        /// <summary>
        /// Getting medical insurance information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledMedicalInsuranceViewModel> GetMedicalInsuranceInfo(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledMedicalInsuranceViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledMedicalInsuranceDto appDto = await edApi.GetMedicalInsuranceInfo(applicationId, username, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledMedicalInsuranceViewModel>(appDto);

                // Sanity check:  Ensure the lists exist even if no data was there:
                viewModel.MedicalInsuranceDetails = viewModel.MedicalInsuranceDetails ?? new List<ElderlyDisabledMedicalInsuranceDetailViewModel>();
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledMedicalInsuranceViewModel>(appDto, tokenId);
            }

            return viewModel;
        }


        /// <summary>
        /// Getting Application details for QIT or Lien.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledApplicationDetailQitLienDto> GetApplicationDetailQitLien(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledApplicationDetailQitLienDto appDto = await edApi.GetApplicationDetailQitLien(applicationId, username, tokenId);

            if (okToDisplay(appDto))
            {

                return appDto;
            }

            return new ElderlyDisabledApplicationDetailQitLienDto();

        }

        /// <summary>
        /// Save medical insurance information.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveMedicalInsuranceInfo(ElderlyDisabledMedicalInsuranceViewModel viewModel, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo = mapper.Map<ElderlyDisabledMedicalInsuranceDto>(viewModel);
            medicalInsuranceInfo.UpdatedBy = username;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            return await edApi.SaveMedicalInsuranceInfo(medicalInsuranceInfo, username, tokenId);
        }

        /// <summary>
        /// Gets E&D veteran info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledVeteranViewModel> GetVeteranDetails(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledVeteranViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledVeteranDto appDto = await edApi.GetVeteranDetails(applicationId, username, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledVeteranViewModel>(appDto);

                // Set the IsClaimNumberSameAsSsn.
                // Since this value is derived, and not stored, this is done here
                // First, get the current SSN based on the relationship
                string theSSN = null;
                ENUMS.enumRelationship relationship = (ENUMS.enumRelationship)(Convert.ToInt32(viewModel.VeteranDetails.RelationshipToVeteran ?? "0"));
                switch (relationship)
                {
                    case ENUMS.enumRelationship.Self:
                        theSSN = viewModel.PrimaryContactInfo.Ssn;
                        break;
                    case ENUMS.enumRelationship.Husband_or_Wife:
                        theSSN = viewModel.SpouseInfo.Ssn;
                        break;
                    default:
                        theSSN = viewModel.VeteranDetails.VeteranSsn;
                        break;
                }
                // If they have entered either a claim # OR an SSN, then perform this check.
                // Otherwise leave IsClaimNumberSameAsSsn null
                if (!string.IsNullOrWhiteSpace(viewModel.VeteranDetails.VeteranClaim) || !string.IsNullOrWhiteSpace(theSSN))
                {
                    var claimReduced = viewModel.VeteranDetails.VeteranClaim?.Trim().Replace("-", String.Empty);
                    viewModel.VeteranDetails.IsClaimNumberSameAsSsn = claimReduced == theSSN;
                }
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledVeteranViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Upsert Elderly Disabled Veteran Info
        /// </summary>
        /// <param name="veteranInfo">The veteran information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SaveElderlyDisabledVeteranInfo(ElderlyDisabledVeteranViewModel veteranInfo, string username, Guid tokenId)
        {
            // Check IsClaimNumberSameAsSsn.  We don't have a place to store this value, so
            // we just copy the SSN into the Claim number
            if (veteranInfo.VeteranDetails.IsClaimNumberSameAsSsn ?? false)
            {
                // Grab the SSN from the correct source, based on the Relationship
                string theSSN = null;
                ENUMS.enumRelationship relationship = (ENUMS.enumRelationship)(Convert.ToInt32(veteranInfo.VeteranDetails.RelationshipToVeteran ?? "0"));
                switch (relationship)
                {
                    case ENUMS.enumRelationship.Self:
                        theSSN = veteranInfo.PrimaryContactInfo.Ssn;
                        break;
                    case ENUMS.enumRelationship.Husband_or_Wife:
                        theSSN = veteranInfo.SpouseInfo.Ssn;
                        break;
                    default:
                        theSSN = veteranInfo.VeteranDetails.VeteranSsn;
                        break;
                }
                veteranInfo.VeteranDetails.VeteranClaim = theSSN;
            }

            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledVeteranDto vetInfo = mapper.Map<ElderlyDisabledVeteranDto>(veteranInfo);
            vetInfo.UpdatedBy = username;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            return await edApi.SaveElderlyDisabledVeteranInfo(vetInfo, username, tokenId);
        }

        /// <summary>
        /// Gets Life Insurance Info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledLifeInsuranceViewModel> GetLifeInsuranceInfo(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledLifeInsuranceViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledLifeInsuranceDto appDto = await edApi.GetLifeInsuranceInfo(applicationId, username, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledLifeInsuranceViewModel>(appDto);

                // Sanity check:  Ensure the lists exist even if no data was there:
                viewModel.LifeInsuranceDetails = viewModel.LifeInsuranceDetails ?? new List<ElderlyDisabledLifeInsuranceDetailViewModel>();
                viewModel.OtherBurialFundDetails = viewModel.OtherBurialFundDetails ?? new List<ElderlyDisabledOtherBurialFundsViewModel>();
                viewModel.AdditionalBurialFundDetails = viewModel.AdditionalBurialFundDetails ?? new List<ElderlyDisabledAdditionalBurialFundsViewModel>();
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledLifeInsuranceViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves Life Insurance Info
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveLifeInsuranceInfo(ElderlyDisabledLifeInsuranceViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledLifeInsuranceDto lifeInsuranceInfo = mapper.Map<ElderlyDisabledLifeInsuranceDto>(formData);
            lifeInsuranceInfo.UpdatedBy = username;
            return await new ElderlyDisabledApi().SaveLifeInsuranceInfo(lifeInsuranceInfo, username, tokenId);
        }

        /// <summary>
        /// Gets personal property info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledPersonalPropertyViewModel> GetPersonalPropertyInfo(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledPersonalPropertyViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledPersonalPropertyDto appDto = await edApi.GetPersonalPropertyInfo(applicationId, username, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledPersonalPropertyViewModel>(appDto);

                // Sanity check:  Ensure the lists exist even if no data was there:
                viewModel.AutoDetails = viewModel.AutoDetails ?? new List<ElderlyDisabledPersonalPropertyAutoViewModel>();
                viewModel.MachineDetails = viewModel.MachineDetails ?? new List<ElderlyDisabledPersonalPropertyMachineViewModel>();
                viewModel.CollectibleDetails = viewModel.CollectibleDetails ?? new List<ElderlyDisabledPersonalPropertyCollectibleViewModel>();
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledPersonalPropertyViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves personal property info.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SavePersonalPropertyInfo(ElderlyDisabledPersonalPropertyViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledPersonalPropertyDto personalPropertyData = mapper.Map<ElderlyDisabledPersonalPropertyDto>(formData);
            personalPropertyData.UpdatedBy = username;

            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var result = await edApi.SavePersonalPropertyInfo(personalPropertyData, username, tokenId);

            return result;
        }

        /// <summary>
        /// Gets person liability info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledLiabilityViewModel> GetPersonLiabilityInfo(long applicationId, long personId, string username, Guid tokenId)
        {
            ElderlyDisabledLiabilityViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledLiabilityDto appDto = await edApi.GetPersonLiabilityInfo(applicationId, personId, username, tokenId);

            if (okToDisplay(appDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ElderlyDisabledLiabilityViewModel>(appDto);

                // Sanity check:  Ensure the lists exist even if no data was there:
                viewModel.RepresentativeInfo = viewModel.RepresentativeInfo ?? new List<RepresentativeInfoViewModel>();
                viewModel.LiabilitySegments = viewModel.LiabilitySegments ?? new List<ElderlyDisabledLiabilityDetailViewModel>();

                if (viewModel.LiabilitySegments.Count > 0)
                {
                    foreach (var liabilitySegment in viewModel.LiabilitySegments)
                    {
                        liabilitySegment.LiabilityChangeCodeIds = liabilitySegment.LiabilityChangeCodes.Select(l => l.LiabilityChangeCodeId.ToString()).OrderBy(l => l).ToArray();

                        // For the GUI, set End Date to the first day of the month.  In the DB it is stored as the last day of the month for reporting
                        if (liabilitySegment.LiabilityEndDate.HasValue)
                        {
                            liabilitySegment.LiabilityEndDate = new DateTime(liabilitySegment.LiabilityEndDate.Value.Year, liabilitySegment.LiabilityEndDate.Value.Month, 1);
                        }
                    }
                }

                // For LiabilityTests, split these out into the containers for the View
                viewModel.Infrequent_IrregularLiabilityTests = viewModel.LiabilityTests.Where(t => t.LiabilityTestTypeId == ENUMS.enumLiabilityTestType.Infrequent_Irregular).ToList();
                viewModel.OtherLiabilityTests = viewModel.LiabilityTests.Where(t => t.LiabilityTestTypeId == ENUMS.enumLiabilityTestType.Other).ToList();
                viewModel.VA_Aid_AttendanceLiabilityTests = viewModel.LiabilityTests.Where(t => t.LiabilityTestTypeId == ENUMS.enumLiabilityTestType.VA_Aid_Attendance).ToList();
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledLiabilityViewModel>(appDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// Saves Liability Test data
        /// </summary>
        /// <param name="formData"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiResponse> UpsertLiabilityTests(ElderlyDisabledLiabilityViewModel formData, string username, Guid tokenId)
        {
            // This must be created, then filled in below
            formData.LiabilityTests = new List<ElderlyDisabledLiabilityTestViewModel>();
            // Copy the Liability Tests back into the original container
            //    remove the Deleted in the process
            formData.LiabilityTests.AddRange(formData.Infrequent_IrregularLiabilityTests.Where(t => !t.IsDeleted));
            formData.LiabilityTests.AddRange(formData.OtherLiabilityTests.Where(t => !t.IsDeleted));
            formData.LiabilityTests.AddRange(formData.VA_Aid_AttendanceLiabilityTests.Where(t => !t.IsDeleted));

            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledLiabilityDto personLiabilityInfo = mapper.Map<ElderlyDisabledLiabilityDto>(formData);
            BaseApiResponse result = await new ElderlyDisabledApi().UpsertLiabilityTestData(personLiabilityInfo, username, tokenId);

            return result;
        }

        /// <summary>
        /// Upsert on liability segment
        /// </summary>
        /// <param name="formData"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<ElderlyDisabledLiabilityDetailViewModel> UpsertLiabilitySegment(ElderlyDisabledLiabilityDetailViewModel formData, string username, Guid tokenId)
        {
            // First, get existing segments in order to check for overlap
            var existingLiability = await GetPersonLiabilityInfo(formData.ApplicationId, formData.PersonId, username, tokenId);

            if (existingLiability.IsSuccessful)
            {
                ElderlyDisabledLiabilityDetailViewModel returnVm;

                if (hasSegmentOverlap(existingLiability, formData))
                {
                    returnVm = new ElderlyDisabledLiabilityDetailViewModel()
                    {
                        IsSuccessful = false,
                        CaresError = CaresError.ValidationError,
                        ErrorMessageForUser = OverlappingSegmentsErrorMsg
                    };
                }
                else
                {
                    var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();

                    //  Need to map from the string[] to the list of person liability change codes.
                    formData.LiabilityChangeCodes = formData.LiabilityChangeCodeIds
                                                    .Select(l => new ElderlyDisabledLiabilityDetailChangeCodeViewModel
                                                    {
                                                        PersonLiabilityId = formData.PersonLiabilityId,
                                                        LiabilityChangeCodeId = Convert.ToByte(l)
                                                    })
                                                    .ToList();

                    // Adjust the Liability End Date to the last day of the month for Reports to run properly
                    if (formData.LiabilityEndDate.HasValue)
                    {
                        // First force it to the first of the month, just in case:
                        formData.LiabilityEndDate = new DateTime(formData.LiabilityEndDate.Value.Year, formData.LiabilityEndDate.Value.Month, 1);
                        // Add one month, subtract one day
                        formData.LiabilityEndDate = formData.LiabilityEndDate.Value.AddMonths(1).AddDays(-1);
                    }

                    ElderlyDisabledLiabilityDetailDto personLiabilityInfo = mapper.Map<ElderlyDisabledLiabilityDetailDto>(formData);
                    ElderlyDisabledLiabilityDetailDto returnDto = await new ElderlyDisabledApi().UpsertLiabilitySegment(personLiabilityInfo, username, tokenId);

                    returnVm = mapper.Map<ElderlyDisabledLiabilityDetailViewModel>(returnDto);

                    if (returnVm.LiabilityChangeCodes.Count > 0)
                    {
                        returnVm.LiabilityChangeCodeIds = returnVm.LiabilityChangeCodes.Select(l => l.LiabilityChangeCodeId.ToString()).OrderBy(l => l).ToArray();
                    }
                }

                return returnVm;
            }

            return new ElderlyDisabledLiabilityDetailViewModel()
            {
                IsSuccessful = false,
                CaresError = existingLiability.CaresError,
                ErrorMessageForLog = existingLiability.ErrorMessageForLog
            };
        }

        /// <summary>
        /// Determines if a new liability Segment overlaps with any existing ones
        /// </summary>
        /// <param name="existingLiability"></param>
        /// <param name="newSegment"></param>
        /// <returns></returns>
        private bool hasSegmentOverlap(ElderlyDisabledLiabilityViewModel existingLiability, ElderlyDisabledLiabilityDetailViewModel newSegment)
        {
            DateTime newStart = newSegment.LiabilityStartDate ?? DateTime.MinValue;
            DateTime newEnd = newSegment.LiabilityEndDate ?? DateTime.MaxValue;

            // Test EACH segment
            foreach (var seg in existingLiability.LiabilitySegments)
            {
                // Upserts on existing Segments:  Skip comparing it to itself
                if (seg.PersonLiabilityId == newSegment.PersonLiabilityId
                    // Occasionally we've had cases where this function is being called AFTER a new segment
                    // has already been added somehow (exists in existingLiability.LiabilitySegments, with a valid PersonLiabilityId),
                    // but newSegment.PersonLiabilityId is 0.  As a result, the user was seeing a Segment Overlap server-side validation,
                    // however the segment was added.
                    // For these cases, a call to areLibSegmentsSame will avoid this false negative
                    || (newSegment.PersonLiabilityId == 0 && areLibSegmentsSame(seg, newSegment)))
                {
                    continue;
                }

                DateTime existingStart = seg.LiabilityStartDate ?? DateTime.MinValue;
                DateTime existingEnd = seg.LiabilityEndDate ?? DateTime.MaxValue;

                //  New segment is NOT entirely before or after this segment:
                if (!(newStart < existingStart && newEnd < existingStart ||
                    newStart > existingEnd && newEnd > existingEnd))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        ///  Compares two liability segments by comparing user editable values
        /// </summary>
        /// <param name="one"></param>
        /// <param name="two"></param>
        /// <returns></returns>
        private bool areLibSegmentsSame(ElderlyDisabledLiabilityDetailViewModel one, ElderlyDisabledLiabilityDetailViewModel two)
        {
            return (one.LiabilityStartDate == two.LiabilityStartDate &&
                    (one.Remarks ?? string.Empty) == (two.Remarks ?? string.Empty) &&
                    one.LiabilityEndDate == two.LiabilityEndDate &&
                    one.LiabilityAmount == two.LiabilityAmount &&
                    one.SerializedLiabilityChangeCodeIds == two.SerializedLiabilityChangeCodeIds);
        }

        /// <summary>
        /// Delete liability segment
        /// </summary>
        /// <param name="personLiabilityId"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiResponse> DeleteLiabilitySegment(long personLiabilityId, string username, Guid tokenId)
        {
            return await new ElderlyDisabledApi().DeleteLiabilitySegment(personLiabilityId, username, tokenId);
        }

        /// <summary>
        /// Gets the application information bar.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledApplicationInfoBarViewModel> GetApplicationInfoBar(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledApplicationInfoBarViewModel vm;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledApplicationInfoBarDto dto = await edApi.GetApplicationInfoBar(applicationId, username, tokenId);

            if (okToDisplay(dto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();

                vm = mapper.Map<ElderlyDisabledApplicationInfoBarViewModel>(dto);
            }
            else
            {
                vm = await generateErroredViewModel<ElderlyDisabledApplicationInfoBarViewModel>(dto, tokenId);
            }

            return vm;
        }

        private void determineProgramNameApplyingFor(ref ElderlyDisabledEligibilityEnrollmentViewModel vm, ElderlyDisabledApplicationDto dto)
        {
            vm.ApplicantInfo.ProgramNameApplyingFor = ENUMS.ElderlyDisabledProgram.GetProgramAndCategoryNameById(dto.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId);
        }

        private void getPrimarySponsorInfo(ElderlyDisabledEligibilityEnrollmentViewModel vm, RepresentativeInfoDto primarySponsorInfo, IMapper mapper, long applicationId, Guid tokenId)
        {
            // If CaresError was "NotFound", then there isn't a primary sponsor
            if (primarySponsorInfo.CaresError != CaresError.NotFound)
            {
                vm.SponsorInfo = mapper.Map<RepresentativeInfoViewModel>(primarySponsorInfo);
            }

            vm.HubInfo = new Hub(ProcessorKind.Worker).GetHubResponseInfo(applicationId.ToString(), tokenId);
        }

        private void getLiabilityInfo(ElderlyDisabledEligibilityEnrollmentViewModel vm, ElderlyDisabledLiabilityDto liabilityInfo, IMapper mapper)
        {
            if (liabilityInfo.IsSuccessful)
            {
                vm.LiabilityInfo = mapper.Map<ElderlyDisabledLiabilityViewModel>(liabilityInfo);

                // Sanity check:  Ensure the lists exist even if no data was there:
                vm.LiabilityInfo.RepresentativeInfo = vm.LiabilityInfo.RepresentativeInfo ?? new List<RepresentativeInfoViewModel>();
                vm.LiabilityInfo.LiabilitySegments = vm.LiabilityInfo.LiabilitySegments ?? new List<ElderlyDisabledLiabilityDetailViewModel>();

                if (vm.LiabilityInfo.LiabilitySegments.Count > 0)
                {
                    foreach (var liabilitySegment in vm.LiabilityInfo.LiabilitySegments)
                    {
                        liabilitySegment.LiabilityChangeCodeIds = liabilitySegment.LiabilityChangeCodes.Select(l => l.LiabilityChangeCodeId.ToString()).OrderBy(l => l).ToArray();
                    }
                }
            }
        }

        public void GetEligibilityInfo(ElderlyDisabledEligibilityEnrollmentViewModel vm, DateTime? receivedDate, ElderlyDisabledEligibilityDeterminationsDto eligDto, IMapper mapper)
        {
            if (eligDto.IsSuccessful)
            {
                vm.MspAwardedAppId = eligDto.MspAwardedAppId;
                foreach (var eligSegment in eligDto.Determinations)
                {
                    // Split these out into the two lists needed in the front end.
                    // If this is an MSP award (based on State Aid cat):
                    if (ApplicationHelper.IsMsp(eligSegment.ProgramSubCategoryId))
                    {
                        // Add to the MSP list
                        vm.MspDeterminations.Add(mapper.Map<ElderlyDisabledEligEnrollAwardDenyViewModel>(eligSegment));
                        //  Order by start date
                        vm.MspDeterminations = vm.MspDeterminations.OrderBy(d => d.StartDate).ToList();
                    }
                    else
                    {
                        if (eligSegment.StartDate != null)
                        {
                            // ... otherwise add do the E&D list
                            vm.EnDDeterminations.Add(mapper.Map<ElderlyDisabledEligEnrollAwardDenyViewModel>(eligSegment));
                            //  Order by start date
                            vm.EnDDeterminations = vm.EnDDeterminations.OrderBy(d => d.StartDate).ToList();
                        }
                        else
                        {
                            // If only no Start Date means it's the overall determination.
                            vm.DenialReasons = eligSegment.DenialReasons;
                        }
                    }
                }

                // Validators need the ReceivedDate placed at the individual segment levels:
                vm.MspDeterminations.ForEach(seg => seg.ApplicationReceivedDate = receivedDate);
                vm.EnDDeterminations.ForEach(seg => seg.ApplicationReceivedDate = receivedDate);

                //Auto-Calculate end date for latest segment
                if (vm.EnDDeterminations.Count > 0)
                {
                    var lastSegment = vm.EnDDeterminations[vm.EnDDeterminations.Count - 1];
                    var earliestSegment = vm.EnDDeterminations[0];
                    var currentAwardDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                    var awardDatePlusOneYear = currentAwardDate.AddYears(1);

                    // Account for if user choose QMB, it must be one year + one month out.
                    if (earliestSegment.AwardOption == ENUMS.enumElderlyDisabledAwardOption.EnDQmb)
                    {
                        awardDatePlusOneYear = awardDatePlusOneYear.AddMonths(1);
                    }

                    // Update only if latest segment hasn't terminated yet and end date is inside allowable timeframe.
                    if (lastSegment.CancelDate.HasValue && !lastSegment.CancelReasonId.HasValue && lastSegment.CancelDate < awardDatePlusOneYear)
                    {
                        lastSegment.CancelDate = awardDatePlusOneYear;
                    }
                }

                // Add enrolled msp.
                for (int i = 0; i < eligDto.MspEnrolledSegments.Count; i++)
                {
                    var mspSegment = eligDto.MspEnrolledSegments[i];
                    vm.MspEnrolledSegments.Add(mapper.Map<EligEnrollAwardDenyViewModel>(mspSegment));
                    // get latest MSP enrolled type.
                    if (i == eligDto.MspEnrolledSegments.Count - 1)
                    {
                        if (mspSegment.ProgramSubCategoryId == (byte)ENUMS.enumProgramSubCategory._92DOSLIMBOnly)
                        {
                            vm.MSPEnrolledType = ENUMS.enumElderlyDisabledAwardOption.EnDSlimb;
                        }
                        else
                        {
                            vm.MSPEnrolledType = ENUMS.enumElderlyDisabledAwardOption.EnDQmb;
                        }
                    }
                }
                vm.MspEnrolledSegments = vm.MspEnrolledSegments.OrderBy(s => s.StartDate).ToList();
                vm.OverrideReasonIdLastMspEnrolledSegments = vm.MspEnrolledSegments.LastOrDefault()?.OverrideReasonId;
                vm.PreEligibilityCheckWarnings.AddRange(eligDto.PreEligibilityCheckWarnings);
                vm.EnDPreEligibilityProcessingCodes.AddRange(eligDto.EnDPreEligibilityProcessingCodes);
                vm.PersonIsSuspended = eligDto.PersonIsSuspended;
            }
        }

        /// <summary>
        /// Gets the eligibility enrollment data.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledEligibilityEnrollmentViewModel> GetEligibilityEnrollment(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledEligibilityEnrollmentViewModel vm;
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledApplicationDto dto = await edApi.GetApplication(applicationId, CallingUsername, tokenId);

            if (okToDisplay(dto))
            {
                vm = new ElderlyDisabledEligibilityEnrollmentViewModel
                {
                    ApplicationId = dto.ApplicationId,
                    ApplicationStatusId = dto.ApplicationStatusId,
                    ApplicationTypeId = dto.ApplicationTypeId,
                    ContactPersonId = dto.ContactPersonId,
                    ReceivedDate = dto.Application.ReceivedDate,
                    SubProgramCatogory = (SubProgramCategories)dto.Application.SubProgramCategoryId,
                    IsAlreadyEnrolledQMBOnly = dto.IsAlreadyEnrolledQMBOnly,
                    LastDateOnQMBOnly = dto.LastDateOnQMBOnly,
                    ApplicantInfo = new EligEnrollApplicantInfoViewModel
                    {
                        ApplicantFullName = Cares.Api.Infrastructure.Helper.PersonHelper.GetCombinedName(dto.Person.FirstName, dto.Person.MiddleName, dto.Person.LastName, dto.Person.SuffixId),
                        AgeFromReceivedDate = (byte)DateHelper.GetAge((DateTime)dto.Person.DateOfBirth, (DateTime)dto.Application.ReceivedDate),
                        Gender = ((ENUMS.enumGender)dto.Person.GenderId).ToString(),
                        MBINumber = !string.IsNullOrEmpty(dto.PersonDetail.MBINumber) ? dto.PersonDetail.MBINumber : string.Empty,
                        MaritalStatus = dto.Application.ContactPersonMaritalStatusId.HasValue ? ((ENUMS.enumMaritalStatus)dto.Application.ContactPersonMaritalStatusId).ToString() : string.Empty,
                        IsAttestedUsCitizen = dto.Person.IsUsCitizen,
                        ProgramNameApplyingId = (int)dto.ApplicationElderlyDisabledDetail.ProgramOrCategory,
                        ElderlyDisabledProgramId = dto.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId
                    }
                };

                determineProgramNameApplyingFor(ref vm, dto);

                // Gets the primary sponsor info
                RepresentativeInfoDto primarySponsorInfo = await edApi.GetPrimarySponsorInfo(applicationId, dto.ContactPersonId, CallingUsername, tokenId);
                getPrimarySponsorInfo(vm, primarySponsorInfo, mapper, applicationId, tokenId);

                // Gets the elderly disabled non-magi income info for Elig/Enroll screen
                ElderlyDisabledNonMagiIncomesDto nonMagiIncomeInfo = await edApi.GetElderlyDisabledNonMagiIncomeInfo(applicationId, dto.ContactPersonId, CallingUsername, tokenId);
                if (nonMagiIncomeInfo.IsSuccessful)
                {
                    vm.ElderlyDisabledNonMagiIncomeList = mapper.Map<ElderlyDisabledNonMagiIncomeViewModel>(nonMagiIncomeInfo);
                }

                // Get the TitleII Monthly income from the SSA composite result:
                // NOTE: This code was copied from the MSP's income page
                var nonMAGIIncomeDAL = new NonMAGIIncomeDAL();
                vm.ElderlyDisabledNonMagiIncomeList.HubVerifiedSsaIncomes = nonMAGIIncomeDAL.GetHubVerifiedSsaIncome((int)applicationId);

                // Gets the liability info
                ElderlyDisabledLiabilityDto liabilityInfo = await edApi.GetPersonLiabilityInfo(applicationId, dto.ContactPersonId, CallingUsername, tokenId);
                getLiabilityInfo(vm, liabilityInfo, mapper);

                // Get the eligibility info
                ElderlyDisabledEligibilityDeterminationsDto eligDto = await edApi.GetEligibility(applicationId, CallingUsername, tokenId);
                GetEligibilityInfo(vm, vm.ReceivedDate, eligDto, mapper);
            }
            else
            {
                vm = await generateErroredViewModel<ElderlyDisabledEligibilityEnrollmentViewModel>(dto, tokenId);
            }

            return vm;
        }

        /// <summary>
        /// Gets the Elderly Disabled eligibility enrollment data for snapshot.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledEligibilityEnrollmentViewModel> GetElderlyDisabledEligibilityEnrollment(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledEligibilityEnrollmentViewModel vm = new ElderlyDisabledEligibilityEnrollmentViewModel();
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var eligDto = edApi.GetElderlyDisabledEligibilityEnrollment(applicationId, username, tokenId);
            var eligHub = Task.Run(() => new Hub(ProcessorKind.Worker).GetHubResponseInfo(applicationId.ToString(), tokenId));
            await Task.WhenAll(eligDto, eligHub);
            if (okToDisplay(eligDto.Result) && okToDisplay(eligDto.Result))
            {
                vm.EnDDeterminations = CombineDenailReasonsForEnrollment(mapper.Map<List<ElderlyDisabledEligEnrollAwardDenyViewModel>>(eligDto.Result.Determinations));
                vm.EnDDeterminations = vm.EnDDeterminations.OrderBy(d => d.StartDate).ToList();
                vm.HubInfo = eligHub.Result;
            }
            else
            {
                vm = await generateErroredViewModel<ElderlyDisabledEligibilityEnrollmentViewModel>(eligDto.Result, tokenId);
            }
            return vm;
        }

        /// <summary>
        /// Gets the Next Eligibility Review date, which is cancel date minus one month
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<DateTime?> GetNextEligibilityReviewDate(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            var eligDto = await edApi.GetElderlyDisabledEligibilityEnrollment(applicationId, username, tokenId);
            if (eligDto.IsSuccessful)
            {
                var reviewEnrollment = eligDto.Determinations
                    .Where(d => d.IsAward)
                    .OrderByDescending(d => d.CancelDate).FirstOrDefault();
                if (reviewEnrollment != null)
                {
                    return reviewEnrollment.CancelDate.Value.AddMonths(-1);
                }
            }
            return null;
        }

        public List<ElderlyDisabledEligEnrollAwardDenyViewModel> CombineDenailReasonsForEnrollment(List<ElderlyDisabledEligEnrollAwardDenyViewModel> elderlyDisabledElig)
        {
            List<ElderlyDisabledEligEnrollAwardDenyViewModel> elderlyDisbaledList = new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();
            Dictionary<(DateTime? start, DateTime? end, byte? stateAidCategoryId), List<short>> elderly = new Dictionary<(DateTime? start, DateTime? end, byte? stateAidCategoryId), List<short>>();
            foreach (var edEnroll in elderlyDisabledElig)
            {
                (DateTime? st, DateTime? ed, byte? scId) objCheck = (edEnroll.StartDate, edEnroll.CancelDate, edEnroll.StateAidCategoryId);
                if (elderly.ContainsKey(objCheck))
                {
                    elderly[objCheck].AddRange(edEnroll.DenialReasons);
                }
                else
                {
                    elderly.Add(objCheck, edEnroll.DenialReasons);
                }
            }
            foreach (var row in elderly)
            {
                ElderlyDisabledEligEnrollAwardDenyViewModel enrollObj = new ElderlyDisabledEligEnrollAwardDenyViewModel()
                {
                    StartDate = row.Key.start,
                    CancelDate = row.Key.end,
                    StateAidCategoryId = row.Key.stateAidCategoryId,
                    DenialReasons = row.Value
                };
                elderlyDisbaledList.Add(enrollObj);
            }
            return elderlyDisbaledList;
        }

		/// <summary>
		/// Returns the Qit and Allocation information for the Application Id
		/// </summary>
		/// <param name="applicationId"></param>
		/// <param name="username"></param>
		/// <param name="tokenId"></param>
		/// <returns></returns>
		public async Task<ElderlyDisabledQitAndAllocationViewModel> GetQitAndAllocationByAppId(long applicationId, string username, Guid tokenId)
		{
            ElderlyDisabledQitAndAllocationViewModel viewModel;
			ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledQitAndAllocationDto qitAndAllocationDto = await edApi.GetQitAndAllocation(applicationId, username, tokenId);

			if (okToDisplay(qitAndAllocationDto))
			{
				var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
				// The viewmodel maps at the incomesDto.NonMagiIncomesDto level:
				viewModel = mapper.Map<ElderlyDisabledQitAndAllocationViewModel>(qitAndAllocationDto);

                //CopyLatestToHeader(viewModel);
                var latestSpouseAllocation = viewModel.SpouseAllocationList.OrderByDescending(d => d.Month).FirstOrDefault();
				if (latestSpouseAllocation != null)
				{
                    viewModel.SpouseAllocationAmount = (decimal)latestSpouseAllocation.AllocationAmount;
                    viewModel.SpouseImpoverishmentInd = latestSpouseAllocation.SpouseImpoverishmentInd;
                    viewModel.SpouseParticipationInd = latestSpouseAllocation.SpouseParticipationInd;
				}
                var latestFamilyAllocation = viewModel.FamilyAllocationList.OrderByDescending(d => d.Month).FirstOrDefault();
                if (latestFamilyAllocation != null)
                {
                    viewModel.FamilyAllocationAmount = (decimal)latestFamilyAllocation.AllocationAmount;
                    viewModel.FamilyImpoverishmentInd = latestFamilyAllocation.FamilyImpoverishmentInd;
                    viewModel.FamilyParticipationInd = latestFamilyAllocation.FamilyParticipationInd;
                }
            }
			else
			{
				viewModel = await generateErroredViewModel<ElderlyDisabledQitAndAllocationViewModel>(qitAndAllocationDto, tokenId);
			}
			return viewModel;
		}

		/// <summary>
		/// Returns non Magi income
		/// </summary>
		/// <param name="applicationId"></param>
		/// <param name="username"></param>
		/// <param name="tokenId"></param>
		/// <returns></returns>
		public async Task<ElderlyDisabledNonMagiIncomeViewModel> GetNonMagiIncomeInfo(long applicationId, string username, Guid tokenId)
        {
            ElderlyDisabledNonMagiIncomeViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ElderlyDisabledNonMagiIncomesDto incomesDto = await edApi.GetNonMagiIncomes(applicationId, username, tokenId);

            if (okToDisplay(incomesDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                // The viewmodel maps at the incomesDto.NonMagiIncomesDto level:
                viewModel = mapper.Map<ElderlyDisabledNonMagiIncomeViewModel>(incomesDto);
            }
            else
            {
                viewModel = await generateErroredViewModel<ElderlyDisabledNonMagiIncomeViewModel>(incomesDto, tokenId);
            }
            return viewModel;
        }

        /// <summary>
        /// Saves Non-Magi income info
        /// </summary>
        /// <param name="formData"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveNonMagiIncomes(ElderlyDisabledNonMagiIncomeViewModel formData, string username, Guid tokenId)
        {
            //  Ensure there is a default value for the DTO mapping.
            formData.NonMagiIncomes.ForEach(i => { i.AttestedIncomeAmount = i.AttestedIncomeAmount ?? 0.00m; i.VerifiedIncomeAmount = i.VerifiedIncomeAmount ?? 0.00m; });
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledNonMagiIncomesDto incomeData = mapper.Map<ElderlyDisabledNonMagiIncomesDto>(formData);
            incomeData.UpdatedBy = username;
            incomeData.UpdatedDate = DateTime.Now;
            var result = await (new ElderlyDisabledApi()).SaveNonMagiIncomes(incomeData, username, tokenId);
            return result;
        }

        /// <summary>
        ///  Calculates the VA net value
        /// </summary>
        /// <param name="appId">The app Id.</param>
        /// <param name="appNonMagiIncomeId">The app non magi income Id.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public Task<BaseApiMessage> CalculateVANetValues(long appId, long appNonMagiIncomeId, string username, Guid tokenId)
        {
            return new ElderlyDisabledApi().CalculateVANetValues(appId, appNonMagiIncomeId, username, tokenId);
        }

        /// <summary>
        /// Saves Spouse and Family Allocation info
        /// </summary>
        /// <param name="formData"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveQitAndAllocation(ElderlyDisabledQitAndAllocationViewModel formData, string username, Guid tokenId)
        {
            //  Ensure there is a default value for the DTO mapping.
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledQitAndAllocationDto allocationData = mapper.Map<ElderlyDisabledQitAndAllocationDto>(formData);
            allocationData.UpdatedBy = username;
            allocationData.UpdatedDate = DateTime.Now;
            var result = await (new ElderlyDisabledApi()).SaveAllocationInfo(allocationData, username, tokenId);
            return result;
        }

        /// <summary>
        /// Saves the eligibility information.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The calling username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseApiResponse> SaveEligibilityInformation(ElderlyDisabledEligibilityEnrollmentViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledEligibilityDeterminationsDto dto = new ElderlyDisabledEligibilityDeterminationsDto
            {
                UpdatedBy = username,
                ApplicationId = formData.ApplicationId
            };

            dto.Determinations.AddRange(formData.MspDeterminations.Select(s => mapper.Map<ElderlyDisabledEligibilityDeterminationDto>(s)));
            dto.Determinations.AddRange(formData.EnDDeterminations.Select(s => mapper.Map<ElderlyDisabledEligibilityDeterminationDto>(s)));
            dto.Determinations = dto.Determinations.Select(d => { d.ApplicationId = formData.ApplicationId; return d; }).ToList();

            if (formData.DenialReasons?.Count > 0)
            {
                dto.Determinations.Add(new ElderlyDisabledEligibilityDeterminationDto
                {
                    DenialReasons = formData.DenialReasons,
                    ApplicationId = formData.ApplicationId
                });
            }

            var result = await (new ElderlyDisabledApi()).SaveEligibilityInformation(dto, username, tokenId);

            return result;
        }

        /// <summary>
        /// Completes the E&D eligibility process.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> EnDEnrollComplete(ElderlyDisabledEligibilityEnrollmentViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledEligibilityDeterminationsDto dto = new ElderlyDisabledEligibilityDeterminationsDto();

            dto.UpdatedBy = username;
            dto.ContactPersonId = formData.ContactPersonId;
            dto.ApplicationId = formData.ApplicationId;
            dto.ProgramNameApplyingId = formData.ApplicantInfo.ProgramNameApplyingId;
            dto.SubProgramCatogory = formData.SubProgramCatogory;
            dto.ApplicationTypeId = formData.ApplicationTypeId;

            // Currently, using 46, 47, 48 for temporary data for EnD + MSP type.
            // Replace later after calculating to determine program.
            // ELTODO: Might be removed, we need to send the AwardOption to the EnrollComplete and Save.
            formData.EnDDeterminations.ForEach(seg =>
            {
                switch (seg.AwardOption)
                {
                    case ENUMS.enumElderlyDisabledAwardOption.EnDQmb:
                        seg.StateAidCategoryId = (byte)ENUMS.enumProgramSubCategory._47DisabledQMB;
                        break;
                    case ENUMS.enumElderlyDisabledAwardOption.EnDSlimb:
                        seg.StateAidCategoryId = (byte)ENUMS.enumProgramSubCategory._48DisabledSLIMB;
                        break;
                    case ENUMS.enumElderlyDisabledAwardOption.EnDOnly:
                        seg.StateAidCategoryId = (byte)ENUMS.enumProgramSubCategory._46Disabled;
                        break;
                    default:
                        seg.StateAidCategoryId = null;
                        break;
                }
            });

            dto.Determinations.AddRange(formData.EnDDeterminations.Select(s => mapper.Map<ElderlyDisabledEligibilityDeterminationDto>(s)));
            dto.Determinations = dto.Determinations.Select(d => { d.ApplicationId = formData.ApplicationId; return d; }).ToList();

            if (formData.DenialReasons?.Count > 0)
            {
                dto.Determinations.Add(new ElderlyDisabledEligibilityDeterminationDto
                {
                    DenialReasons = formData.DenialReasons,
                    ApplicationId = formData.ApplicationId,
                    StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1)
                });
            }

            BaseApiResponse result = await (new ElderlyDisabledApi()).EnrollComplete(dto, username, tokenId);
            Cares.Data.Constants.AwardOrDeny isAwardOrDeny = (Cares.Data.Constants.AwardOrDeny)result.Id;

            switch (isAwardOrDeny)
            {
                case Cares.Data.Constants.AwardOrDeny.Award:
                    await autoCreateAwardLetter(formData.ApplicationId, tokenId);
                    break;
                case Cares.Data.Constants.AwardOrDeny.Deny:
                    await autoCreateDenialLetter(formData.ApplicationId, tokenId);
                    break;
            }

            return result;
        }

        /// <summary>
        /// Auto create Award letter
        /// </summary>
        /// <param name="appId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        private async Task autoCreateAwardLetter(long appId, Guid tokenId)
        {
            try
            {
                // Needing to create an instance of ElderlyDisabledLettersController.  A good reason to keep your Controllers thin!
                ElderlyDisabledLettersController controller = new ElderlyDisabledLettersController();
                BaseController.InitializeControllerFromExistingController(_controller, controller);
                var jsonResult = await controller.AutoGenerateLetterDraft((int)ElderlyDisabledLettersTypes.AwardLetter, appId) as JsonResult;
                var type = jsonResult?.Data.GetType();
                var successPropertyInfo = type?.GetProperty("success");
                bool? success = successPropertyInfo?.GetValue(jsonResult.Data, null) as bool?;

                if (!(success ?? false))
                {
                    _log.Error("Error occurred while trying to generate Award Letter", tokenId: tokenId);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, tokenId);
            }
        }

        /// <summary>
        /// Auto create Denial letter
        /// </summary>
        /// <param name="appId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        private async Task autoCreateDenialLetter(long appId, Guid tokenId)
        {
            try
            {
                // Needing to create an instance of ElderlyDisabledLettersController.  A good reason to keep your Controllers thin!
                ElderlyDisabledLettersController controller = new ElderlyDisabledLettersController();
                BaseController.InitializeControllerFromExistingController(_controller, controller);
                var jsonResult = await controller.AutoGenerateLetterDraft((int)ElderlyDisabledLettersTypes.DenialLetter, appId) as JsonResult;
                var type = jsonResult?.Data.GetType();
                var successPropertyInfo = type?.GetProperty("success");
                bool? success = successPropertyInfo?.GetValue(jsonResult.Data, null) as bool?;

                if (!(success ?? false))
                {
                    _log.Error("Error occurred while trying to generate Denial Letter", tokenId: tokenId);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, tokenId);
            }
        }

        /// <summary>
        /// Completes the MSP eligibility process.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> MSPEnrollComplete(ElderlyDisabledEligibilityEnrollmentViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledEligibilityDeterminationsDto dto = new ElderlyDisabledEligibilityDeterminationsDto();

            dto.UpdatedBy = username;
            dto.ApplicationId = formData.ApplicationId;
            dto.ContactPersonId = formData.ContactPersonId;
            dto.Determinations.AddRange(formData.MspDeterminations.Select(s => mapper.Map<ElderlyDisabledEligibilityDeterminationDto>(s)));
            dto.Determinations = dto.Determinations.Select(d => { d.ApplicationId = formData.ApplicationId; return d; }).ToList();

            var result = await (new ElderlyDisabledApi()).EnrollMsp(dto, username, tokenId);

            return result;
        }

        /// <summary>
        /// Gets facilities for Expedite Facility Admin page
        /// </summary>
        /// <returns></returns>
        public async Task<ExpediteFacilityProvidersViewModel> GetExpediteFacilityProviders(string username, Guid tokenId)
        {
            ExpediteFacilityProvidersViewModel viewModel = new ExpediteFacilityProvidersViewModel();
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ExpediteFacilityProvidersDto facilitiesDto = await edApi.GetExpediteFacilityProviders(username, tokenId);

            if (okToDisplay(facilitiesDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ExpediteFacilityProvidersViewModel>(facilitiesDto);
            }
            else
            {
                viewModel = await generateErroredViewModel<ExpediteFacilityProvidersViewModel>(facilitiesDto, tokenId);
            }

            _securityComponent.SanitizeViewModel(viewModel, _controller);

            return viewModel;
        }

        /// <summary>
        /// Gets facilities for Expedite Facility Applicant Info
        /// </summary>
        /// <returns></returns>
        public async Task<ExpediteFacilityProvidersViewModel> GetExpediteFacilitiesByStr(string searchText, string username, Guid tokenId)
        {
            ExpediteFacilityProvidersViewModel viewModel = new ExpediteFacilityProvidersViewModel();

            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ExpediteFacilityProvidersDto facilitiesDto = await edApi.GetExpediteFacilityProvidersByString(searchText, username, tokenId);

            if (okToDisplay(facilitiesDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<ExpediteFacilityProvidersViewModel>(facilitiesDto);
            }
            else
            {
                viewModel = await generateErroredViewModel<ExpediteFacilityProvidersViewModel>(facilitiesDto, tokenId);
            }
            _securityComponent.SanitizeViewModel(viewModel, _controller);

            return viewModel;
        }

        /// <summary>
        /// Gets Financial Institutions Info by a string entry
        /// </summary>
        /// <param name="searchText">The search text.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<FinancialInstitutionsViewModel> GetFinancialInstitutionsByStr(string searchText, string username, Guid tokenId)
        {
            FinancialInstitutionsViewModel viewModel;
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            FinancialInstitutionsDto financialDto = await edApi.GetFinancialInstitutionsByString(searchText, username, tokenId);

            if (okToDisplay(financialDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<FinancialInstitutionsViewModel>(financialDto);
            }
            else
            {
                viewModel = await generateErroredViewModel<FinancialInstitutionsViewModel>(financialDto, tokenId);
            }
            _securityComponent.SanitizeViewModel(viewModel, _controller);

            return viewModel;
        }

        /// <summary>
        /// Returns single Financial Institution data by BankId or Return All.
        /// </summary>
        /// <param name="bankId">The bank id.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<FinancialInstitutionsViewModel> GetFinancialInstitutions(int? bankId, string username, Guid tokenId)
        {
            FinancialInstitutionsViewModel viewModel;
            var edApi = new ElderlyDisabledApi();
            var financialDto = await edApi.GetFinancialInstitutions(bankId, username, tokenId);

            if (financialDto.IsSuccessful)
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
                viewModel = mapper.Map<FinancialInstitutionsViewModel>(financialDto);
            }
            else
            {
                viewModel = await generateErroredViewModel<FinancialInstitutionsViewModel>(financialDto, tokenId);
            }

            _securityComponent.SanitizeViewModel(viewModel, _controller);

            return viewModel;
        }

        /// <summary>
        /// Updates Financial Institution data.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> UpdateFinancialInstitution(FinancialInstitutionViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            FinancialInstitutionDto financialInstitution = mapper.Map<FinancialInstitutionDto>(formData);

            financialInstitution.BankName = !string.IsNullOrEmpty(formData.MergedBankName) ? formData.MergedBankName : string.Empty;

            // set current date if users want to close bank but not give closed date
            if (formData.BankIsActive == false)
            {
                financialInstitution.BankClosedDate = formData.BankClosedDate ?? DateTime.Today;
            }

            return await new ElderlyDisabledApi().UpdateFinancialInstitution(financialInstitution, financialInstitution.MergedBankName != null, username, tokenId);
        }

        /// <summary>
        /// Returns the view model for manually selecting a Provider for a specific Expedite Facility
        /// </summary>
        /// <param name="expediteFacilityId"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<SelectExpediteFacilityProviderViewModel> GetSelectExpediteFacilityProviderViewModel(int expediteFacilityId, string username, Guid tokenId)
        {
            SelectExpediteFacilityProviderViewModel viewModel = new SelectExpediteFacilityProviderViewModel();
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();
            ExpediteFacilityDto facilityDto = await edApi.GetExpediteFacilityById(expediteFacilityId, username, tokenId);
            ProvidersDto providers = await edApi.GetProviders(username, tokenId);

            if (okToDisplay(facilityDto))
            {
                var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();

                viewModel.Facility = mapper.Map<ExpediteFacilityViewModel>(facilityDto);

                foreach (var p in providers.Providers)
                {
                    viewModel.Providers.Add(mapper.Map<ProviderViewModel>(p));
                }
            }
            else
            {
                viewModel = await generateErroredViewModel<SelectExpediteFacilityProviderViewModel>(facilityDto, tokenId);
            }

            return viewModel;
        }

        /// <summary>
        /// ONLY sets the PROVIDER_ID for a given Expedite Facility
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SetExpediteFacilityProvider(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            ElderlyDisabledApi edApi = new ElderlyDisabledApi();

            return await edApi.SetExpediteFacilityProvider(expediteFacility, username, tokenId);
        }

        /// <summary>
        /// Saves the expedite facility.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> InsertExpediteFacility(ExpediteFacilityViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ExpediteFacilityDto expediteFacilityInfo = mapper.Map<ExpediteFacilityDto>(formData);

            return await new ElderlyDisabledApi().InsertExpediteFacility(expediteFacilityInfo, username, tokenId);
        }

        /// <summary>
        /// Saves the financial institution.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> InsertFinancialInstitution(FinancialInstitutionViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            FinancialInstitutionDto financialInstitutionInfo = mapper.Map<FinancialInstitutionDto>(formData);

            return await new ElderlyDisabledApi().InsertFinancialInstitution(financialInstitutionInfo, username, tokenId);
        }

        /// <summary>
        /// Get expedite facility by id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ExpediteFacilityViewModel> GetExpediteFacilityById(int id, string username, Guid tokenId)
        {
            var expediteFacility = await new ElderlyDisabledApi().GetExpediteFacilityById(id, username, tokenId);
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ExpediteFacilityViewModel vm = mapper.Map<ExpediteFacilityViewModel>(expediteFacility);

            return vm;
        }

        /// <summary>
        /// Updates Expedite Facility data.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> UpdateExpediteFacility(ExpediteFacilityViewModel formData, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ExpediteFacilityDto expediteFacility = mapper.Map<ExpediteFacilityDto>(formData);

            return await new ElderlyDisabledApi().UpdateExpediteFacility(expediteFacility, username, tokenId);
        }

        /// <summary>
        /// Called by various E&D Get methods in the event that there was an error
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="response"></param>
        /// <returns></returns>
        private async Task<T> generateErroredViewModel<T>(BaseApiResponse response, Guid tokenId) where T : BaseViewModel, new()
        {
            T viewModel = new T()
            {
                IsSuccessful = false,
                CaresError = response.CaresError,
                TokenId = tokenId
            };

            // Check if this is an in-process ExpediteImport app that should not be displayed.
            // Generate an error message instead
            if (response.ApplicationStatusId == (byte)ENUMS.enumApplicationStatus.Import_Processing)
            {
                // Need to get the ExpediteImportId for display to the user:
                ElderlyDisabledApi edApi = new ElderlyDisabledApi();
                var expediteDetails = await edApi.GetImportExpediteDetails(0, response.ApplicationId, CallingUsername, tokenId);

                viewModel.ErrorMessageForUser = string.Format(ExpediteImportProcessingMessage, expediteDetails.ExpediteImportId);
            }

            return viewModel;
        }

        /// <summary>
        /// Determines if a DTO returned from the BLL is ok for display to the user
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private bool okToDisplay(BaseApiResponse dto)
        {
            // If successful and not an in-process ExpediteImport
            return dto.IsSuccessful && dto.ApplicationStatusId != (byte)ENUMS.enumApplicationStatus.Import_Processing;
        }

        /// <summary>
        /// Retrieves applicationSNapshotHTML as String
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<ApplicationSnapshotHtmlViewModel> GetApplicationSnapshotHtml(long applicationId, string username, Guid tokenId)
        {
            var snapshot = await new ApplicationApi().GetApplicationSnapshot(applicationId, username, tokenId);
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ApplicationSnapshotHtmlViewModel snapshotViewmodel = mapper.Map<ApplicationSnapshotHtmlViewModel>(snapshot);
            snapshotViewmodel.ApplicationSnapshot = StringCompressor.Decompress(snapshotViewmodel.ApplicationSnapshot);
            return snapshotViewmodel;
        }

        /// <summary>
        /// Do not allow User from saving data if not authorized
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="memberName"></param>
        /// <returns></returns>
        public async Task<bool> PerformEandDValidations(long applicationId, Guid tokenId, [CallerMemberName] string memberName = "")
        {
            //All non-E&D users can only view the application
            if (!CaresSecurity.IsInRole(CaresSecurity.EandDRoles))
            {
                return true;
            }

            ElderlyDisabledApplicationInfoBarViewModel vm = await this.GetApplicationInfoBar(applicationId, CallingUsername, tokenId);

            memberName = memberName.Contains("Ajax") ? memberName.Replace("Ajax", string.Empty) : memberName;
            memberName = memberName.Contains("Save") ? memberName.Replace("Save", string.Empty) : memberName;

            return this.IsUserReadOnly(vm, memberName);
        }

        /// <summary>
        /// Determines whether user the user has read only access based on the role, the terminal status, and the screen.
        /// </summary>
        /// <param name="vm">The vm.</param>
        /// <param name="callingMethodName">Name of the calling method.</param>
        /// <returns>
        ///   <c>true</c> if the user has read only access; otherwise, <c>false</c>.
        /// </returns>
        public bool IsUserReadOnly(BaseViewModel vm, string callingMethodName)
        {
            //All non-E&D users can only view the application
            if (!CaresSecurity.IsInRole(CaresSecurity.EandDRoles))
            {
                return true;
            }

            DateTime enrollmentCancelDate = ElderlyDisabledDal.GetEnrollmentEndDateByApplicationId(vm.ApplicationId);
            if ((vm.ApplicationStatusId == (byte)ENUMS.enumApplicationStatus.Admin_Closed
               || (enrollmentCancelDate < DateTime.Now && vm.ApplicationStatusId == (byte)ENUMS.enumApplicationStatus.Approved))
               && (callingMethodName != "WorkerReminders" && callingMethodName != "ElderlyDisabledLetters" && callingMethodName != "ElderlyDisabledLiability" && callingMethodName != "ElderlyDisabledLiabilityTests"))
            {
                return true;
            }

            bool terminalStatus = ApplicationHelper.IsEnrollmentComplete(vm.ApplicationStatusId);
            ElderlyDisabledUserRoles viewUserRoles = null;
            var userRole = CaresSecurity.GetEandDRoleId();

            if (terminalStatus)
            {
                viewUserRoles = ElderlyDisabledViewAccessByRole.ElderlyDisabledTerminalStatus.ContainsKey(callingMethodName) ?
                    ElderlyDisabledViewAccessByRole.ElderlyDisabledTerminalStatus[callingMethodName] : null;
            }
            else
            {
                viewUserRoles = ElderlyDisabledViewAccessByRole.ElderlyDisabledNonTerminalStatus.ContainsKey(callingMethodName) ?
                    ElderlyDisabledViewAccessByRole.ElderlyDisabledNonTerminalStatus[callingMethodName] : null;
            }

            return ElderlyDisabledViewAccessByRole.GetUserAccessOnView(viewUserRoles, userRole);
        }

        /// <summary>
        /// Determines whether user the user has read only access based on the role, the terminal status (which is known), and the screen.
        /// </summary>
        /// <param name="isInTerminalStatus">if set to <c>true</c> [is in terminal status].</param>
        /// <param name="callingMethodName">Name of the calling method.</param>
        /// <returns>
        ///   <c>true</c> if the user has read only access; otherwise, <c>false</c>.
        /// </returns>
        public bool IsUserReadOnly(bool isInTerminalStatus, string callingMethodName)
        {
            //All non-E&D users can only view the application
            if (!CaresSecurity.IsInRole(CaresSecurity.EandDRoles))
            {
                return true;
            }

            ElderlyDisabledUserRoles viewUserRoles = null;
            var userRole = CaresSecurity.GetEandDRoleId();

            if (isInTerminalStatus)
            {
                viewUserRoles = ElderlyDisabledViewAccessByRole.ElderlyDisabledTerminalStatus.ContainsKey(callingMethodName) ?
                    ElderlyDisabledViewAccessByRole.ElderlyDisabledTerminalStatus[callingMethodName] : null;
            }
            else
            {
                viewUserRoles = ElderlyDisabledViewAccessByRole.ElderlyDisabledNonTerminalStatus.ContainsKey(callingMethodName) ?
                    ElderlyDisabledViewAccessByRole.ElderlyDisabledNonTerminalStatus[callingMethodName] : null;
            }

            return ElderlyDisabledViewAccessByRole.GetUserAccessOnView(viewUserRoles, userRole);
        }

        /// <summary>
        /// Get the last Exparte Application info for a person.
        /// </summary>
        /// <param name="personId">The person id.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ExparteApplicationInfoViewModel> GetExparteAppInfoByPersonId(int personId, string username, Guid tokenId)
        {
            var exparteDto = await new ElderlyDisabledApi().GetExparteAppInfoByPersonId(personId, username, tokenId);
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ExparteApplicationInfoViewModel vm = mapper.Map<ExparteApplicationInfoViewModel>(exparteDto);

            return vm;
        }

        /// <summary>
        /// Logic to determine if overlap would occur for this specific enrollee.
        /// </summary>
        /// <param name="enrollmentDeterminations"></param>
        /// <param name="personId"></param>
        /// <returns></returns>
        internal (bool HasOverlaps, IEnumerable<(int? ApplicationID, string CertifyingAgencyCode, string StateAidCat)> OverlappedApplications) CheckForEnrollmentOverlaps(List<ElderlyDisabledEligEnrollAwardDenyViewModel> enrollmentDeterminations, long personId)
        {
            // Combine the StartDate and CancelDate into two separate strings to pass into the stored procedure
            string startDatesParam = string.Join(",", enrollmentDeterminations.Select(v => v.StartDate.HasValue ? v.StartDate.Value.ToString("yyyy-MM-dd") : ""));
            string endDatesParam = string.Join(",", enrollmentDeterminations.Select(v => v.CancelDate.HasValue ? v.CancelDate.Value.ToString("yyyy-MM-dd") : ""));

            // Call the CheckEnrollmentOverlaps method to find any overlaps
            var overlaps = ElderlyDisabledDal.CheckEnrollmentOverlaps((int)personId, startDatesParam, endDatesParam);

            //Applications in conflict with these segments
            var applicationsInConflict = overlaps.Select(o => (o.APPLICATION_ID, o.CERTIFYING_AGENCY_CODE, o.STATE_AID_CATEGORY_ID)).Distinct().ToList();
            var hasOverlaps = applicationsInConflict.Count > 0;

            // If there are any overlaps, return true; otherwise, return false
            return (hasOverlaps, applicationsInConflict);
        }
    }
}