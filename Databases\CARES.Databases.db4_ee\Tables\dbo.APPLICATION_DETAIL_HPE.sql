USE [Db4_ee];
GO

SET ANSI_NULLS ON;
GO

SET QUOTED_IDENTIFIER ON;
GO

/* =============================================================================================
   Author        : Ra<PERSON><PERSON><PERSON>
   Create Date   : 2025-09-22 
   Description   : Create table [dbo].[APPLICATION_DETAIL_HPE] to hold HPE Determiner details.
   =============================================================================================
   Version History:
   ---------------------------------------------------------------------------------------------
   Ver |    Date     | Author                  | Description
   ----|-------------|-------------------------|-------------------------------------------------
   01  | 2025-09-22  | Raveendran Krishnasamy  | PBI#253365 : Initial version
   ============================================================================================= */

CREATE TABLE [dbo].[APPLICATION_DETAIL_HPE]
(
    [APP_DETAIL_HPE_ID] INT IDENTITY(1,1) NOT NULL   -- PK
  , [APPLICATION_ID]    BIGINT            NOT NULL   -- FK to APPLICATION
  , [PERSON_ID]         BIGINT            NOT NULL   -- FK to PERSON
  , [DETERMINER_ID]     INT               NOT NULL   -- FK to PRESUMPTIVE_DETERMINER
  , [PROVIDER_ID]       INT               NOT NULL   -- FK to PRESUMPTIVE_PROVIDER
  , [CREATED_BY]        VARCHAR(50)       NOT NULL
  , [CREATED_DATE]      DATETIME2(7)      NOT NULL
  , [UPDATED_BY]        VARCHAR(50)       NOT NULL
  , [UPDATED_DATE]      DATETIME2(7)      NOT NULL

    CONSTRAINT [PK_APPLICATION_DETAIL_HPE] 
        PRIMARY KEY CLUSTERED ([APP_DETAIL_HPE_ID] ASC)
        WITH 
        (
            PAD_INDEX = OFF
          , STATISTICS_NORECOMPUTE = OFF
          , IGNORE_DUP_KEY = OFF
          , ALLOW_ROW_LOCKS = ON
          , ALLOW_PAGE_LOCKS = ON
        ) ON [PRIMARY]
) ON [PRIMARY];
GO

/* =============================================================================================
   Default Constraints for Audit Columns - APPLICATION_DETAIL_HPE
   ============================================================================================= */

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [DF_APPDETAILHPE_CREATED_BY] 
    DEFAULT (SUSER_SNAME()) FOR [CREATED_BY];
GO

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [DF_APPDETAILHPE_CREATED_DATE] 
    DEFAULT (SYSDATETIME()) FOR [CREATED_DATE];
GO

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [DF_APPDETAILHPE_UPDATED_BY] 
    DEFAULT (SUSER_SNAME()) FOR [UPDATED_BY];
GO

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [DF_APPDETAILHPE_UPDATED_DATE] 
    DEFAULT (SYSDATETIME()) FOR [UPDATED_DATE];
GO

/* =============================================================================================
   Foreign Key Constraints
   ============================================================================================= */

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [FK_APPDETAILHPE_APPLICATION]
    FOREIGN KEY ([APPLICATION_ID])
    REFERENCES [dbo].[APPLICATION] ([APPLICATION_ID]);
GO

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [FK_APPDETAILHPE_PERSON]
    FOREIGN KEY ([PERSON_ID])
    REFERENCES [dbo].[sysnl_donotmodify$$PERSON] ([PERSON_ID]);
GO

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [FK_APPDETAILHPE_DETERMINER_ID]
    FOREIGN KEY ([DETERMINER_ID])
    REFERENCES [dbo].[PRESUMPTIVE_DETERMINER] ([DETERMINER_ID]);
GO

ALTER TABLE [dbo].[APPLICATION_DETAIL_HPE]
    ADD CONSTRAINT [FK_APPDETAILHPE_PROVIDER_ID]
    FOREIGN KEY ([PROVIDER_ID])
    REFERENCES [dbo].[PRESUMPTIVE_PROVIDER] ([PROVIDER_ID]);
GO

/* =============================================================================================
   Extended Properties for APPLICATION_DETAIL_HPE
   ============================================================================================= */

-- Table description
EXEC sys.sp_addextendedproperty 
      @name  = N'Table Description'
    , @value = N'APPLICATION_DETAIL_HPE table holds HPE determiner and provider assignment details.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE';
GO

-- Column descriptions
EXEC sys.sp_addextendedproperty 
      @name  = N'APP_DETAIL_HPE_ID Column Description'
    , @value = N'Unique ID for the Application Detail HPE entry.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'APP_DETAIL_HPE_ID';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'APPLICATION_ID Column Description'
    , @value = N'ID of the related Application record.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'APPLICATION_ID';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'PERSON_ID Column Description'
    , @value = N'ID of the related Person record.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'PERSON_ID';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'DETERMINER_ID Column Description'
    , @value = N'ID of the related Determiner (FK to PRESUMPTIVE_DETERMINER).'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'DETERMINER_ID';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'PROVIDER_ID Column Description'
    , @value = N'ID of the related Provider (FK to PRESUMPTIVE_PROVIDER).'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'PROVIDER_ID';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'CREATED_BY Column Description'
    , @value = N'User who created the record.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'CREATED_BY';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'CREATED_DATE Column Description'
    , @value = N'Date when the record was created.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'CREATED_DATE';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'UPDATED_BY Column Description'
    , @value = N'User who last updated the record.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'UPDATED_BY';
GO

EXEC sys.sp_addextendedproperty 
      @name  = N'UPDATED_DATE Column Description'
    , @value = N'Date when the record was last updated.'
    , @level0type = N'SCHEMA' 
	, @level0name = N'dbo'
    , @level1type = N'TABLE'  
	, @level1name = N'APPLICATION_DETAIL_HPE'
    , @level2type = N'COLUMN' 
	, @level2name = N'UPDATED_DATE';
GO
