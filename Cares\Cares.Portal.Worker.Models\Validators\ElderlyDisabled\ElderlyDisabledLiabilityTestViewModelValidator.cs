﻿using Cares.Api.Infrastructure.Enums;
using Cares.Portal.Infrastructure;
using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel;
using FluentValidation;
using FluentValidation.Mvc;
using FluentValidation.Results;
using System.Linq;
using System.Web.Mvc;
using ElderlyDisabledRes = Cares.Portal.Worker.Resources.ElderlyDisabled.ElderlyDisabled;
using ValidationRes = Cares.Portal.Worker.Resources.Shared.Validation;

namespace Cares.Portal.Worker.Models.Validators.ElderlyDisabled
{
    public class ElderlyDisabledLiabilityTestViewModelValidator : AbstractValidator<ElderlyDisabledLiabilityTestViewModel>, IValidatorInterceptor
    {
        public ElderlyDisabledLiabilityTestViewModelValidator()
        {
            validateLiabilityTest();
        }

        public ValidationResult AfterMvcValidation(ControllerContext controllerContext, ValidationContext validationContext, ValidationResult result)
        {
            return ValidatorHelper.DistinctValidationErrors(result);
        }

        public ValidationContext BeforeMvcValidation(ControllerContext controllerContext, ValidationContext validationContext)
        {
            return validationContext;
        }

        private void validateLiabilityTest()
        {
            // Liability EffectiveDate is required
            RuleFor(ld => ld.EffectiveDate)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .NotEmpty().WithName("From Date").WithMessage(ValidationRes.requiredAttribute);

            // Amount is required
            RuleFor(ld => ld.Amount)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .ScalePrecision(2, 9).WithName("Amount").WithMessage(ValidationRes.vmMaxLengthError)
                .InclusiveBetween(0.00m, 9999999.99m).WithMessage(ValidationRes.amountMustBeGreaterThanOrEqualToZero);

            RuleFor(ld => ld.Description)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .MaximumLength(250).WithMessage(ValidationRes.vmMaxLengthError)
                .Matches(ValidatorHelper.ResourceDetailRemarkRegex).WithMessage(ValidationRes.vmGenericInvalidError)
                .When(ld => !string.IsNullOrWhiteSpace(ld.Description));
        }
    }
}