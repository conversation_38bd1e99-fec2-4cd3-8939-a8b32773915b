﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Main {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ACADef {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ACADef() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Main.ACADef", typeof(ACADef).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Affordable Care Act Definitions.
        /// </summary>
        public static string acaButton {
            get {
                return ResourceManager.GetString("acaButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ACA is the comprehensive health care reform law enacted in March 2010. The law was enacted in two parts: The Patient Protection and Affordable Care Act was signed into law on March 23, 2010 and was amended by the Health Care and Education Reconciliation Act on March 30, 2010. The name “Affordable Care Act” is used to refer to the final, amended version of the law..
        /// </summary>
        public static string definition1_1 {
            get {
                return ResourceManager.GetString("definition1_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A navigator is an individual or organization that&apos;s trained and able to help consumers, small businesses, and their employees as they look for health coverage options through the Marketplace, including completing eligibility and enrollment forms. These individuals and organizations are required to be unbiased. Their services are free to consumers..
        /// </summary>
        public static string definition10_1 {
            get {
                return ResourceManager.GetString("definition10_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The period of time that individuals are eligible to enroll in a Qualified Health Plan can enroll in a plan in the Marketplace.  For 2014, the Open Enrollment Period is October 1, 2013–March 31, 2014. For 2015 and later years, the Open Enrollment Period is October 15 to December 7 of the previous year. Individuals may also qualify for Special Enrollment Periods outside of Open Enrollment if they experience certain events. (See Special Enrollment Period and Qualifying Life Event).
        /// </summary>
        public static string definition11_1 {
            get {
                return ResourceManager.GetString("definition11_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can submit an application for health coverage outside of the Marketplace, or apply for Medicaid or CHIP, at any time of the year..
        /// </summary>
        public static string definition11_2 {
            get {
                return ResourceManager.GetString("definition11_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Affordable Care Act provides a new tax credit to help you afford health coverage purchased through the Marketplace. Advance payments of the tax credit can be used right away to lower your monthly premium costs. If you qualify, you may choose how much advance credit payments to apply to your premiums each month, up to a maximum amount. If the amount of advance credit payments you get for the year is less than the tax credit you&apos;re due, you’ll get the difference as a refundable credit when you file your f [rest of string was truncated]&quot;;.
        /// </summary>
        public static string definition12_1 {
            get {
                return ResourceManager.GetString("definition12_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The net income you earn from your own trade or business. For example, any net income (profit) you earn from goods you sell or services you provide to others counts as self-employment income..
        /// </summary>
        public static string definition13_1 {
            get {
                return ResourceManager.GetString("definition13_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Self-employment income could also come from a distributive share from a partnership..
        /// </summary>
        public static string definition13_2 {
            get {
                return ResourceManager.GetString("definition13_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Affordable Care Act provides a new tax credit to help you afford health coverage purchased through the Marketplace. Advance payments of the tax credit can be used right away to lower your monthly premium costs. If you qualify, you may choose how much advance credit payments to apply to your premiums each month, up to a maximum amount. If the amount of advance credit payments you get for the year is less than the tax credit you&apos;re due, you&apos;ll get the difference as a refundable credit when you file your f [rest of string was truncated]&quot;;.
        /// </summary>
        public static string definition2_1 {
            get {
                return ResourceManager.GetString("definition2_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An authorized representative is someone who you choose to act on your behalf with the Marketplace, like a family member or other trusted person. Some authorized representatives may have legal authority to act on your behalf..
        /// </summary>
        public static string definition3_1 {
            get {
                return ResourceManager.GetString("definition3_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An individual (affiliated with a designated organization) who is trained and able to help consumers, small businesses, and their employees as they look for health coverage options through the Marketplace, including helping them complete eligibility and enrollment forms. Their services are free to consumers..
        /// </summary>
        public static string definition4_1 {
            get {
                return ResourceManager.GetString("definition4_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The share of costs covered by your insurance that you pay out of your own pocket. This term generally includes deductibles, coinsurance, and copayments, or similar charges, but it doesn&apos;t include premiums, balance billing amounts for non-network providers, or the cost of non-covered services. Cost sharing in Medicaid and CHIP also includes premiums..
        /// </summary>
        public static string definition5_1 {
            get {
                return ResourceManager.GetString("definition5_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A discount that lowers the amount you have to pay out-of-pocket for deductibles, coinsurance, and copayments. You can get this reduction if you get health insurance through the Marketplace, your income is below a certain level, and you choose a health plan from the Silver plan category (see Health Plan Categories). If you&apos;re a member of a federally recognized tribe, you may qualify for additional cost-sharing benefits..
        /// </summary>
        public static string definition6_1 {
            get {
                return ResourceManager.GetString("definition6_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A resource where individuals, families, and small businesses can learn about their health coverage options; compare health insurance plans based on costs, benefits, and other important features; choose a plan; and enroll in coverage. The Marketplace also provides information on programs that help people with low to moderate income and resources pay for coverage. This includes ways to save on the monthly premiums and out-of-pocket costs of coverage available through the Marketplace, and information about oth [rest of string was truncated]&quot;;.
        /// </summary>
        public static string definition7_1 {
            get {
                return ResourceManager.GetString("definition7_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The type of coverage an individual needs to have to meet the individual responsibility requirement under the Affordable Care Act. This includes individual market policies, job-based coverage, Medicare, Medicaid, CHIP, TRICARE and certain other coverage..
        /// </summary>
        public static string definition8_1 {
            get {
                return ResourceManager.GetString("definition8_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The figure used to determine eligibility for lower costs in the Marketplace and for Medicaid and CHIP. Generally, modified adjusted gross income is your adjusted gross income plus any tax-exempt Social Security, interest, or foreign income you have..
        /// </summary>
        public static string definition9_1 {
            get {
                return ResourceManager.GetString("definition9_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is the Affordable Care Act?.
        /// </summary>
        public static string question1 {
            get {
                return ResourceManager.GetString("question1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is a Navigator?.
        /// </summary>
        public static string question10 {
            get {
                return ResourceManager.GetString("question10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is meant by the Open Enrollment Period?.
        /// </summary>
        public static string question11 {
            get {
                return ResourceManager.GetString("question11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is a Premium Tax Credit?.
        /// </summary>
        public static string question12 {
            get {
                return ResourceManager.GetString("question12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is Self-Employment Income?.
        /// </summary>
        public static string question13 {
            get {
                return ResourceManager.GetString("question13", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is a Advanced Premium Tax Credit?.
        /// </summary>
        public static string question2 {
            get {
                return ResourceManager.GetString("question2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is an Authorized Representative?.
        /// </summary>
        public static string question3 {
            get {
                return ResourceManager.GetString("question3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is a Certified Applicant Counselor?.
        /// </summary>
        public static string question4 {
            get {
                return ResourceManager.GetString("question4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is Cost Sharing?.
        /// </summary>
        public static string question5 {
            get {
                return ResourceManager.GetString("question5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is Cost Sharing Reduction?.
        /// </summary>
        public static string question6 {
            get {
                return ResourceManager.GetString("question6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is the Health Insurance Marketplace?.
        /// </summary>
        public static string question7 {
            get {
                return ResourceManager.GetString("question7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is meant by Minimum Essential Coverage?.
        /// </summary>
        public static string question8 {
            get {
                return ResourceManager.GetString("question8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is Modified Adjusted Gross Income(MAGI)?.
        /// </summary>
        public static string question9 {
            get {
                return ResourceManager.GetString("question9", resourceCulture);
            }
        }
    }
}
