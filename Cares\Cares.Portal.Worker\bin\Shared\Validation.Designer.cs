﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.Shared {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Validation {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Validation() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.Shared.Validation", typeof(Validation).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must accept the terms..
        /// </summary>
        public static string acceptTerms {
            get {
                return ResourceManager.GetString("acceptTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Error. Could not create an account..
        /// </summary>
        public static string accountNotCreated {
            get {
                return ResourceManager.GetString("accountNotCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address is required..
        /// </summary>
        public static string addressIsRequired {
            get {
                return ResourceManager.GetString("addressIsRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age must be between 0 and 19 years..
        /// </summary>
        public static string ageBetween0And19 {
            get {
                return ResourceManager.GetString("ageBetween0And19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age cannot be more than 120 years..
        /// </summary>
        public static string ageLessThanEqual120 {
            get {
                return ResourceManager.GetString("ageLessThanEqual120", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age must be less than or equal to 19 years..
        /// </summary>
        public static string ageLessThanOrEqual19 {
            get {
                return ResourceManager.GetString("ageLessThanOrEqual19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alien number is required..
        /// </summary>
        public static string alienNumber {
            get {
                return ResourceManager.GetString("alienNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} amount must be greater than or equal to 0..
        /// </summary>
        public static string amountMustBeGreaterThanOrEqualToZero {
            get {
                return ResourceManager.GetString("amountMustBeGreaterThanOrEqualToZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please answer all required fields marked with *.
        /// </summary>
        public static string answerAllRequired {
            get {
                return ResourceManager.GetString("answerAllRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment should not exceed amount.
        /// </summary>
        public static string atCurrentBalanceAmt {
            get {
                return ResourceManager.GetString("atCurrentBalanceAmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment amount is not match with total applied amount..
        /// </summary>
        public static string atPaymentAppliedAmt {
            get {
                return ResourceManager.GetString("atPaymentAppliedAmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Date must be within one year of the Start Date.
        /// </summary>
        public static string cancelDateWithinOnYearOfStartDate {
            get {
                return ResourceManager.GetString("cancelDateWithinOnYearOfStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} cannot be a future year..
        /// </summary>
        public static string cannotBeAFutureYear {
            get {
                return ResourceManager.GetString("cannotBeAFutureYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Birth for unborn cannot be set before the unborn was created..
        /// </summary>
        public static string cannotBeforeCreated {
            get {
                return ResourceManager.GetString("cannotBeforeCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a year greater or equal to 1900..
        /// </summary>
        public static string cannotBeLessThanYear1900 {
            get {
                return ResourceManager.GetString("cannotBeLessThanYear1900", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a year greater or equal to 1950..
        /// </summary>
        public static string cannotBeLessThanYear1950 {
            get {
                return ResourceManager.GetString("cannotBeLessThanYear1950", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} cannot exceed one year from the current year..
        /// </summary>
        public static string cannotExceedOneFromCurrentYear {
            get {
                return ResourceManager.GetString("cannotExceedOneFromCurrentYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Birth must be a valid date that is not in the future..
        /// </summary>
        public static string cannotFutureDate {
            get {
                return ResourceManager.GetString("cannotFutureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Card/receipt number is required..
        /// </summary>
        public static string cardReceiptptNumber {
            get {
                return ResourceManager.GetString("cardReceiptptNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;{PropertyName}&apos; and &apos;{0}&apos; fields do not match..
        /// </summary>
        public static string compareFields {
            get {
                return ResourceManager.GetString("compareFields", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Social Security Number and Social Security Number fields do not match..
        /// </summary>
        public static string compareFieldsPI {
            get {
                return ResourceManager.GetString("compareFieldsPI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please confirm only one provider..
        /// </summary>
        public static string confirmOnlyOneProvider {
            get {
                return ResourceManager.GetString("confirmOnlyOneProvider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country of Issuance is required..
        /// </summary>
        public static string countryofIssuance {
            get {
                return ResourceManager.GetString("countryofIssuance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From date cannot be beyond 12 months in the future from the current date..
        /// </summary>
        public static string dateGreaterThan12MonthsInTheFuture {
            get {
                return ResourceManager.GetString("dateGreaterThan12MonthsInTheFuture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} cannot be beyond 12 months in the future from the current date..
        /// </summary>
        public static string dateGreaterThan12MonthsInTheFutureGeneral {
            get {
                return ResourceManager.GetString("dateGreaterThan12MonthsInTheFutureGeneral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From date cannot be beyond 36 months in the past.
        /// </summary>
        public static string dateGreaterThan36MonthsInThePast {
            get {
                return ResourceManager.GetString("dateGreaterThan36MonthsInThePast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date must be greater than the Date of Birth..
        /// </summary>
        public static string dateMustBeGreaterThanTheDateOfBirth {
            get {
                return ResourceManager.GetString("dateMustBeGreaterThanTheDateOfBirth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be between {1} and {2}.
        /// </summary>
        public static string dateRange {
            get {
                return ResourceManager.GetString("dateRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document description is required..
        /// </summary>
        public static string documentDescription {
            get {
                return ResourceManager.GetString("documentDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document expiry date is required..
        /// </summary>
        public static string documentExpiryDate {
            get {
                return ResourceManager.GetString("documentExpiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of death cannot be prior to date of birth..
        /// </summary>
        public static string dodBeforeDob {
            get {
                return ResourceManager.GetString("dodBeforeDob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to That SSN has already been used by a different person. Please resolve before continuing..
        /// </summary>
        public static string drasticAlertSSSN {
            get {
                return ResourceManager.GetString("drasticAlertSSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email is already registered..
        /// </summary>
        public static string duplicateEmail {
            get {
                return ResourceManager.GetString("duplicateEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserName already exists. Please enter another..
        /// </summary>
        public static string duplicateUsername {
            get {
                return ResourceManager.GetString("duplicateUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Name of Company, Policy #, Policy Value, Cash Surrender Value or Address..
        /// </summary>
        public static string edLifeInsuranceDetailRecordIncomplete {
            get {
                return ResourceManager.GetString("edLifeInsuranceDetailRecordIncomplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multiple phones of the same type specified. Please submit only one (1) phone of each type..
        /// </summary>
        public static string edRepresentativeDuplicatePhoneType {
            get {
                return ResourceManager.GetString("edRepresentativeDuplicatePhoneType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multiple resource types of the same type specified. Please submit only one (1) resource type of each type..
        /// </summary>
        public static string edResourceDetailDuplicateType {
            get {
                return ResourceManager.GetString("edResourceDetailDuplicateType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please complete at least one additional field..
        /// </summary>
        public static string edResourceDetailRecordIncomplete {
            get {
                return ResourceManager.GetString("edResourceDetailRecordIncomplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please fix the following.
        /// </summary>
        public static string errorSummaryHeader {
            get {
                return ResourceManager.GetString("errorSummaryHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be {0} characters..
        /// </summary>
        public static string exactLength {
            get {
                return ResourceManager.GetString("exactLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name should not start with Unborn..
        /// </summary>
        public static string firstNameUnborn {
            get {
                return ResourceManager.GetString("firstNameUnborn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be on the first of the month..
        /// </summary>
        public static string firstOfTheMonth {
            get {
                return ResourceManager.GetString("firstOfTheMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid {PropertyName}..
        /// </summary>
        public static string genericValidationError {
            get {
                return ResourceManager.GetString("genericValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home address is required..
        /// </summary>
        public static string homeAddressIsRequired {
            get {
                return ResourceManager.GetString("homeAddressIsRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I94 number is required..
        /// </summary>
        public static string i94 {
            get {
                return ResourceManager.GetString("i94", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;div id=&apos;InfoHeader&apos;&gt;&lt;img src=&apos;../Content/images/info.png&apos; alt=&apos;information&apos; style=&apos;padding-bottom: 5px;padding-right: 10px;&apos;&gt;&lt;span&gt;[HeaderMessage]&lt;/span&gt;&lt;/div&gt;&lt;br&gt;&lt;ul style=&apos;margin-left: 25px;&apos;&gt;.
        /// </summary>
        public static string InformationalMessage {
            get {
                return ResourceManager.GetString("InformationalMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal error. Please contact administrator !!!.
        /// </summary>
        public static string internalErrorOnAccountCreation {
            get {
                return ResourceManager.GetString("internalErrorOnAccountCreation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter valid account number..
        /// </summary>
        public static string invalidAccountNumber {
            get {
                return ResourceManager.GetString("invalidAccountNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid Alien number..
        /// </summary>
        public static string invalidAlienNumber {
            get {
                return ResourceManager.GetString("invalidAlienNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid {PropertyName} amount..
        /// </summary>
        public static string invalidAmount {
            get {
                return ResourceManager.GetString("invalidAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid amount received or given..
        /// </summary>
        public static string invalidAmountReceivedOrGiven {
            get {
                return ResourceManager.GetString("invalidAmountReceivedOrGiven", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid attested income amount.
        /// </summary>
        public static string invalidAttestedAmount {
            get {
                return ResourceManager.GetString("invalidAttestedAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid name of bank/credit union/brokerage firm..
        /// </summary>
        public static string invalidBankOrCreditUnionOrBrokerageFirm {
            get {
                return ResourceManager.GetString("invalidBankOrCreditUnionOrBrokerageFirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid claim number.
        /// </summary>
        public static string invalidClaimNumber {
            get {
                return ResourceManager.GetString("invalidClaimNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid current balance amount..
        /// </summary>
        public static string invalidCurrentBalance {
            get {
                return ResourceManager.GetString("invalidCurrentBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid date closed..
        /// </summary>
        public static string invalidDateClosed {
            get {
                return ResourceManager.GetString("invalidDateClosed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid date given or sold..
        /// </summary>
        public static string invalidDateSoldOrGiven {
            get {
                return ResourceManager.GetString("invalidDateSoldOrGiven", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid death verification source.
        /// </summary>
        public static string invalidDeathVerificationSource {
            get {
                return ResourceManager.GetString("invalidDeathVerificationSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be a valid email..
        /// </summary>
        public static string invalidEmail {
            get {
                return ResourceManager.GetString("invalidEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid fax number..
        /// </summary>
        public static string invalidFaxNumber {
            get {
                return ResourceManager.GetString("invalidFaxNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter valid Item description..
        /// </summary>
        public static string invalidItemDescription {
            get {
                return ResourceManager.GetString("invalidItemDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid other type of account..
        /// </summary>
        public static string invalidOtherAccountType {
            get {
                return ResourceManager.GetString("invalidOtherAccountType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid other description.
        /// </summary>
        public static string invalidOtherDescription {
            get {
                return ResourceManager.GetString("invalidOtherDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid other living arrangement..
        /// </summary>
        public static string invalidOtherLivingArrangement {
            get {
                return ResourceManager.GetString("invalidOtherLivingArrangement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid owner of the other phone number.
        /// </summary>
        public static string invalidOtherPhoneOwner {
            get {
                return ResourceManager.GetString("invalidOtherPhoneOwner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid other race..
        /// </summary>
        public static string invalidOtherRace {
            get {
                return ResourceManager.GetString("invalidOtherRace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid other spoken language.
        /// </summary>
        public static string invalidOtherSpokenLanguage {
            get {
                return ResourceManager.GetString("invalidOtherSpokenLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid {PropertyName}..
        /// </summary>
        public static string invalidReason {
            get {
                return ResourceManager.GetString("invalidReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid remarks..
        /// </summary>
        public static string invalidRemarks {
            get {
                return ResourceManager.GetString("invalidRemarks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid rent amount.
        /// </summary>
        public static string invalidRentAmount {
            get {
                return ResourceManager.GetString("invalidRentAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid name of person to whom it was sold or given..
        /// </summary>
        public static string invalidToWhomSoldOrGiven {
            get {
                return ResourceManager.GetString("invalidToWhomSoldOrGiven", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid verified income amount.
        /// </summary>
        public static string invalidVerifiedAmount {
            get {
                return ResourceManager.GetString("invalidVerifiedAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZipCode must be 5 or 9 characters..
        /// </summary>
        public static string invalidZipCode {
            get {
                return ResourceManager.GetString("invalidZipCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be a valid future date..
        /// </summary>
        public static string isFutureDate {
            get {
                return ResourceManager.GetString("isFutureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} cannot be less than Date of Birth..
        /// </summary>
        public static string lessThanDob {
            get {
                return ResourceManager.GetString("lessThanDob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Liability Through Date must be greater than or equal to the From Date..
        /// </summary>
        public static string liabilityToDateMustBeGreaterThanFromDate {
            get {
                return ResourceManager.GetString("liabilityToDateMustBeGreaterThanFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mailing address is required..
        /// </summary>
        public static string mailingAddressIsRequired {
            get {
                return ResourceManager.GetString("mailingAddressIsRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marriage End Date must be after Begin Date..
        /// </summary>
        public static string marriageEndDateAfterBeginDate {
            get {
                return ResourceManager.GetString("marriageEndDateAfterBeginDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be between 5 and  {MaxLength} characters..
        /// </summary>
        public static string maxLength {
            get {
                return ResourceManager.GetString("maxLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;li&gt;&lt;a style=&apos;cursor: pointer&apos;data-error-field-name=&apos;[Element]&apos;&gt;[message]&lt;/a&gt;&lt;/li&gt;.
        /// </summary>
        public static string messageLi {
            get {
                return ResourceManager.GetString("messageLi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be a minimum of {MinLength} characters..
        /// </summary>
        public static string minLength {
            get {
                return ResourceManager.GetString("minLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be a minimum of 3 characters, special characters not counted..
        /// </summary>
        public static string minLengthWithOutSpecialCharacters {
            get {
                return ResourceManager.GetString("minLengthWithOutSpecialCharacters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be between {MinLength} and {MaxLength} characters..
        /// </summary>
        public static string minMaxLength {
            get {
                return ResourceManager.GetString("minMaxLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be greater than or equal to 0..
        /// </summary>
        public static string mustBeGreaterThanOrEqualToZero {
            get {
                return ResourceManager.GetString("mustBeGreaterThanOrEqualToZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be selected..
        /// </summary>
        public static string mustBeTrueAttribute {
            get {
                return ResourceManager.GetString("mustBeTrueAttribute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please make unborn a newborn before providing a valid SSN..
        /// </summary>
        public static string mustNewbornForSSN {
            get {
                return ResourceManager.GetString("mustNewbornForSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you provide a Death verification, you must include a DOD..
        /// </summary>
        public static string needDoD {
            get {
                return ResourceManager.GetString("needDoD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be within 3 months of received date.
        /// </summary>
        public static string newBornDOB {
            get {
                return ResourceManager.GetString("newBornDOB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Segment cannot be created after terminating..
        /// </summary>
        public static string noAnySegmentsAfterTerminatedSegment {
            get {
                return ResourceManager.GetString("noAnySegmentsAfterTerminatedSegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} cannot be future date..
        /// </summary>
        public static string noFutureDate {
            get {
                return ResourceManager.GetString("noFutureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A gap in coverage is not allowed.
        /// </summary>
        public static string noGaps {
            get {
                return ResourceManager.GetString("noGaps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The worker must specify the termination reason..
        /// </summary>
        public static string noLessThanOneYear {
            get {
                return ResourceManager.GetString("noLessThanOneYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} cannot be less than 2 years from the current date..
        /// </summary>
        public static string noLessthanTwoYears {
            get {
                return ResourceManager.GetString("noLessthanTwoYears", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terminated segment cannot have multiple segment..
        /// </summary>
        public static string noMultipleTerminatedSegments {
            get {
                return ResourceManager.GetString("noMultipleTerminatedSegments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select at least one option..
        /// </summary>
        public static string nonCitizenDocumentInformation {
            get {
                return ResourceManager.GetString("nonCitizenDocumentInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} cannot be past date..
        /// </summary>
        public static string noPastDate {
            get {
                return ResourceManager.GetString("noPastDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select one Primary Sponsor.
        /// </summary>
        public static string OnePrimarySponsor {
            get {
                return ResourceManager.GetString("OnePrimarySponsor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enrollment coverage must be up to one year past the Award Date..
        /// </summary>
        public static string oneYearOfCoverage {
            get {
                return ResourceManager.GetString("oneYearOfCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select either Award or Denial..
        /// </summary>
        public static string onlyAwardOrDenial {
            get {
                return ResourceManager.GetString("onlyAwardOrDenial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Ethnicity description is required..
        /// </summary>
        public static string otherEthnicityRequired {
            get {
                return ResourceManager.GetString("otherEthnicityRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter valid other insurance name..
        /// </summary>
        public static string otherInsuranceName {
            get {
                return ResourceManager.GetString("otherInsuranceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Race description is required..
        /// </summary>
        public static string otherRaceRequired {
            get {
                return ResourceManager.GetString("otherRaceRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overlapping segments are not allowed.
        /// </summary>
        public static string overlappingSegmentsNotAllowed {
            get {
                return ResourceManager.GetString("overlappingSegmentsNotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passport expiry date is required..
        /// </summary>
        public static string passportExpirydate {
            get {
                return ResourceManager.GetString("passportExpirydate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passport Number is required..
        /// </summary>
        public static string passportNumber {
            get {
                return ResourceManager.GetString("passportNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} is invalid..
        /// </summary>
        public static string patternAttribute {
            get {
                return ResourceManager.GetString("patternAttribute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to That person was not Found..
        /// </summary>
        public static string personNotFound {
            get {
                return ResourceManager.GetString("personNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telephone number must contain 10 digits..
        /// </summary>
        public static string PhoneNumberRequired {
            get {
                return ResourceManager.GetString("PhoneNumberRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone Type Id is required..
        /// </summary>
        public static string phoneTypeIdRequired {
            get {
                return ResourceManager.GetString("phoneTypeIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QMB Start Date must be first of next month after award month..
        /// </summary>
        public static string qmbMustBeFirstOfNextMonth {
            get {
                return ResourceManager.GetString("qmbMustBeFirstOfNextMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select either &quot;Yes&quot; or &quot;No&quot; for all options..
        /// </summary>
        public static string radioButtonSelectionRequired {
            get {
                return ResourceManager.GetString("radioButtonSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valid range is between {1} and {2}..
        /// </summary>
        public static string range {
            get {
                return ResourceManager.GetString("range", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount received or given must be greater than or equal to 0..
        /// </summary>
        public static string receivedOrGivenAmountGreaterThanOrEqualToZero {
            get {
                return ResourceManager.GetString("receivedOrGivenAmountGreaterThanOrEqualToZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provide either an Application Id or a Worker Reminder Id.
        /// </summary>
        public static string reminderAppIdOrReminderId {
            get {
                return ResourceManager.GetString("reminderAppIdOrReminderId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid Other description..
        /// </summary>
        public static string reminderOtherDescription {
            get {
                return ResourceManager.GetString("reminderOtherDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other description must be between 0 and 250 characters..
        /// </summary>
        public static string reminderOtherDescriptionLength {
            get {
                return ResourceManager.GetString("reminderOtherDescriptionLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} is required..
        /// </summary>
        public static string requiredAttribute {
            get {
                return ResourceManager.GetString("requiredAttribute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select at least one option in fields marked as required..
        /// </summary>
        public static string selectionRequired {
            get {
                return ResourceManager.GetString("selectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sevis Id is required..
        /// </summary>
        public static string sevisId {
            get {
                return ResourceManager.GetString("sevisId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sex is a required field..
        /// </summary>
        public static string sexRequired {
            get {
                return ResourceManager.GetString("sexRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sex cannot be &quot;Unknown&quot;..
        /// </summary>
        public static string sexUnknown {
            get {
                return ResourceManager.GetString("sexUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSN and Confirm SSN must match..
        /// </summary>
        public static string ssnAndConfirmSsnMissMatchError {
            get {
                return ResourceManager.GetString("ssnAndConfirmSsnMissMatchError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (?:\d{3})-(?:\d{2})-(\d{4}).
        /// </summary>
        public static string ssnPattern {
            get {
                return ResourceManager.GetString("ssnPattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date must be less than Cancel Date.
        /// </summary>
        public static string startDateLessThanCancelDate {
            get {
                return ResourceManager.GetString("startDateLessThanCancelDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date must be equal or greater than the retro date. ({RETRODATE}).
        /// </summary>
        public static string startDateNotPriorToRetroDate {
            get {
                return ResourceManager.GetString("startDateNotPriorToRetroDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date cannot be prior to Retro.
        /// </summary>
        public static string startDatePriorToRetro {
            get {
                return ResourceManager.GetString("startDatePriorToRetro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must accept terms and conditions to proceed..
        /// </summary>
        public static string termsAndCondition {
            get {
                return ResourceManager.GetString("termsAndCondition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please try again later..
        /// </summary>
        public static string tryLater {
            get {
                return ResourceManager.GetString("tryLater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unexpected error occurred. Please try again later !!!.
        /// </summary>
        public static string userRejected {
            get {
                return ResourceManager.GetString("userRejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For the Quarterly frequency the months should be selected as either &apos;March, June, September, or December&apos;.
        /// </summary>
        public static string validateQuarterlyFrequency {
            get {
                return ResourceManager.GetString("validateQuarterlyFrequency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} must be a valid date..
        /// </summary>
        public static string validDate {
            get {
                return ResourceManager.GetString("validDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid employer name..
        /// </summary>
        public static string validEmployerName {
            get {
                return ResourceManager.GetString("validEmployerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid first name..
        /// </summary>
        public static string validFirstName {
            get {
                return ResourceManager.GetString("validFirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid last name..
        /// </summary>
        public static string validLastName {
            get {
                return ResourceManager.GetString("validLastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid maiden name..
        /// </summary>
        public static string validMaidenName {
            get {
                return ResourceManager.GetString("validMaidenName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid middle name..
        /// </summary>
        public static string validMiddleName {
            get {
                return ResourceManager.GetString("validMiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid name..
        /// </summary>
        public static string validName {
            get {
                return ResourceManager.GetString("validName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter valid phone extension..
        /// </summary>
        public static string ValidPhoneExtension {
            get {
                return ResourceManager.GetString("ValidPhoneExtension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter valid other VA information.
        /// </summary>
        public static string validVAOtherInfo {
            get {
                return ResourceManager.GetString("validVAOtherInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An SSN is required when &quot;Is VA Claim# same as SSN?&quot; is answered &quot;Yes&quot;..
        /// </summary>
        public static string veteranSsnRequiredForClaimNumber {
            get {
                return ResourceManager.GetString("veteranSsnRequiredForClaimNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} is invalid..
        /// </summary>
        public static string vmGenericInvalidError {
            get {
                return ResourceManager.GetString("vmGenericInvalidError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} is greater than the max length of {MaxLength}..
        /// </summary>
        public static string vmGenericMaxLengthError {
            get {
                return ResourceManager.GetString("vmGenericMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  does not meet the number of characters required..
        /// </summary>
        public static string vmLengthError {
            get {
                return ResourceManager.GetString("vmLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} is greater than the max length..
        /// </summary>
        public static string vmMaxLengthError {
            get {
                return ResourceManager.GetString("vmMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  is greater than the max value..
        /// </summary>
        public static string vmMaxValueError {
            get {
                return ResourceManager.GetString("vmMaxValueError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  is less than the min length..
        /// </summary>
        public static string vmMinLengthError {
            get {
                return ResourceManager.GetString("vmMinLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  is less than the min value..
        /// </summary>
        public static string vmMinValueError {
            get {
                return ResourceManager.GetString("vmMinValueError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} has invalid characters or is improperly formatted..
        /// </summary>
        public static string vmPatternError {
            get {
                return ResourceManager.GetString("vmPatternError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Server-Side Schema error.  Please call support center..
        /// </summary>
        public static string vmSchemaError {
            get {
                return ResourceManager.GetString("vmSchemaError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid who owns the property.
        /// </summary>
        public static string whoOwnsProperty {
            get {
                return ResourceManager.GetString("whoOwnsProperty", resourceCulture);
            }
        }
    }
}
