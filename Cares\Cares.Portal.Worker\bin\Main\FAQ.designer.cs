﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Main {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class FAQ {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FAQ() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Main.FAQ", typeof(FAQ).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOBRA, ALL Kids and Medicaid for Low Income Families (MLIF) are programs that pay for health care coverage for children under age 19 in low income families.  SOBRA and MLIF pay for health care coverage for pregnant women.  MLIF also pays for health care coverage for low income adults with children under age 19.  Plan First is a program for low income females, aged 19 - 55, for family planning (birth control) services only..
        /// </summary>
        public static string answer1_1 {
            get {
                return ResourceManager.GetString("answer1_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On the Alabamacares.Alabama.gov Login page:.
        /// </summary>
        public static string answer10_1 {
            get {
                return ResourceManager.GetString("answer10_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter your user name and password to log into your account.
        /// </summary>
        public static string answer10_2 {
            get {
                return ResourceManager.GetString("answer10_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From the &quot;Dashboard&quot; screen, click the &quot;Finish Incomplete Application&quot; button beside the application information..
        /// </summary>
        public static string answer10_3 {
            get {
                return ResourceManager.GetString("answer10_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information that you have entered into a particular field can be corrected while you are still in that field by simply using the backspace key to remove anything that you entered.  After doing this you can retype your information correctly.  There are several section summary screens that will allow remind you to make sure all previous information is correct before moving on.  At these summary screens you there may be ways to delete and to add information.  At any time, while filling out the application, you [rest of string was truncated]&quot;;.
        /// </summary>
        public static string answer11_1 {
            get {
                return ResourceManager.GetString("answer11_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open the Help Center by clicking the ? in the top right of the website.  For questions or help filling out the on-line application, call the ALL Kids toll-free number at [CHIPPHONE] Monday-Friday to speak with a Customer Service representative.  Spanish is available if needed.  You may leave a message if needed.  Email is also available for help by clicking the link in the Help Center(?) section under Contact Us..
        /// </summary>
        public static string answer12_1 {
            get {
                return ResourceManager.GetString("answer12_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Under most programs you will be eligible for one year from the date your application is awarded. You will be sent a “renewal” notice prior to the one year anniversary and you will return the renewal to the program and you will have to be re-determined as to whether you and/or your children are still eligible for that program. If it is determined that you are no longer eligible for that program, your renewal will be forwarded to the agency you and/or your children may be eligible for..
        /// </summary>
        public static string answer13_1 {
            get {
                return ResourceManager.GetString("answer13_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The information on the Review and Sign page means that everything you have entered into the on-line application will be used to verify (check) income, insurance, or other information. The agencies check with other state and federal agencies, banks, employers, etc., to make sure the information you entered is correct. Also, if you are not satisfied by the decision reached on your application, you may request a fair hearing and the agency will look at the application again. By checking the box on the Review a [rest of string was truncated]&quot;;.
        /// </summary>
        public static string answer14_1 {
            get {
                return ResourceManager.GetString("answer14_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The website will tell you immediately if it looks like you and/or your children may be eligible for one or more of the programs. .
        /// </summary>
        public static string answer15_1 {
            get {
                return ResourceManager.GetString("answer15_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NOTE]: The computer cannot make the decision as to whether you and/or your children are eligible for these health care programs if any documentation is required, information from other agencies need to be verified, discrepancies are noted in the entered data, etc...  It may take up to 8 weeks to process the application due to these issues..
        /// </summary>
        public static string answer15_2 {
            get {
                return ResourceManager.GetString("answer15_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A “household” is made up of children under age 19, their parent(s), guardian(s), and/or caretaker(s) who all live in the home.  For the MLIF program, the caretaker must be related to the child and the relationship must be verified(must have proof).  For all other programs the caretaker does not have to be related to the children living in the home..
        /// </summary>
        public static string answer16_1 {
            get {
                return ResourceManager.GetString("answer16_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Household income includes income from a job or self-employment, and other income, such as:.
        /// </summary>
        public static string answer17_1 {
            get {
                return ResourceManager.GetString("answer17_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Miner&apos;s Benefits.
        /// </summary>
        public static string answer17_10 {
            get {
                return ResourceManager.GetString("answer17_10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Black Lung Benefits.
        /// </summary>
        public static string answer17_11 {
            get {
                return ResourceManager.GetString("answer17_11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Contributions (from relatives, others).
        /// </summary>
        public static string answer17_12 {
            get {
                return ResourceManager.GetString("answer17_12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rental Income (land, buildings, from roomer).
        /// </summary>
        public static string answer17_13 {
            get {
                return ResourceManager.GetString("answer17_13", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Loans (from relatives, others).
        /// </summary>
        public static string answer17_14 {
            get {
                return ResourceManager.GetString("answer17_14", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unemployment Compensation.
        /// </summary>
        public static string answer17_15 {
            get {
                return ResourceManager.GetString("answer17_15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance Annuity or Proceeds.
        /// </summary>
        public static string answer17_16 {
            get {
                return ResourceManager.GetString("answer17_16", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Government Payments on Land.
        /// </summary>
        public static string answer17_17 {
            get {
                return ResourceManager.GetString("answer17_17", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Royalties.
        /// </summary>
        public static string answer17_18 {
            get {
                return ResourceManager.GetString("answer17_18", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security.
        /// </summary>
        public static string answer17_2 {
            get {
                return ResourceManager.GetString("answer17_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interest on Savings.
        /// </summary>
        public static string answer17_20 {
            get {
                return ResourceManager.GetString("answer17_20", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Legal Settlements.
        /// </summary>
        public static string answer17_21 {
            get {
                return ResourceManager.GetString("answer17_21", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sheltered Workshop Earnings.
        /// </summary>
        public static string answer17_22 {
            get {
                return ResourceManager.GetString("answer17_22", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lump Sums.
        /// </summary>
        public static string answer17_23 {
            get {
                return ResourceManager.GetString("answer17_23", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dividends.
        /// </summary>
        public static string answer17_24 {
            get {
                return ResourceManager.GetString("answer17_24", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to School Grants or Loans.
        /// </summary>
        public static string answer17_25 {
            get {
                return ResourceManager.GetString("answer17_25", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coal, Oil, Gravel Rights and Timber Leases.
        /// </summary>
        public static string answer17_26 {
            get {
                return ResourceManager.GetString("answer17_26", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NOTE]: If you receive money from someone else, that money is also counted as income..
        /// </summary>
        public static string answer17_27 {
            get {
                return ResourceManager.GetString("answer17_27", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSI.
        /// </summary>
        public static string answer17_3 {
            get {
                return ResourceManager.GetString("answer17_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public Assistance.
        /// </summary>
        public static string answer17_4 {
            get {
                return ResourceManager.GetString("answer17_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Railroad Retirement.
        /// </summary>
        public static string answer17_5 {
            get {
                return ResourceManager.GetString("answer17_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veterans Benefits, Pensions, Compensation or Insurance.
        /// </summary>
        public static string answer17_6 {
            get {
                return ResourceManager.GetString("answer17_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Federal Civil Service Annuity.
        /// </summary>
        public static string answer17_7 {
            get {
                return ResourceManager.GetString("answer17_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State Retirement/Pension.
        /// </summary>
        public static string answer17_8 {
            get {
                return ResourceManager.GetString("answer17_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private Pension.
        /// </summary>
        public static string answer17_9 {
            get {
                return ResourceManager.GetString("answer17_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you have medical bills in the 3 months before you submit an application for Medicaid, you may be eligible for Medicaid to pay those bills.  Your income from those months must have been within the Medicaid income limits and the medical services received must have been Medicaid covered services..
        /// </summary>
        public static string answer18_1 {
            get {
                return ResourceManager.GetString("answer18_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.  You must have a signed statement from a doctor or health care clinic verifying (proving) that you are pregnant and the date your baby is due..
        /// </summary>
        public static string answer19_1 {
            get {
                return ResourceManager.GetString("answer19_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOBRA - Low income children under age 19 and pregnant women..
        /// </summary>
        public static string answer2_1 {
            get {
                return ResourceManager.GetString("answer2_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ALL Kids - Low income children under age 19 whose income is higher than the SOBRA income limits..
        /// </summary>
        public static string answer2_2 {
            get {
                return ResourceManager.GetString("answer2_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MLIF – Children under age 19 and adults with children under age 19 with very low income..
        /// </summary>
        public static string answer2_3 {
            get {
                return ResourceManager.GetString("answer2_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plan First – Low income females aged 19 – 55 for family planning services only..
        /// </summary>
        public static string answer2_4 {
            get {
                return ResourceManager.GetString("answer2_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NOTE]: Your household size and household income will be the deciding factors as to which of the programs you and/or your children may be eligible for..
        /// </summary>
        public static string answer2_5 {
            get {
                return ResourceManager.GetString("answer2_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go to a health care clinic or your local health department to get proof of your pregnancy and the date your baby is due..
        /// </summary>
        public static string answer20_1 {
            get {
                return ResourceManager.GetString("answer20_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Eligibility Results screen will let you know if someone is not eligible for any program.  This screen will provide a customer service phone number for any questions you may have at that time.  If you still wish to apply anyway, you may [DOWNLOAD_APP_URL] to download a blank application, fill it out and mail it in..
        /// </summary>
        public static string answer21_1 {
            get {
                return ResourceManager.GetString("answer21_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOBRA, MLIF and Plan First - There is no cost for children under age 18. There are small co-pays for some services. ALL Kids - Cost is based on family size and income. Families pay from $52 to $104 per year, per child. Small co-pays may be required at time of service. There are no co-pays for preventive services like regular check-ups, immunizations, dental cleanings and vision exams..
        /// </summary>
        public static string answer3_1 {
            get {
                return ResourceManager.GetString("answer3_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For SOBRA and Medicaid for Low Income Families (MLIF), you may have other health insurance and still be eligible. For ALL Kids and Plan First, you cannot have other health insurance and be eligible. There are rules for the ALL Kids program regarding lost or cancelled health insurance. For questions, call the ALL Kids toll-free number at [CHIPPHONE]..
        /// </summary>
        public static string answer4_1 {
            get {
                return ResourceManager.GetString("answer4_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NOTE]: If you have Medicaid, Blue Cross, CHAMPUS or any other health insurance, please list those..
        /// </summary>
        public static string answer4_2 {
            get {
                return ResourceManager.GetString("answer4_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No. You cannot transfer Medicaid from one state to another. Each state’s Medicaid program is different. You may be eligible for Medicaid in Alabama based on your income and family size. You may submit an on-line application to find out if you might be eligible for Medicaid or any of the other health care programs in Alabama. If you were eligible for Medicaid in another state, you must get a copy of your termination letter from that state to send to Alabama Medicaid..
        /// </summary>
        public static string answer5_1 {
            get {
                return ResourceManager.GetString("answer5_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NOTE]: If you were eligible for the Social Security Administration’s SSI program in another state, you will need to contact the Social Security Administration and let them know you have moved. If you are still eligible for the SSI program, you will automatically receive Medicaid in Alabama..
        /// </summary>
        public static string answer5_2 {
            get {
                return ResourceManager.GetString("answer5_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a user account by entering a user name and password (remember your password or write it down)..
        /// </summary>
        public static string answer6_1 {
            get {
                return ResourceManager.GetString("answer6_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fill in all the blanks for each question on every page..
        /// </summary>
        public static string answer6_3 {
            get {
                return ResourceManager.GetString("answer6_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once completed, click the “Submit Application” button. (The application will tell you which program you and/or your children may be eligible for. You will be able to check your answers before you submit the application.).
        /// </summary>
        public static string answer6_4 {
            get {
                return ResourceManager.GetString("answer6_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter your user name and password and visit your account dashboard to update your contact information, view the status of your last application, or view enrollment information for your children..
        /// </summary>
        public static string answer6_6 {
            get {
                return ResourceManager.GetString("answer6_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you do not have a Social Security number, you may leave it blank.  This may slow down the process of determining eligibility..
        /// </summary>
        public static string answer7_1 {
            get {
                return ResourceManager.GetString("answer7_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It should take about 30 minutes to complete this application.  If you are unable to complete the application at this time, you will have 30 days to come back to the application and finish it.  The website will automatically delete the application if it is not completed within 30 days.  You will have to start a new application if you wait longer than 30 days from the time you start..
        /// </summary>
        public static string answer8_1 {
            get {
                return ResourceManager.GetString("answer8_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You may click on the &quot;Sign Out&quot; link at the top of the screen.  You will have 30 days to come back to your application. After 30 days, the website will automatically delete the application and you will have to start a new one..
        /// </summary>
        public static string answer9_1 {
            get {
                return ResourceManager.GetString("answer9_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard FAQs.
        /// </summary>
        public static string faqButton {
            get {
                return ResourceManager.GetString("faqButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download Paper Application.
        /// </summary>
        public static string PaperApplication {
            get {
                return ResourceManager.GetString("PaperApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What are these programs for?.
        /// </summary>
        public static string question1 {
            get {
                return ResourceManager.GetString("question1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How do I get back into my application, if I don’t finish it?.
        /// </summary>
        public static string question10 {
            get {
                return ResourceManager.GetString("question10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What if I make a mistake?.
        /// </summary>
        public static string question11 {
            get {
                return ResourceManager.GetString("question11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What if I have trouble with the on-line application?.
        /// </summary>
        public static string question12 {
            get {
                return ResourceManager.GetString("question12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How long will I or my children be covered by these programs?.
        /// </summary>
        public static string question13 {
            get {
                return ResourceManager.GetString("question13", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What does the Review and Sign page mean?.
        /// </summary>
        public static string question14 {
            get {
                return ResourceManager.GetString("question14", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How long does it take to find out if I or my children are eligible for any of these programs?.
        /// </summary>
        public static string question15 {
            get {
                return ResourceManager.GetString("question15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is a household?.
        /// </summary>
        public static string question16 {
            get {
                return ResourceManager.GetString("question16", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is household income?.
        /// </summary>
        public static string question17 {
            get {
                return ResourceManager.GetString("question17", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What if I already have medical bills?.
        /// </summary>
        public static string question18 {
            get {
                return ResourceManager.GetString("question18", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can I use my ultrasound picture as proof of pregnancy?.
        /// </summary>
        public static string question19 {
            get {
                return ResourceManager.GetString("question19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who are these programs for?.
        /// </summary>
        public static string question2 {
            get {
                return ResourceManager.GetString("question2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How can I get proof of pregnancy if the doctor only sees Medicaid patients?.
        /// </summary>
        public static string question20 {
            get {
                return ResourceManager.GetString("question20", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What if the computer says I or my children are not eligible?.
        /// </summary>
        public static string question21 {
            get {
                return ResourceManager.GetString("question21", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much do these programs cost?.
        /// </summary>
        public static string question3 {
            get {
                return ResourceManager.GetString("question3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If I have other insurance, would I still be eligible for these programs?.
        /// </summary>
        public static string question4 {
            get {
                return ResourceManager.GetString("question4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I was on Medicaid in another state, can I transfer my Medicaid?.
        /// </summary>
        public static string question5 {
            get {
                return ResourceManager.GetString("question5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How does the on-line application work?.
        /// </summary>
        public static string question6 {
            get {
                return ResourceManager.GetString("question6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What if I don’t have a Social Security number?.
        /// </summary>
        public static string question7 {
            get {
                return ResourceManager.GetString("question7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How long will it take me to fill out the on-line application?.
        /// </summary>
        public static string question8 {
            get {
                return ResourceManager.GetString("question8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What if I cannot finish the on-line application?.
        /// </summary>
        public static string question9 {
            get {
                return ResourceManager.GetString("question9", resourceCulture);
            }
        }
    }
}
