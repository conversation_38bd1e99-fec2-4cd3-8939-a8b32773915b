﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.Shared {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class MoreAboutPerson {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MoreAboutPerson() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.Shared.MoreAboutPerson", typeof(MoreAboutPerson).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How old was [NAME] when [HE/SHE] left the foster care system?.
        /// </summary>
        public static string ageWhenLeftFosterCare {
            get {
                return ResourceManager.GetString("ageWhenLeftFosterCare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age when left foster care is required..
        /// </summary>
        public static string ageWhenLeftFosterCareRequired {
            get {
                return ResourceManager.GetString("ageWhenLeftFosterCareRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        public static string dueDate {
            get {
                return ResourceManager.GetString("dueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who were ever in foster care.
        /// </summary>
        public static string everInFosterCare {
            get {
                return ResourceManager.GetString("everInFosterCare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who want to apply for, or continue to receive, Family Planning services.
        /// </summary>
        public static string familyPlanning {
            get {
                return ResourceManager.GetString("familyPlanning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Was [NAME] getting health care through the Alabama Medicaid Agency?.
        /// </summary>
        public static string fosterCarePersonUsedMedicaid {
            get {
                return ResourceManager.GetString("fosterCarePersonUsedMedicaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who are full-time students.
        /// </summary>
        public static string fullTimeStudents {
            get {
                return ResourceManager.GetString("fullTimeStudents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full-time students selection is required..
        /// </summary>
        public static string fullTimeStudentsSelectionRequired {
            get {
                return ResourceManager.GetString("fullTimeStudentsSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who have a physical disability or mental health condition that limits their ability to work, attend school, or take care of their daily needs.
        /// </summary>
        public static string havePhysicalDisability {
            get {
                return ResourceManager.GetString("havePhysicalDisability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More About This Household.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How many babies is [NAME] expecting during this pregnancy?.
        /// </summary>
        public static string howManyBabies {
            get {
                return ResourceManager.GetString("howManyBabies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who are American Indian or Alaska Native.
        /// </summary>
        public static string IndianOrAlaskan {
            get {
                return ResourceManager.GetString("IndianOrAlaskan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of babies selection is required..
        /// </summary>
        public static string nbrOfBabiesSelectionRequired {
            get {
                return ResourceManager.GetString("nbrOfBabiesSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who need help with daily living activities(bathing, dressing, using the bathroom), or live in a medical facility or nursing home.
        /// </summary>
        public static string needHelpWithDailyActivities {
            get {
                return ResourceManager.GetString("needHelpWithDailyActivities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None of these people.
        /// </summary>
        public static string noneOfThese {
            get {
                return ResourceManager.GetString("noneOfThese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do one or both of [NAME]&apos;s parents live in Alabama?.
        /// </summary>
        public static string parentLivingInAlabama {
            get {
                return ResourceManager.GetString("parentLivingInAlabama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME] have a parent living in the same state where [NAME] goes to school?.
        /// </summary>
        public static string parentLivingSameStateAsSchool {
            get {
                return ResourceManager.GetString("parentLivingSameStateAsSchool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any of these people who are pregnant.
        /// </summary>
        public static string pregnant {
            get {
                return ResourceManager.GetString("pregnant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State in foster care selection is required..
        /// </summary>
        public static string stateInFosterCareRequired {
            get {
                return ResourceManager.GetString("stateInFosterCareRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In what state was [NAME] in the foster care program?.
        /// </summary>
        public static string whatStateInFosterCare {
            get {
                return ResourceManager.GetString("whatStateInFosterCare", resourceCulture);
            }
        }
    }
}
