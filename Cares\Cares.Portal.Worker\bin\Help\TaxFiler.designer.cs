﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Help {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class TaxFiler {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TaxFiler() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Help.TaxFiler", typeof(TaxFiler).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;
        ///For example, you might be claimed as a dependent if you live with someone who pays for most of your costs, like housing, food and clothing. Even if you don’t live with the person who pays these costs, they may be able to claim you as a dependent if they’re related to you and your income is less than a certain amount.&lt;/p&gt;.
        /// </summary>
        public static string claimedAsDependent {
            get {
                return ResourceManager.GetString("claimedAsDependent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;Most tax filers claim their own children as their dependents if the children are 19 or younger, full-time students younger than 25, or are disabled. Tax filers also might claim other people as dependents when they pay for most of their costs, like housing, food, and clothing.&lt;/p&gt;.
        /// </summary>
        public static string dependent {
            get {
                return ResourceManager.GetString("dependent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;If you and/or your family want health coverage for next year, tell us about any expected differences between who you claim as dependents now, and why you’ll claim on the tax return for 2015.&lt;br&gt;
        ///Sometimes dependents change from one year to another if:&lt;br&gt;
        ///• Parents alternate claiming children as dependents.&lt;/p&gt;&lt;br&gt;
        ///• A child turns 19 or 25 next year and won’t be claimed.&lt;br&gt;
        ///• A dependent moved out of the home recently and won’t live with the tax filer next year.&lt;br&gt;
        ///• A dependent has a new job and  [rest of string was truncated]&quot;;.
        /// </summary>
        public static string differentDependent {
            get {
                return ResourceManager.GetString("differentDependent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;If you’re a married couple and you agree to file a joint federal income tax return, you and your spouse report combined income information on one tax return. To be eligible for a tax credit to help pay for health coverage for your family, you and your spouse must file a joint federal income tax return for the year you want health coverage.&lt;br&gt;
        ///If you’re a married couple, but you haven’t filed joint returns in the past, but you plan to file jointly, you can select “Yes.” Spouses may file a joint return e [rest of string was truncated]&quot;;.
        /// </summary>
        public static string fileJointly {
            get {
                return ResourceManager.GetString("fileJointly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;If you’ll file a federal income tax return for&amp;nbsp;[COVERAGEYEAR], you may be able to get help paying for health coverage through a new tax credit. If you don’t file a tax return, you or your family may be eligible for other types of free or low-cost health benefits. Tell us if you plan to file so we know what you’re eligible for.&lt;/p&gt;.
        /// </summary>
        public static string fileTaxNextYear {
            get {
                return ResourceManager.GetString("fileTaxNextYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to • If you’re separated but not divorced, select “Yes.”&lt;br&gt;
        ///• If you live with your partner, but aren’t legally married, select “No.”&lt;br&gt;
        ///• If you have a same-sex spouse, select “Yes.” Each state has its own rules about same-sex marriage. .
        /// </summary>
        public static string isMarried {
            get {
                return ResourceManager.GetString("isMarried", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;You live with your spouse if you spend most nights in the same household. You can still count as living with your spouse if you’re away temporarily, like for school or a short-term job, if you’re expected to return.&lt;/p&gt;.
        /// </summary>
        public static string liveWithSpouse {
            get {
                return ResourceManager.GetString("liveWithSpouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;The claiming tax filer (or tax filer) is the main person filing a household’s federal income tax return. If a spouse files a joint return with this person, he or she is a tax filer, too. Anyone claimed as a dependent on this person’s federal income tax return is a “tax dependent.”&lt;br&gt;
        ///Select “Yes” if you can get the tax filer’s information, for example, if he or she lives with you. This will give the tax dependent the best chance of getting help paying for coverage.&lt;/p&gt;.
        /// </summary>
        public static string provideTaxFilerInfo {
            get {
                return ResourceManager.GetString("provideTaxFilerInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;If the dependent is this person’s legally adopted child, choose “son/daughter” from the relationship choices, even if another relationship also applies.&lt;/p&gt;&lt;br&gt;
        ///&lt;b&gt;Who’s a dependent?&lt;/b&gt;
        ///When you file a tax return, your child, stepchild, foster child, or sibling (if younger) is likely to be&lt;br&gt;
        ///
        ///your dependent if he or she lives with you, doesn’t provide more than half of his or her own support for the year, and is younger than 19 or a full-time student younger than 25..
        /// </summary>
        public static string relationToTaxFiler {
            get {
                return ResourceManager.GetString("relationToTaxFiler", resourceCulture);
            }
        }
    }
}
