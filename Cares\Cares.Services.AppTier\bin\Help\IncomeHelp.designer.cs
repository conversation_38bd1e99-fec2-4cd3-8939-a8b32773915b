﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Help {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class IncomeHelp {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal IncomeHelp() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Help.IncomeHelp", typeof(IncomeHelp).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Alimony&lt;/b&gt;
        ///Alimony received is money you get from a spouse you no longer live with, or a former spouse, if paid to you as part of a divorce agreement, separation agreement, or court order. Payments designated in the agreement or ordered as child support or as a non-taxable property settlement aren’t alimony. For more information, see
        ///IRS Publication 504 (page 12).&lt;br&gt;&lt;br&gt;
        ///If you select “alimony received,” you’ll be asked to enter the amount. You’ll also be asked how often you get this amount.&lt;/p&gt;.
        /// </summary>
        public static string alimony {
            get {
                return ResourceManager.GetString("alimony", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Alimony&lt;/b&gt;&lt;br&gt;
        ///        &lt;ul&gt;&lt;li&gt;If your divorce or separation was finalized:&lt;/li&gt;
        ///            &lt;ul&gt;&lt;li&gt;&lt;b&gt;Before&lt;/b&gt; January 1, 2019: &lt;b&gt;Include&lt;/b&gt; alimony as income. The payer may deduct this as an expense.&lt;/li&gt;
        ///                &lt;li&gt;&lt;b&gt;On or after&lt;/b&gt; January 1, 2019: &lt;b&gt;Don&apos;t include&lt;/b&gt; alimony as income. The payer can&apos;t deduct this as an expense.&lt;/li&gt;
        ///            &lt;/ul&gt;
        ///            &lt;li&gt;Report any payments you&apos;re currently getting from a spouse or former spouse if the payments are part of a divo [rest of string was truncated]&quot;;.
        /// </summary>
        public static string alimonydeduction {
            get {
                return ResourceManager.GetString("alimonydeduction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Capital gains&lt;/b&gt;
        ///Capital gains are the amount you profit from selling property. For example, if you buy stock for $1,000 and sell it for $1,250, you have a capital gain of $250. You don’t need to include a capital gain if it’s from the sale of the main home you owned for at least 5 years (and the profit is less than $250,000). For more information, see IRS Publication 17 (chapter 14, page 104) or IRS Publication 544.&lt;br&gt;&lt;br&gt;
        ///If you select “capital gains,” you’ll be asked how much you expect to get  [rest of string was truncated]&quot;;.
        /// </summary>
        public static string captialGains {
            get {
                return ResourceManager.GetString("captialGains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Farming or fishing income&lt;/b&gt;
        ///If you have income from farming or fishing, you can enter it as “farming or fishing” income or “self-employment” income, but you can only enter it once. A person is in the business of farming if he or she cultivates, operates, or manages a farm for profit, either as owner or tenant. A farm can include livestock, dairy, poultry, fish, or fruit. It can also include plantations, ranches, ranges, and orchards. For more information on farming income, see IRS Publication 225 ( [rest of string was truncated]&quot;;.
        /// </summary>
        public static string farmingFishing {
            get {
                return ResourceManager.GetString("farmingFishing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Investment income&lt;/b&gt;&lt;br&gt;
        ///Investment income is the income you get from an investment. Examples of investment income include interest you get from a bank account or dividends from a person’s stock. For more information, see IRS Publication 550.&lt;br&gt;&lt;br&gt;
        ///If you select “investment income,” you’ll be asked to enter the amount you get from investment income, like interest and dividends. You’ll also be asked how often you get this amount.&lt;/p&gt;.
        /// </summary>
        public static string investment {
            get {
                return ResourceManager.GetString("investment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;b&gt;Income from your job&lt;/b&gt;
        ///You’ll be asked about how you’re paid:&lt;br&gt;
        ///• Income amount: If you’re asked about your income amount, enter the amount that’s shown on your pay stub before taxes are taken out.&lt;br&gt;
        ///• How often? After you enter how much you earn, select how often. Choose one option, like “hourly,” “daily,” or “weekly.” If you’re paid through a one-time contract, you can select “one time only.”&lt;br&gt;
        ///• Hours per week? Enter the number of hours or days you work each week..
        /// </summary>
        public static string job {
            get {
                return ResourceManager.GetString("job", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Other income&lt;/b&gt;
        ///You might have other types of income that aren’t listed above. If you do, select “other income.” If you’re a member of the clergy or a religious order, exclude the same income excluded on your federal income tax return.&lt;br&gt;&lt;br&gt;
        ///If you select “other income,” you’ll be asked if you have income from any of the following sources. If you do, enter the amount.&lt;/p&gt;.
        /// </summary>
        public static string otherIncome {
            get {
                return ResourceManager.GetString("otherIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Pension&lt;/b&gt;&lt;br&gt;
        ///A pension is generally a payment or series of payments made to a person after he or she retires from work. Generally, the amount of the income from a pension account distribution depends on the type of pension account, how much was contributed to the pension account, and whether the amounts contributed were already taxed. You don’t have to include a qualified distribution from a designated Roth account as income. For more information, see IRS Publication 575.&lt;br&gt;&lt;br&gt;
        ///If you select “p [rest of string was truncated]&quot;;.
        /// </summary>
        public static string pension {
            get {
                return ResourceManager.GetString("pension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Rental or royalty income&lt;/b&gt;
        ///Rental income is the amount someone pays you to use your property after you subtract your property expenses. Royalty income includes any payments you get from a patent, copyright, or some other natural resource you own. For more information, see IRS Publication 17 (chapter 9, pages 67-74).&lt;br&gt;&lt;br&gt;
        ///If you select “rental or royalty income,” you’ll be asked how much you get from these types of income. You’ll also be asked how often you get this amount. Enter your net rental [rest of string was truncated]&quot;;.
        /// </summary>
        public static string rental {
            get {
                return ResourceManager.GetString("rental", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Retirement&lt;/b&gt;&lt;br&gt;
        ///A retirement benefit is generally a payment or series of payments made to a person after he or she retires from work. Generally, the amount of the income from a retirement account distribution depends on the type of retirement account, how much was contributed to the retirement account, and whether the amounts contributed were already taxed. You don’t have to include a qualified distribution from a designated Roth account as income. For more information, see IRS Publication 575.&lt;br [rest of string was truncated]&quot;;.
        /// </summary>
        public static string retiement {
            get {
                return ResourceManager.GetString("retiement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Self-employment income&lt;/b&gt;
        ///This is the net income a person earns from their own trade or business. For example, any net income (profit) you earn from goods you sell or services you provide to others counts as self-employment income. Self-employment income could also come from a distributive share from a partnership.&lt;br&gt;&lt;br&gt;
        ///If you select “self-employment,” you’ll describe the kind of work this self-employment is. There’s no special format – simply describe the work. For example, if you clean houses, [rest of string was truncated]&quot;;.
        /// </summary>
        public static string selfEmployment {
            get {
                return ResourceManager.GetString("selfEmployment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Social Security benefits&lt;/b&gt;&lt;br&gt;
        ///These are the amount a person gets from Social Security disability, retirement (income railroad retirement (RRB)), or survivor’s benefits each month.&lt;br&gt;&lt;br&gt;
        ///&lt;b&gt;If you select “Social Security benefits,”&lt;/b&gt; you’ll enter the amount you get from Social Security benefits. You’ll also select how often you get this amount: one time only, monthly, or yearly. You can find the amount on the cost-of-living increase letter you get each year. Enter the full amount before any de [rest of string was truncated]&quot;;.
        /// </summary>
        public static string ssIncome {
            get {
                return ResourceManager.GetString("ssIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b&gt;Unemployment&lt;/b&gt;&lt;br&gt;
        ///Unemployment compensation includes any amount you get under an unemployment compensation law of the United States or a state. You usually must include unemployment benefits (including from an employer or union) as income. To see the limited exceptions, see IRS Publication 525, page 26.
        ///If you select “unemployment,” you’ll enter the amount you get from unemployment, and how often you get this amount. You’ll be asked which state or former employer provides you with unemployment be [rest of string was truncated]&quot;;.
        /// </summary>
        public static string unemployment {
            get {
                return ResourceManager.GetString("unemployment", resourceCulture);
            }
        }
    }
}
