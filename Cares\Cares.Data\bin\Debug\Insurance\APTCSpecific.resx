﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="addAnotherEmployer" xml:space="preserve">
    <value>Add Another Employer</value>
  </data>
  <data name="adoptionDate" xml:space="preserve">
    <value>When was [NAME], (DOB: [DOB]) adopted or placed for adoption?</value>
    <comment>G</comment>
  </data>
  <data name="adoptionLast60Days" xml:space="preserve">
    <value>Have any of these people been adopted or placed for adoption in the last 60 days?</value>
    <comment>G</comment>
  </data>
  <data name="aianInformation" xml:space="preserve">
    <value>American Indian/Alaska Native Information</value>
    <comment>F</comment>
  </data>
  <data name="aptcProgramQuestions" xml:space="preserve">
    <value>Tax Credit Program Questions</value>
  </data>
  <data name="atleastOneEmployerOffersHealthCoverage" xml:space="preserve">
    <value>Employer that offers health coverage is required</value>
  </data>
  <data name="changesToCoverage" xml:space="preserve">
    <value>Does [NAME], (DOB: [DOB]) expect [EMPLOYER] to make any of these changes to the coverage offered to [NAME] in [COVERAGEYEAR]?</value>
    <comment>D</comment>
  </data>
  <data name="changesToCoverageA" xml:space="preserve">
    <value>[EMPLOYER] will no longer offer health coverage</value>
    <comment>D</comment>
  </data>
  <data name="changesToCoverageA1" xml:space="preserve">
    <value>What will be the last day [EMPLOYER] offers coverage?</value>
    <comment>D</comment>
  </data>
  <data name="changesToCoverageB" xml:space="preserve">
    <value>[EMPLOYER] will change the cost of premiums for the lowest-cost plan available to the employee that meets minimum value.(Only tell us about plans that aren't family plans.)</value>
    <comment>D</comment>
  </data>
  <data name="changesToCoverageB1" xml:space="preserve">
    <value>How much will the employee have to pay in premiums for this plan?  If the employer has wellness programs, provide the premium that the employee would pay if [HE/SHE] received the maximum discount for any tobacco cessation programs, and didn't receive any other discounts based on wellness programs.</value>
    <comment>D</comment>
  </data>
  <data name="contactEmail" xml:space="preserve">
    <value>Contact Email</value>
  </data>
  <data name="contactName" xml:space="preserve">
    <value>Contact Name</value>
  </data>
  <data name="contactPhone" xml:space="preserve">
    <value>Contact Phone</value>
  </data>
  <data name="coverageIsCobra" xml:space="preserve">
    <value>Is the coverage from [EMPLOYER] COBRA coverage?</value>
    <comment>C</comment>
  </data>
  <data name="coverageIsRetiree" xml:space="preserve">
    <value>Is [NAME]'s, (DOB: [DOB]) coverage from [EMPLOYER] a retiree health plan?</value>
    <comment>C</comment>
  </data>
  <data name="coverageStartDate" xml:space="preserve">
    <value>Coverage Start Date</value>
  </data>
  <data name="coverageStartDateRequired" xml:space="preserve">
    <value>Coverage start date is required</value>
  </data>
  <data name="currentlyEnrolled" xml:space="preserve">
    <value>Is [NAME], (DOB: [DOB]) currently enrolled in this employer's health coverage?</value>
    <comment>C</comment>
  </data>
  <data name="dateCoverageCouldStart" xml:space="preserve">
    <value>Date [NAME]'s, (DOB: [DOB]) coverage could start</value>
    <comment>C</comment>
  </data>
  <data name="didNotPayPremiums" xml:space="preserve">
    <value>Did [NAME], (DOB: [DOB]) lose health coverage because [HE/SHE] didn't pay premiums?</value>
    <comment>G</comment>
  </data>
  <data name="eligibleImmStatusDate" xml:space="preserve">
    <value>When did [NAME], (DOB: [DOB]) gain eligible immigration status?</value>
    <comment>G</comment>
  </data>
  <data name="eligibleImmStatusLast60Days" xml:space="preserve">
    <value>Did any of these people gain eligible immigration status in the last 60 days?</value>
    <comment>G</comment>
  </data>
  <data name="empCoverageToolInfo" xml:space="preserve">
    <value>Tell us about [EMPLOYER]'s Health Coverage for [COVERAGEYEAR]. First, print out and take the [LINK] to [EMPLOYER] to collect the information you need for this section for using the tool to fill out the application. Instructions on the employer coverage form provide step-by-step instructions for using the tool to answer the questions in this section.</value>
    <comment>C...coverage year = next year for Oct-Dec and current year for Jan-Mar</comment>
  </data>
  <data name="empCoverageToolLinkText" xml:space="preserve">
    <value>Employer CoverageTool</value>
  </data>
  <data name="employeeStatus" xml:space="preserve">
    <value>What's [NAME]'s current work status at this employer?</value>
    <comment>C</comment>
  </data>
  <data name="employerAddress" xml:space="preserve">
    <value>Address</value>
    <comment>C</comment>
  </data>
  <data name="employerContactInformation" xml:space="preserve">
    <value>Employer Contact Information</value>
    <comment>E</comment>
  </data>
  <data name="employerEIN" xml:space="preserve">
    <value>Identification Number (EIN)</value>
    <comment>C</comment>
  </data>
  <data name="employerHealthCoverage" xml:space="preserve">
    <value>Employer Health Coverage</value>
    <comment>C</comment>
  </data>
  <data name="employerHealthCoverageDetail" xml:space="preserve">
    <value>Employer Health Coverage Detail</value>
    <comment>D</comment>
  </data>
  <data name="employerInformation" xml:space="preserve">
    <value>Employer Information</value>
    <comment>C</comment>
  </data>
  <data name="employerNameRequired" xml:space="preserve">
    <value>Employer Name is required.</value>
  </data>
  <data name="employerPhone" xml:space="preserve">
    <value>Phone number</value>
    <comment>C</comment>
  </data>
  <data name="employerPhoneRequired" xml:space="preserve">
    <value>Employer phone number is required.</value>
  </data>
  <data name="employersContact" xml:space="preserve">
    <value>Who can we contact about this employer's health coverage?  If you're not sure, ask your employer.</value>
    <comment>C</comment>
  </data>
  <data name="enrollDate" xml:space="preserve">
    <value>When could [NAME], (DOB: [DOB]) enroll in coverage?</value>
  </data>
  <data name="expectToDropCoverage" xml:space="preserve">
    <value>Does [NAME], (DOB: [DOB]) expect to drop [EMPLOYER]'s health coverage in [COVERAGEYEAR]?</value>
    <comment>D</comment>
  </data>
  <data name="expectToDropCoverageDate" xml:space="preserve">
    <value>What's [NAME]'s, (DOB: [DOB]) last day of coverage through [EMPLOYER]'s health plan?</value>
    <comment>D</comment>
  </data>
  <data name="getMarriedDate" xml:space="preserve">
    <value>When did [NAME], (DOB: [DOB]) get married?</value>
    <comment>G</comment>
  </data>
  <data name="getMarriedLast60Days" xml:space="preserve">
    <value>Did any of these people get married in the last 60 days?</value>
    <comment>G</comment>
  </data>
  <data name="healthCoverageSelectionRequired" xml:space="preserve">
    <value>Health coverage program is required</value>
  </data>
  <data name="howOftenPayAmt" xml:space="preserve">
    <value>How often would [NAME], (DOB: [DOB]) pay this amount?</value>
    <comment>D</comment>
  </data>
  <data name="instructions" xml:space="preserve">
    <value>Tell us about [EMPLOYER]'s health coverage for [COVERAGEYEAR]. First, print out and take the Employer coverage tool to [EMPLOYER] to collect the information you need for this section for using the tool to fill our the application. Instructions on the employer coverage form provide step-by-step instructions for using the tool to answer the questions in this section.</value>
    <comment>D...coverage year = next year for Oct-Dec and current year for Jan-Mar</comment>
  </data>
  <data name="isEligibleThroughJob" xml:space="preserve">
    <value>Is [NAME], (DOB: [DOB]) currently eligible for health coverage through a job (even if it's from another person's job, like a spouse[UNDER26])?</value>
    <comment>C</comment>
  </data>
  <data name="loseCoverageDate" xml:space="preserve">
    <value>When will [NAME]'s, (DOB: [DOB]) health coverage end?</value>
    <comment>G</comment>
  </data>
  <data name="loseCoverageNext60Days" xml:space="preserve">
    <value>Are any of these people going to lose their health coverage in the next 60 days?</value>
    <comment>G</comment>
  </data>
  <data name="lostCoverageDate" xml:space="preserve">
    <value>When did [NAME], (DOB: [DOB]) lose health coverage?</value>
    <comment>G</comment>
  </data>
  <data name="lostCoverageLast60Days" xml:space="preserve">
    <value>Did any of these people lose health coverage in the last 60 days?</value>
    <comment>G</comment>
  </data>
  <data name="lowestCostPlan" xml:space="preserve">
    <value>For the lowest-cost plan available only to the employee that meets the minimum value standard: (Only tell us about plans that aren't family plans).</value>
    <comment>D</comment>
  </data>
  <data name="lowestCostPlanA" xml:space="preserve">
    <value>How much would the employee have to pay in premiums for this plan?  If the employer has wellness programs, provide the premium that the employee would pay if [HE/SHE] received the maximum discount for any tobacco cessation programs and didn't receive any other discounts based on wellness programs.</value>
    <comment>D</comment>
  </data>
  <data name="meetsMinValueStandard" xml:space="preserve">
    <value>Does [EMPLOYER] offer a health plan that meets the minimum value standard?</value>
    <comment>D</comment>
  </data>
  <data name="memberOfAnyTribe" xml:space="preserve">
    <value>Are any of these people a member of a federally recognized tribe?</value>
    <comment>F</comment>
  </data>
  <data name="memberOfThisTribe" xml:space="preserve">
    <value>Who is a member of the [TRIBE] tribe?</value>
    <comment>F</comment>
  </data>
  <data name="moveDate" xml:space="preserve">
    <value>When did [HE/SHE] move?</value>
    <comment>G</comment>
  </data>
  <data name="moveFromZipCounty" xml:space="preserve">
    <value>What was the Zip code and county of [NAME]'s, (DOB: [DOB]) last address?</value>
    <comment>G</comment>
  </data>
  <data name="moveLast60Days" xml:space="preserve">
    <value>Did any of these people move in the last 60 days?</value>
    <comment>G</comment>
  </data>
  <data name="mvsHelpNote" xml:space="preserve">
    <value>* An employer-sponsored health plan meets the “minimum value standard” if the plan’s share of the total allowed benefit costs covered by the plan is no less than 60 percent of such costs (Section 36B(c)(2)(C)(ii) of the Internal Revenue Code of 1986)</value>
  </data>
  <data name="notFileJointlyChangeAnswers" xml:space="preserve">
    <value>For [NAME], (DOB: [DOB]) to get help paying for health coverage, [HE/SHE] must file a joint federal income tax return with [HIS/HER] spouse. Do you want to change your answers about how [NAME], (DOB: [DOB]) will file taxes for [COVERAGEYEAR]?</value>
    <comment>A</comment>
  </data>
  <data name="notTaxFilerOrDepChangeAnswers" xml:space="preserve">
    <value>For [NAME], (DOB: [DOB]) to get help paying for health coverage, each person must file a tax return or be claimed as a dependent on some else's tax return. Do you want to change your answers about how [NAME], (DOB: [DOB]) will file taxes for [COVERAGEYEAR]?</value>
    <comment>A</comment>
  </data>
  <data name="otherRelationship" xml:space="preserve">
    <value>You selected "other relative" or "other unrelated" for relationship of [NAME] to [CONTACT]. Select one of these options to describe the relationship of [NAME] to [CONTACT]</value>
    <comment>A</comment>
  </data>
  <data name="phone" xml:space="preserve">
    <value>Phone</value>
    <comment>C</comment>
  </data>
  <data name="planningToEnroll" xml:space="preserve">
    <value>Is [NAME], (DOB: [DOB]) planning to enroll in [EMPLOYER]'s, (DOB: [DOB]) health coverage in [COVERAGEYEAR]?</value>
    <comment>D</comment>
  </data>
  <data name="planningToEnrollDate" xml:space="preserve">
    <value>What's the first day [NAME], (DOB: [DOB]) will be covered by [EMPLOYER]'s, (DOB: [DOB]) health plan?</value>
    <comment>D</comment>
  </data>
  <data name="premiumAmount" xml:space="preserve">
    <value>Premium Amount</value>
  </data>
  <data name="provideSSNNow" xml:space="preserve">
    <value>[FILER] indicated [HE/SHE] is the claiming tax filer for [NAMES], (DOB: [DOB]), but a Social Security number(SSN) hasn't been entered for [FILER]. Providing an SSN may help [NAMES], (DOB: [DOB]) pay for health coverage. The SSN you provide won't be used to verify citizenship or immigration status. Does [FILER] want to provide one now?</value>
    <comment>A</comment>
  </data>
  <data name="releasedFromJailDate" xml:space="preserve">
    <value>When was [NAME] released from incarceration?</value>
    <comment>G</comment>
  </data>
  <data name="releasedFromJailLast60Days" xml:space="preserve">
    <value>Did any of these people get released from incarceration (detention or jail) in the last 60 days?</value>
    <comment>G</comment>
  </data>
  <data name="selectCoverageEnrolledIn" xml:space="preserve">
    <value>Is [NAME], (DOB: [DOB]) enrolled in health coverage from any of the following?</value>
    <comment>B</comment>
  </data>
  <data name="selectPeopleAtEmployee" xml:space="preserve">
    <value>Please select current work status.</value>
  </data>
  <data name="stateAndTribe" xml:space="preserve">
    <value>Select a state and tribe.</value>
    <comment>F</comment>
  </data>
  <data name="String1" xml:space="preserve">
    <value>plan is no less than 60 percent of such costs (Section 36B(c)(2)(C)(ii) of the Internal Revenue Code of 1986)</value>
  </data>
  <data name="tellUsAboutEmployer" xml:space="preserve">
    <value>Tell us about [EMPLOYER].</value>
    <comment>E</comment>
  </data>
  <data name="tribeName" xml:space="preserve">
    <value>Tribe name</value>
    <comment>F</comment>
  </data>
  <data name="under26Text" xml:space="preserve">
    <value> or parent/guardian</value>
    <comment>C</comment>
  </data>
  <data name="waitingPeriod" xml:space="preserve">
    <value>Is [NAME], (DOB: [DOB]) currently in a waiting or probationary period?</value>
  </data>
  <data name="whenWillChangeTakeEffect" xml:space="preserve">
    <value>When will [EMPLOYER], make this change?</value>
    <comment>D</comment>
  </data>
  <data name="whereLiveInState" xml:space="preserve">
    <value>Where will [NAME], (DOB: [DOB]) live in Alabama?</value>
    <comment>A</comment>
  </data>
  <data name="whichEmployerOffersCoverage" xml:space="preserve">
    <value>Tell us which employer(s) offer(s) health coverage to [NAME], (DOB: [DOB])</value>
    <comment>C</comment>
  </data>
  <data name="whichIsEmployee" xml:space="preserve">
    <value>Which of these people is the employee at this Employer?</value>
    <comment>C</comment>
  </data>
  <data name="willBeEligibleThroughJob" xml:space="preserve">
    <value>Will [NAME], (DOB: [DOB]) be eligible for health coverage from a job during [COVERAGEYEAR] (even if it's from another person's job, like a spouse[UNDER26])?</value>
    <comment>C...coverage year = next year for Oct-Dec and current year for Jan-Mar</comment>
  </data>
  <data name="enrollmentDate" xml:space="preserve">
    <value>Enrollment Date</value>
  </data>
  <data name="CoverageChangeDate" xml:space="preserve">
    <value>Date when coverage will change</value>
  </data>
  <data name="lastCoverageDate" xml:space="preserve">
    <value>Last day of coverage</value>
  </data>
  <data name="employerEmail" xml:space="preserve">
    <value>Employer Email</value>
  </data>
  <data name="pleaseSelectEmployee" xml:space="preserve">
    <value>Please select who is employed at this employer</value>
  </data>
</root>