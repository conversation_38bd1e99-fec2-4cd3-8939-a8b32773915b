﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Help {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Combine {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Combine() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Help.Combine", typeof(Combine).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;We’ll use this address to indicate whether or not you’re a resident of the state where you’re seeking health coverage.&lt;br&gt;&lt;br&gt;
        ///If we’re asking this question about a child who splits time between 2 parents who don’t live together, choose the address where the child spends most of his or her nights.&lt;/p&gt;.
        /// </summary>
        public static string homeAddress {
            get {
                return ResourceManager.GetString("homeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;Sometimes people leave their homes for a period of time, like to go to school, for a short-term job, or for a short-term military deployment. Children may sometimes live in a different state for a period of time if they’re staying with a family member during a summer break or attending boarding school.&lt;br&gt;&lt;br&gt;
        ///To decide whether the period of time is temporary, we’re asking whether there’s a plan for you to return to the state in the question. If you do plan to return, select “Yes.”&lt;/p&gt;.
        /// </summary>
        public static string outsideState {
            get {
                return ResourceManager.GetString("outsideState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;You’ll be asked if the name on the application is the same name on this person’s Social Security card. Most people select “Yes” to this question, but sometimes people change their names, like when they get married.&lt;br&gt;&lt;br&gt;
        ///If the name on this person’s Social Security card is different than the name you already told us for this application, select “No,” and we’ll ask you what name is on this person’s Social Security card. Enter the name exactly as it appears on the Social Security card, even if there’s a [rest of string was truncated]&quot;;.
        /// </summary>
        public static string sameNameOnSSC {
            get {
                return ResourceManager.GetString("sameNameOnSSC", resourceCulture);
            }
        }
    }
}
