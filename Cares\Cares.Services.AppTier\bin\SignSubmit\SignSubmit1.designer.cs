﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.SignSubmit {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class SignSubmit {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SignSubmit() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.SignSubmit.SignSubmit", typeof(SignSubmit).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To make it easier to determine my eligibility for help paying for health coverage in future years, I agree to allow the Medicaid and ALL Kids to use income data, including information from tax returns, for the next 5 years(the maximum numbers of years allowed).  The Medicaid and ALL Kids will send me a notice, let me make any changes, and I can opt out at any time..
        /// </summary>
        public static string allowToUseDataFor5Years {
            get {
                return ResourceManager.GetString("allowToUseDataFor5Years", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I know I&apos;ll be asked to cooperate with the agency that collects medical support from an absent parent.  If I think that cooperating to collect medical support will harm me or my children, I can tell the agency and I may not have to cooperate..
        /// </summary>
        public static string cooperateToPursue {
            get {
                return ResourceManager.GetString("cooperateToPursue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME]&apos;s electronic signature.
        /// </summary>
        public static string esignatureContact {
            get {
                return ResourceManager.GetString("esignatureContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I know that I must tell the program I&apos;ll be enrolled in if information I listed on this application changes.  I know I can make changes in &quot;My Account&quot; in this online application or by calling [CHIPPHONE].  I understand that a change in my information could affect my eligibility for member(s) of my household..
        /// </summary>
        public static string informUsOfChanges {
            get {
                return ResourceManager.GetString("informUsOfChanges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select number of years for Renewal..
        /// </summary>
        public static string nbrYrsForRenewal {
            get {
                return ResourceManager.GetString("nbrYrsForRenewal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No one applying for health coverage on this application is incarcerated(detained or jailed)..
        /// </summary>
        public static string noOneIncarcerated {
            get {
                return ResourceManager.GetString("noOneIncarcerated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is this person pending disposition?.
        /// </summary>
        public static string pendingDisposition {
            get {
                return ResourceManager.GetString("pendingDisposition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes, renew my eligibility automatically for a period of.
        /// </summary>
        public static string permissionToAutoRenewPeriodOfTime {
            get {
                return ResourceManager.GetString("permissionToAutoRenewPeriodOfTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I&apos;m signing this application under penalty of perjury, which means I&apos;ve provided true answers to all of the questions to the best of my knowledge.  I know that I may be subject to penalties under federal law if I intentionally provide false or untrue information..
        /// </summary>
        public static string providedTrueInformation {
            get {
                return ResourceManager.GetString("providedTrueInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  I&apos;m giving the Medicaid agency our rights to pursue and get any money from other health insurance, legal settlements, or other third parties.  I&apos;m also giving to the Medicaid agency rights to pursue and get medical support from a spouse or parent..
        /// </summary>
        public static string rightsToPursue {
            get {
                return ResourceManager.GetString("rightsToPursue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select the person who is incarcerated..
        /// </summary>
        public static string selectIncarceratedPerson {
            get {
                return ResourceManager.GetString("selectIncarceratedPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign &amp; Submit.
        /// </summary>
        public static string signAndSubmit {
            get {
                return ResourceManager.GetString("signAndSubmit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signature is required..
        /// </summary>
        public static string signatureRequired {
            get {
                return ResourceManager.GetString("signatureRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who is incarcerated(detained or jailed)?.
        /// </summary>
        public static string whoIsIncarcerated {
            get {
                return ResourceManager.GetString("whoIsIncarcerated", resourceCulture);
            }
        }
    }
}
