﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="agreementStatement" xml:space="preserve">
    <value>By clicking here, I hereby agree to allow the selected party to act on my behalf to the extent I've identified.  I understand that this person will have access to my personal and financial identifying information and all information contained in an application I submit through this online application for Medicaid and ALL Kids health care coverage.</value>
  </data>
  <data name="authRepCreateAcctStatement" xml:space="preserve">
    <value>This person will need to create an account in order to get notices and act on your behalf.  Enter his or her email address below.  We'll send information on how to create an account and become your authorized representative.  You can still continue with your application now.</value>
  </data>
  <data name="authRepHeader" xml:space="preserve">
    <value>Authorized Representative</value>
  </data>
  <data name="authRepStatement" xml:space="preserve">
    <value>You can give a trusted person permission to talk about this application with us, see your information, and act for you on matters related to this application, including getting information about your application and signing your application on your behalf.  This person is called an "authorized representative".</value>
  </data>
  <data name="enterAuthRepInfo" xml:space="preserve">
    <value>Enter the first name, last name, email address, and phone number for this person's account.</value>
  </data>
  <data name="nameAuthRep" xml:space="preserve">
    <value>Do you want to name someone as your authorized representative?</value>
  </data>
  <data name="personHaveAccountAlready" xml:space="preserve">
    <value>Does this person already have their own account?</value>
  </data>
  <data name="personPartOfOrganization" xml:space="preserve">
    <value>Is this person part of an organization helping you apply for coverage?</value>
  </data>
  <data name="UsernameOrEmailRequired" xml:space="preserve">
    <value>Either 'Username' or 'Email' is required.</value>
  </data>
  <data name="authUserSelectRequired" xml:space="preserve">
    <value>Selection of an authorized representative is required.</value>
  </data>
  <data name="AuthorizedEmailSubject" xml:space="preserve">
    <value>AlabamaCares :  Notification of Authorized User.</value>
  </data>
  <data name="AuthorizedUserEmailFormat" xml:space="preserve">
    <value>&lt;!DOCTYPE html&gt;
&lt;html xmlns="http://www.w3.org/1999/xhtml"&gt;
&lt;head&gt;
    &lt;title&gt;&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;p&gt;Dear [AuthorizedUserFullName],&lt;/p&gt;

    &lt;p&gt;[AuthorizerFullName] has selected you as official Authorized User for his/her account on &lt;a href="[url]"&gt; Alabamacares.Alabama.gov &lt;/a&gt;.&lt;/p&gt;

    &lt;p&gt;To approve or decline the Authorization, please take appropriate action as mentioned below :-  &lt;/p&gt;   
&lt;ul&gt; 
&lt;li&gt; If you already have an account with &lt;a href="[url]"&gt; Alabamacares.Alabama.gov &lt;/a&gt; please follow the link to log-in "[url]".&lt;/li&gt;
&lt;li&gt; If you do not have an account with &lt;a href="[url]"&gt; Alabamacares.Alabama.gov &lt;/a&gt;, please follow the link "[url]" to create one. &lt;br/&gt;
&lt;b&gt; Note: - Please make sure that you use the same email address as this i.e. "[Email]" while creating an account on&lt;a href="[url]"&gt; Alabamacares.Alabama.gov &lt;/a&gt;. &lt;/b&gt;&lt;/li&gt;
&lt;/ul&gt;
 
&lt;p&gt;If you are not the intended recipient or received this email in error, please ignore this email.&lt;/p&gt;
    
    &lt;p&gt;Regards,&lt;br/&gt;
    AlabamaCares&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</value>
  </data>
  <data name="NoSelfAuthroizedUser" xml:space="preserve">
    <value>Authorized user has to be someone other than yourself.</value>
  </data>
  <data name="AuthorizedEmailNotifiedFormat" xml:space="preserve">
    <value>&lt;!DOCTYPE html&gt;
&lt;html xmlns="http://www.w3.org/1999/xhtml"&gt;
&lt;head&gt;
    &lt;title&gt;&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;p&gt;Dear [AuthorizerFullName],&lt;/p&gt;

    &lt;p&gt;An email has been sent  at "[Email]" as you have authorized that person as an Authorized user for your application at &lt;a href="[url]"&gt; Alabamacares.Alabama.gov &lt;/a&gt;.&lt;/p&gt;
 
&lt;p&gt;If you are not the intended recipient or received this email in error,  please call us at 1-888-373-5437.&lt;/p&gt;
    
&lt;p&gt;Regards,&lt;br/&gt;  
 AlabamaCares&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</value>
  </data>
  <data name="AuthorizedEmailNotifiedSubject" xml:space="preserve">
    <value>AlabamaCares :  Authorized User has been notified by email !!!</value>
  </data>
</root>