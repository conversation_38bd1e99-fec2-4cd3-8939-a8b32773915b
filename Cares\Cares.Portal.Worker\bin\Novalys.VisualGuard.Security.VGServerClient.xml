<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Novalys.VisualGuard.Security.VGServerClient</name>
    </assembly>
    <members>
        <member name="M:Novalys.VisualGuard.Security.VGServer.IVGPrivateWebService.GetAllDistributedTransientData">
            <summary>
            Gets all distributed transient data.
            </summary>
            <returns></returns>
            
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGServer.IVGPrivateWebService.GetDistributedTransientData(System.String,System.String)">
            <summary>
            Gets distributed transient Data
            </summary>
            <param name="type">type</param>
            <param name="key">key</param>
            <returns></returns>
            
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGServer.IVGPrivateWebService.SetDistributedTransientData(System.Byte[])">
            <summary>
            Sets (create/update) distributed transient data
            </summary>
            <param name="distributedTransientDataInformation"></param>
            
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGServer.IVGPrivateWebService.DeleteDistributedTransientData(System.Byte[])">
            <summary>
            Deletes distributed transient data
            </summary>
            <param name="distributedTransientDataInformation">distributedTransientDataInformation</param>
            
        </member>
    </members>
</doc>
