INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (1, 'Failure to Provide', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (2, 'By Request', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (3, 'Do not meet', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (4, 'No Child at home', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (5, 'Out of State', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (6, 'Deceased', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (7, 'Non-Verified Citizenship', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (8, 'Over Income – General', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (9, 'Over Income – POCR', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (10, 'Past Age Limit', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (11, 'Wrong or Invalid SSN', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (12, 'Suspension', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (13, 'Non-Payment Premium', NULL, NULL, NULL, NULL)
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (14, 'Eligible through another Medicaid Program', 'ca1jbush', '2019-03-12 12:04:33.787', 'ca1jbush', '2019-03-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (15, 'Due to Creditable Insurance', 'ca1jbush', '2019-03-12 12:04:33.787', 'ca1jbush', '2019-03-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (16, 'Treatment Ended', 'ca1jbush', '2019-03-12 12:04:33.787', 'ca1jbush', '2019-03-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (17, 'Non-Verified Need for Treatment', 'Ca1darockiam', '2020-01-16 14:48:44.7933333', 'Ca1darockiam', '2020-01-16 14:48:44.7933333')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (18, 'Failure to Co-operate', 'ca1ajay', '2020-08-12 12:04:33.787', 'ca1ajay', '2020-08-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (19, 'No Annual Review', 'ca1ajay', '2020-08-12 12:04:33.787', 'ca1ajay', '2020-08-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (20, 'Non Citizen', 'ca1ajay', '2020-08-12 12:04:33.787', 'ca1ajay', '2020-08-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (21, 'Non Citizen Documentation', 'ca1ajay', '2020-08-12 12:04:33.787', 'ca1ajay', '2020-08-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (22, 'Omitted Facts', 'ca1ajay', '2020-08-12 12:04:33.787', 'ca1ajay', '2020-08-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (23, 'Other State Medicaid', 'ca1ajay', '2020-08-12 12:04:33.787', 'ca1ajay', '2020-08-12 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (24, 'No Medicare', 'ca1ajay', '2020-12-22 12:04:33.787', 'ca1ajay', '2020-12-22 12:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (25, 'Non Custodial Parent', 'ca1jgutala', '2021-07-14 9:04:33.787', 'ca1jgutala', '2021-07-14 9:04:33.787')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (26, 'HPE has been received in the last 12 months','ca1ravee','2025-09-10 15:30:01.9884154','ca1ravee','2025-09-10 15:30:01.9884154')
INSERT INTO [dbo].[REF_INELIGIBILITY_REASONS] ([INELIGIBILITY_REASON_ID], [INELIGIBILITY_REASON_DESCRIPTION], [CREATED_BY], [CREATED_DATE], [UPDATED_BY], [UPDATED_DATE]) VALUES (27, 'PEP has already been awarded for this pregnancy','ca1ravee','2025-09-10 15:30:01.9884154','ca1ravee','2025-09-10 15:30:01.9884154')