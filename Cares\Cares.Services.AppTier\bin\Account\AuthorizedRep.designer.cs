﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Account {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class AuthorizedRep {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AuthorizedRep() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Account.AuthorizedRep", typeof(AuthorizedRep).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By clicking here, I hereby agree to allow the selected party to act on my behalf to the extent I&apos;ve identified.  I understand that this person will have access to my personal and financial identifying information and all information contained in an application I submit through this online application for Medicaid and ALL Kids health care coverage..
        /// </summary>
        public static string agreementStatement {
            get {
                return ResourceManager.GetString("agreementStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html xmlns=&quot;http://www.w3.org/1999/xhtml&quot;&gt;
        ///&lt;head&gt;
        ///    &lt;title&gt;&lt;/title&gt;
        ///&lt;/head&gt;
        ///&lt;body&gt;
        ///    &lt;p&gt;Dear [AuthorizerFullName],&lt;/p&gt;
        ///
        ///    &lt;p&gt;An email has been sent  at &quot;[Email]&quot; as you have authorized that person as an Authorized user for your application at &lt;a href=&quot;[url]&quot;&gt; Alabamacares.Alabama.gov &lt;/a&gt;.&lt;/p&gt;
        /// 
        ///&lt;p&gt;If you are not the intended recipient or received this email in error,  please call us at 1-************.&lt;/p&gt;
        ///    
        ///&lt;p&gt;Regards,&lt;br/&gt;  
        /// AlabamaCares&lt;/p&gt;
        ///&lt;/body&gt;
        ///&lt;/html&gt;.
        /// </summary>
        public static string AuthorizedEmailNotifiedFormat {
            get {
                return ResourceManager.GetString("AuthorizedEmailNotifiedFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AlabamaCares :  Authorized User has been notified by email !!!.
        /// </summary>
        public static string AuthorizedEmailNotifiedSubject {
            get {
                return ResourceManager.GetString("AuthorizedEmailNotifiedSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AlabamaCares :  Notification of Authorized User..
        /// </summary>
        public static string AuthorizedEmailSubject {
            get {
                return ResourceManager.GetString("AuthorizedEmailSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html xmlns=&quot;http://www.w3.org/1999/xhtml&quot;&gt;
        ///&lt;head&gt;
        ///    &lt;title&gt;&lt;/title&gt;
        ///&lt;/head&gt;
        ///&lt;body&gt;
        ///    &lt;p&gt;Dear [AuthorizedUserFullName],&lt;/p&gt;
        ///
        ///    &lt;p&gt;[AuthorizerFullName] has selected you as official Authorized User for his/her account on &lt;a href=&quot;[url]&quot;&gt; Alabamacares.Alabama.gov &lt;/a&gt;.&lt;/p&gt;
        ///
        ///    &lt;p&gt;To approve or decline the Authorization, please take appropriate action as mentioned below :-  &lt;/p&gt;   
        ///&lt;ul&gt; 
        ///&lt;li&gt; If you already have an account with &lt;a href=&quot;[url]&quot;&gt; Alabamacares.Alabama.gov &lt;/a&gt; pl [rest of string was truncated]&quot;;.
        /// </summary>
        public static string AuthorizedUserEmailFormat {
            get {
                return ResourceManager.GetString("AuthorizedUserEmailFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This person will need to create an account in order to get notices and act on your behalf.  Enter his or her email address below.  We&apos;ll send information on how to create an account and become your authorized representative.  You can still continue with your application now..
        /// </summary>
        public static string authRepCreateAcctStatement {
            get {
                return ResourceManager.GetString("authRepCreateAcctStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorized Representative.
        /// </summary>
        public static string authRepHeader {
            get {
                return ResourceManager.GetString("authRepHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can give a trusted person permission to talk about this application with us, see your information, and act for you on matters related to this application, including getting information about your application and signing your application on your behalf.  This person is called an &quot;authorized representative&quot;..
        /// </summary>
        public static string authRepStatement {
            get {
                return ResourceManager.GetString("authRepStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selection of an authorized representative is required..
        /// </summary>
        public static string authUserSelectRequired {
            get {
                return ResourceManager.GetString("authUserSelectRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the first name, last name, email address, and phone number for this person&apos;s account..
        /// </summary>
        public static string enterAuthRepInfo {
            get {
                return ResourceManager.GetString("enterAuthRepInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to name someone as your authorized representative?.
        /// </summary>
        public static string nameAuthRep {
            get {
                return ResourceManager.GetString("nameAuthRep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorized user has to be someone other than yourself..
        /// </summary>
        public static string NoSelfAuthroizedUser {
            get {
                return ResourceManager.GetString("NoSelfAuthroizedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does this person already have their own account?.
        /// </summary>
        public static string personHaveAccountAlready {
            get {
                return ResourceManager.GetString("personHaveAccountAlready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is this person part of an organization helping you apply for coverage?.
        /// </summary>
        public static string personPartOfOrganization {
            get {
                return ResourceManager.GetString("personPartOfOrganization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Either &apos;Username&apos; or &apos;Email&apos; is required..
        /// </summary>
        public static string UsernameOrEmailRequired {
            get {
                return ResourceManager.GetString("UsernameOrEmailRequired", resourceCulture);
            }
        }
    }
}
