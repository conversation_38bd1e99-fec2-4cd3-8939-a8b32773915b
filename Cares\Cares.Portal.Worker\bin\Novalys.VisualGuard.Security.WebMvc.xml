<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Novalys.VisualGuard.Security.WebMvc</name>
    </assembly>
    <members>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String)">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="obj"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.Object)">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.String)">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="controllerName">The name of controller.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.Web.Routing.RouteValueDictionary)">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.Object,System.Object)">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <param name="htmlAttributes">An object that contains the HTML attributes for the element. The attributes are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.Web.Routing.RouteValueDictionary,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <param name="htmlAttributes">An object that contains the HTML attributes for the element. The attributes are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.String,System.Object,System.Object)">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="controllerName">The name of controller.</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <param name="htmlAttributes">An object that contains the HTML attributes for the element. The attributes are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Object,System.Object)">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="controllerName">The name of controller.</param>
            <param name="protocol">The protocol for the URL, such as "http" or https".</param>
            <param name="hostName">The host name for the URL.</param>
            <param name="fragment">The URL fragment name(the anchor name).</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <param name="htmlAttributes">An object that contains the HTML attributes for the element. The attributes are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper,System.Object,System.String,System.String,System.String,System.String,System.Web.Routing.RouteValueDictionary,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="controllerName">The name of controller.</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <param name="htmlAttributes">An object that contains the HTML attributes for the element. The attributes are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.HtmlHelperSecurityExtensions.VGActionLink(System.Web.Mvc.HtmlHelper{System.Object},System.Object,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Web.Routing.RouteValueDictionary,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Visual Guard method to return either action element or element label based on permission passed as parameter to this method.
            </summary>
            <param name="htmlHelper"></param>
            <param name="permissionFullName">Full Name of Permission(with folder name if exists)</param>
            <param name="linkTextAllow">The inner text of anchor element.</param>
            <param name="linkTextDisallow">The inner text of label, if not assigned VG Permission.</param>
            <param name="actionName">The name of action.</param>
            <param name="controllerName">The name of controller.</param>
            <param name="protocol">The protocol for the URL, such as "http" or https".</param>
            <param name="hostName">The host name for the URL.</param>
            <param name="fragment">The URL fragment name(the anchor name).</param>
            <param name="routeValues">An object that contains the parameters for a route. The parameters are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <param name="htmlAttributes">An object that contains the HTML attributes for the element. The attributes are retrieved through reflection by examining the properties of the object. The object is typically created by using object initializer syntax.</param>
            <returns>Based on VG permission, it returns an anchor element(a element) that contains the virtual path of the specified action.</returns>
        </member>
        <member name="P:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.Permissions">
            <summary>
            Property to define permissions in comma seperated
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.PermissionIds">
            <summary>
            Property to define permission Ids in comma seperated
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.RoleIds">
            <summary>
            Property to define Role Ids in comma seperated
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.SplitString(System.String)">
            <summary>
            Splits the Comma seperated String into array 
            </summary>
            <param name="original">Comma seperated string</param>
            <returns>Returns string array</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.SplitGuid(System.String)">
            <summary>
            Splits the Comma seperated String into Guid array 
            </summary>
            <param name="original">Comma seperated string</param>
            <returns>Returns Guid array</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.OnAuthorization(System.Web.Http.Controllers.HttpActionContext)">
            <summary>
            Overrided method to authorize VG user
            </summary>
            <param name="actionContext"></param>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.IsAuthorized(System.Web.Http.Controllers.HttpActionContext)">
            <summary>
            Method to check if the user is authorized and has permissions assigned.
            </summary>
            <param name="actionContext"></param>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.VGAuthorizeAttribute.SkipAuthorization(System.Web.Http.Controllers.HttpActionContext)">
            <summary>
            Method to check whether to skip authorization if anonymous is allowed
            </summary>
            <param name="actionContext"></param>
            <returns>Returns Boolean value</returns>
        </member>
        <member name="P:Novalys.VisualGuard.Security.Web.VGWebAuthorizeAttribute.Permissions">
            <summary>
            Property to define permissions in comma seperated
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.Web.VGWebAuthorizeAttribute.PermissionIds">
            <summary>
            Property to define permission Ids in comma seperated
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.Web.VGWebAuthorizeAttribute.RoleIds">
            <summary>
            Property to define Role Ids in comma seperated
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.VGWebAuthorizeAttribute.SplitString(System.String)">
            <summary>
            Splits the Comma seperated String into array 
            </summary>
            <param name="original">Comma seperated string</param>
            <returns>Returns string array</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.Web.VGWebAuthorizeAttribute.SplitGuid(System.String)">
            <summary>
            Splits the Comma seperated String into Guid array 
            </summary>
            <param name="original">Comma seperated string</param>
            <returns>Returns Guid array</returns>
        </member>
    </members>
</doc>
