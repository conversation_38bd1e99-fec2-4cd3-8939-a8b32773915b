{"Version": 1, "WorkspaceRootPath": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|c:\\azure-sd-code-devops\\main\\cares\\cares.portal.worker\\scripts\\jquery-3.4.1.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}|", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\scripts\\jquery-3.4.1.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}|"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\controllers\\application\\contactcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\controllers\\application\\contactcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_MONTHLY_PEP_ENROLLED_IN_MEDICAID.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_MONTHLY_HPE_TO_MEDICAID_FLIP.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.USP_SELECT_PRIVACY_LETTERS.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_APPLICATION_DETAILS_FOR_PEP.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\applicationsnapshot\\_pepdeterminerprovidersectionpartialview.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\applicationsnapshot\\_pepdeterminerprovidersectionpartialview.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker.models\\models\\snapshot\\viewmodel\\pepdeterminerproviderviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|solutionrelative:cares.portal.worker.models\\models\\snapshot\\viewmodel\\pepdeterminerproviderviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\applicationsnapshot\\applicationsnapshot.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\applicationsnapshot\\applicationsnapshot.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\presumptiveeligibility\\_pepdeterminerproviderinfo.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\presumptiveeligibility\\_pepdeterminerproviderinfo.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{505845FB-0862-467B-92DC-09F27AEA9002}|Cares.Api.Messages\\Cares.Api.Messages.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.api.messages\\presumptiveeligibility\\pepsnapshotdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{505845FB-0862-467B-92DC-09F27AEA9002}|Cares.Api.Messages\\Cares.Api.Messages.csproj|solutionrelative:cares.api.messages\\presumptiveeligibility\\pepsnapshotdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{505845FB-0862-467B-92DC-09F27AEA9002}|Cares.Api.Messages\\Cares.Api.Messages.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.api.messages\\presumptiveeligibility\\pepsearchdeterminersdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{505845FB-0862-467B-92DC-09F27AEA9002}|Cares.Api.Messages\\Cares.Api.Messages.csproj|solutionrelative:cares.api.messages\\presumptiveeligibility\\pepsearchdeterminersdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker.models\\models\\elderlydisabled\\viewmodel\\elderlydisabledotherburialfundsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|solutionrelative:cares.portal.worker.models\\models\\elderlydisabled\\viewmodel\\elderlydisabledotherburialfundsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{123BC641-5065-4DCD-B044-47E93936F79A}|Cares.Data\\Cares.Data.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.data\\dataabstractionlayer\\presumptiveeligibilitydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{123BC641-5065-4DCD-B044-47E93936F79A}|Cares.Data\\Cares.Data.csproj|solutionrelative:cares.data\\dataabstractionlayer\\presumptiveeligibilitydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4B440975-8857-4F5C-821E-57394D01F42B}|Cares.Api\\Cares.Api.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.api\\person\\contactinformationdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4B440975-8857-4F5C-821E-57394D01F42B}|Cares.Api\\Cares.Api.csproj|solutionrelative:cares.api\\person\\contactinformationdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4B440975-8857-4F5C-821E-57394D01F42B}|Cares.Api\\Cares.Api.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.api\\applicationsnapshot\\applicationsnapshotdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4B440975-8857-4F5C-821E-57394D01F42B}|Cares.Api\\Cares.Api.csproj|solutionrelative:cares.api\\applicationsnapshot\\applicationsnapshotdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{123BC641-5065-4DCD-B044-47E93936F79A}|Cares.Data\\Cares.Data.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.data\\dataabstractionlayer\\person\\persondal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{123BC641-5065-4DCD-B044-47E93936F79A}|Cares.Data\\Cares.Data.csproj|solutionrelative:cares.data\\dataabstractionlayer\\person\\persondal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\contact\\contactinformation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\contact\\contactinformation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\controllers\\application\\applicationsnapshotcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\controllers\\application\\applicationsnapshotcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\bll\\landing\\landingbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\bll\\landing\\landingbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7CEEF279-5423-4D34-89EC-222D7119AF94}|Cares.Services.AppTier\\Cares.Services.AppTier.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.services.apptier\\bll\\landing\\landing.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7CEEF279-5423-4D34-89EC-222D7119AF94}|Cares.Services.AppTier\\Cares.Services.AppTier.csproj|solutionrelative:cares.services.apptier\\bll\\landing\\landing.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7CEEF279-5423-4D34-89EC-222D7119AF94}|Cares.Services.AppTier\\Cares.Services.AppTier.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.services.apptier\\controllers\\landingapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7CEEF279-5423-4D34-89EC-222D7119AF94}|Cares.Services.AppTier\\Cares.Services.AppTier.csproj|solutionrelative:cares.services.apptier\\controllers\\landingapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker.models\\models\\snapshot\\viewmodel\\applicationsnapshotviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|solutionrelative:cares.portal.worker.models\\models\\snapshot\\viewmodel\\applicationsnapshotviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker.models\\models\\presumptiveeligibility\\viewmodel\\pepdeterminerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|solutionrelative:cares.portal.worker.models\\models\\presumptiveeligibility\\viewmodel\\pepdeterminerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EE96E415-E65C-4069-858A-3897238DCF11}|Cares.Portal.Infrastructure\\Cares.Portal.Infrastructure.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.infrastructure\\basemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EE96E415-E65C-4069-858A-3897238DCF11}|Cares.Portal.Infrastructure\\Cares.Portal.Infrastructure.csproj|solutionrelative:cares.portal.infrastructure\\basemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\controllers\\application\\summarycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\controllers\\application\\summarycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\enrollmenthistory\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\enrollmenthistory\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{D8F7AB2C-BB31-4FE1-B470-9AE360F5ABE5}|Cares.Api.Infrastructure\\Cares.Api.Infrastructure.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.api.infrastructure\\enums\\enumuserroles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D8F7AB2C-BB31-4FE1-B470-9AE360F5ABE5}|Cares.Api.Infrastructure\\Cares.Api.Infrastructure.csproj|solutionrelative:cares.api.infrastructure\\enums\\enumuserroles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EE96E415-E65C-4069-858A-3897238DCF11}|Cares.Portal.Infrastructure\\Cares.Portal.Infrastructure.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.infrastructure\\vgsecurity\\caressecurity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EE96E415-E65C-4069-858A-3897238DCF11}|Cares.Portal.Infrastructure\\Cares.Portal.Infrastructure.csproj|solutionrelative:cares.portal.infrastructure\\vgsecurity\\caressecurity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker.models\\models\\application\\contactinformation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}|Cares.Portal.Worker.Models\\Cares.Portal.Worker.Models.csproj|solutionrelative:cares.portal.worker.models\\models\\application\\contactinformation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\landing\\landing.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\landing\\landing.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{D8F7AB2C-BB31-4FE1-B470-9AE360F5ABE5}|Cares.Api.Infrastructure\\Cares.Api.Infrastructure.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.api.infrastructure\\constants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D8F7AB2C-BB31-4FE1-B470-9AE360F5ABE5}|Cares.Api.Infrastructure\\Cares.Api.Infrastructure.csproj|solutionrelative:cares.api.infrastructure\\constants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\landing\\_landingapplications.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\landing\\_landingapplications.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 64, "SelectedChildIndex": 14, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{004be353-6879-467c-9d1e-9ac23cdf6d49}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:1:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:15:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:17:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:22:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:23:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:24:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:25:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:26:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:27:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:28:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "jquery-3.4.1.js", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Scripts\\jquery-3.4.1.js", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Scripts\\jquery-3.4.1.js", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Scripts\\jquery-3.4.1.js", "RelativeToolTip": "Cares.Portal.Worker\\Scripts\\jquery-3.4.1.js", "ViewState": "AgIAAPoOAAAAAAAAAAAewAkPAAACAAAAAAAAAA==", "Icon": "************************************.001646|", "WhenOpened": "2025-09-05T15:45:03.748Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{99b8fa2f-ab90-4f57-9c32-949f146f1914}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "dbo.usp_SELECT_MONTHLY_PEP_ENROLLED_IN_MEDICAID.sql", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_MONTHLY_PEP_ENROLLED_IN_MEDICAID.sql", "RelativeDocumentMoniker": "..\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_MONTHLY_PEP_ENROLLED_IN_MEDICAID.sql", "ToolTip": "dbo.usp_SELECT_MONTHLY_PEP_ENROLLED_IN_MEDICAID.sql", "Icon": "************************************.000826|", "WhenOpened": "2025-08-26T21:51:14.022Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "dbo.usp_SELECT_MONTHLY_HPE_TO_MEDICAID_FLIP.sql", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_MONTHLY_HPE_TO_MEDICAID_FLIP.sql", "RelativeDocumentMoniker": "..\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_MONTHLY_HPE_TO_MEDICAID_FLIP.sql", "ToolTip": "dbo.usp_SELECT_MONTHLY_HPE_TO_MEDICAID_FLIP.sql", "Icon": "************************************.000826|", "WhenOpened": "2025-08-25T15:08:08.918Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "dbo.USP_SELECT_PRIVACY_LETTERS.sql", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.USP_SELECT_PRIVACY_LETTERS.sql", "RelativeDocumentMoniker": "..\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.USP_SELECT_PRIVACY_LETTERS.sql", "ToolTip": "dbo.USP_SELECT_PRIVACY_LETTERS.sql", "Icon": "************************************.000826|", "WhenOpened": "2025-08-20T20:20:32.102Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "dbo.usp_SELECT_APPLICATION_DETAILS_FOR_PEP.sql", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_APPLICATION_DETAILS_FOR_PEP.sql", "RelativeDocumentMoniker": "..\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_APPLICATION_DETAILS_FOR_PEP.sql", "ToolTip": "dbo.usp_SELECT_APPLICATION_DETAILS_FOR_PEP.sql", "Icon": "************************************.000826|", "WhenOpened": "2025-08-19T14:51:03.197Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "dbo.usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT.sql", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT.sql", "RelativeDocumentMoniker": "..\\Databases\\CARES.Databases.db4_ee\\Stored Procedures\\dbo.usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT.sql", "ToolTip": "dbo.usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT.sql", "Icon": "************************************.000826|", "WhenOpened": "2025-08-27T15:39:20.443Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 11, "Title": "PEPSnapshotDto.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Messages\\PresumptiveEligibility\\PEPSnapshotDto.cs", "RelativeDocumentMoniker": "Cares.Api.Messages\\PresumptiveEligibility\\PEPSnapshotDto.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Messages\\PresumptiveEligibility\\PEPSnapshotDto.cs", "RelativeToolTip": "Cares.Api.Messages\\PresumptiveEligibility\\PEPSnapshotDto.cs", "ViewState": "AgIAABsAAAAAAAAAAAAiwAUAAABlAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-20T16:03:54.054Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "_PepDeterminerProviderInfo.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\PresumptiveEligibility\\_PepDeterminerProviderInfo.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\PresumptiveEligibility\\_PepDeterminerProviderInfo.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\PresumptiveEligibility\\_PepDeterminerProviderInfo.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\PresumptiveEligibility\\_PepDeterminerProviderInfo.cshtml", "ViewState": "AgIAAMYAAAAAAAAAAAAAAOAAAABGAAAAAAAAAA==", "Icon": "************************************.000759|", "WhenOpened": "2025-08-06T15:00:06.112Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ElderlyDisabledOtherBurialFundsViewModel.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\ElderlyDisabled\\ViewModel\\ElderlyDisabledOtherBurialFundsViewModel.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker.Models\\Models\\ElderlyDisabled\\ViewModel\\ElderlyDisabledOtherBurialFundsViewModel.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\ElderlyDisabled\\ViewModel\\ElderlyDisabledOtherBurialFundsViewModel.cs", "RelativeToolTip": "Cares.Portal.Worker.Models\\Models\\ElderlyDisabled\\ViewModel\\ElderlyDisabledOtherBurialFundsViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-20T15:15:10.115Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "ContactInformationDAL.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api\\Person\\ContactInformationDAL.cs", "RelativeDocumentMoniker": "Cares.Api\\Person\\ContactInformationDAL.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api\\Person\\ContactInformationDAL.cs", "RelativeToolTip": "Cares.Api\\Person\\ContactInformationDAL.cs", "ViewState": "AgIAAKAAAAAAAAAAAAAqwM0AAAAfAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-06T23:03:18.621Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ContactController.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\ContactController.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Controllers\\Application\\ContactController.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\ContactController.cs", "RelativeToolTip": "Cares.Portal.Worker\\Controllers\\Application\\ContactController.cs", "ViewState": "AgIAACADAAAAAAAAAAD4vy0DAAAAAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-07-30T22:16:47.772Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "PresumptiveEligibilityDAL.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Data\\DataAbstractionLayer\\PresumptiveEligibilityDAL.cs", "RelativeDocumentMoniker": "Cares.Data\\DataAbstractionLayer\\PresumptiveEligibilityDAL.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Data\\DataAbstractionLayer\\PresumptiveEligibilityDAL.cs", "RelativeToolTip": "Cares.Data\\DataAbstractionLayer\\PresumptiveEligibilityDAL.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAABoAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-06T20:01:56.485Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "PepDeterminerProviderViewModel.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\PepDeterminerProviderViewModel.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\PepDeterminerProviderViewModel.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\PepDeterminerProviderViewModel.cs", "RelativeToolTip": "Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\PepDeterminerProviderViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAARAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T19:14:21.366Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ApplicationSnapshot.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "ViewState": "AgIAAJcAAAAAAAAAAAAEwKcAAAADAAAAAAAAAA==", "Icon": "************************************.000759|", "WhenOpened": "2025-08-14T19:57:00.677Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "Web.config", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Web.config", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Web.config", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Web.config", "RelativeToolTip": "Cares.Portal.Worker\\Web.config", "ViewState": "AgIAABUAAAAAAAAAAAAAADYAAABAAAAAAAAAAA==", "Icon": "************************************.000601|", "WhenOpened": "2025-08-14T22:12:15.578Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "ApplicationSnapshotController.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\ApplicationSnapshotController.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Controllers\\Application\\ApplicationSnapshotController.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\ApplicationSnapshotController.cs", "RelativeToolTip": "Cares.Portal.Worker\\Controllers\\Application\\ApplicationSnapshotController.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwC8AAAAMAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T21:44:13.656Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "LandingBll.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\BLL\\Landing\\LandingBll.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker\\BLL\\Landing\\LandingBll.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\BLL\\Landing\\LandingBll.cs", "RelativeToolTip": "Cares.Portal.Worker\\BLL\\Landing\\LandingBll.cs", "ViewState": "AgIAAOMAAAAAAAAAAAAAwAkBAAAMAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T21:44:38.165Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "LandingApiController.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Services.AppTier\\Controllers\\LandingApiController.cs", "RelativeDocumentMoniker": "Cares.Services.AppTier\\Controllers\\LandingApiController.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Services.AppTier\\Controllers\\LandingApiController.cs", "RelativeToolTip": "Cares.Services.AppTier\\Controllers\\LandingApiController.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAmwHsAAAAQAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T21:45:06.726Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "Landing.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Services.AppTier\\BLL\\Landing\\Landing.cs", "RelativeDocumentMoniker": "Cares.Services.AppTier\\BLL\\Landing\\Landing.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Services.AppTier\\BLL\\Landing\\Landing.cs", "RelativeToolTip": "Cares.Services.AppTier\\BLL\\Landing\\Landing.cs", "ViewState": "AgIAALAAAAAAAAAAAADgv8cAAAAMAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T21:45:29.626Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "ApplicationSnapshotDAL.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api\\ApplicationSnapshot\\ApplicationSnapshotDAL.cs", "RelativeDocumentMoniker": "Cares.Api\\ApplicationSnapshot\\ApplicationSnapshotDAL.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api\\ApplicationSnapshot\\ApplicationSnapshotDAL.cs", "RelativeToolTip": "Cares.Api\\ApplicationSnapshot\\ApplicationSnapshotDAL.cs", "ViewState": "AgIAAKYIAAAAAAAAAAAQwNMIAABPAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T18:53:53.139Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "_PepDeterminerProviderSectionPartialView.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\ApplicationSnapshot\\_PepDeterminerProviderSectionPartialView.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\ApplicationSnapshot\\_PepDeterminerProviderSectionPartialView.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\ApplicationSnapshot\\_PepDeterminerProviderSectionPartialView.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\ApplicationSnapshot\\_PepDeterminerProviderSectionPartialView.cshtml", "ViewState": "AgIAACcAAAAAAAAAAAAAAD0AAABCAAAAAAAAAA==", "Icon": "************************************.000759|", "WhenOpened": "2025-08-14T20:12:00.44Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "PepDeterminerViewModel.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\PresumptiveEligibility\\ViewModel\\PepDeterminerViewModel.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker.Models\\Models\\PresumptiveEligibility\\ViewModel\\PepDeterminerViewModel.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\PresumptiveEligibility\\ViewModel\\PepDeterminerViewModel.cs", "RelativeToolTip": "Cares.Portal.Worker.Models\\Models\\PresumptiveEligibility\\ViewModel\\PepDeterminerViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T19:13:31.999Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "ApplicationSnapshotViewModel.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\ApplicationSnapshotViewModel.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\ApplicationSnapshotViewModel.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\ApplicationSnapshotViewModel.cs", "RelativeToolTip": "Cares.Portal.Worker.Models\\Models\\Snapshot\\ViewModel\\ApplicationSnapshotViewModel.cs", "ViewState": "AgIAAI4AAAAAAAAAAAAAAJcAAAAAAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-14T19:06:23.419Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "BaseModel.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Infrastructure\\BaseModel.cs", "RelativeDocumentMoniker": "Cares.Portal.Infrastructure\\BaseModel.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Infrastructure\\BaseModel.cs", "RelativeToolTip": "Cares.Portal.Infrastructure\\BaseModel.cs", "ViewState": "AgIAAF0AAAAAAAAAAAD4v2EAAAARAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-08T14:53:00.522Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "PersonDal.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "RelativeDocumentMoniker": "Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "RelativeToolTip": "Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "ViewState": "AgIAAE4CAAAAAAAAAAAwwH0CAABhAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-06T23:06:07.484Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "SummaryController.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "RelativeToolTip": "Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "ViewState": "AgIAAAACAAAAAAAAAAAAABICAABIAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-04-09T22:15:28.611Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "ContactInformation.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\Contact\\ContactInformation.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\Contact\\ContactInformation.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\Contact\\ContactInformation.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\Contact\\ContactInformation.cshtml", "ViewState": "AgIAAAYAAAAAAAAAAAAAABUAAAAwAAAAAAAAAA==", "Icon": "************************************.000759|", "WhenOpened": "2025-07-14T20:40:15.654Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "PEPSearchDeterminersDto.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Messages\\PresumptiveEligibility\\PEPSearchDeterminersDto.cs", "RelativeDocumentMoniker": "Cares.Api.Messages\\PresumptiveEligibility\\PEPSearchDeterminersDto.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Messages\\PresumptiveEligibility\\PEPSearchDeterminersDto.cs", "RelativeToolTip": "Cares.Api.Messages\\PresumptiveEligibility\\PEPSearchDeterminersDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAB7AAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-08-06T22:06:13.132Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "ContactInformation.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\Application\\ContactInformation.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker.Models\\Models\\Application\\ContactInformation.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker.Models\\Models\\Application\\ContactInformation.cs", "RelativeToolTip": "Cares.Portal.Worker.Models\\Models\\Application\\ContactInformation.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAWwGEAAAAkAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-07-14T20:54:42.085Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "CaresSecurity.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Infrastructure\\VGSecurity\\CaresSecurity.cs", "RelativeDocumentMoniker": "Cares.Portal.Infrastructure\\VGSecurity\\CaresSecurity.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Infrastructure\\VGSecurity\\CaresSecurity.cs", "RelativeToolTip": "Cares.Portal.Infrastructure\\VGSecurity\\CaresSecurity.cs", "ViewState": "AgIAAGsCAAAAAAAAAAAAAIgCAAAuAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-06-09T22:21:50.732Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "enumUserRoles.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Infrastructure\\Enums\\enumUserRoles.cs", "RelativeDocumentMoniker": "Cares.Api.Infrastructure\\Enums\\enumUserRoles.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Infrastructure\\Enums\\enumUserRoles.cs", "RelativeToolTip": "Cares.Api.Infrastructure\\Enums\\enumUserRoles.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAdAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-07-07T23:12:15.001Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "_LandingApplications.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\Landing\\_LandingApplications.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\Landing\\_LandingApplications.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\Landing\\_LandingApplications.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\Landing\\_LandingApplications.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "************************************.000759|", "WhenOpened": "2025-07-02T18:52:20.169Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "Landing.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\Landing\\Landing.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\Landing\\Landing.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\Landing\\Landing.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\Landing\\Landing.cshtml", "ViewState": "AgIAAFsAAAAAAAAAAIAwwHUAAABfAAAAAAAAAA==", "Icon": "************************************.000759|", "WhenOpened": "2025-06-09T22:12:01.656Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "Constants.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Infrastructure\\Constants.cs", "RelativeDocumentMoniker": "Cares.Api.Infrastructure\\Constants.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Infrastructure\\Constants.cs", "RelativeToolTip": "Cares.Api.Infrastructure\\Constants.cs", "ViewState": "AgIAABcAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "************************************.000738|", "WhenOpened": "2025-04-10T22:08:56.027Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "Index.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\EnrollmentHistory\\Index.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\EnrollmentHistory\\Index.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\EnrollmentHistory\\Index.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\EnrollmentHistory\\Index.cshtml", "ViewState": "AgIAAL0AAAAAAAAAAAAAANcAAABrAAAAAAAAAA==", "Icon": "************************************.000759|", "WhenOpened": "2025-04-04T18:50:36.2Z", "EditorCaption": ""}]}]}]}