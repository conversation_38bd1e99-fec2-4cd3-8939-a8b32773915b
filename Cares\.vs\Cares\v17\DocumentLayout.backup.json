{"Version": 1, "WorkspaceRootPath": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\controllers\\application\\summarycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\controllers\\application\\summarycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{123BC641-5065-4DCD-B044-47E93936F79A}|Cares.Data\\Cares.Data.csproj|c:\\azure-sd-code-devops\\main\\cares\\cares.data\\dataabstractionlayer\\person\\persondal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{123BC641-5065-4DCD-B044-47E93936F79A}|Cares.Data\\Cares.Data.csproj|solutionrelative:cares.data\\dataabstractionlayer\\person\\persondal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{505845FB-0862-467B-92DC-09F27AEA9002}|Cares.Api.Messages\\Cares.Api.Messages.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.api.messages\\jiy\\applicationdetailsjiydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{505845FB-0862-467B-92DC-09F27AEA9002}|Cares.Api.Messages\\Cares.Api.Messages.csproj|solutionrelative:cares.api.messages\\jiy\\applicationdetailsjiydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\views\\applicationsnapshot\\applicationsnapshot.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\views\\applicationsnapshot\\applicationsnapshot.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\cares.portal.worker\\web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{98B9F145-F04B-4BBF-BF69-B6F956CC652C}|Cares.Portal.Worker\\Cares.Portal.Worker.csproj|solutionrelative:cares.portal.worker\\web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 64, "SelectedChildIndex": 15, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{004be353-6879-467c-9d1e-9ac23cdf6d49}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:1:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:17:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:22:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:23:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:24:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:25:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:26:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:27:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:28:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{99b8fa2f-ab90-4f57-9c32-949f146f1914}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SummaryController.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "RelativeToolTip": "Cares.Portal.Worker\\Controllers\\Application\\SummaryController.cs", "ViewState": "AgIAADwAAAAAAAAAAAAowPABAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-09T22:15:28.611Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "PersonDal.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "RelativeDocumentMoniker": "Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "RelativeToolTip": "Cares.Data\\DataAbstractionLayer\\Person\\PersonDal.cs", "ViewState": "AgIAAFwCAAAAAAAAAAAAAFwCAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T20:25:32.37Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ApplicationDetailsJiyDto.cs", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Messages\\JIY\\ApplicationDetailsJiyDto.cs", "RelativeDocumentMoniker": "Cares.Api.Messages\\JIY\\ApplicationDetailsJiyDto.cs", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Api.Messages\\JIY\\ApplicationDetailsJiyDto.cs", "RelativeToolTip": "Cares.Api.Messages\\JIY\\ApplicationDetailsJiyDto.cs", "ViewState": "AgIAAAkAAAAAAAAAAAA5wBEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T20:25:05.015Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ApplicationSnapshot.cshtml", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "RelativeToolTip": "Cares.Portal.Worker\\Views\\ApplicationSnapshot\\ApplicationSnapshot.cshtml", "ViewState": "AgIAAJcAAAAAAAAAAAAEwKcAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-14T19:57:00.677Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Web.config", "DocumentMoniker": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Web.config", "RelativeDocumentMoniker": "Cares.Portal.Worker\\Web.config", "ToolTip": "C:\\AZURE-SD-CODE-DevOps\\Main\\Cares\\Cares.Portal.Worker\\Web.config", "RelativeToolTip": "Cares.Portal.Worker\\Web.config", "ViewState": "AgIAABMAAAAAAAAAAABBwDYAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-08-14T22:12:15.578Z", "EditorCaption": ""}]}]}]}