﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Assistor {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Assistance {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Assistance() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Assistor.Assistance", typeof(Assistance).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Assistance.
        /// </summary>
        public static string applicationAssistance {
            get {
                return ResourceManager.GetString("applicationAssistance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certified application counselors:
        ///A certified application counselor is a staff member or volunteer of an organization who’s trained to help consumers looking for health coverage options through this website, including the completion of this application..
        /// </summary>
        public static string assistorNote {
            get {
                return ResourceManager.GetString("assistorNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A Certified Application Counselor is a person who has been trained to help citizens apply for health insurance through the federal marketplace.  CACs may be volunteers or employees of an organization..
        /// </summary>
        public static string certifiedApplicationCounselor {
            get {
                return ResourceManager.GetString("certifiedApplicationCounselor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is a Certified Application Counselor helping you with this application?.
        /// </summary>
        public static string GetHelp {
            get {
                return ResourceManager.GetString("GetHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organization Name.
        /// </summary>
        public static string orgName {
            get {
                return ResourceManager.GetString("orgName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By clicking here, I hereby agree to allow the selected person to act on my behalf to the extent I’ve identified. I understand that this person will have access to my personal and financial identifying information and all information contained in an application I submit through this online application for Medicaid and ALL Kids health care coverage..
        /// </summary>
        public static string secondaryAccountHolder {
            get {
                return ResourceManager.GetString("secondaryAccountHolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Account Holder.
        /// </summary>
        public static string secondaryAccountHolderHeader {
            get {
                return ResourceManager.GetString("secondaryAccountHolderHeader", resourceCulture);
            }
        }
    }
}
