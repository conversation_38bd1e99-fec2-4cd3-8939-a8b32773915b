USE [db4_ee]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [SSI].[usp_UPDATE_PERSON_DOD_BATCH_PROCESS] @CREATED_BY VARCHAR(50) = NULL
AS
-- ====================================================================================================================================================
-- Author:		Jeremy Hu
-- Create date: 11/25/2019
-- Description:	PBI 130824 - This procedure runs batch process to update DOD for existing person from SDX input file
-- 06/11/2020 - <PERSON> Hu - PBI 149043 - Convert temp table ##TEMP_SDX_APP_MAPPING_STAGING into dbo.SDX_DATA_STAGING_MAPPING
-- 06/24/2020 - <PERSON> Hu - PBI 149043 - Fix the issue of not creating records in person_detail table with DOD info
-- 06/24/2020 - Jeremy Hu - PBI 149043 - Correct the IS_DOD_Change flag
-- 06/24/2020 - Jeremy Hu - PBI 149043 - Fix the issue of assigning IS_DOD_Change flag for new created person
-- 07/21/2020 - Jeremy Hu - PBI 153128 - Add stored proc to update DOD enrollment if recipient still has active enrollment
-- 12/04/2020 - Jeremy Hu - PBI 162694 - Skip patient matching exception records.
-- 02/08/2021 - Henry Lin - PBI 167468 - If verification source is 'VITAL STATS', don't update DOD
-- 03/04/2021 - Henry Lin - PBI 170986 - Fix VITAL STATS got updated issue
-- 03/12/2021 - Henry Lin - PBI 167246 - Do not update incoming pending SDX DOD
-- 04/09/2021 - Jeremy Hu - PBI 167246 - Do not check if DOD person has active enrollment before enrollment update.
-- 04/09/2021 - Henry Lin - PBI 167564 - Update to exclude suspension scenario.
-- 04/09/2021 - Jeremy Hu - PBI 167564 - Correct input parameters for dbo.usp_Update_DOD_Enrollments
-- 08/26/2021 - Jeremy Hu - PBI 181640 - Remove DOD enrollment update process and run it earlier in SDX DOD termination step
-- 09/04/2025 - Monika Paruchuri - ENH 252954 - Modified to Remove Date of Death (DOD) when received within SDX File
-- ===================================================================================================================================================
BEGIN
	SET NOCOUNT ON;

	DROP TABLE IF EXISTS dbo.#TEMP_DOD_CHANGE

	DECLARE @ROW_ID INT = 1
	DECLARE @MAX_ROW_ID INT = 0
	DECLARE @APPLICATION_ID INT = 0
	DECLARE @PERSON_ID INT = 0
	DECLARE @DOD DATE = NULL
	DECLARE @DEATH_VERIFICATION_SOURCE VARCHAR(100) = ''
	DECLARE @DEATH_VERIFICATION_DATE DATE = NULL
	DECLARE @PROGRAM_ID TINYINT = NULL -- used for below fnHasActiveEnrollment, null means check all programs


	--1) Identify DOD update recipients
	SELECT ROW_NUMBER() OVER (
			ORDER BY sdsm.person_ID
			) AS ROW_ID
		,db4_ee.dbo.fn_GET_LATEST_APPLICATION_ID(sdsm.PERSON_ID) as APPLICATION_ID
		,sdsm.PERSON_ID
		,sdsm.DOD
		,sdsm.DEATH_VERIFICATION_SOURCE
	INTO dbo.#TEMP_DOD_CHANGE
	FROM staging.dbo.SDX_DATA_STAGING_MAPPING sdsm
	LEFT JOIN dbo.PERSON_DETAIL pd ON sdsm.PERSON_ID = pd.PERSON_ID
	WHERE sdsm.IS_EXCEPTION = 0
	    AND sdsm.PERSON_ID IS NOT NULL
		AND (
				(
					(
						ISNULL(sdsm.DOD, '') <> ISNULL(pd.DATE_OF_DEATH, '')
						OR ISNULL(sdsm.DEATH_VERIFICATION_SOURCE, '') <> ISNULL(pd.DEATH_VERIFICATION_SOURCE, '') 
					)
					AND ISNULL(pd.DEATH_VERIFICATION_SOURCE, '') <> 'VITAL STATS'
					)
				OR (
					pd.PERSON_ID IS NULL
					AND sdsm.DOD IS NOT NULL
					)
				)
		AND sdsm.SDX_STATUS != 1
		AND (sdsm.SDX_STATUS != 5 AND ((sdsm.DOD IS NOT NULL AND pd.DATE_OF_DEATH IS NULL) OR (pd.DATE_OF_DEATH IS NOT NULL AND sdsm.DOD IS NULL)))

	--2) Process DOD update
	SELECT @MAX_ROW_ID = (
			SELECT MAX(ROW_ID) AS MAX_ROW_ID
			FROM dbo.#TEMP_DOD_CHANGE
			)

	WHILE (@ROW_ID <= @MAX_ROW_ID)
	BEGIN
		SELECT @APPLICATION_ID = APPLICATION_ID
			,@PERSON_ID = PERSON_ID
			,@DOD = DOD
			,@DEATH_VERIFICATION_SOURCE = DEATH_VERIFICATION_SOURCE
		FROM dbo.#TEMP_DOD_CHANGE
		WHERE ROW_ID = @ROW_ID

		EXEC dbo.usp_Upsert_Person_Detail_And_Notes_DOD @APPLICATION_ID
			,@PERSON_ID
			,@DOD
			,@CREATED_BY
			,@DEATH_VERIFICATION_DATE
			,@DEATH_VERIFICATION_SOURCE

		SET @ROW_ID = @ROW_ID + 1
	END

	--3) Update IS_INSURANCE_SEND_IND_UPDATE and IS_DOD_CHANGE flag in SDX_DATA_STAGING_MAPPING table
	UPDATE sdsm
	SET sdsm.IS_INSURANCE_SEND_IND_UPDATE = 1
		,sdsm.IS_DOD_CHANGE = 1
	FROM staging.dbo.SDX_DATA_STAGING_MAPPING sdsm
	INNER JOIN dbo.#TEMP_DOD_CHANGE tdc ON sdsm.PERSON_ID = tdc.PERSON_ID
	WHERE sdsm.IS_NEW_PERSON = 0
END
GO