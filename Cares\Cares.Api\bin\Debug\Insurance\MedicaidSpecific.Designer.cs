﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Insurance {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class MedicaidSpecific {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MedicaidSpecific() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Insurance.MedicaidSpecific", typeof(MedicaidSpecific).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some people may qualify to receive help even if they already have health coverage. &lt;br/&gt; Do any of these people have health coverage now?.
        /// </summary>
        public static string haveHealthCoverage {
            get {
                return ResourceManager.GetString("haveHealthCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want help paying for [NAME]&apos;s, (DOB: [DOB]) medical bills from the last 3 months?.
        /// </summary>
        public static string helpLast3Months {
            get {
                return ResourceManager.GetString("helpLast3Months", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent&apos;s work hours per week are required..
        /// </summary>
        public static string hoursPerWeekRequired {
            get {
                return ResourceManager.GetString("hoursPerWeekRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medicaid Program Questions.
        /// </summary>
        public static string medicaidSpecificQuestions {
            get {
                return ResourceManager.GetString("medicaidSpecificQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent 1:.
        /// </summary>
        public static string parent1 {
            get {
                return ResourceManager.GetString("parent1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent 2:.
        /// </summary>
        public static string parent2 {
            get {
                return ResourceManager.GetString("parent2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME], (DOB: [DOB]) have a parent living outside the home?.
        /// </summary>
        public static string parentLivingOutsideHome {
            get {
                return ResourceManager.GetString("parentLivingOutsideHome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How many hours per week do [NAME]&apos;s, (DOB: [DOB]) parents work?.
        /// </summary>
        public static string parentWorkHours {
            get {
                return ResourceManager.GetString("parentWorkHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select either &quot;Yes&quot; or &quot;No&quot; for required options..
        /// </summary>
        public static string radioRequiredSelection {
            get {
                return ResourceManager.GetString("radioRequiredSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME], (DOB: [DOB]) could receive free or low-cost health coverage if [HE/SHE] has enough of a work history in the U.S. on [HIS/HER] own or through a family member. We checked [NAME]&apos;s, (DOB: [DOB]) work history because you gave us [HIS/HER] Social Security number (SSN)..
        /// </summary>
        public static string workHistoryGiveSSN_1 {
            get {
                return ResourceManager.GetString("workHistoryGiveSSN_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We can also check the work history for [NAME]&apos;s, (DOB: [DOB]) family members if you give us the SSN of [HIS/HER] parent or spouse (and he or she agrees you may use their SSN for this purpose). Would you like to give an SSN now?.
        /// </summary>
        public static string workHistoryGiveSSN_2_Married {
            get {
                return ResourceManager.GetString("workHistoryGiveSSN_2_Married", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We can also check the work history for [NAME]&apos;s, (DOB: [DOB]) family members if you give us the SSN of [HIS/HER] father or mother (and he or she agrees you may use their SSN for this purpose). Would you like to give an SSN now?.
        /// </summary>
        public static string workHistoryGiveSSN_2_NotMarried {
            get {
                return ResourceManager.GetString("workHistoryGiveSSN_2_NotMarried", resourceCulture);
            }
        }
    }
}
