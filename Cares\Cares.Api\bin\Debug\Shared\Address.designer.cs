﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Shared {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Address {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Address() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Shared.Address", typeof(Address).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Phone Number.
        /// </summary>
        public static string addPhone {
            get {
                return ResourceManager.GetString("addPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Line 1.
        /// </summary>
        public static string addressLine1 {
            get {
                return ResourceManager.GetString("addressLine1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Street 2 (Apt, Suite, Lot #).
        /// </summary>
        public static string aptSuiteLot {
            get {
                return ResourceManager.GetString("aptSuiteLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        public static string city {
            get {
                return ResourceManager.GetString("city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to County.
        /// </summary>
        public static string county {
            get {
                return ResourceManager.GetString("county", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Address.
        /// </summary>
        public static string email {
            get {
                return ResourceManager.GetString("email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Re-Enter Email Address.
        /// </summary>
        public static string emailReenter {
            get {
                return ResourceManager.GetString("emailReenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you have a home address where you stay right now?.
        /// </summary>
        public static string haveHomeAddress {
            get {
                return ResourceManager.GetString("haveHomeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home Address.
        /// </summary>
        public static string homeAddress {
            get {
                return ResourceManager.GetString("homeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To be eligible for Medicaid or ALL Kids, you must be a resident of Alabama..
        /// </summary>
        public static string liveInALToBeEligible {
            get {
                return ResourceManager.GetString("liveInALToBeEligible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mailing Address.
        /// </summary>
        public static string mailingAddress {
            get {
                return ResourceManager.GetString("mailingAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        public static string state {
            get {
                return ResourceManager.GetString("state", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Street 1.
        /// </summary>
        public static string street {
            get {
                return ResourceManager.GetString("street", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Where do you live or stay?.
        /// </summary>
        public static string whereDoYouLiveOrStay {
            get {
                return ResourceManager.GetString("whereDoYouLiveOrStay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who&apos;s Phone Is This?.
        /// </summary>
        public static string whosePhone {
            get {
                return ResourceManager.GetString("whosePhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zip Code.
        /// </summary>
        public static string zipCode {
            get {
                return ResourceManager.GetString("zipCode", resourceCulture);
            }
        }
    }
}
