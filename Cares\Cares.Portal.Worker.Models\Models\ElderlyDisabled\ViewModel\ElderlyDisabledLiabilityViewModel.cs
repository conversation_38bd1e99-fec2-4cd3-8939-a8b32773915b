﻿using Cares.Portal.Worker.Models.Validators.ElderlyDisabled;
using FluentValidation.Attributes;
using System;
using System.Collections.Generic;

namespace Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel
{
    /// <summary>
    /// View Model for Elderly Disabled Liability
    /// </summary>
    [Validator(typeof(ElderlyDisabledLiabilityViewModelValidator))]
    public class ElderlyDisabledLiabilityViewModel : BaseViewModel
    {
        public byte? ElderlyDisabledProgramId { get; set; }

        /// <summary>
        /// For the various Liability Test sections
        /// </summary>
        public List<ElderlyDisabledLiabilityTestViewModel> LiabilityTests { get; set; } = new List<ElderlyDisabledLiabilityTestViewModel>();

        #region LiabilityTests containers for the View:
        public List<ElderlyDisabledLiabilityTestViewModel> Infrequent_IrregularLiabilityTests { get; set; } = new List<ElderlyDisabledLiabilityTestViewModel>();
        public List<ElderlyDisabledLiabilityTestViewModel> OtherLiabilityTests { get; set; } = new List<ElderlyDisabledLiabilityTestViewModel>();
        public List<ElderlyDisabledLiabilityTestViewModel> VA_Aid_AttendanceLiabilityTests { get; set; } = new List<ElderlyDisabledLiabilityTestViewModel>();
        #endregion LiabilityTests containers for the View:

        /// <summary>
        /// Representative Information
        /// </summary>
        public List<RepresentativeInfoViewModel> RepresentativeInfo { get; set; } = new List<RepresentativeInfoViewModel>();

        /// <summary>
        /// The sole purpose for this is to have a ViewModel item that allows us to
        /// easily add HTML controls to the view (ie: @Html.DropDownListFor)
        /// </summary>
        public ElderlyDisabledLiabilityDetailViewModel AddOrEditSegment { get; set; } = new ElderlyDisabledLiabilityDetailViewModel();

        /// <summary>
        /// Gets or sets the liability segments.
        /// </summary>
        /// <value>
        /// The liability segments.
        /// </value>
        public List<ElderlyDisabledLiabilityDetailViewModel> LiabilitySegments { get; set; } = new List<ElderlyDisabledLiabilityDetailViewModel>();

        public bool IsDoDReported { get; set; }

        public string UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; } = new DateTime();
    }
}