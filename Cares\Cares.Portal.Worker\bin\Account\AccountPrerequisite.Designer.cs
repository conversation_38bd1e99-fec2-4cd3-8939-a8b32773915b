﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Account {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class AccountPrerequisite {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AccountPrerequisite() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Account.AccountPrerequisite", typeof(AccountPrerequisite).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save &amp; Continue.
        /// </summary>
        public static string ButtonTitle {
            get {
                return ResourceManager.GetString("ButtonTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you have children?.
        /// </summary>
        public static string HaveChild {
            get {
                return ResourceManager.GetString("HaveChild", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you the Head of Household?.
        /// </summary>
        public static string HeadofHousehold {
            get {
                return ResourceManager.GetString("HeadofHousehold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In order for your application to be processed faster, the account holder should be the Head of Household..
        /// </summary>
        public static string HeadofHouseholdWarning {
            get {
                return ResourceManager.GetString("HeadofHouseholdWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you pregnant?.
        /// </summary>
        public static string IsPregnant {
            get {
                return ResourceManager.GetString("IsPregnant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you a tax filer?.
        /// </summary>
        public static string IsTaxFiler {
            get {
                return ResourceManager.GetString("IsTaxFiler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tell us about yourself.
        /// </summary>
        public static string PageTitle {
            get {
                return ResourceManager.GetString("PageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you under 19 years of age?.
        /// </summary>
        public static string Under19 {
            get {
                return ResourceManager.GetString("Under19", resourceCulture);
            }
        }
    }
}
