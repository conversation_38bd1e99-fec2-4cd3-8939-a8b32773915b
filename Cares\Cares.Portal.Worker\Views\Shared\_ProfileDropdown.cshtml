﻿@using Cares.Api.Infrastructure
@using Cares.Portal.Infrastructure
@using Cares.Portal.Infrastructure.VGSecurity

<div class="dropdown">
    <button type="button" id="abtnLandingPerson" class="nav-item btn btn-circle btn-sm navLandingButton workerRemindersCount" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Profile">
        @CommonHtmlExtensions.IconHelper(Constants.Icons.IconPerson)
        <span class="badge badge-pill badge-danger" style="display: none; position: absolute; right: -10px; top: -5px; font-size: 12px;">0</span>
    </button>
    <div class="dropdown-menu dropdown-menu-left dropdown-menu-lg-right navbarDropdownOptions">
        <a id="aProfile" class="dropdown-item" href="@Url.Action("Landing", "Landing")">Profile (Landing Page)</a>
        @if (CaresSecurity.IsInRole(CaresSecurity.EandDRoles))
        {
            <a id="aExpediteFacilityData" class="dropdown-item" href="@Url.Action("ExpediteFacilityAdminPage", "ElderlyDisabled")">Facility</a>
            <a id="aFinancialInstitution" class="dropdown-item" href="@Url.Action("FinancialInstitutionAdminPage", "ElderlyDisabled")">Financial Institution</a>
        }
        @if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN) || CaresSecurity.IsInRole(CaresSecurity.UserRoles.JIY_ELIG))
        {
            <a id="aDYSFacilityData" class="dropdown-item" href="@Url.Action("DYSFacilityAdminPage", "Facility")">JIY Facility</a>
        }
        @if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN) || CaresSecurity.IsInRole(CaresSecurity.UserRoles.PEP_ELIG))
        {
            <a id="aPepAdminData" class="dropdown-item" href="@Url.Action("PepAdminPage", "PresumptiveEligibility")">PEP Providers & Determiners</a>
        }
        @if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN) || CaresSecurity.IsInRole(CaresSecurity.UserRoles.HPE_ELIG))
        {
            <a id="aHpeAdminData" class="dropdown-item" href="@Url.Action("HpeAdminPage", "PresumptiveEligibility")">HPE Providers & Determiners</a>
        }
        <a id="aReminder" class="dropdown-item workerRemindersCount" href="@Url.Action("WorkerDashboard", "WorkerPortalAccount")">
            Worker Reminders
            <span class="badge badge-pill badge-danger" style="display:none; position: relative; right: -10px; top: 0.2rem; font-size: 0.75rem; float: right;">0</span>
        </a>
    </div>
</div>

@*This script is created runtime by SignalR Library.*@
<script src="~/signalr/hubs"></script>

<script>
    //Logic concerned to the Reminders and SignalR connection.
    $(function () {
        if (workerNumberGlobal != null && workerNumberGlobal != '' && officeIdGlobal != null && officeIdGlobal != '') {
            // Reference the auto-generated proxy for the hub.
            var remindersHub = $.connection.remindersHub;

            remindersHub.client.updateReminders = function (counter) {
                Reminder.SetReminderCount({ success: true, workerReminderCount: counter }, "workerRemindersCount");
            };

            var currentWorkerNumber = officeIdGlobal + '' + workerNumberGlobal;

            // Start the connection.
            $.connection.hub.start().done(function () {
                remindersHub.server.addCounter(currentWorkerNumber);
            });

            //Obtaining The counter for the first time after creating the connection.
            remindersHub.client.susHubCreated = function (result) {
                if (result) {
                    Reminder.GetReminderCount(null, officeIdGlobal + '' + workerNumberGlobal, () => { });
                }
            };
        }
    });
</script>