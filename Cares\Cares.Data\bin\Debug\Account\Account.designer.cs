﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Account {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Account {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Account() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Account.Account", typeof(Account).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We were unable to create your account at this time. Please contact ALL Kids customer service by toll-free phone at 1-888-373-KIDS(5437) Monday-Friday 7:30AM to 5:00PM..
        /// </summary>
        public static string accountAlreadyExists {
            get {
                return ResourceManager.GetString("accountAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We were unable to create your account at this time. Please contact ALL Kids customer service by toll-free phone at 1-888-373-KIDS(5437) Monday-Friday 7:30AM to 5:00PM..
        /// </summary>
        public static string AccountCreationFailure {
            get {
                return ResourceManager.GetString("AccountCreationFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You need to create a new account if personal information of the account holder is incorrect..
        /// </summary>
        public static string accountCreationInfo {
            get {
                return ResourceManager.GetString("accountCreationInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information provided by you does not match with what we have currently on file for you. Please try again..
        /// </summary>
        public static string AccountDetailsNotVerified {
            get {
                return ResourceManager.GetString("AccountDetailsNotVerified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your account has been locked temporarily due to incorrect username/password. Please call the ALL Kids toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative..
        /// </summary>
        public static string accountLock {
            get {
                return ResourceManager.GetString("accountLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Account is locked. Please contact helpdesk..
        /// </summary>
        public static string accountLocked {
            get {
                return ResourceManager.GetString("accountLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have exceeded the number of incorrect attempts. We have locked your account to protect your information. Please contact Alabama Medicaid and CHIP toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative..
        /// </summary>
        public static string AccountLockExceededAttempts {
            get {
                return ResourceManager.GetString("AccountLockExceededAttempts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Authentication Required.
        /// </summary>
        public static string AdditonalAuthReq {
            get {
                return ResourceManager.GetString("AdditonalAuthReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer.
        /// </summary>
        public static string Answer {
            get {
                return ResourceManager.GetString("Answer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AOL.
        /// </summary>
        public static string aol {
            get {
                return ResourceManager.GetString("aol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to applying for other family members but not myself..
        /// </summary>
        public static string applyingFamilyMembersOnly {
            get {
                return ResourceManager.GetString("applyingFamilyMembersOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applying For.
        /// </summary>
        public static string applyingFor {
            get {
                return ResourceManager.GetString("applyingFor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applying For is required..
        /// </summary>
        public static string applyingForReq {
            get {
                return ResourceManager.GetString("applyingForReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to applying for myself and other family members..
        /// </summary>
        public static string applyingSelfAndAllFamily {
            get {
                return ResourceManager.GetString("applyingSelfAndAllFamily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to applying for myself only..
        /// </summary>
        public static string applyingSelfOnly {
            get {
                return ResourceManager.GetString("applyingSelfOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can still apply for health coverage if you do not have a home address or are homeless..
        /// </summary>
        public static string canStillApplyIfHomeless {
            get {
                return ResourceManager.GetString("canStillApplyIfHomeless", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Password.
        /// </summary>
        public static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Password request failed. Please try again later. if Issue persists, please contact helpdesk..
        /// </summary>
        public static string changePasswordFailure {
            get {
                return ResourceManager.GetString("changePasswordFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your account password was changed successfully..
        /// </summary>
        public static string ChangePasswordSuccess {
            get {
                return ResourceManager.GetString("ChangePasswordSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please click here to return to Login screen.
        /// </summary>
        public static string clickOn {
            get {
                return ResourceManager.GetString("clickOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Email Address.
        /// </summary>
        public static string ConfirmEmail {
            get {
                return ResourceManager.GetString("ConfirmEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm New Password.
        /// </summary>
        public static string confirmNewPassword {
            get {
                return ResourceManager.GetString("confirmNewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Password.
        /// </summary>
        public static string ConfirmPassword {
            get {
                return ResourceManager.GetString("ConfirmPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;ul&gt;&lt;li&gt;Confirm Password must match Password.&lt;/li&gt;&lt;/ul&gt;.
        /// </summary>
        public static string confirmPasswordPopOverContent {
            get {
                return ResourceManager.GetString("confirmPasswordPopOverContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Social Security Number (SSN).
        /// </summary>
        public static string ConfirmSSN {
            get {
                return ResourceManager.GetString("ConfirmSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are unable to process your request at this time. Please contact Alabama Medicaid and CHIP toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative.&lt;br/&gt;
        ///Error Code : &lt;b&gt;CDED500&lt;/b&gt;.
        /// </summary>
        public static string contactPersonDeceased {
            get {
                return ResourceManager.GetString("contactPersonDeceased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CREATE ACCOUNT.
        /// </summary>
        public static string CreateAccountButton {
            get {
                return ResourceManager.GetString("CreateAccountButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Congratulations!  Account created successfully..
        /// </summary>
        public static string createdSuccessfully {
            get {
                return ResourceManager.GetString("createdSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Of Birth.
        /// </summary>
        public static string DateOfBirth {
            get {
                return ResourceManager.GetString("DateOfBirth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate phone numbers of same type are not allowed..
        /// </summary>
        public static string duplicatePhone {
            get {
                return ResourceManager.GetString("duplicatePhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Address.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;Email&quot; and &quot;Confirm Email&quot; fields must match..
        /// </summary>
        public static string emailCompare {
            get {
                return ResourceManager.GetString("emailCompare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string EmailSection {
            get {
                return ResourceManager.GetString("EmailSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter the requested information for {0}:.
        /// </summary>
        public static string EnterInfo {
            get {
                return ResourceManager.GetString("EnterInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Username/Password.  Please try again..
        /// </summary>
        public static string failure {
            get {
                return ResourceManager.GetString("failure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please follow the link to log-in : -.
        /// </summary>
        public static string followLinktoHomePage {
            get {
                return ResourceManager.GetString("followLinktoHomePage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://my.screenname.aol.com/.
        /// </summary>
        public static string getEmailURLAOL {
            get {
                return ResourceManager.GetString("getEmailURLAOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://accounts.google.com/SignUp?service=mail&amp;continue=http%3A%2F%2Fmail.google.com%2Fmail%2Fe-11-109a5a55d7610df0e7957ac039c47579-ed2a46d7696ce586b7fe29a45c9ba0be71a9c732&amp;hl=en-us.
        /// </summary>
        public static string getEmailURLGmail {
            get {
                return ResourceManager.GetString("getEmailURLGmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://login.live.com/login.srf?.
        /// </summary>
        public static string getEmailURLOutLook {
            get {
                return ResourceManager.GetString("getEmailURLOutLook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://login.yahoo.com/config/login_verify2?&amp;.src=ym.
        /// </summary>
        public static string getEmailURLYahoo {
            get {
                return ResourceManager.GetString("getEmailURLYahoo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GMAIL.
        /// </summary>
        public static string gmail {
            get {
                return ResourceManager.GetString("gmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Don&apos;t have an email address? .
        /// </summary>
        public static string haveEmailAddress {
            get {
                return ResourceManager.GetString("haveEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go to [FREE_EMAIL_URLS] and create one..
        /// </summary>
        public static string howToGetOne {
            get {
                return ResourceManager.GetString("howToGetOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Email. Please correct it..
        /// </summary>
        public static string invalidEmail {
            get {
                return ResourceManager.GetString("invalidEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Name and Password can not be same.
        /// </summary>
        public static string invalidPassword {
            get {
                return ResourceManager.GetString("invalidPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid characters found in username.Please enter another username..
        /// </summary>
        public static string invalidUsername {
            get {
                return ResourceManager.GetString("invalidUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Password.
        /// </summary>
        public static string newPassword {
            get {
                return ResourceManager.GetString("newPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;New Password&quot; and &quot;Confirm New Password&quot; must match..
        /// </summary>
        public static string newPasswordCompare {
            get {
                return ResourceManager.GetString("newPasswordCompare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old Password.
        /// </summary>
        public static string oldPassword {
            get {
                return ResourceManager.GetString("oldPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;New Password&quot; can not be same as &quot;Old Password&quot;..
        /// </summary>
        public static string oldPasswordCompare {
            get {
                return ResourceManager.GetString("oldPasswordCompare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old Password does not match. Please try again later..
        /// </summary>
        public static string oldPasswordDoesNotMatch {
            get {
                return ResourceManager.GetString("oldPasswordDoesNotMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Our records indicate that you already have an application which is currently in process. We need to complete processing of that application before you can submit another application. Please contact Alabama Medicaid and CHIP toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative..
        /// </summary>
        public static string openApp {
            get {
                return ResourceManager.GetString("openApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OR you will be redirected to the Login screen in.
        /// </summary>
        public static string orRedirectToLogin {
            get {
                return ResourceManager.GetString("orRedirectToLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outlook.
        /// </summary>
        public static string outlook {
            get {
                return ResourceManager.GetString("outlook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Account.
        /// </summary>
        public static string PageTitleCreateAccount {
            get {
                return ResourceManager.GetString("PageTitleCreateAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Account.
        /// </summary>
        public static string PageTitleMyAccount {
            get {
                return ResourceManager.GetString("PageTitleMyAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download Paper Application.
        /// </summary>
        public static string PaperApplication {
            get {
                return ResourceManager.GetString("PaperApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Username/Password.  You have 1 more attempt before your account is locked..
        /// </summary>
        public static string passwordAboutToLock {
            get {
                return ResourceManager.GetString("passwordAboutToLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;Password&quot; and &quot;Confirm Password&quot; fields must match..
        /// </summary>
        public static string passwordCompare {
            get {
                return ResourceManager.GetString("passwordCompare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation Password Does not match..
        /// </summary>
        public static string PasswordConfirmationDoesNotMatch {
            get {
                return ResourceManager.GetString("PasswordConfirmationDoesNotMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your password has expired.  Please contact helpdesk..
        /// </summary>
        public static string passwordExpired {
            get {
                return ResourceManager.GetString("passwordExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your account is locked.  Please contact helpdesk..
        /// </summary>
        public static string passwordIsLocked {
            get {
                return ResourceManager.GetString("passwordIsLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;ul&gt;&lt;li&gt;Password must be 8 to 14 characters long.&lt;/li&gt;&lt;li&gt;Password must at least contain one uppercase character, one lowercase character, one number and at least one special symbol !@#$&amp;%^*+-.=_&lt;/li&gt;&lt;/ul&gt;.
        /// </summary>
        public static string passwordPopOverContent {
            get {
                return ResourceManager.GetString("passwordPopOverContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        public static string PhoneSection {
            get {
                return ResourceManager.GetString("PhoneSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string SaveButton {
            get {
                return ResourceManager.GetString("SaveButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer .
        /// </summary>
        public static string SecurityAnswer {
            get {
                return ResourceManager.GetString("SecurityAnswer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer 1.
        /// </summary>
        public static string SecurityAnswer1 {
            get {
                return ResourceManager.GetString("SecurityAnswer1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer 1 is required..
        /// </summary>
        public static string SecurityAnswer1Required {
            get {
                return ResourceManager.GetString("SecurityAnswer1Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer 2.
        /// </summary>
        public static string SecurityAnswer2 {
            get {
                return ResourceManager.GetString("SecurityAnswer2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer 2 is required..
        /// </summary>
        public static string SecurityAnswer2Required {
            get {
                return ResourceManager.GetString("SecurityAnswer2Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer 3.
        /// </summary>
        public static string SecurityAnswer3 {
            get {
                return ResourceManager.GetString("SecurityAnswer3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer 3 is required..
        /// </summary>
        public static string SecurityAnswer3Required {
            get {
                return ResourceManager.GetString("SecurityAnswer3Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can not use the same security answer more than once..
        /// </summary>
        public static string SecurityAsnwersSame {
            get {
                return ResourceManager.GetString("SecurityAsnwersSame", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Details.
        /// </summary>
        public static string SecurityDetails {
            get {
                return ResourceManager.GetString("SecurityDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Question 1.
        /// </summary>
        public static string SecurityQuestion1 {
            get {
                return ResourceManager.GetString("SecurityQuestion1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Question 1 is required..
        /// </summary>
        public static string SecurityQuestion1Required {
            get {
                return ResourceManager.GetString("SecurityQuestion1Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Question 2.
        /// </summary>
        public static string SecurityQuestion2 {
            get {
                return ResourceManager.GetString("SecurityQuestion2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Question 2 is required..
        /// </summary>
        public static string SecurityQuestion2Required {
            get {
                return ResourceManager.GetString("SecurityQuestion2Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Question 3.
        /// </summary>
        public static string SecurityQuestion3 {
            get {
                return ResourceManager.GetString("SecurityQuestion3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Question 3 is required..
        /// </summary>
        public static string SecurityQuestion3Required {
            get {
                return ResourceManager.GetString("SecurityQuestion3Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Question .
        /// </summary>
        public static string SecurityQuestions {
            get {
                return ResourceManager.GetString("SecurityQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can not use the same security question more than once..
        /// </summary>
        public static string SecurityQuestionSame {
            get {
                return ResourceManager.GetString("SecurityQuestionSame", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer to Security Question {0} is required..
        /// </summary>
        public static string SecurityQuestionsRequired {
            get {
                return ResourceManager.GetString("SecurityQuestionsRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One session is live in another window, Please logout from there or try after 30 min..
        /// </summary>
        public static string sessionAlive {
            get {
                return ResourceManager.GetString("sessionAlive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security Number (SSN).
        /// </summary>
        public static string SSN {
            get {
                return ResourceManager.GetString("SSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security Number is required if you applying for health coverage..
        /// </summary>
        public static string ssnReqIfApplying {
            get {
                return ResourceManager.GetString("ssnReqIfApplying", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Creation failed due to too many users.  Please try again later. If issue persists, please contact us at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative..
        /// </summary>
        public static string tooManyUsers {
            get {
                return ResourceManager.GetString("tooManyUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to perform this operation. Please contact helpdesk..
        /// </summary>
        public static string unableToPerformOperation {
            get {
                return ResourceManager.GetString("unableToPerformOperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must be over 14 years old to create an online account.  If you&apos;re not older than 14, you can [CLICKHERE] to download a paper application and apply by mail..
        /// </summary>
        public static string under14Message {
            get {
                return ResourceManager.GetString("under14Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must be over 14 years old to create an online account..
        /// </summary>
        public static string under14OnlyMessage {
            get {
                return ResourceManager.GetString("under14OnlyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Creation failed unexpectedly.  Please contact site administrator or try again later..
        /// </summary>
        public static string UnexpectedFailure {
            get {
                return ResourceManager.GetString("UnexpectedFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unique code provided does not match our records..
        /// </summary>
        public static string UniqueCodeDontMatch {
            get {
                return ResourceManager.GetString("UniqueCodeDontMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Just one more step. In order to continue, please enter the unique code below which has been sent to .
        /// </summary>
        public static string UniqueCodeEmailSent {
            get {
                return ResourceManager.GetString("UniqueCodeEmailSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unique Code is required in order to proceed..
        /// </summary>
        public static string UniqueCodeRequired {
            get {
                return ResourceManager.GetString("UniqueCodeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Information updated successfully..
        /// </summary>
        public static string UpdateSuccess {
            get {
                return ResourceManager.GetString("UpdateSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Account has expired.  Please contact helpdesk..
        /// </summary>
        public static string userAccountExpired {
            get {
                return ResourceManager.GetString("userAccountExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username already exists! Please try another username..
        /// </summary>
        public static string userNameAlreadyExist {
            get {
                return ResourceManager.GetString("userNameAlreadyExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not authorized to login.  Access Denied!.
        /// </summary>
        public static string UserNotAuthorized {
            get {
                return ResourceManager.GetString("UserNotAuthorized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to locate the Account.  Please contact helpdesk..
        /// </summary>
        public static string UserNotFound {
            get {
                return ResourceManager.GetString("UserNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verification Complete.
        /// </summary>
        public static string VerificationComplete {
            get {
                return ResourceManager.GetString("VerificationComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have been identified as an existing user..
        /// </summary>
        public static string VerificationInfo1 {
            get {
                return ResourceManager.GetString("VerificationInfo1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In the upcoming application, you will be able to view the latest information we have on file for your household..
        /// </summary>
        public static string VerificationInfo2 {
            get {
                return ResourceManager.GetString("VerificationInfo2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You may not be able to change some of the personal information for your household members online..
        /// </summary>
        public static string VerificationInfo3 {
            get {
                return ResourceManager.GetString("VerificationInfo3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YAHOO.
        /// </summary>
        public static string yahoo {
            get {
                return ResourceManager.GetString("yahoo", resourceCulture);
            }
        }
    }
}
