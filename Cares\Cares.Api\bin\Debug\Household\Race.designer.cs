﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Race {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Race() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.Race", typeof(Race).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ethnicity &amp; Race Information.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB:[DOB]) of Hispanic, Latino, or Spanish origin?.
        /// </summary>
        public static string isHispanic {
            get {
                return ResourceManager.GetString("isHispanic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The next questions are optional.  The information they provide will help the U.S. Department of Health and Human Services(HHS) better understand and improve the health of and health coverage for all Americans.  Providing this information won&apos;t impact your eligibility for health coverage, your health plan options, or your costs in any way..
        /// </summary>
        public static string optionalInformationStatement {
            get {
                return ResourceManager.GetString("optionalInformationStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Ethnicity Description is required..
        /// </summary>
        public static string otherEthnicityDescRequired {
            get {
                return ResourceManager.GetString("otherEthnicityDescRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Ethnicity Description.
        /// </summary>
        public static string otherEthnicityDescription {
            get {
                return ResourceManager.GetString("otherEthnicityDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Race Description is required..
        /// </summary>
        public static string otherRaceDescRequired {
            get {
                return ResourceManager.GetString("otherRaceDescRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Race Description.
        /// </summary>
        public static string otherRaceDescription {
            get {
                return ResourceManager.GetString("otherRaceDescription", resourceCulture);
            }
        }
    }
}
