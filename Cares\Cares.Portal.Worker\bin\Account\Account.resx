﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CreateAccountButton" xml:space="preserve">
    <value>CREATE ACCOUNT</value>
  </data>
  <data name="ConfirmEmail" xml:space="preserve">
    <value>Confirm Email Address</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>Date Of Birth</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="PageTitleCreateAccount" xml:space="preserve">
    <value>Create Account</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="PhoneSection" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="SecurityAnswer1" xml:space="preserve">
    <value>Security Answer 1</value>
  </data>
  <data name="SecurityQuestion1" xml:space="preserve">
    <value>Security Question 1</value>
  </data>
  <data name="SSN" xml:space="preserve">
    <value>Social Security Number (SSN)</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="EmailSection" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="failure" xml:space="preserve">
    <value>Invalid Username/Password.  Please try again.</value>
  </data>
  <data name="passwordExpired" xml:space="preserve">
    <value>Your password has expired.  Please contact helpdesk.</value>
  </data>
  <data name="passwordAboutToLock" xml:space="preserve">
    <value>Invalid Username/Password.  You have 1 more attempt before your account is locked.</value>
  </data>
  <data name="passwordIsLocked" xml:space="preserve">
    <value>Your account is locked.  Please contact helpdesk.</value>
  </data>
  <data name="userAccountExpired" xml:space="preserve">
    <value>Your Account has expired.  Please contact helpdesk.</value>
  </data>
  <data name="UserNotAuthorized" xml:space="preserve">
    <value>You are not authorized to login.  Access Denied!</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>Unable to locate the Account.  Please contact helpdesk.</value>
  </data>
  <data name="emailCompare" xml:space="preserve">
    <value>"Email" and "Confirm Email" fields must match.</value>
  </data>
  <data name="passwordCompare" xml:space="preserve">
    <value>"Password" and "Confirm Password" fields must match.</value>
  </data>
  <data name="haveEmailAddress" xml:space="preserve">
    <value>Don't have an email address? </value>
  </data>
  <data name="howToGetOne" xml:space="preserve">
    <value>Go to [FREE_EMAIL_URLS] and create one.</value>
  </data>
  <data name="clickOn" xml:space="preserve">
    <value>Please click here to return to Login screen</value>
  </data>
  <data name="createdSuccessfully" xml:space="preserve">
    <value>Congratulations!  Account created successfully.</value>
  </data>
  <data name="getEmailURLYahoo" xml:space="preserve">
    <value>https://login.yahoo.com/config/login_verify2?&amp;.src=ym</value>
    <comment>DO NOT TRANSLATE...yahoo email url</comment>
  </data>
  <data name="SecurityAnswer1Required" xml:space="preserve">
    <value>Security Answer 1 is required.</value>
  </data>
  <data name="SecurityAnswer2" xml:space="preserve">
    <value>Security Answer 2</value>
  </data>
  <data name="SecurityAnswer2Required" xml:space="preserve">
    <value>Security Answer 2 is required.</value>
  </data>
  <data name="SecurityAnswer3" xml:space="preserve">
    <value>Security Answer 3</value>
  </data>
  <data name="SecurityAnswer3Required" xml:space="preserve">
    <value>Security Answer 3 is required.</value>
  </data>
  <data name="SecurityQuestion1Required" xml:space="preserve">
    <value>Security Question 1 is required.</value>
  </data>
  <data name="SecurityQuestion2" xml:space="preserve">
    <value>Security Question 2</value>
  </data>
  <data name="SecurityQuestion2Required" xml:space="preserve">
    <value>Security Question 2 is required.</value>
  </data>
  <data name="SecurityQuestion3" xml:space="preserve">
    <value>Security Question 3</value>
  </data>
  <data name="SecurityQuestion3Required" xml:space="preserve">
    <value>Security Question 3 is required.</value>
  </data>
  <data name="SecurityQuestionSame" xml:space="preserve">
    <value>You can not use the same security question more than once.</value>
  </data>
  <data name="SecurityAsnwersSame" xml:space="preserve">
    <value>You can not use the same security answer more than once.</value>
  </data>
  <data name="SecurityDetails" xml:space="preserve">
    <value>Security Details</value>
  </data>
  <data name="UnexpectedFailure" xml:space="preserve">
    <value>Account Creation failed unexpectedly.  Please contact site administrator or try again later.</value>
  </data>
  <data name="userNameAlreadyExist" xml:space="preserve">
    <value>Username already exists! Please try another username.</value>
  </data>
  <data name="SecurityAnswer" xml:space="preserve">
    <value>Security Answer </value>
  </data>
  <data name="SecurityQuestions" xml:space="preserve">
    <value>Security Question </value>
  </data>
  <data name="PageTitleMyAccount" xml:space="preserve">
    <value>My Account</value>
  </data>
  <data name="UpdateSuccess" xml:space="preserve">
    <value>Account Information updated successfully.</value>
  </data>
  <data name="newPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="ChangePasswordSuccess" xml:space="preserve">
    <value>Your account password was changed successfully.</value>
  </data>
  <data name="oldPassword" xml:space="preserve">
    <value>Old Password</value>
  </data>
  <data name="newPasswordCompare" xml:space="preserve">
    <value>"New Password" and "Confirm New Password" must match.</value>
  </data>
  <data name="accountLocked" xml:space="preserve">
    <value>Your Account is locked. Please contact helpdesk.</value>
  </data>
  <data name="changePasswordFailure" xml:space="preserve">
    <value>Change Password request failed. Please try again later. if Issue persists, please contact helpdesk.</value>
  </data>
  <data name="oldPasswordDoesNotMatch" xml:space="preserve">
    <value>Old Password does not match. Please try again later.</value>
  </data>
  <data name="PasswordConfirmationDoesNotMatch" xml:space="preserve">
    <value>Confirmation Password Does not match.</value>
  </data>
  <data name="unableToPerformOperation" xml:space="preserve">
    <value>Unable to perform this operation. Please contact helpdesk.</value>
  </data>
  <data name="confirmPasswordPopOverContent" xml:space="preserve">
    <value>&lt;ul&gt;&lt;li&gt;Confirm Password must match Password.&lt;/li&gt;&lt;/ul&gt;</value>
  </data>
  <data name="passwordPopOverContent" xml:space="preserve">
    <value>&lt;ul&gt;&lt;li&gt;Password must be 8 to 14 characters long.&lt;/li&gt;&lt;li&gt;Password must at least contain one uppercase character, one lowercase character, one number and at least one special symbol !@#$&amp;%^*+-.=_&lt;/li&gt;&lt;/ul&gt;</value>
  </data>
  <data name="under14Message" xml:space="preserve">
    <value>You must be over 14 years old to create an online account.  If you're not older than 14, you can [CLICKHERE] to download a paper application and apply by mail.</value>
  </data>
  <data name="under14OnlyMessage" xml:space="preserve">
    <value>You must be over 14 years old to create an online account.</value>
  </data>
  <data name="orRedirectToLogin" xml:space="preserve">
    <value>OR you will be redirected to the Login screen in</value>
  </data>
  <data name="duplicatePhone" xml:space="preserve">
    <value>Duplicate phone numbers of same type are not allowed.</value>
  </data>
  <data name="getEmailURLGmail" xml:space="preserve">
    <value>https://accounts.google.com/SignUp?service=mail&amp;continue=http%3A%2F%2Fmail.google.com%2Fmail%2Fe-11-109a5a55d7610df0e7957ac039c47579-ed2a46d7696ce586b7fe29a45c9ba0be71a9c732&amp;hl=en-us</value>
    <comment>DO NOT TRANSLATE...gmail email url</comment>
  </data>
  <data name="gmail" xml:space="preserve">
    <value>GMAIL</value>
  </data>
  <data name="yahoo" xml:space="preserve">
    <value>YAHOO</value>
  </data>
  <data name="canStillApplyIfHomeless" xml:space="preserve">
    <value>You can still apply for health coverage if you do not have a home address or are homeless.</value>
  </data>
  <data name="accountLock" xml:space="preserve">
    <value>Your account has been locked temporarily due to incorrect username/password. Please call the ALL Kids toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative.</value>
  </data>
  <data name="getEmailURLAOL" xml:space="preserve">
    <value>https://my.screenname.aol.com/</value>
  </data>
  <data name="getEmailURLOutLook" xml:space="preserve">
    <value>https://login.live.com/login.srf?</value>
  </data>
  <data name="aol" xml:space="preserve">
    <value>AOL</value>
  </data>
  <data name="outlook" xml:space="preserve">
    <value>Outlook</value>
  </data>
  <data name="followLinktoHomePage" xml:space="preserve">
    <value>Please follow the link to log-in : -</value>
  </data>
  <data name="invalidEmail" xml:space="preserve">
    <value>Invalid Email. Please correct it.</value>
  </data>
  <data name="invalidUsername" xml:space="preserve">
    <value>Invalid characters found in username.Please enter another username.</value>
  </data>
  <data name="tooManyUsers" xml:space="preserve">
    <value>Account Creation failed due to too many users.  Please try again later. If issue persists, please contact us at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative.</value>
  </data>
  <data name="sessionAlive" xml:space="preserve">
    <value>One session is live in another window, Please logout from there or try after 30 min.</value>
  </data>
  <data name="confirmNewPassword" xml:space="preserve">
    <value>Confirm New Password</value>
  </data>
  <data name="applyingFamilyMembersOnly" xml:space="preserve">
    <value>applying for other family members but not myself.</value>
  </data>
  <data name="applyingSelfAndAllFamily" xml:space="preserve">
    <value>applying for myself and other family members.</value>
  </data>
  <data name="applyingSelfOnly" xml:space="preserve">
    <value>applying for myself only.</value>
  </data>
  <data name="ssnReqIfApplying" xml:space="preserve">
    <value>Social Security Number is required if you applying for health coverage.</value>
  </data>
  <data name="applyingFor" xml:space="preserve">
    <value>Applying For</value>
  </data>
  <data name="applyingForReq" xml:space="preserve">
    <value>Applying For is required.</value>
  </data>
  <data name="ConfirmSSN" xml:space="preserve">
    <value>Confirm Social Security Number (SSN)</value>
  </data>
  <data name="accountAlreadyExists" xml:space="preserve">
    <value>We were unable to create your account at this time. Please contact ALL Kids customer service by toll-free phone at 1-888-373-KIDS(5437) Monday-Friday 7:30AM to 5:00PM.</value>
  </data>
  <data name="AccountDetailsNotVerified" xml:space="preserve">
    <value>Information provided by you does not match with what we have currently on file for you. Please try again.</value>
  </data>
  <data name="AdditonalAuthReq" xml:space="preserve">
    <value>Additional Authentication Required</value>
  </data>
  <data name="EnterInfo" xml:space="preserve">
    <value>Please enter the requested information for {0}:</value>
  </data>
  <data name="PaperApplication" xml:space="preserve">
    <value>Download Paper Application</value>
  </data>
  <data name="SecurityQuestionsRequired" xml:space="preserve">
    <value>Answer to Security Question {0} is required.</value>
  </data>
  <data name="UniqueCodeDontMatch" xml:space="preserve">
    <value>Unique code provided does not match our records.</value>
  </data>
  <data name="UniqueCodeEmailSent" xml:space="preserve">
    <value>Just one more step. In order to continue, please enter the unique code below which has been sent to </value>
  </data>
  <data name="UniqueCodeRequired" xml:space="preserve">
    <value>Unique Code is required in order to proceed.</value>
  </data>
  <data name="contactPersonDeceased" xml:space="preserve">
    <value>We are unable to process your request at this time. Please contact Alabama Medicaid and CHIP toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative.&lt;br/&gt;
Error Code : &lt;b&gt;CDED500&lt;/b&gt;</value>
  </data>
  <data name="openApp" xml:space="preserve">
    <value>Our records indicate that you already have an application which is currently in process. We need to complete processing of that application before you can submit another application. Please contact Alabama Medicaid and CHIP toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative.</value>
  </data>
  <data name="AccountLockExceededAttempts" xml:space="preserve">
    <value>You have exceeded the number of incorrect attempts. We have locked your account to protect your information. Please contact Alabama Medicaid and CHIP toll-free number at 1-888-373-KIDS(5437) Monday-Friday to speak with a Customer Service representative.</value>
  </data>
  <data name="VerificationComplete" xml:space="preserve">
    <value>Verification Complete</value>
  </data>
  <data name="VerificationInfo1" xml:space="preserve">
    <value>You have been identified as an existing user.</value>
  </data>
  <data name="VerificationInfo2" xml:space="preserve">
    <value>In the upcoming application, you will be able to view the latest information we have on file for your household.</value>
  </data>
  <data name="VerificationInfo3" xml:space="preserve">
    <value>You may not be able to change some of the personal information for your household members online.</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Answer</value>
  </data>
  <data name="accountCreationInfo" xml:space="preserve">
    <value>You need to create a new account if personal information of the account holder is incorrect.</value>
  </data>
  <data name="AccountCreationFailure" xml:space="preserve">
    <value>We were unable to create your account at this time. Please contact ALL Kids customer service by toll-free phone at 1-888-373-KIDS(5437) Monday-Friday 7:30AM to 5:00PM.</value>
  </data>
  <data name="invalidPassword" xml:space="preserve">
    <value>User Name and Password can not be same</value>
  </data>
  <data name="oldPasswordCompare" xml:space="preserve">
    <value>"New Password" can not be same as "Old Password".</value>
  </data>
</root>