﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Account {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Login {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Login() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Account.Login", typeof(Login).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;u style=&quot;margin-left: 6px;&quot;&gt;C&lt;/u&gt;reate Account.
        /// </summary>
        public static string btnCreateAccount {
            get {
                return ResourceManager.GetString("btnCreateAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;u style=&quot;margin-left: 6px;&quot;&gt;S&lt;/u&gt;ign In.
        /// </summary>
        public static string btnSignIn {
            get {
                return ResourceManager.GetString("btnSignIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign Out.
        /// </summary>
        public static string btnSignOut {
            get {
                return ResourceManager.GetString("btnSignOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Don&apos;t have an account?.
        /// </summary>
        public static string lblHaveAccount {
            get {
                return ResourceManager.GetString("lblHaveAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign In.
        /// </summary>
        public static string lblSignIn {
            get {
                return ResourceManager.GetString("lblSignIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;u&gt;**EFFECTIVE JANUARY 2017&lt;/u&gt; Identity Proofing will be required for all CARES on-line users. If you have a current CARES account you will not be able to use your account until you have completed the Identity Proofing process. This process will help protect your information in the CARES system. Please do not call the Medicaid or ALL KIDS help desks..
        /// </summary>
        public static string note {
            get {
                return ResourceManager.GetString("note", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string phPassword {
            get {
                return ResourceManager.GetString("phPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User ID.
        /// </summary>
        public static string phUserId {
            get {
                return ResourceManager.GetString("phUserId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot your .
        /// </summary>
        public static string qsForgotUID_PWD {
            get {
                return ResourceManager.GetString("qsForgotUID_PWD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Account.
        /// </summary>
        public static string titleCreateAccount {
            get {
                return ResourceManager.GetString("titleCreateAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        public static string userName {
            get {
                return ResourceManager.GetString("userName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This system may contain restricted US government information, including data you enter. The only people who may look at this information are authorized users.  Any unauthorized use, misuse or modification of this computer system or the data in it may result in criminal prosecution.  This system is monitored to make sure that the system and all data in it are kept safe. By using this system, you agree to this monitoring.
        /// </summary>
        public static string warning1 {
            get {
                return ResourceManager.GetString("warning1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ANYONE USING THIS SYSTEM EXPRESSLY CONSENTS TO SUCH MONITORING..
        /// </summary>
        public static string warning2 {
            get {
                return ResourceManager.GetString("warning2", resourceCulture);
            }
        }
    }
}
