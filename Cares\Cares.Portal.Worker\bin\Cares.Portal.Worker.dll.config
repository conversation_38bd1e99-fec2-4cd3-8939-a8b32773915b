﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <section name="dotless" type="dotless.Core.configuration.DotlessConfigurationSectionHandler, dotless.Core" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <connectionStrings>
    <!--  The VG Connection String is no longer needed unless you are debugging against a local VG database. Will need to set UserVGServer setting to false in order to connect to a VG database. -->
    <!--<add name="VGConnectionString" connectionString="server=CARESDEVSQL1\cares;Initial Catalog=VisualGuard;Integrated Security=True;Encrypt=True;trustServerCertificate=true" providerName="System.Data.SqlClient" />-->
    <add name="CaresApplicationDBEntities" connectionString="metadata=res://*/CaresApplicationDB.csdl|res://*/CaresApplicationDB.ssdl|res://*/CaresApplicationDB.msl;provider=System.Data.SqlClient;provider connection string='data source=CARESDEVSQL1\cares;initial catalog=db4_ee;Integrated Security=True;Encrypt=True;trustServerCertificate=true;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />
    <add name="CaresStagingDBEntities" connectionString="metadata=res://*/CaresStagingDB.csdl|res://*/CaresStagingDB.ssdl|res://*/CaresStagingDB.msl;provider=System.Data.SqlClient;provider connection string='data source=CARESDEVSQL1\cares;initial catalog=staging;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />
    <add name="CaresLogDBEntities" connectionString="metadata=res://*/CaresLogDB.csdl|res://*/CaresLogDB.ssdl|res://*/CaresLogDB.msl;provider=System.Data.SqlClient;provider connection string='data source=CARESDEVSQL1\cares;initial catalog=Log;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />
    <add name="AdoCaresStagingDB" connectionString="data source=CARESDEVSQL1\cares;initial catalog=staging;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true" providerName="System.Data.SqlClient" />
    <add name="Log4NetConnectionString" connectionString="data source=CARESDEVSQL1\cares;initial catalog=Log;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true" />
  </connectionStrings>
  <appSettings>
    <add key="IsMergeTabConfigured" value="true" />
    <add key="Instrumented" value="true" />
    <add key="LoggingComponent" value="Worker Portal" />
    <add key="LogHttpRequests" value="true" />
    <add key="smtpHost" value="email.medicaid.alabama.gov" />
    <add key="smtpPort" value="25" />
    <add key="noReply" value="<EMAIL>" />
    <add key="AccountCreation" value="Alabamacares : Account Creation" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="PreserveLoginUrl" value="true" />
    <add key="VGAppId" value="61e85684-7171-4e9b-aa11-abdb7e2770f7" />
    <add key="VGAdminUserName" value="UserManager" />
    <add key="VGAdminPassword" value="C@res|36104" />
    <add key="VGTraceLogging" value="false" />
    <add key="VGTraceLogPath" value="" />
    <!--If blank, stores "VisualGuardLog.txt" file in web root folder.  Or use something like:  value="D:\VgLogs\" -->
    <add key="UseVGServer" value="true" />
    <add key="VGServerURL" value="https://caresdevapp.medicaid.al.gov:6100" />
    <add key="WebMethodUserName" value="esbserviceuser" />
    <add key="WebMethodPassword" value="esbuserdev@123" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="IsMSPEnabled" value="true" />
    <add key="UseCaptchaForLogin" value="false" />
    <add key="IsSVESEnabled" value="true" />
    <add key="IsSVESMockEnabled" value="true" />
    <add key="WebMethodUrl" value="https://caresdevis1.medicaid.al.gov:7000/rest/EEInHouseProcessor/restgateway/" />
    <add key="WebMethodLookup" value="https://caresdevis1.medicaid.al.gov:7000/rest/EEOnlineAppProcessor/EECreateApp/restgateway/" />
    <add key="WebMethodHubUrl" value="https://caresdevis1.medicaid.al.gov:7000/rest/EEHubProcessor/restGateway/" />
    <add key="LettersURL" value="https://caresdevis1.medicaid.al.gov:7000/rest/EELetterGenerator/restGateway/createLetter" />
    <add key="AccountingLettersFolderPath" value="\\caresstgats1.medicaid.al.gov\Dev-data\Letter\Completed\AccountingLetters" />
    <!--<add key="AppTierBaseUrl" value="https://caresdevapp1.medicaid.al.gov:8080/AppTierService/" />-->
    <add key="AppTierBaseUrl" value="http://localhost:65267/" />
    <add key="MaterialIcons" value="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <add key="ElderlyDisabledLettersFolderPath" value="\\caresstgats1.medicaid.al.gov\Dev-data\Letter\Completed\ElderlyDisabledLetters" />
    <add key="owin:AutomaticAppStartup" value="true" />
    <!--DataDome Settings-->
    <add key="dome:domain" value="api.datadome.co" />
    <add key="dome:protocol" value="http" />
    <add key="dome:license" value="xFfhdiSKnJ1dPR6" />
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <globalization requestEncoding="utf-8" responseEncoding="utf-8" />
    <httpRuntime targetFramework="4.6" />
    <compilation debug="true" targetFramework="4.8">
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
      </buildProviders>
      <assemblies>
        <add assembly="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
        <add assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
        <add assembly="netstandard, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51" />
      </assemblies>
    </compilation>
    <customErrors mode="RemoteOnly" />
    <sessionState timeout="30" cookieSameSite="Strict" />
    <authentication mode="Forms">
      <forms loginUrl="~/UserAccount/SignIn" timeout="2880" cookieSameSite="Strict" />
    </authentication>
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.WebPages" />
      </namespaces>
    </pages>
    <httpHandlers>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" validate="false" />
    </httpHandlers>
    <httpCookies requireSSL="true" httpOnlyCookies="true" />
  </system.web>
  <system.webServer>
    <httpProtocol>
      <customHeaders>
        <add name="Content-Security-Policy" value="frame-ancestors 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' js.datadome.co ct.captcha-delivery.com; connect-src 'self' api-js.datadome.co; frame-src 'self' geo.captcha-delivery.com; worker-src blob: " />
        <add name="X-Frame-Options" value="sameorigin" />
        <add name="X-Content-Type-Options" value="nosniff" />
      </customHeaders>
    </httpProtocol>
    <handlers>
      <add name="dotless" path="*.less" verb="GET" type="dotless.Core.LessCssHttpHandler,dotless.Core" resourceType="File" preCondition="" />
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
      <add name="ReportViewerWebControlHandler" verb="*" path="Reserved.ReportViewerWebControl.axd" preCondition="integratedMode" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
    </handlers>
    <modules runAllManagedModulesForAllRequests="true">
      <!--Insert DataDome as a managed module:-->
      <add name="DataDome" type="DataDome.Web.ConnectorHttpModule, DataDome.SystemWeb" />
    </modules>
    <validation validateIntegratedModeConfiguration="false" />
    <security>
      <requestFiltering>
        <requestLimits>
          <headerLimits>
            <add header="X-HTTP-METHOD-OVERRIDE" sizeLimit="0" />
            <add header="X-HTTP-METHOD" sizeLimit="0" />
            <add header="X-METHOD-OVERRIDE" sizeLimit="0" />
          </headerLimits>
        </requestLimits>
      </requestFiltering>
    </security>
  </system.webServer>
  <system.serviceModel>
    <bindings>
      <webHttpBinding>
        <binding maxBufferPoolSize="2147483647" maxReceivedMessageSize="2147483647" maxBufferSize="2147483647" transferMode="Streamed" />
      </webHttpBinding>
    </bindings>
  </system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Extensions" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.29.0" newVersion="2.2.29.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.29.0" newVersion="4.2.29.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="FluentValidation" publicKeyToken="7de548da2fbae0f0" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Novalys.VisualGuard.Security" publicKeyToken="8e423a8f05ffd0dc" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2024.4.2504.8" newVersion="2024.4.2504.8" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.1.0" newVersion="2.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Permissions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Principal.Windows" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.AccessControl" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.4" newVersion="6.0.0.4" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <interceptors>
      <interceptor type="Cares.Data.DataAbstractionLayer.TemporalTreeCommandInterceptor, Cares.Data" />
    </interceptors>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <log4net>
    <appender name="AdoNetAppender" type="log4net.Appender.AdoNetAppender">
      <bufferSize value="1" />
      <!-- Only use this for testing. Normal value should be 50 -->
      <connectionType value="System.Data.SqlClient.SqlConnection, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      <connectionStringName value="Log4NetConnectionString" />
      <!-- TokenID, Component, UserName, URL, Msg, Request, Response, BrowserInfo, IPAddress, ScreenAction are custom columns in the log table -->
      <commandText value="INSERT INTO Application_Log ([ApplicationLogDateTime],[TokenId],[Level],[Component],[Logger],[Message],[Exception],[UserName],[ApplicationId],[PersonId],[URL],[REQUEST],[RESPONSE],[BrowserInfo],[IPAddress],[ScreenAction]) VALUES(@Log_applicationlogdatetime,@TokenId,@Log_level,@Component,@Logger,IIF(LEN(@Msg) = 0, NULL, @Msg),IIF(LEN(@Exception) = 0, NULL, @Exception),IIF(LEN(@UserName) = 0, NULL, @UserName),IIF(@ApplicationId = 0, NULL, @ApplicationId),IIF(@PersonId = 0, NULL, @PersonId),IIF(LEN(@URL) = 0, NULL, @URL),@Request,@Response,IIF(LEN(@BrowserInfo) = 0, NULL, @BrowserInfo),IIF(LEN(@IPAddress) = 0, NULL, @IPAddress),IIF(LEN(@ScreenAction)=0,NULL,@ScreenAction));" />
      <parameter>
        <parameterName value="@log_applicationlogdatetime" />
        <dbType value="DateTime" />
        <size value="25" />
        <layout type="log4net.Layout.RawTimeStampLayout" />
      </parameter>
      <parameter>
        <parameterName value="@tokenid" />
        <dbType value="String" />
        <size value="50" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{tokenid}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_level" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%level" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@component" />
        <dbType value="String" />
        <size value="20" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{component}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logger" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%logger" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@message" />
        <dbType value="String" />
        <size value="4000" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@exception" />
        <dbType value="String" />
        <size value="2000" />
        <layout type="log4net.Layout.ExceptionLayout" />
      </parameter>
      <!-- Custom columns in the log table -->
      <parameter>
        <parameterName value="@username" />
        <dbType value="String" />
        <size value="255" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{username}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@applicationId" />
        <dbType value="Int64" />
        <size value="64" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{applicationId}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@personId" />
        <dbType value="Int64" />
        <size value="64" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{personId}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@url" />
        <dbType value="String" />
        <size value="255" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{url}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@msg" />
        <dbType value="String" />
        <size value="4000" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{msg}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@request" />
        <dbType value="Xml" />
        <size value="30000" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{request}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@response" />
        <dbType value="Xml" />
        <size value="30000" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{response}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@browserinfo" />
        <dbType value="String" />
        <size value="255" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{browserinfo}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@ipaddress" />
        <dbType value="String" />
        <size value="15" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{ipaddress}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@screenaction" />
        <dbType value="String" />
        <size value="400" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{screenaction}" />
        </layout>
      </parameter>
    </appender>
    <root>
      <level value="ALL" />
      <appender-ref ref="AdoNetAppender" />
    </root>
  </log4net>
  <dotless minifyCss="false" cache="true" web="false" strictMath="false" />
</configuration>