﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.Income {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Income {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Income() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.Income.Income", typeof(Income).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        public static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant Gross Amount.
        /// </summary>
        public static string ApplicantGrossAmount {
            get {
                return ResourceManager.GetString("ApplicantGrossAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant Total Income.
        /// </summary>
        public static string ApplicantTotalIncome {
            get {
                return ResourceManager.GetString("ApplicantTotalIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In the past year.
        /// </summary>
        public static string ChangeInCircumstance {
            get {
                return ResourceManager.GetString("ChangeInCircumstance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changed Job.
        /// </summary>
        public static string ChangeJob {
            get {
                return ResourceManager.GetString("ChangeJob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child Gross Amount.
        /// </summary>
        public static string ChildGrossAmount {
            get {
                return ResourceManager.GetString("ChildGrossAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Claim Number.
        /// </summary>
        public static string ClaimNumber {
            get {
                return ResourceManager.GetString("ClaimNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provider Company Name.
        /// </summary>
        public static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DEDUCTIONS:.
        /// </summary>
        public static string Deduction {
            get {
                return ResourceManager.GetString("Deduction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note: System will automatically deem the earned and unearned income for claimant, spouse, and child. Please enter the gross verified amount in &quot;Verified&quot; Field..
        /// </summary>
        public static string DeemNote {
            get {
                return ResourceManager.GetString("DeemNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frequency.
        /// </summary>
        public static string Frequency {
            get {
                return ResourceManager.GetString("Frequency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does this Person have any Income?.
        /// </summary>
        public static string HasIncome {
            get {
                return ResourceManager.GetString("HasIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How Often?.
        /// </summary>
        public static string HowOften {
            get {
                return ResourceManager.GetString("HowOften", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If there is no verified income, Please enter &quot;0&quot;..
        /// </summary>
        public static string IncomeRequired {
            get {
                return ResourceManager.GetString("IncomeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is any of this income from a scholarship or grant?.
        /// </summary>
        public static string IncomeScholarship {
            get {
                return ResourceManager.GetString("IncomeScholarship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Source.
        /// </summary>
        public static string IncomeSource {
            get {
                return ResourceManager.GetString("IncomeSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Summary.
        /// </summary>
        public static string IncomeSummary {
            get {
                return ResourceManager.GetString("IncomeSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Type.
        /// </summary>
        public static string IncomeType {
            get {
                return ResourceManager.GetString("IncomeType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minor Child Total Income.
        /// </summary>
        public static string MinorChildTotalIncome {
            get {
                return ResourceManager.GetString("MinorChildTotalIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next year total Income.
        /// </summary>
        public static string NxtYrIncome {
            get {
                return ResourceManager.GetString("NxtYrIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Income.
        /// </summary>
        public static string OtherIncome {
            get {
                return ResourceManager.GetString("OtherIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit/Loss.
        /// </summary>
        public static string ProfitLoss {
            get {
                return ResourceManager.GetString("ProfitLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spouse Gross Amount.
        /// </summary>
        public static string SpouseGrossAmount {
            get {
                return ResourceManager.GetString("SpouseGrossAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spouse Total Income.
        /// </summary>
        public static string SpouseTotalIncome {
            get {
                return ResourceManager.GetString("SpouseTotalIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Started Working Fewer Hours.
        /// </summary>
        public static string StartFewerHours {
            get {
                return ResourceManager.GetString("StartFewerHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stopped Working.
        /// </summary>
        public static string StopWorking {
            get {
                return ResourceManager.GetString("StopWorking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type Of Work.
        /// </summary>
        public static string TypeofWork {
            get {
                return ResourceManager.GetString("TypeofWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unemploymemt benefits are set to expire?.
        /// </summary>
        public static string UnemploymemtBenefitExpire {
            get {
                return ResourceManager.GetString("UnemploymemtBenefitExpire", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note: Use the Veteran worksheet and enter Countable amount from the worksheet in the &quot;Verified&quot; field..
        /// </summary>
        public static string VeteranDeemNote {
            get {
                return ResourceManager.GetString("VeteranDeemNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This year total Income.
        /// </summary>
        public static string YearIncome {
            get {
                return ResourceManager.GetString("YearIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YEARLY INCOME:.
        /// </summary>
        public static string YearlyIncome {
            get {
                return ResourceManager.GetString("YearlyIncome", resourceCulture);
            }
        }
    }
}
