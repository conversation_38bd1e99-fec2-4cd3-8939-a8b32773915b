﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.Application {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ContactInfo {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ContactInfo() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.Application.ContactInfo", typeof(ContactInfo).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Social Security Number.
        /// </summary>
        public static string ConfirmSSN {
            get {
                return ResourceManager.GetString("ConfirmSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Information.
        /// </summary>
        public static string contact {
            get {
                return ResourceManager.GetString("contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME] and other family members.
        /// </summary>
        public static string contactAndAllFamily {
            get {
                return ResourceManager.GetString("contactAndAllFamily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You&apos;ll be contacted when a notice is ready for you on this website. How can we contact you?.
        /// </summary>
        public static string contactBy {
            get {
                return ResourceManager.GetString("contactBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you are seeking coverage for yourself, or others in your household, please enter your contact information..
        /// </summary>
        public static string contactHeader {
            get {
                return ResourceManager.GetString("contactHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME] only.
        /// </summary>
        public static string contactOnly {
            get {
                return ResourceManager.GetString("contactOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Person.
        /// </summary>
        public static string contactPerson {
            get {
                return ResourceManager.GetString("contactPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        public static string contactPhone {
            get {
                return ResourceManager.GetString("contactPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Address is required if Email option is selected..
        /// </summary>
        public static string ContactPreferenceEmailRequired {
            get {
                return ResourceManager.GetString("ContactPreferenceEmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone Number is required if Text Message is selected..
        /// </summary>
        public static string ContactPreferencePhoneRequired {
            get {
                return ResourceManager.GetString("ContactPreferencePhoneRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text Message or Email is required to receive an online notice..
        /// </summary>
        public static string ContactPreferenceRequired {
            get {
                return ResourceManager.GetString("ContactPreferenceRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preferences.
        /// </summary>
        public static string contactPreferences {
            get {
                return ResourceManager.GetString("contactPreferences", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We need to know the best way to contact you about this application and your health coverage if you&apos;re eligible. Do you want to read your notices about your application on your electronic &quot;Dashboard&quot; on this website?.
        /// </summary>
        public static string electronicNotice {
            get {
                return ResourceManager.GetString("electronicNotice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a Contact Preference..
        /// </summary>
        public static string ElectronicNoticeRadioRequired {
            get {
                return ResourceManager.GetString("ElectronicNoticeRadioRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string email {
            get {
                return ResourceManager.GetString("email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application does not have a completed date. Please contact administrator..
        /// </summary>
        public static string ErrorApplicationCompletedDateMissing {
            get {
                return ResourceManager.GetString("ErrorApplicationCompletedDateMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other family members but not [NAME].
        /// </summary>
        public static string familyMembersButNoContact {
            get {
                return ResourceManager.GetString("familyMembersButNoContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I would like to receive information about this application by:.
        /// </summary>
        public static string howToContactMe {
            get {
                return ResourceManager.GetString("howToContactMe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard Mail.
        /// </summary>
        public static string mail {
            get {
                return ResourceManager.GetString("mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Messaging rates may apply..
        /// </summary>
        public static string msgRateApply {
            get {
                return ResourceManager.GetString("msgRateApply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No. I want to get paper notices sent to me in the mail..
        /// </summary>
        public static string noOnline {
            get {
                return ResourceManager.GetString("noOnline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Online Notices.
        /// </summary>
        public static string onlineNotices {
            get {
                return ResourceManager.GetString("onlineNotices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We&apos;ll contact you by [TYPE] to let you know there&apos;s a message for you to read on your &quot;Dashboard&quot;. &lt;br/&gt;Do you also want to get paper copies in the mail?.
        /// </summary>
        public static string paperCopyMail {
            get {
                return ResourceManager.GetString("paperCopyMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone Type is required for Primary Phone..
        /// </summary>
        public static string phoneTypeRequired {
            get {
                return ResourceManager.GetString("phoneTypeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relationship is required..
        /// </summary>
        public static string RelationshipRequired {
            get {
                return ResourceManager.GetString("RelationshipRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only administrators can reopen applications older than 1 year old.  Please contact a system administrator..
        /// </summary>
        public static string ReopenAdministrator {
            get {
                return ResourceManager.GetString("ReopenAdministrator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only supervisors can reopen an application.  Please contact your supervisor for assistance..
        /// </summary>
        public static string ReopenSupervisor {
            get {
                return ResourceManager.GetString("ReopenSupervisor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is your mailing address the same as your home address?.
        /// </summary>
        public static string sameAsHomeAddress {
            get {
                return ResourceManager.GetString("sameAsHomeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preferred Spoken Language.
        /// </summary>
        public static string spokenLanguage {
            get {
                return ResourceManager.GetString("spokenLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select one preferred spoken language..
        /// </summary>
        public static string spokenLanguageRequired {
            get {
                return ResourceManager.GetString("spokenLanguageRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security Number.
        /// </summary>
        public static string ssn {
            get {
                return ResourceManager.GetString("ssn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text Message.
        /// </summary>
        public static string text {
            get {
                return ResourceManager.GetString("text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who are you applying for health coverage for?.
        /// </summary>
        public static string whoApplying {
            get {
                return ResourceManager.GetString("whoApplying", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who needs health coverage?.
        /// </summary>
        public static string whoNeedsCoverageHeader {
            get {
                return ResourceManager.GetString("whoNeedsCoverageHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The worker number you are trying to assign is not associated with any user..
        /// </summary>
        public static string workerNumber {
            get {
                return ResourceManager.GetString("workerNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preferred Written Language.
        /// </summary>
        public static string writtenLanguage {
            get {
                return ResourceManager.GetString("writtenLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select one preferred written language..
        /// </summary>
        public static string writtenLanguageRequired {
            get {
                return ResourceManager.GetString("writtenLanguageRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes. I want to read my notices online..
        /// </summary>
        public static string yesOnline {
            get {
                return ResourceManager.GetString("yesOnline", resourceCulture);
            }
        }
    }
}
