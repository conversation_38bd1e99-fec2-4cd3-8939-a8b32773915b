﻿<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:element name="Person">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:restriction base="xsd:anyType">
          <xsd:sequence>
            <xsd:element name="PersonInfo">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="FirstName" type="xsd:string" />
                      <xsd:element name="LastName" type="xsd:string" />
                      <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="MaidenName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="SSN" type="xsd:string" />
                      <xsd:element name="DOB" type="xsd:string" nillable="true" />
                      <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="Gender" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsUSCitizen" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsSSNVerified" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsCitizenVerified" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="PersonDetail" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="MedicaidId" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="CheckDigit" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="BCBSId" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="DateofDeath" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="DeathVerficationDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="DeathVerficationSource" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsUnborn" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsSterile" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="EverInFosterCare" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="AgeLeftFosterCare" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FosterCareState" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="BiologicalMotherInfo" minOccurs="0" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="SSN" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="DOB" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                      <xsd:element name="LegacyMedicaidId" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="DoNotUpdateInd" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="AddressList" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="Address" maxOccurs="unbounded" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="AddressType" type="xsd:string" nillable="true" />
                                <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="County" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="PhoneList" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="Phone" maxOccurs="unbounded" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="PhoneType" type="xsd:string" nillable="true" />
                                <xsd:element name="PhoneNumber" type="xsd:string" nillable="true" />
                                <xsd:element name="PhoneExtension" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="EthnicityList" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="EthnicityInfo" maxOccurs="unbounded" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="Ethnicity" type="xsd:string" maxOccurs="unbounded" nillable="true" />
                                <xsd:element name="OtherEthnicity" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="RaceList" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="RaceInfo" maxOccurs="unbounded" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="Race" type="xsd:string" maxOccurs="unbounded" nillable="true" />
                                <xsd:element name="OtherRace" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="EmailList" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="Email" type="xsd:string" maxOccurs="unbounded" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="SSC" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="FirstName" type="xsd:string" />
                      <xsd:element name="LastName" type="xsd:string" />
                      <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="SponsorInfoList" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="SponsorInfo" maxOccurs="unbounded" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="SponsorRole" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Relationship" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="SponsorType" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="LegalAuthority" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Email" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Fax" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="County" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="SponsorPhoneList" minOccurs="0" nillable="true">
                                  <xsd:complexType>
                                    <xsd:complexContent>
                                      <xsd:restriction base="xsd:anyType">
                                        <xsd:sequence>
                                          <xsd:element name="SponsorPhone" maxOccurs="unbounded" nillable="true">
                                            <xsd:complexType>
                                              <xsd:complexContent>
                                                <xsd:restriction base="xsd:anyType">
                                                  <xsd:sequence>
                                                    <xsd:element name="PhoneType" type="xsd:string" nillable="true" />
                                                    <xsd:element name="PhoneNumber" type="xsd:string" nillable="true" />
                                                    <xsd:element name="PhoneExtension" type="xsd:string" minOccurs="0" nillable="true" />
                                                  </xsd:sequence>
                                                </xsd:restriction>
                                              </xsd:complexContent>
                                            </xsd:complexType>
                                          </xsd:element>
                                        </xsd:sequence>
                                      </xsd:restriction>
                                    </xsd:complexContent>
                                  </xsd:complexType>
                                </xsd:element>
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="AuthorizedInfo" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="County" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PhoneType" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PhoneExtension" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsPartOfOrganization" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="OrganizationName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="OrganizationId" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="AssistorInfo" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="OrganizationName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="OrganizationId" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsPEDeterminer" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="DeterminerId" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="EligEnroll" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="ProgramName" type="xsd:string" />
                      <xsd:element name="ProgramSubcategoryName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="StartDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="CancelDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FeeCode" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="CurrentAmount" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PremiumApplied" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="CancelReason" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="MedicaidIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ChipIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="Override" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="OverrideDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="OverrideReason" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="OverrideBy" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FPL" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="RenewalType" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="InsuranceSendInd" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="InsuranceSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="InsuranceCancelSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="InsuranceUpdateSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="BCBSOverride" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="LegacyPersonId" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="EligEnrollHistoryList" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="EligEnrollHistory" maxOccurs="unbounded" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="ProgramName" type="xsd:string" />
                                <xsd:element name="ProgramSubcategoryName" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="StartDate" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="CancelDate" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="FeeCode" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="CurrentAmount" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="PremiumApplied" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="CancelReason" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="MedicaidIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="ChipIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Override" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="OverrideDate" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="OverrideReason" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="OverrideBy" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="IneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="FPL" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="RenewalType" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="InsuranceSendInd" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="InsuranceSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="InsuranceCancelSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="InsuranceUpdateSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="BCBSOverride" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="LegacyPersonId" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="SSIInfo" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="SSIApplicationDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ALResidencyDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="Title2ClaimNumber" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="SSIEligibilityDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="CountryofResidence" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="TDALastTransDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="MonthlySSIAmount" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="MasterFileType" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="LivingArrangement" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="EO2Month" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ReasonFor8036" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ReinstateNHSSIDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PayeeName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PayeeUSAddress" minOccurs="0" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="County" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                      <xsd:element name="PayeeNonUSAddress" minOccurs="0" nillable="true">
                        <xsd:complexType>
                          <xsd:complexContent>
                            <xsd:restriction base="xsd:anyType">
                              <xsd:sequence>
                                <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="CityTown" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="StateProvinceRegion" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="PostArea" type="xsd:string" minOccurs="0" nillable="true" />
                                <xsd:element name="Country" type="xsd:string" minOccurs="0" nillable="true" />
                              </xsd:sequence>
                            </xsd:restriction>
                          </xsd:complexContent>
                        </xsd:complexType>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="HouseholdInfo" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="Age" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="AttestedSSN" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsPregnant" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ExpectedNumberOfBabies" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PregnancyDuedate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="IsMiscarriage" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="MiscarriageDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PostpartumStartDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="PostpartumEndDate" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="WorkerInfo" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:sequence>
                      <xsd:element name="WorkerCountyNumber" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="WorkerNumber" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:sequence>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
            <xsd:element name="JiyFacilityInfo" minOccurs="0" nillable="true">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:restriction base="xsd:anyType">
                    <xsd:all>
                      <xsd:element name="FacilityAddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityAptSuiteLot" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityCity" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityStateAbrv" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityZipCode" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityCounty" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ConfinementDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ReleaseDate30DaysPrior" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ReleaseDate" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="ReleaseDate30DaysPost" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="CommittingCounty" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="InstitutionType" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="InstitutionTypeOther" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityContactName" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityPhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityOtherPhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                      <xsd:element name="FacilityEmail" type="xsd:string" minOccurs="0" nillable="true" />
                    </xsd:all>
                  </xsd:restriction>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
          </xsd:sequence>
        </xsd:restriction>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PersonSchemaDoc">
    <xsd:complexContent>
      <xsd:restriction base="xsd:anyType">
        <xsd:sequence>
          <xsd:element name="Person" nillable="true">
            <xsd:complexType>
              <xsd:complexContent>
                <xsd:restriction base="xsd:anyType">
                  <xsd:sequence>
                    <xsd:element name="PersonInfo">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="FirstName" type="xsd:string" />
                              <xsd:element name="LastName" type="xsd:string" />
                              <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="MaidenName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="SSN" type="xsd:string" />
                              <xsd:element name="DOB" type="xsd:string" nillable="true" />
                              <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="Gender" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsUSCitizen" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsSSNVerified" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsCitizenVerified" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="PersonDetail" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="MedicaidId" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="CheckDigit" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="BCBSId" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="DateofDeath" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="DeathVerficationDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="DeathVerficationSource" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsUnborn" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsSterile" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="EverInFosterCare" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="AgeLeftFosterCare" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FosterCareState" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="BiologicalMotherInfo" minOccurs="0" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="SSN" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="DOB" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                              <xsd:element name="LegacyMedicaidId" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="DoNotUpdateInd" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="AddressList" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="Address" maxOccurs="unbounded" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="AddressType" type="xsd:string" nillable="true" />
                                        <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="County" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="PhoneList" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="Phone" maxOccurs="unbounded" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="PhoneType" type="xsd:string" nillable="true" />
                                        <xsd:element name="PhoneNumber" type="xsd:string" nillable="true" />
                                        <xsd:element name="PhoneExtension" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="EthnicityList" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="EthnicityInfo" maxOccurs="unbounded" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="Ethnicity" type="xsd:string" maxOccurs="unbounded" nillable="true" />
                                        <xsd:element name="OtherEthnicity" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="RaceList" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="RaceInfo" maxOccurs="unbounded" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="Race" type="xsd:string" maxOccurs="unbounded" nillable="true" />
                                        <xsd:element name="OtherRace" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="EmailList" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="Email" type="xsd:string" maxOccurs="unbounded" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="SSC" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="FirstName" type="xsd:string" />
                              <xsd:element name="LastName" type="xsd:string" />
                              <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="SponsorInfoList" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="SponsorInfo" maxOccurs="unbounded" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="SponsorRole" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Relationship" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="SponsorType" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="LegalAuthority" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Email" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Fax" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="SponsorPhoneList" minOccurs="0" nillable="true">
                                          <xsd:complexType>
                                            <xsd:complexContent>
                                              <xsd:restriction base="xsd:anyType">
                                                <xsd:sequence>
                                                  <xsd:element name="SponsorPhone" maxOccurs="unbounded" nillable="true">
                                                    <xsd:complexType>
                                                      <xsd:complexContent>
                                                        <xsd:restriction base="xsd:anyType">
                                                          <xsd:sequence>
                                                            <xsd:element name="PhoneType" type="xsd:string" nillable="true" />
                                                            <xsd:element name="PhoneNumber" type="xsd:string" nillable="true" />
                                                            <xsd:element name="PhoneExtension" type="xsd:string" minOccurs="0" nillable="true" />
                                                          </xsd:sequence>
                                                        </xsd:restriction>
                                                      </xsd:complexContent>
                                                    </xsd:complexType>
                                                  </xsd:element>
                                                </xsd:sequence>
                                              </xsd:restriction>
                                            </xsd:complexContent>
                                          </xsd:complexType>
                                        </xsd:element>
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="AuthorizedInfo" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PhoneType" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PhoneExtension" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsPartOfOrganization" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="AssistorInfo" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="FirstName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="LastName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="Suffix" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="OrganizationName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="OrganizationId" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsPEDeterminer" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="DeterminerId" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="EligEnroll" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="ProgramName" type="xsd:string" />
                              <xsd:element name="ProgramSubcategoryName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="StartDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="CancelDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FeeCode" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="CurrentAmount" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PremiumApplied" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="CancelReason" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="MedicaidIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ChipIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="Override" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="OverrideDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="OverrideReason" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="OverrideBy" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FPL" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="RenewalType" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="InsuranceSendInd" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="InsuranceSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="InsuranceCancelSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="InsuranceUpdateSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="BCBSOverride" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="LegacyPersonId" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="EligEnrollHistoryList" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="EligEnrollHistory" maxOccurs="unbounded" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="ProgramName" type="xsd:string" />
                                        <xsd:element name="ProgramSubcategoryName" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="StartDate" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="CancelDate" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="FeeCode" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="CurrentAmount" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="PremiumApplied" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="CancelReason" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="MedicaidIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="ChipIneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Override" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="OverrideDate" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="OverrideReason" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="OverrideBy" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="IneligibilityReason" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="FPL" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="RenewalType" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="InsuranceSendInd" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="InsuranceSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="InsuranceCancelSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="InsuranceUpdateSendDate" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="BCBSOverride" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="LegacyPersonId" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="SSIInfo" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="SSIApplicationDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ALResidencyDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="Title2ClaimNumber" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="SSIEligibilityDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="CountryofResidence" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="TDALastTransDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="MonthlySSIAmount" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="MasterFileType" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="LivingArrangement" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="EO2Month" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ReasonFor8036" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ReinstateNHSSIDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PayeeName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PayeeUSAddress" minOccurs="0" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="City" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="State" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="County" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Zipcode" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                              <xsd:element name="PayeeNonUSAddress" minOccurs="0" nillable="true">
                                <xsd:complexType>
                                  <xsd:complexContent>
                                    <xsd:restriction base="xsd:anyType">
                                      <xsd:sequence>
                                        <xsd:element name="AddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="AddressLine2" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="CityTown" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="StateProvinceRegion" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="PostArea" type="xsd:string" minOccurs="0" nillable="true" />
                                        <xsd:element name="Country" type="xsd:string" minOccurs="0" nillable="true" />
                                      </xsd:sequence>
                                    </xsd:restriction>
                                  </xsd:complexContent>
                                </xsd:complexType>
                              </xsd:element>
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="HouseholdInfo" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="Age" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="AttestedSSN" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsPregnant" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ExpectedNumberOfBabies" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PregnancyDuedate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="IsMiscarriage" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="MiscarriageDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PostpartumStartDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="PostpartumEndDate" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="WorkerInfo" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:sequence>
                              <xsd:element name="WorkerCountyNumber" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="WorkerNumber" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:sequence>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="JiyFacilityInfo" minOccurs="0" nillable="true">
                      <xsd:complexType>
                        <xsd:complexContent>
                          <xsd:restriction base="xsd:anyType">
                            <xsd:all>
                              <xsd:element name="FacilityAddressLine1" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityAptSuiteLot" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityCity" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityStateAbrv" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityZipCode" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityCounty" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ConfinementDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ReleaseDate30DaysPrior" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ReleaseDate" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="ReleaseDate30DaysPost" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="CommittingCounty" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="InstitutionType" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="InstitutionTypeOther" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityContactName" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityPhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityOtherPhoneNumber" type="xsd:string" minOccurs="0" nillable="true" />
                              <xsd:element name="FacilityEmail" type="xsd:string" minOccurs="0" nillable="true" />
                            </xsd:all>
                          </xsd:restriction>
                        </xsd:complexContent>
                      </xsd:complexType>
                    </xsd:element>                    
                  </xsd:sequence>
                </xsd:restriction>
              </xsd:complexContent>
            </xsd:complexType>
          </xsd:element>
        </xsd:sequence>
      </xsd:restriction>
    </xsd:complexContent>
  </xsd:complexType>
</xsd:schema>
