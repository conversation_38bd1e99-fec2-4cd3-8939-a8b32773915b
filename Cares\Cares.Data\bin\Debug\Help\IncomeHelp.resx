﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="alimony" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Alimony&lt;/b&gt;
Alimony received is money you get from a spouse you no longer live with, or a former spouse, if paid to you as part of a divorce agreement, separation agreement, or court order. Payments designated in the agreement or ordered as child support or as a non-taxable property settlement aren’t alimony. For more information, see
IRS Publication 504 (page 12).&lt;br&gt;&lt;br&gt;
If you select “alimony received,” you’ll be asked to enter the amount. You’ll also be asked how often you get this amount.&lt;/p&gt;</value>
  </data>
  <data name="alimonydeduction" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Alimony&lt;/b&gt;&lt;br&gt;
        &lt;ul&gt;&lt;li&gt;If your divorce or separation was finalized:&lt;/li&gt;
            &lt;ul&gt;&lt;li&gt;&lt;b&gt;Before&lt;/b&gt; January 1, 2019: &lt;b&gt;Include&lt;/b&gt; alimony as income. The payer may deduct this as an expense.&lt;/li&gt;
                &lt;li&gt;&lt;b&gt;On or after&lt;/b&gt; January 1, 2019: &lt;b&gt;Don't include&lt;/b&gt; alimony as income. The payer can't deduct this as an expense.&lt;/li&gt;
            &lt;/ul&gt;
            &lt;li&gt;Report any payments you're currently getting from a spouse or former spouse if the payments are part of a divorce agreement, separation agreement, or court order.&lt;/li&gt;
            &lt;li&gt;&lt;b&gt;Don't report&lt;/b&gt; payments designated or ordered as &lt;b&gt;child support&lt;/b&gt; or as a &lt;b&gt;non-taxable property settlement.&lt;/b&gt;&lt;/li&gt;
            &lt;li&gt;When we show you a yearly estimate of expected total income, you can adjust it for expected differences during the year.&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/br&gt;
    &lt;/p&gt;</value>
  </data>
  <data name="captialGains" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Capital gains&lt;/b&gt;
Capital gains are the amount you profit from selling property. For example, if you buy stock for $1,000 and sell it for $1,250, you have a capital gain of $250. You don’t need to include a capital gain if it’s from the sale of the main home you owned for at least 5 years (and the profit is less than $250,000). For more information, see IRS Publication 17 (chapter 14, page 104) or IRS Publication 544.&lt;br&gt;&lt;br&gt;
If you select “capital gains,” you’ll be asked how much you expect to get from net capital gains this month and this year. Enter your capital gains income after subtracting capital losses. You’ll select “profit” or “loss” when you enter these amounts.&lt;/p&gt;</value>
  </data>
  <data name="farmingFishing" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Farming or fishing income&lt;/b&gt;
If you have income from farming or fishing, you can enter it as “farming or fishing” income or “self-employment” income, but you can only enter it once. A person is in the business of farming if he or she cultivates, operates, or manages a farm for profit, either as owner or tenant. A farm can include livestock, dairy, poultry, fish, or fruit. It can also include plantations, ranches, ranges, and orchards. For more information on farming income, see IRS Publication 225 (2011), page 1 (farming), or page 15 (exclusions from income).&lt;br&gt;&lt;br&gt;
Fishing income includes amounts you get from catching, taking, harvesting, cultivating, or farming fish, shellfish, crustacean, sponges, seaweeds, or other aquatic forms of animal or vegetable life, as well as money from patronage dividends and fuel tax credits and refunds. For more information on fishing income, visit the IRS Fishing Tax Center.&lt;/p&gt;</value>
  </data>
  <data name="investment" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Investment income&lt;/b&gt;&lt;br&gt;
Investment income is the income you get from an investment. Examples of investment income include interest you get from a bank account or dividends from a person’s stock. For more information, see IRS Publication 550.&lt;br&gt;&lt;br&gt;
If you select “investment income,” you’ll be asked to enter the amount you get from investment income, like interest and dividends. You’ll also be asked how often you get this amount.&lt;/p&gt;</value>
  </data>
  <data name="job" xml:space="preserve">
    <value>&lt;b&gt;Income from your job&lt;/b&gt;
You’ll be asked about how you’re paid:&lt;br&gt;
• Income amount: If you’re asked about your income amount, enter the amount that’s shown on your pay stub before taxes are taken out.&lt;br&gt;
• How often? After you enter how much you earn, select how often. Choose one option, like “hourly,” “daily,” or “weekly.” If you’re paid through a one-time contract, you can select “one time only.”&lt;br&gt;
• Hours per week? Enter the number of hours or days you work each week.</value>
  </data>
  <data name="otherIncome" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Other income&lt;/b&gt;
You might have other types of income that aren’t listed above. If you do, select “other income.” If you’re a member of the clergy or a religious order, exclude the same income excluded on your federal income tax return.&lt;br&gt;&lt;br&gt;
If you select “other income,” you’ll be asked if you have income from any of the following sources. If you do, enter the amount.&lt;/p&gt;</value>
  </data>
  <data name="pension" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Pension&lt;/b&gt;&lt;br&gt;
A pension is generally a payment or series of payments made to a person after he or she retires from work. Generally, the amount of the income from a pension account distribution depends on the type of pension account, how much was contributed to the pension account, and whether the amounts contributed were already taxed. You don’t have to include a qualified distribution from a designated Roth account as income. For more information, see IRS Publication 575.&lt;br&gt;&lt;br&gt;
If you select “pension,” you’ll be asked how much you get from pension account distributions. You’ll also be asked how often you get this amount. Enter what you receive as a distribution from your pension, even if you aren’t retired.&lt;/p&gt;</value>
  </data>
  <data name="rental" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Rental or royalty income&lt;/b&gt;
Rental income is the amount someone pays you to use your property after you subtract your property expenses. Royalty income includes any payments you get from a patent, copyright, or some other natural resource you own. For more information, see IRS Publication 17 (chapter 9, pages 67-74).&lt;br&gt;&lt;br&gt;
If you select “rental or royalty income,” you’ll be asked how much you get from these types of income. You’ll also be asked how often you get this amount. Enter your net rental or royalty income (your profit after subtracting costs). You’ll select “profit” or “loss” when you enter this amount.&lt;/p&gt;</value>
  </data>
  <data name="retiement" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Retirement&lt;/b&gt;&lt;br&gt;
A retirement benefit is generally a payment or series of payments made to a person after he or she retires from work. Generally, the amount of the income from a retirement account distribution depends on the type of retirement account, how much was contributed to the retirement account, and whether the amounts contributed were already taxed. You don’t have to include a qualified distribution from a designated Roth account as income. For more information, see IRS Publication 575.&lt;br&gt;&lt;br&gt;
If you select “retirement,” you’ll be asked how much you get from retirement account distributions. You’ll also be asked how often you get this amount. Enter what you receive as a distribution from retirement investment, even if you aren’t retired.&lt;/p&gt;</value>
  </data>
  <data name="selfEmployment" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Self-employment income&lt;/b&gt;
This is the net income a person earns from their own trade or business. For example, any net income (profit) you earn from goods you sell or services you provide to others counts as self-employment income. Self-employment income could also come from a distributive share from a partnership.&lt;br&gt;&lt;br&gt;
If you select “self-employment,” you’ll describe the kind of work this self-employment is. There’s no special format – simply describe the work. For example, if you clean houses, enter “house cleaning.” If you make jewelry, enter “jewelry making.” If you work on construction projects, enter “construction.” For more information, visit IRS.gov to view “Instructions for Schedule C” or IRS Publication 334 (2012), pages 30-39.&lt;/p&gt;</value>
  </data>
  <data name="ssIncome" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Social Security benefits&lt;/b&gt;&lt;br&gt;
These are the amount a person gets from Social Security disability, retirement (income railroad retirement (RRB)), or survivor’s benefits each month.&lt;br&gt;&lt;br&gt;
&lt;b&gt;If you select “Social Security benefits,”&lt;/b&gt; you’ll enter the amount you get from Social Security benefits. You’ll also select how often you get this amount: one time only, monthly, or yearly. You can find the amount on the cost-of-living increase letter you get each year. Enter the full amount before any deductions, like Medicare premiums, income tax withholding, overpayments, child support, or alimony. Don’t enter Supplemental Security Income (SSI) benefits.&lt;/p&gt;</value>
  </data>
  <data name="unemployment" xml:space="preserve">
    <value>&lt;p&gt;&lt;b&gt;Unemployment&lt;/b&gt;&lt;br&gt;
Unemployment compensation includes any amount you get under an unemployment compensation law of the United States or a state. You usually must include unemployment benefits (including from an employer or union) as income. To see the limited exceptions, see IRS Publication 525, page 26.
If you select “unemployment,” you’ll enter the amount you get from unemployment, and how often you get this amount. You’ll be asked which state or former employer provides you with unemployment benefits. You’ll also be asked if there’s a date that unemployment benefits are set to expire.&lt;/p&gt;</value>
  </data>
</root>