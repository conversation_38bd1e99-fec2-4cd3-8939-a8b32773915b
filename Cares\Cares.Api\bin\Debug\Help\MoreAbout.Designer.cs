﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Help {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class MoreAbout {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MoreAbout() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Help.MoreAbout", typeof(MoreAbout).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to American Indians and Alaska Natives can still get services from the Indian Health Services, tribal health programs, or urban Indian health programs, and the results of this application won’t change that..
        /// </summary>
        public static string aian {
            get {
                return ResourceManager.GetString("aian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activities of daily living include seeing, hearing, walking, eating, sleeping, standing, lifting, bending, breathing, learning, reading, communicating, thinking, and working.
        ///If a person has a cognitive or mental health condition, they may need help with these activities of daily living through coaching or instruction.
        ///If a person only needs help because he or she is too young to be able to do these activities without help, select “No.”.
        /// </summary>
        public static string helpActivities {
            get {
                return ResourceManager.GetString("helpActivities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select “Yes” if one or more of these conditions applies to you:&lt;br&gt;
        ///• You’re blind, deaf, or hard of hearing.&lt;br&gt;
        ///• You get Social Security Disability Insurance (SSDI) or Supplemental Security Insurance (SSI).&lt;br&gt;
        ///• You have a physical, cognitive, intellectual, or mental health condition, which causes one or more of these:&lt;br&gt;
        ///o Difficulty doing errands like visiting a doctor’s office or shopping.&lt;br&gt;
        ///o Serious difficulty concentrating, remembering, or making decisions.&lt;br&gt;
        ///o Difficulty walking or cli [rest of string was truncated]&quot;;.
        /// </summary>
        public static string phyDisability {
            get {
                return ResourceManager.GetString("phyDisability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If any of the women on your application are pregnant, telling us here will help the whole household get the most help possible paying for health coverage.&lt;br&gt;If you’re pregnant, telling us how many babies you’re expecting during this pregnancy will help the whole household get the most help possible paying for health coverage..
        /// </summary>
        public static string pregnant {
            get {
                return ResourceManager.GetString("pregnant", resourceCulture);
            }
        }
    }
}
