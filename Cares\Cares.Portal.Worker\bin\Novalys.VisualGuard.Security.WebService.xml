<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Novalys.VisualGuard.Security.WebService</name>
    </assembly>
    <members>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGAuthorizationPolicy">
            <summary>Defines a set of rules for authorizing a Visual Guard user.</summary>
            <remarks>
            <P>If you need to integrate Visual Guard in a WCF application, we strongly recommend to read the <A href="IntegrateVisualGuard(WCF).htm">"How to integrate Visual Guard in a WCF application"</A> document.</P>
            </remarks>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGAuthorizationPolicy.#ctor">
            <summary> Defines a set of rules for authorizing a user, given a set of claims. </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGAuthorizationPolicy.System#IdentityModel#Policy#IAuthorizationPolicy#Evaluate(System.IdentityModel.Policy.EvaluationContext,System.Object@)">
            <summary>Evaluates whether a user meets the requirements for this authorization policy.</summary>
            <returns>false if the method for this authorization policy must be called if additional claims are added by other authorization policies to evaluationContext; otherwise, true to state no additional evaluation is required by this authorization policy. </returns>
            <param name="evaluationContext">An <see cref="T:System.IdentityModel.Policy.EvaluationContext"></see> that contains the claim set that the authorization policy evaluates.</param>
            <param name="state">A <see cref="T:System.Object"></see>, passed by reference that represents the custom state for this authorization policy. </param>
        </member>
        <member name="P:Novalys.VisualGuard.Security.WebService.VGAuthorizationPolicy.Issuer">
            <summary>Gets a claim set that represents the issuer of the authorization policy. </summary>
            <returns>Always returns <see cref="P:System.IdentityModel.Claims.ClaimSet.System"></see>.</returns>
        </member>
        <member name="P:Novalys.VisualGuard.Security.WebService.VGAuthorizationPolicy.Id">
            <summary>
            Gets the unique Id of the authorization policy
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGICredentialInformationContainer">
            <summary>
            Class used as VGICredential information container, It holds VGICredential object for authentication.
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGWCFCredentialTokenLibrary.VGWCFClientCredentials">
            <summary>
            CreditCardClientCredentials for use with VGCustomCredentialInformation
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGWCFCredentialTokenLibrary.VGWCFCredentialTokenParameters">
            <summary>
            VGCredentialTokenParameters for use with the VG Custom Credential Token
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGWCFCredentialTokenLibrary.VGWCFCustomCredentialSecurityTokenSerializer">
            <summary>
            VGWCF Custom TokenSerializer for use with the VGWCFCustomCredentialToken
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGWCFCredentialTokenLibrary.VGWCFCustomCredentialTokenAuthenticator">
             <summary>
            TokenAuthenticator for use with the Custom Token
             This validates the incoming credential token against a file of valid credential
             </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGWCFCredentialTokenLibrary.VGWCFServiceCredentials">
            <summary>
            VGWCFServiceCredentials for use with the VGWCFCustomCredential Token. It serves up a Custom SecurityTokenManager
            VGWCFServiceCredentialsSecurityTokenManager - that knows about our custom token.
            </summary>
            
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper">
            <summary>
            Class Used to searialize VGICredential data.
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper.SerializeCredential(Novalys.VisualGuard.Security.AuthenticationModule.VGICredential)">
            <summary>
            Serializes VGICredential information
            </summary>
            <param name="credential">VGICredential object</param>
            <returns>string format serialzed data for VGICredential object.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper.SerializeData(System.Object)">
            <summary>
            Serializes object data.
            </summary>
            <param name="data">any object to serialize</param>
            <returns>string format serialzed data for object</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper.DeserializeCredential(System.String)">
            <summary>
            Deserializes Credential string in VGICredential object.
            </summary>
            <param name="strCredentialDetail">serialized string of VGICredential</param>
            <returns>VGICredential</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper.DeserializeData(System.String)">
            <summary>
            Desrialize string in Object
            </summary>
            <param name="data">serialised string of object</param>
            <returns>object</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper.GetVGICredentialElementString(System.String)">
            <summary>
            
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper.GetCredential(System.String)">
            <summary>
            Gets VGICredential object passed via ClientCredentials.Username.Username, Username represents VGICredential object.
            </summary>
            <param name="strCredentialDetail">strCredentialDetail</param>
            <returns>VGICredential object passed via ClientCredentials.Username.Username</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWCFCustomCredentialHelper.GetPassword(System.String)">
            <summary>
            Gets password passed via ClientCredentials.Username.Password, Password represents moduleName.
            </summary>
            <param name="strPasswordData">strPasswordData</param>
            <returns>password passed via ClientCredentials.Username.Password</returns>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager">
            <summary>
            Provides Visual Guard authorization access checking for service operations.
            </summary>
            <remarks>You have to declare this class in the &lt;serviceAuthorization&gt; element of the application configuration file.
            <P>For more information about integration of Visual Guard in a WCF application, we strongly recommend to read the <A href="IntegrateVisualGuard(WCF).htm">"How to integrate Visual Guard in a WCF application"</A> document.</P>
            </remarks>
            <example>Example of application configuration file: <br/>
            &lt;serviceAuthorization principalPermissionMode="Custom" serviceAuthorizationManagerType="Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager, Novalys.VisualGuard.Security.WebService" /&gt;
            </example>
        </member>
        <member name="F:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager.PrincipalClaimType">
            <summary>Gets the URI for a claim that identifies the Visual Guard principal.</summary>
            <returns>The URI for a claim that identifies the Visual Guard principal.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager.#ctor">
            <summary>
            Creates a new instance of the class <see cref="T:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager"/>
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager.CheckAccess(System.ServiceModel.OperationContext,System.ServiceModel.Channels.Message@)">
            <summary>
            Checks authorization for the given operation context.
            </summary>
            <param name="operationContext">the operation context.</param>
            <param name="message">The System.ServiceModel.Channels.Message to be examined to determine authorization.</param>
            <returns>true if access is granted; otherwise; otherwise false.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager.CheckAccessCore(System.ServiceModel.OperationContext)">
            <summary>
            Checks authorization for the given operation context based on default policy evaluation.
            </summary>
            <param name="operationContext">The System.ServiceModel.OperationContext for the current authorization request.</param>
            <returns>true if access is granted; otherwise, false. The default is true.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager.GetAuthorizationPolicies(System.ServiceModel.OperationContext)">
            <summary>
            Gets the set of policies that participate in policy evaluation.
            </summary>
            <param name="operationContext">The System.ServiceModel.OperationContext of the current authorization request.</param>
            <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1"/> of type <see cref="T:System.IdentityModel.Policy.IAuthorizationPolicy"/>.</returns>
        </member>
        <member name="P:Novalys.VisualGuard.Security.WebService.VGServiceAuthorizationManager.VisualGuardIssuer">
            <summary>Gets a <see cref="T:System.IdentityModel.Claims.ClaimSet"></see> object that represents a Visual Guard trusted issuer.</summary>
            <returns>The system <see cref="T:System.IdentityModel.Claims.ClaimSet"></see> object.</returns>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGServiceRuntimeProvider">
            <summary>
            Provides the <see cref="T:Novalys.VisualGuard.Security.VGSecurityRuntime"/> object used to secure the current web service 
            </summary>
        </member>
        <member name="E:Novalys.VisualGuard.Security.WebService.VGServiceRuntimeProvider.RuntimeInitialized">
            <summary>
            Occurs when the <see cref="T:Novalys.VisualGuard.Security.VGSecurityRuntime"/> object assigned by default to the security manager is initialized.
            </summary>
            <remarks>
            The event handler receives an argument of type <see cref="T:Novalys.VisualGuard.Security.VGSecurityInitializedEventArgs"/> containing data related to this event.
            The <see cref="E:Novalys.VisualGuard.Security.WebService.VGServiceRuntimeProvider.RuntimeInitialized"/> event allows to change the default security settings used by Visual Guard to load the security for the specified service description.
            </remarks>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceRuntimeProvider.GetDefaultRuntime">
            <summary>
            Gets the default Runtime for VG cache system
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceRuntimeProvider.GetCurrentServiceRuntimeWithoutContext">
            <summary>
            Gets the Runtime without Context for UserNamePasswordValidator and other class
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceRuntimeProvider.GetRuntimeFromContext(System.IdentityModel.Policy.EvaluationContext)">
            <summary>
            Gets the Runtime with Context for client request 
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGServiceRuntimeProvider.GetRuntimeFromContext(System.IdentityModel.Policy.AuthorizationContext)">
            <summary>
            Gets the Runtime with Context for client request 
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.WebService.VGUserNameValidator">
            <summary>
             Validates a username and password against a Visual Guard repository.
            </summary>
             <remarks>You have to declare this class in the &lt;userNameAuthentication&gt; element of the &lt;serviceCredentials&gt; section of the application configuration file.
             <P>For more information about integration of Visual Guard in a WCF application, we strongly recommend to read the <A href="IntegrateVisualGuard(WCF).htm">"How to integrate Visual Guard in a WCF application"</A> document.</P>
             </remarks>
             <example>Example of application configuration file: <br/>
             &lt;userNameAuthentication userNamePasswordValidationMode="Custom" includeWindowsGroups="false" customUserNamePasswordValidatorType="Novalys.VisualGuard.Security.WebService.VGUserNameValidator, Novalys.VisualGuard.Security.WebService" cacheLogonTokens="true" /&gt;
             </example>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGUserNameValidator.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Novalys.VisualGuard.Security.WebService.VGUserNameValidator"/> class.</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGUserNameValidator.Validate(System.String,System.String)">
            <summary>
            Authenticates the specified user name and password.
            </summary>
            <param name="userName">User Name</param>
            <param name="password">User Password</param>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenValidationException">userName and password combination are not valid</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="userName"/> or <paramref name="password"/> is null</exception>
            <exception cref="T:System.ArgumentException">username is empty.
            <para>or</para>
            <paramref name="userName"/> or <paramref name="password"/> is longer than 64 characters.</exception>
            <exception cref="T:Novalys.VisualGuard.Security.VGConfigurationException">The configuration file is not valid
            <para>or</para>
            A problem occurs during the connection to the repository.
            <para>or</para>
            The version of the repository or the version of the permissions is not supported by the application.
            </exception>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWebServiceAuthentication.SetCredendial(System.ServiceModel.Security.UserNamePasswordClientCredential,Novalys.VisualGuard.Security.VGIPrincipal)">
            <summary>
            Sets VGIPrincipal object to ClientCredential
            </summary>
            <param name="credential"></param>
            <param name="principal"></param>
        </member>
        <member name="M:Novalys.VisualGuard.Security.WebService.VGWebServiceAuthentication.SetCredential(System.ServiceModel.Description.ClientCredentials,Novalys.VisualGuard.Security.AuthenticationModule.VGICredential)">
            <summary>
            Sets VGICredential to ClientCredential object.
            </summary>
            <param name="credential"></param>
            <param name="vgCredential">VGICredential object</param>
        </member>
        <member name="T:Novalys.VisualGuard.VGCredentialTokenLibrary.VGWCFCustomCredentialTokenProvider">
            <summary>
            CredentialTokenProvider for use with the CredentialToken
            </summary>
        </member>
    </members>
</doc>
