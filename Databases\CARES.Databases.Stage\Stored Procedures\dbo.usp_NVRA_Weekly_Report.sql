CREATE PROCEDURE [dbo].[usp_NVRA_Weekly_Report]
		@ReportDay DATE = NULL

AS
-- =============================================================================
-- Author: Unknown
-- Create date: N/A
-- Description:	NVRA (National Voter Registration Act) Weekly Report

-- **************************
--      Change History
-- **************************
-- Date         Author                 Description
-- --------	   ---------   ---------------------------------------------------------
-- 08/31/2020	Monika		Converted Custom SQL Script to Stored Procedure
-- 04/02/2021	Monika		Added App#, Person#, and Notice_Code to the report
-- 07/14/2021	Monika		Modified to exclude duplicate records with multiple apps
-- 08/25/2021	Kiran 		Modified logic to look for any mailing address change within the week period.
-- 07/11/2023   Emmanuel    Excluding StatusID 26 (Import Processing) from the results.
-- 07/18/2023	Emmanuel	Update query Adding Nursing Home Facility Address for NH applications.
-- 07/21/2023   Emmanuel	Filtering only Nursing Home programs to get the NH Address if exist, else, uses the Mailing Address.
-- 04/16/2024	Monika		Modified to exclude deceased records
-- 12/13/2024   Divya 		Excluded Justice Involved Youth (JIY) apps.
-- 08/08/2025   Balaram		Added Notes section 
-- 08/22/2025	Raveendran	Excluded PEP Program
-- ==================================================================================

BEGIN
	SET NOCOUNT ON;

		SET TRAN ISOLATION LEVEL READ UNCOMMITTED;

		SELECT	 @ReportDay = ISNULL(@ReportDay, GETDATE());

		DECLARE @StartDate DATE = DATEADD(WEEK, DATEDIFF(WEEK, 0, @ReportDay) - 1, 0),
				@EndDate DATE = DATEADD(DAY, 6, DATEADD(WEEK, DATEDIFF(WEEK, 0, @ReportDay) - 1, 0));

		DECLARE @NVRA TABLE
			(
				CAREOF						VARCHAR(50) NULL,
				[NAME]						VARCHAR(50) NOT NULL,
				ADDRESS_LINE1				VARCHAR(50) NOT NULL,
				ADDRESS_LINE2				VARCHAR(50) NULL,
				CITY						VARCHAR(50) NOT NULL,
				STATE_ABREV					VARCHAR(50) NOT NULL,
				ZIPCODE						VARCHAR(10) NOT NULL,
				APPLICATION_ID				INT	 NOT NULL,
				PERSON_ID					INT	 NOT NULL,
				NOTICE_CODE                 CHAR(5) NOT NULL
			);

		INSERT INTO @NVRA
		SELECT
		DISTINCT	CAREOF,
					[NAME],
					ADDRESS_LINE1,
					ADDRESS_LINE2,
					CITY,
					STATE_ABREV,
					ZIPCODE,
					APPLICATION_ID,
					PERSON_ID,
					NOTICE_CODE
		FROM		(
						SELECT	ROW_NUMBER() OVER (PARTITION BY P.PERSON_ID ORDER BY A.APPLICATION_ID DESC) AS RowId,
									SUBSTRING('C/O '+SUBSTRING(FIRST_NAME,1,1)+'. '+LAST_NAME, 1, 25) AS CAREOF,
									SUBSTRING(FIRST_NAME + ' ' + LAST_NAME, 1, 25) AS [NAME],
									--NURSING HOME OR OTHER
									CASE WHEN RF.EXPEDITE_FACILITY_PROVIDER_ID IS NULL THEN
										SUBSTRING(AA.ADDRESS_LINE1, 1, 25) ELSE SUBSTRING(RF.ADDRESS_LINE1, 1, 25) END AS ADDRESS_LINE1,
									CASE WHEN RF.EXPEDITE_FACILITY_PROVIDER_ID IS NULL THEN
										SUBSTRING(AA.ADDRESS_LINE2, 1, 25) ELSE SUBSTRING(RF.ADDRESS_LINE2, 1, 25) END AS ADDRESS_LINE2,
									CASE WHEN RF.EXPEDITE_FACILITY_PROVIDER_ID IS NULL THEN
										SUBSTRING(AA.CITY, 1, 25) ELSE SUBSTRING(RF.CITY, 1, 25) END AS CITY,
									CASE WHEN RF.EXPEDITE_FACILITY_PROVIDER_ID IS NULL THEN
										SUBSTRING(db4_ee.[dbo].[fnGetStateAbrev](AA.STATE_ID), 1, 8) ELSE SUBSTRING(db4_ee.[dbo].[fnGetStateAbrev](RF.STATE_ID), 1, 8) END AS STATE_ABREV,
									CASE WHEN RF.EXPEDITE_FACILITY_PROVIDER_ID IS NULL THEN
										SUBSTRING(AA.ZIPCODE, 1, 10) ELSE SUBSTRING(RF.ZIPCODE, 1, 10) END AS ZIPCODE,
									A.APPLICATION_ID,
									P.PERSON_ID,
									'N0051' AS NOTICE_CODE
						FROM	db4_ee.[dbo].[APPLICATION] A WITH (NOLOCK)
								INNER JOIN db4_ee.[dbo].[PERSON] P WITH (NOLOCK) ON P.PERSON_ID = A.CONTACT_PERSON_ID
								INNER JOIN db4_ee.[dbo].[PERSON_DETAIL] PD WITH (NOLOCK) ON P.PERSON_ID = PD.PERSON_ID
								INNER JOIN db4_ee.[dbo].[PERSON_ADDRESS] AS PA ON P.PERSON_ID = PA.PERSON_ID
								INNER JOIN db4_ee.DBO.[ADDRESS] AS AA ON PA.ADDRESS_ID = AA.ADDRESS_ID
								LEFT JOIN db4_ee.[dbo].APPLICATION_ELDERLY_DISABLED_DETAIL AS EDD ON A.APPLICATION_ID = EDD.APPLICATION_ID AND EDD.ELDERLY_DISABLED_PROGRAM_ID IN (2,32,33,34,37) -- Nursing Home related programs
								LEFT JOIN db4_ee.[dbo].REF_EXPEDITE_FACILITY_PROVIDER AS RF ON EDD.EXPEDITE_FACILITY_PROVIDER_ID = RF.EXPEDITE_FACILITY_PROVIDER_ID
						WHERE	A.APPLICATION_STATUS_ID <> 1
								AND A.SUB_PROGRAM_CATEGORY_ID NOT IN (10,11)	-- 10 - Justice Involved Youth (JIY) 11 - PEP
								AND A.APPLICATION_STATUS_ID <> 26				-- 26 = Import Processing
								AND PA.ADDRESS_TYPE_ID = 2
								AND LEN(AA.ADDRESS_LINE1) > 3
								AND LEN(AA.CITY) > 3
								AND (AA.CREATED_DATE BETWEEN @StartDate AND @EndDate
									OR AA.UPDATED_DATE BETWEEN @StartDate AND @EndDate
									OR RF.UPDATED_DATE BETWEEN @StartDate AND @EndDate)
								AND PD.DATE_OF_DEATH IS NULL
					) A 
		WHERE		A.RowId = 1;

		DELETE	FROM @NVRA
		WHERE	STATE_ABREV = 'AL'
				AND LEFT(ZIPCODE, 5) NOT IN (SELECT ZIPCODE FROM db4_ee.dbo.REF_ZIP_COUNTY);

-- Notes Section Begin.


	DECLARE @TEMP_NOTES TABLE (
			APPLICATION_NOTES_ID BIGINT NULL,
			APPLICATION_ID       INT    NULL,
			PERSON_ID INT NULL
			);

	DECLARE @CREATEDBY VARCHAR(50) = 'NVRA'

	INSERT dbo.APPLICATION_NOTES
		(
			APPLICATION_ID,
			NOTES_REF_ID,
			NOTES_DESC,
			CREATED_BY,
			UPDATED_BY
		)
		OUTPUT INSERTED.APPLICATION_NOTES_ID, INSERTED.APPLICATION_ID
		INTO   @TEMP_NOTES (APPLICATION_NOTES_ID, APPLICATION_ID)
		SELECT DISTINCT
			   N.APPLICATION_ID,
			   123,
			   'NVRA Letter was sent to the mailing vendor for '+ P.FIRST_NAME + SPACE(1) + P.LAST_NAME,
			   @CREATEDBY,
			   @CREATEDBY
		FROM @NVRA AS N
		JOIN dbo.PERSON AS P WITH (NOLOCK)
		  ON N.PERSON_ID = P.PERSON_ID;

		UPDATE TN
		SET    TN.PERSON_ID = N.PERSON_ID
		FROM   @TEMP_NOTES AS TN
		JOIN   @NVRA       AS N
		  ON   N.APPLICATION_ID = TN.APPLICATION_ID;

		INSERT dbo.APPLICATION_NOTES_DETAIL
		(
			APPLICATION_NOTES_ID,
			PERSON_ID,
			CREATED_BY,
			UPDATED_BY
		)
		SELECT APPLICATION_NOTES_ID,
			   PERSON_ID,
			   @CREATEDBY,
			   @CREATEDBY
		FROM   @TEMP_NOTES;
	-- Notes Section End.

		SELECT	CAREOF,
				[NAME],
				ADDRESS_LINE1,
				ADDRESS_LINE2,
				CITY,
				STATE_ABREV,
				ZIPCODE,
				APPLICATION_ID,
				PERSON_ID,
				NOTICE_CODE
		FROM	@NVRA;

		SET TRAN ISOLATION LEVEL READ COMMITTED;

END
Go