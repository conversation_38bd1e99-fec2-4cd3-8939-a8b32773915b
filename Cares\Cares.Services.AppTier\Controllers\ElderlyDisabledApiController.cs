﻿using Cares.Api.Infrastructure;
using Cares.Api.Infrastructure.WebAPI;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Api.Messages.InRule;
using Cares.Api.Messages.Representatives;
using Cares.Data.DataAbstractionLayer;
using Cares.Services.AppTier.BLL;
using Cares.Services.AppTier.BLL.AuthorizedRep;
using Cares.Services.AppTier.BLL.ElderlyDisabled;
using System;
using System.Web.Http;


namespace Cares.Services.AppTier.Controllers
{
    public class ElderlyDisabledApiController : BaseApiController
    {
        /// <summary>
        /// Given an Application ID returns Application data
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledApplicationDto GetApplication(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledApplicationDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetApplication(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Saves an ElderlyDisabledApplicationResponse.  If new, returns the Id
        /// </summary>
        /// <param name="application">The application.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse SaveApplication(ElderlyDisabledApplicationDto application, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                // Skip the isElderlyDisabledApplication if this is a new app
                if (application.Application.ApplicationId != 0)
                {
                    var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(application.Application.ApplicationId, tokenId);

                    if (edAppError != null)
                    {
                        return edAppError;
                    }
                }

                return new ElderlyDisabledBll().SaveApplication(application, username, tokenId);
            });
        }

        /// <summary>
        /// Gets Spouse Info for an app
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledSpouseDto GetSpouseInfo(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledSpouseDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetSpouseInfo(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Upsert for Spouse data
        /// </summary>
        /// <param name="spouseData">The spouse data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse UpsertElderlyDisabledSpouseInfo(ElderlyDisabledSpouseDto spouseData, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(spouseData.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().UpsertElderlyDisabledSpouseInfo(spouseData, username, tokenId);
            });
        }

        /// <summary>
        /// Get Veteran Information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledVeteranDto GetVeteranDetails(int applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledVeteranDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SelectVeteranDetails(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Get E&D Eligibility Enrollment.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledEligibilityDeterminationsDto GetElderlyDisabledEligibilityEnrollment(int applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledEligibilityDeterminationsDto>(applicationId, tokenId);
                if (edAppError != null)
                {
                    return edAppError;
                }
                return new ElderlyDisabledBll().GetElderlyDisabledEligibilityEnrollmentSnapshot(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Saves Veteran Information.
        /// </summary>
        /// <param name="veteranInfo"> The veteran information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse SaveElderlyDisabledVeteranInfo(ElderlyDisabledVeteranDto veteranInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(veteranInfo.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().UpsertElderlyDisabledVeteranInfo(veteranInfo, tokenId);
            });
        }

        /// <summary>
        /// Gets household members
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledHouseholdMembersDto GetHouseholdMembers(int applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledHouseholdMembersDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetHouseholdMembers(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Saves household members
        /// </summary>
        /// <param name="memberInfo"> The member information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage SaveHouseholdMembers(ElderlyDisabledHouseholdMembersDto memberInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(memberInfo.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SaveHouseholdMembers(memberInfo, tokenId);
            });
        }

        /// <summary>
        /// Deletes household member
        /// </summary>
        /// <param name="applicationNonMagiNoSsnPersonId">The application non-magi no ssn person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public BaseApiMessage DeleteHouseholdMember(int applicationNonMagiNoSsnPersonId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().DeleteHouseholdMember(applicationNonMagiNoSsnPersonId, tokenId);
            });
        }

        /// <summary>
        /// Gets the resource information by applicationId
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledResourceDto GetResourceInformation(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledResourceDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetResourceInformation(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Gets the property information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledPropertyDto GetPropertyInformation(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledPropertyDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetPropertyInformation(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled property information.
        /// </summary>
        /// <param name="propertyInformation">The property information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse SavePropertyInformation(ElderlyDisabledPropertyDto propertyInformation, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(propertyInformation.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SavePropertyInformation(propertyInformation, username, tokenId);
            });
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled resource information.
        /// </summary>
        /// <param name="applicationResourceInfo">The application resource information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse SaveResourceInformation(ElderlyDisabledResourceDto applicationResourceInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(applicationResourceInfo.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SaveResourceInformation(applicationResourceInfo, username, tokenId);
            });
        }

        /// <summary>
        /// Get E&D life insurance information by applicationId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledLifeInsuranceDto GetLifeInsuranceInfo(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledLifeInsuranceDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetLifeInsuranceInfo(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Get personal property information by applicationId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledPersonalPropertyDto GetPersonalPropertyInfo(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledPersonalPropertyDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetPersonalPropertyInfo(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled life insurance information.
        /// </summary>
        /// <param name="applicationInsuranceInfo">The insurance information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage SaveLifeInsuranceInfo(ElderlyDisabledLifeInsuranceDto applicationInsuranceInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(applicationInsuranceInfo.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SaveLifeInsuranceInfo(applicationInsuranceInfo, tokenId);
            });
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the personal property information.
        /// </summary>
        /// <param name="personalPropertyInfo">The personal property information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage SavePersonalPropertyInfo(ElderlyDisabledPersonalPropertyDto personalPropertyInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(personalPropertyInfo.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SavePersonalPropertyInfo(personalPropertyInfo, tokenId);
            });
        }

        /// <summary>
        /// Get E&D medical insurance information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledMedicalInsuranceDto GetMedicalInsuranceInfo(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledMedicalInsuranceDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().GetMedicalInsuranceInfo(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the medical insurance information.
        /// </summary>
        /// <param name="medicalInsuranceInfo">The medical insurance info.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiMessage SaveMedicalInsuranceInfo(ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(medicalInsuranceInfo.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SaveMedicalInsuranceInfo(medicalInsuranceInfo, tokenId);
            });
        }

        /// <summary>
        /// Save Representative information
        /// </summary>
        /// <param name="representativeData">The representative data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage SaveRepresentativeInfo(RepresentativeInfo representativeData, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(representativeData.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new AuthorizedRepBll().SaveRepresentativeInfo(representativeData, username, tokenId);
            });
        }

        /// <summary>
        /// Get representative information
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public RepresentativeInfo GetRepresentativeInfo(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<RepresentativeInfo>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                var authRep = new AuthorizedRepBll().GetRepresentativeInfo(applicationId, username, tokenId);
                // Also need the app status, which isn't returned in the above call
                var appInfoBar = new ElderlyDisabledBll().GetAppInfoBarInfo(applicationId, tokenId);
                authRep.ApplicationStatusId = appInfoBar.ApplicationStatusId;

                return authRep;
            });
        }

        [HttpGet]
        public ElderlyDisabledLiabilityDto GetPersonLiabilityInfo(long applicationId, long personId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetPersonLiabilityInfo(applicationId, personId, tokenId);
            });
        }

        /// <summary>
        /// Upsert Liability Test data
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public BaseApiResponse UpsertLiabilityTestData(ElderlyDisabledLiabilityDto personLiabilityInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().UpsertLiabilityTestData(personLiabilityInfo, username, tokenId);
            });
        }

        /// <summary>
        /// Upsert on a single liability segment
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        [HttpPost]
        public ElderlyDisabledLiabilityDetailDto UpsertLiabilitySegment(ElderlyDisabledLiabilityDetailDto personLiabilityInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledLiabilityDetailDto>(personLiabilityInfo.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                personLiabilityInfo.CreatedDate = personLiabilityInfo.CreatedDate == default(DateTime) ? DateTime.Now : personLiabilityInfo.CreatedDate;
                personLiabilityInfo.CreatedBy = username;
                personLiabilityInfo.Remarks = personLiabilityInfo.Remarks ?? string.Empty; // Since this is a non-nullable field in the database, setting to an empty string to prevent issues.

                return new ElderlyDisabledBll().UpsertLiabilitySegment(personLiabilityInfo, username, tokenId);
            });
        }

        /// <summary>
        /// Delete liability segment
        /// </summary>
        /// <param name="personLiabilityId"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        [HttpGet]
        public BaseApiMessage DeleteLiabilitySegment(long personLiabilityId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().DeleteLiabilitySegment(personLiabilityId, username);
            });
        }

        /// <summary>
        /// Truncate the liability segments
        /// </summary>
        /// <param name="enrollmentInfo">The elderly disabled eligibility determination dto.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage TruncateLiabilitySegments(ElderlyDisabledEligibilityDeterminationsDto enrollmentInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().TruncateLiabilitySegments(enrollmentInfo, username, tokenId);
            });
        }

        /// <summary>
        /// Gets the application information bar information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledApplicationInfoBarDto GetAppInfoBarInfo(long applicationId, string username, Guid tokenId)
        {
            //TODO - BN: Make this a Generic method
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetAppInfoBarInfo(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Get renew application.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public BaseApiResponse GetRenewApplication(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<ElderlyDisabledApplicationInfoBarDto>(applicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().RenewApplication(applicationId, username, tokenId);
            });
        }

        /// <summary>
        /// Gets E&D Income info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledNonMagiIncomesDto GetNonMagiIncomes(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                ElderlyDisabledNonMagiIncomesDto income = new ElderlyDisabledNonMagiIncomesDto();
                income = (new ElderlyDisabledBll()).GetNonMagiIncome(applicationId, tokenId);

                return income;
            });
        }

        /// <summary>
        ///  Upsert non Magi income
        /// </summary>
        /// <param name="nonMagiIncome">The non-magi income data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage SaveApplicationNonMagiIncome(ElderlyDisabledNonMagiIncomesDto income, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var result = (new ElderlyDisabledBll()).SaveApplicationNonMagiIncome(income, username, tokenId);

                return result;
            });
        }

        /// <summary>
        ///  Calls the calculation for VA Net amount
        /// </summary>
        /// <param name="applicationNonMagiIncomeId">The income id.</param>
        /// <returns></returns>
        [HttpGet]
        public BaseApiMessage CalculateVANetValues(long appId, long applicationNonMagiIncomeId, string username, Guid tokenId)
        {

            return handleControllerAction(username, tokenId, () =>
            {
                var result = (new ElderlyDisabledBll()).CalculateVANetValues(appId, applicationNonMagiIncomeId, username, tokenId);

                return result;
            });
        }

        /// <summary>
        ///  Upsert Allocation info
        /// </summary>
        /// <param name="allocationInfo">The spouse and family allocation data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage SaveAllocationInfo(ElderlyDisabledQitAndAllocationDto allocationInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var result = (new ElderlyDisabledBll()).SaveAllocationInfo(allocationInfo, username, tokenId);

                return result;
            });
        }

        /// <summary>
        /// Gets the eligibility determination.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledEligibilityDeterminationsDto GetEligibilityDeterminations(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return (new ElderlyDisabledBll()).GetEligibilityDeterminations(applicationId, username, tokenId);
            });
        }

        /// <summary>
        /// Gets the Detail info for QIT and LIEN indicators for an app.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledApplicationDetailQitLienDto GetApplicationDetailQitLien(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return (new ElderlyDisabledBll()).GetElderlyDisabledDetailQitDto(applicationId, username, tokenId);
            });
        }

        /// <summary>
        /// Saves the eligibility determinations.
        /// </summary>
        /// <param name="eligibility">The eligibility.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse SaveEligibilityDeterminations(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(eligibility.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().SaveEligibilityDeterminations(eligibility, username, tokenId);
            });
        }

        /// <summary>
        /// Creates an MSP app for this E&D enrollment
        /// </summary>
        /// <param name="eligibility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse EnrollMsp(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(eligibility.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }

                return new ElderlyDisabledBll().EnrollMsp(eligibility, username, tokenId);
            });
        }

        /// <summary>
        /// Get the correct State Aid Category from inRule etermination.
        /// </summary>
        /// <param name="request">The requested object to calculate StateAidCategory</param>
        /// <param name="username">The username</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public EnDStateAidCategoryResponseMessage GetStateAidDetermination(EnDStateAidCategoryDeterminationRequest request, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new InRuleBll().GetEnDStateAidDetermination(request, tokenId);
            });
        }

        /// <summary>
        /// Creates an E&D app for this E&D enrollment
        /// </summary>
        /// <param name="eligibility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiResponse EnrollComplete(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                var edAppError = isValidElderlyDisabledApplication<BaseApiResponse>(eligibility.ApplicationId, tokenId);

                if (edAppError != null)
                {
                    return edAppError;
                }
                ElderlyDisabledBll edbll = new ElderlyDisabledBll();
                BaseApiResponse apiresponse = edbll.EnrollComplete(eligibility, username, tokenId);
                if (apiresponse.IsSuccessful)
                {
                    BaseApiResponse snapshotapiresponse = edbll.GenerateAndSaveApplicationSnapshot(eligibility.ApplicationId, username, tokenId);
                    if (!snapshotapiresponse.IsSuccessful)
                    {
                        // Even though EnrollComplete succeeded (IsSuccessful = true), we do want to report back
                        // this failure, but leave IsSuccessful = true:
                        apiresponse.CaresError = CaresError.ApplicationSnapshotFailed;
                    }
                }

                return apiresponse;
            });
        }


        /// <summary>
        /// Gets the primary sponsor info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public RepresentativeInfoDto GetPrimarySponsorInfo(long applicationId, long personId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetPrimarySponsorInfo(personId, tokenId);
            });
        }

        /// <summary>
        /// Get QIT and Allocation Information
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledQitAndAllocationDto GetQitAndAllocation(long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetQitAndAllocationInfo(applicationId, tokenId);
            });
        }

        /// <summary>
        /// Gets the elderly disabled non-magi income info for Elig/Enroll screen.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ElderlyDisabledNonMagiIncomesDto GetElderlyDisabledNonMagiIncomeInfo(long applicationId, long personId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetElderlyDisabledNonMagiIncomeInfo(applicationId, personId, tokenId);
            });
        }

        /// <summary>
        /// Imports an Expedite flat file line loaded into the staging EXPEDITE_IMPORT table.
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public BaseApiResponse ImportExpedite(int expediteImportId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().ImportExpedite(this, expediteImportId, username, tokenId);
            });
        }

        /// <summary>
        /// Called by the front end, for dashboard display, or other (displaying error to user who tries to navigate to a partial import)
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public StagingExpediteImportDetailsDto GetImportExpediteDetails(int expediteImportId, long applicationId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetImportExpediteDetails(expediteImportId, applicationId, username, tokenId);
            });
        }

        /// <summary>
        /// Returns providers from the REF_NH_PROVIDER table
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ProvidersDto GetProviders(string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetProviders();
            });
        }

        /// <summary>
        /// Gets the expedite facilities and providers
        /// </summary>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ExpediteFacilityProvidersDto GetExpediteFacilityProviders(string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetExpediteFacilityProviders(username, tokenId);
            });
        }

        /// <summary>
        /// Gets the expedite facilities.
        /// </summary>
        /// <param name="searchText">The query to search for facility name or facility code.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ExpediteFacilityProvidersDto GetExpediteFacilitiesByString(string searchText, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetExpediteFacilitiesByString(searchText, username, tokenId);
            });
        }

        /// <summary>
        /// Returns a single ExpediteFacility, with provider match data
        /// </summary>
        /// <param name="expediteFacilityId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public ExpediteFacilityDto GetExpediteFacilityById(int expediteFacilityId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetExpediteFacilityById(expediteFacilityId, tokenId);
            });
        }

        /// <summary>
        /// Updates Expedite Facility data
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public BaseApiResponse UpdateExpediteFacility(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().UpdateExpediteFacility(expediteFacility, username, tokenId);
            });
        }

        /// <summary>
        /// ONLY sets the PROVIDER_ID for a given Expedite Facility
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public BaseApiMessage SetExpediteFacilityProvider(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().SetExpediteFacilityProvider(expediteFacility, username, tokenId);
            });
        }

        /// <summary>
        /// Inserts the expedite facility information.
        /// </summary>
        /// <param name="expediteFacilityInfo">The new expedite facility information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage InsertExpediteFacility(ExpediteFacilityDto expediteFacilityInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().InsertExpediteFacility(expediteFacilityInfo, username, tokenId);
            });
        }

        /// <summary>
        /// Inserts the financial institution information.
        /// </summary>
        /// <param name="financialInstitutionInfo">The new financial institution information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpPost]
        public BaseApiMessage InsertFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().InsertFinancialInstitution(financialInstitutionInfo, username, tokenId);
            });
        }

        /// <summary>
        /// Get the financial institution based on BankCode or BankName.
        /// </summary>
        /// <param name="searchText">Search info</param>
        /// <param name="username">The username</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public FinancialInstitutionsDto GetFinancialInstitutionsByString(string searchText, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetFinancialInstitutionsByString(searchText, tokenId);
            });
        }

        /// <summary>
        /// Get the financial institution based on bankId or get all if bankId is null.
        /// </summary>
        /// <param name="bankId">search by bankId</param>
        /// <param name="username">The username</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public FinancialInstitutionsDto GetFinancialInstitutionsByBankId(int? bankId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetFinancialInstitutions(bankId, tokenId);
            });
        }

        /// <summary>
        /// Get the last Exparte Application info for a person.
        /// </summary>
        /// <param name="personId">Search by person Id</param>
        /// <param name="username">The username</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public ExparteApplicationInfoDto GetExparteInfoByPersonId(int personId, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().GetExparteAppInfoByPersonId(personId, tokenId);
            });
        }

        /// <summary>
        /// Saves the financial institution.
        /// </summary>
        /// <param name="financialInstitutionInfo">The financial institution information.</param>
        /// <param name="MergeFlag">Merge or not merge</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiMessage UpdateFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, bool MergeFlag, string username, Guid tokenId)
        {
            return handleControllerAction(username, tokenId, () =>
            {
                return new ElderlyDisabledBll().UpdateFinancialInstitution(financialInstitutionInfo, MergeFlag, username, tokenId);
            });
        }

        /// <summary>
        /// Determines whether 1) It's an E&D app, and 2) It's ok to continue loading it.
        /// Some E&D apps can be in the process of being imported via Expedite, and aren't ready to be displayed
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        private T isValidElderlyDisabledApplication<T>(long applicationId, Guid tokenId) where T : BaseApiResponse, new()
        {
            //  If it is not an E&D application and not RMA, then return WrongApplicationType
            if (ApplicationDal.GetSubProgramCategoryByApplicationId(applicationId) != Constants.SubProgramCategories.EandD && ApplicationDal.GetSubProgramCategoryByApplicationId(applicationId) != Constants.SubProgramCategories.RMA)
            {
                return new T
                {
                    CaresError = CaresError.WrongApplicationType,
                    IsSuccessful = false
                };
            }

            // Ok to continue, return null
            return null;
        }
    }
}