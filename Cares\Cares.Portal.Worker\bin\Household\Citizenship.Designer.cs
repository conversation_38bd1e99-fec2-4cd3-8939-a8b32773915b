﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Citizenship {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Citizenship() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.Citizenship", typeof(Citizenship).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alien Number.
        /// </summary>
        public static string alienNumber {
            get {
                return ResourceManager.GetString("alienNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Card/Receipt Number.
        /// </summary>
        public static string cardReceiptNumber {
            get {
                return ResourceManager.GetString("cardReceiptNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;ul&gt;
        ///&lt;li&gt;Invalid Card/Receipt Number.&lt;/li&gt;
        ///&lt;li&gt;Card/Receipt number must be  13 characters long  with the first 3 letters followed by 10 digits.&lt;/li&gt;&lt;li&gt;Card number can not be all same digits Eg{111,999}. &lt;/li&gt;
        ///&lt;/ul&gt;.
        /// </summary>
        public static string cardRecieptInvalid {
            get {
                return ResourceManager.GetString("cardRecieptInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB:[DOB]) a U.S. citizen or U.S. national?.
        /// </summary>
        public static string citizenOrNational {
            get {
                return ResourceManager.GetString("citizenOrNational", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate of Citizenship.
        /// </summary>
        public static string citizenshipCertificate {
            get {
                return ResourceManager.GetString("citizenshipCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Citizenship Certificate Number.
        /// </summary>
        public static string citizenshipCertificateNumber {
            get {
                return ResourceManager.GetString("citizenshipCertificateNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country of issuance.
        /// </summary>
        public static string countryOfIssuance {
            get {
                return ResourceManager.GetString("countryOfIssuance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permanent Resident Card(&apos;Green Card&apos;, I-551).
        /// </summary>
        public static string dialogImmigrationStatus1 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate of Eligibility for Nonimmigrant(F-1) Student Status(I-20).
        /// </summary>
        public static string dialogImmigrationStatus10 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate of Eligibility for Exchange Visitor(J-1) Status(DS2019).
        /// </summary>
        public static string dialogImmigrationStatus11 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notice of Action(I-797).
        /// </summary>
        public static string dialogImmigrationStatus12 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temporary I-551 Stamp(on passport or I-94, I-94A).
        /// </summary>
        public static string dialogImmigrationStatus2 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine Readable Immigrant Visa(with temporary I-551 language).
        /// </summary>
        public static string dialogImmigrationStatus3 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employment Authorization Card(EAD, I-766).
        /// </summary>
        public static string dialogImmigrationStatus4 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrival/Department Record(I-94, I-94A).
        /// </summary>
        public static string dialogImmigrationStatus5 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrival/Department Record in foreign passport(I-94).
        /// </summary>
        public static string dialogImmigrationStatus6 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Foreign passport.
        /// </summary>
        public static string dialogImmigrationStatus7 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reentry permit(I-327).
        /// </summary>
        public static string dialogImmigrationStatus8 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refugee Travel Document(I-571).
        /// </summary>
        public static string dialogImmigrationStatus9 {
            get {
                return ResourceManager.GetString("dialogImmigrationStatus9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review the list below of eligible statuses..
        /// </summary>
        public static string dialogStatement1 {
            get {
                return ResourceManager.GetString("dialogStatement1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If one of them pertains to this person, select YES to eligible immigration status..
        /// </summary>
        public static string dialogStatement2 {
            get {
                return ResourceManager.GetString("dialogStatement2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If there is no relevant status, this person might still be eligible for services if he/she has an emergency or is pregnant..
        /// </summary>
        public static string dialogStatement3 {
            get {
                return ResourceManager.GetString("dialogStatement3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eligible Immigration Message.
        /// </summary>
        public static string dlgEligibleImmigrationHeader {
            get {
                return ResourceManager.GetString("dlgEligibleImmigrationHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document description.
        /// </summary>
        public static string docDescription {
            get {
                return ResourceManager.GetString("docDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document description.
        /// </summary>
        public static string documentDesc {
            get {
                return ResourceManager.GetString("documentDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document expiration date.
        /// </summary>
        public static string documentExpiration {
            get {
                return ResourceManager.GetString("documentExpiration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Type.
        /// </summary>
        public static string documentType {
            get {
                return ResourceManager.GetString("documentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME], (DOB:[DOB]) have eligible immigration status?.
        /// </summary>
        public static string doesNameHaveEligibleImmigrationStatus {
            get {
                return ResourceManager.GetString("doesNameHaveEligibleImmigrationStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide supporting information.
        /// </summary>
        public static string eligImmigrationDocTypeSupportingInfo {
            get {
                return ResourceManager.GetString("eligImmigrationDocTypeSupportingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the same name as shown on [NAME]&apos;s, (DOB:[DOB]) document.
        /// </summary>
        public static string enterDocumentName {
            get {
                return ResourceManager.GetString("enterDocumentName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME], (DOB:[DOB]) have any of these documents?.
        /// </summary>
        public static string haveAnyTheseDocs {
            get {
                return ResourceManager.GetString("haveAnyTheseDocs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Citizenship Information.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I-94 should follow one of the following format: &lt;br&gt;
        ///    *  11 Numbers&lt;br&gt;
        ///    *  [ 9 digit numeric][1 Alphabet][1 digit numeric].
        /// </summary>
        public static string i94 {
            get {
                return ResourceManager.GetString("i94", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I-94 number.
        /// </summary>
        public static string i94Number {
            get {
                return ResourceManager.GetString("i94Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME], (DOB:[DOB]) have eligible immigration status?.
        /// </summary>
        public static string immigrationStatus {
            get {
                return ResourceManager.GetString("immigrationStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Has [NAME], (DOB:[DOB]) lived in the U.S. since before August 1996?.
        /// </summary>
        public static string inUSSince1996 {
            get {
                return ResourceManager.GetString("inUSSince1996", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB:[DOB]) an honorably discharged veteran or active-duty member of the military?.
        /// </summary>
        public static string military {
            get {
                return ResourceManager.GetString("military", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naturalization Certificate.
        /// </summary>
        public static string naturalizationCertificate {
            get {
                return ResourceManager.GetString("naturalizationCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naturalization Certificate Number.
        /// </summary>
        public static string naturalizationCertificateNumber {
            get {
                return ResourceManager.GetString("naturalizationCertificateNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB:[DOB]) a naturalized or derived citizen?.
        /// </summary>
        public static string naturalizedOrDerived {
            get {
                return ResourceManager.GetString("naturalizedOrDerived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select at least one option from list of Other Document Types..
        /// </summary>
        public static string OtherDocumentSelectRequired {
            get {
                return ResourceManager.GetString("OtherDocumentSelectRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passport  Number.
        /// </summary>
        public static string passport {
            get {
                return ResourceManager.GetString("passport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passport Expiration Date.
        /// </summary>
        public static string passportExpiration {
            get {
                return ResourceManager.GetString("passportExpiration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB:[DOB]) the same name that appears on [HIS/HER] document?.
        /// </summary>
        public static string sameNameOnDocument {
            get {
                return ResourceManager.GetString("sameNameOnDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select appropriate document type..
        /// </summary>
        public static string selectDocumentType {
            get {
                return ResourceManager.GetString("selectDocumentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SEVIS ID Number.
        /// </summary>
        public static string sevisID {
            get {
                return ResourceManager.GetString("sevisID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visa Number.
        /// </summary>
        public static string visaNumber {
            get {
                return ResourceManager.GetString("visaNumber", resourceCulture);
            }
        }
    }
}
