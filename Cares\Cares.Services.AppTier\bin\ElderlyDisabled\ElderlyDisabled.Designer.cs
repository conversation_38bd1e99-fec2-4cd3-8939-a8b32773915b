﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.ElderlyDisabled {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ElderlyDisabled {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ElderlyDisabled() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.ElderlyDisabled.ElderlyDisabled", typeof(ElderlyDisabled).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is the applicant eligible for E&amp;D coverage?.
        /// </summary>
        public static string AwardEandD {
            get {
                return ResourceManager.GetString("AwardEandD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you need to award MSP?.
        /// </summary>
        public static string AwardMSP {
            get {
                return ResourceManager.GetString("AwardMSP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Elderly &amp; Disabled is unavailable.
        /// </summary>
        public static string ElderlyDisabledTitle {
            get {
                return ResourceManager.GetString("ElderlyDisabledTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Elderly &amp; Disabled program benefits are currently unavailable. Please click below..
        /// </summary>
        public static string ElderlyDisabledUnavailable {
            get {
                return ResourceManager.GetString("ElderlyDisabledUnavailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing a change code or have Award selected with another change code.
        /// </summary>
        public static string InvalidLiabilityChangeCodes {
            get {
                return ResourceManager.GetString("InvalidLiabilityChangeCodes", resourceCulture);
            }
        }
    }
}
