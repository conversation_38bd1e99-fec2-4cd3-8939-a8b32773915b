﻿@model Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.ElderlyDisabledNonMagiIncomeViewModel
@using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel
@using AlabamaConnectExpress.Controllers.ElderlyAndDisabled
@using Cares.Portal.Infrastructure
@using Cares.Api.Infrastructure
@using DD = Cares.Classes.DropDownValues
@using Constants = Cares.Api.Infrastructure.Constants;
@using Enums = Cares.Api.Infrastructure.Enums;

@{
	Layout = "~/Views/Shared/_ElderlyDisabledMasterLayout.cshtml";
	ViewBag.Title = "E&D - Non-Magi Income";
	var namePrefix = @Html.NameFor(model => model.NonMagiIncomes) + "[{index}].";
	string nameDetailPrefix = namePrefix + "EDNonMagiIncomeDetail" + "[{detailIndex}].";

	// For the income delete confirmation modal
	var strIncomeHandle = "Income";
	var strIncomeDetailHandle = "IncomeDetail";
	var strIncomeDetailHandleTitle = "Income Detail";
	// For the Personal Needs Allowance delete confirmation modal
	var strPersonalNeedsAllowance = "PersonalNeedsAllowance";
	var strPersonalNeedsAllowanceTitle = "Personal Needs Allowance";
	string personalNeedsAllowancePrefix = @Html.NameFor(model => model.PersonalNeedsAllowances) + "[{PersonalNeedsAllowanceIndex}].";
	// If this page is being loaded with some pending deletes, we need to show the Save Required banner:
	string showSaveDeleteRequired = Model.NonMagiIncomes.Any(i => i.IsDeleted && i.ApplicationNonMagiIncomeId > 0) ||
									Model.PersonalNeedsAllowances.Any(i => i.IsPersonalNeedsAllowanceDeleted && i.ApplicationPersonalNeedsAllowanceId > 0)
									? "show" : "hide";
}

@section viewStyles
{
    <style>
        /*  This will hide the day picker  */
        .pickerMonthYearClass .ui-datepicker-calendar,
        /*  This will hide the default datepicker button (using a custom one)    */
        .datepickerMonthYear + button.ui-datepicker-trigger,
        .datepickerDateStop + button.ui-datepicker-trigger,
        .datepickerAdditionalDetails + button.ui-datepicker-trigger,
        /*  This will hide the Today button in the datepicker    */
        .pickerMonthYearClass button.ui-datepicker-current {
            display: none;
        }

        /*  Highlights the row being edited. Set important because of bootstrap class overwriting.    */
        .edit-row {
            background-color: var(--ed-editing) !important;
        }

        /*  Highlights the row that was created or edited. Set important because of bootstrap class overwriting.    */
        .edited-row {
            background-color: var(--ed-editedRow) !important;
        }

        /*  Highlights the row being deleted. Set important because of bootstrap class overwriting.    */
        .delete-row {
            background-color: var(--ed-deleteRow) !important;
        }
    </style>
}

@using (Html.BeginForm("ElderlyDisabledNonMagiIncome", "ElderlyDisabled", FormMethod.Post, new { id = ElderlyDisabledController.ElderlyDisabledNonMagiIncomeFormName }))
{
    <div class="elderly-disabled-non-magi-income">
        @Html.HiddenFor(model => model.ApplicationId)
        @Html.HiddenFor(model => model.ApplicationStatusId)
        @Html.HiddenFor(model => model.SubProgramCatogory)

        <div class="card">
            <div class="card-header text-white">
                <label class="m-auto">
                    Additional Income Details
                </label>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-lg-3 d-flex justify-content-center align-items-center">
                        <div>
                            <a class="btn btn-info btn-non-magi-income-bendex btn-gradient shadow mr-2" id="aNonMagiIncomeBendex" target="_blank" href="@Url.Action("Index", "DataExchange", new { screen = "BENDEX" })">BENDEX</a>
                            <a class="btn btn-info btn-non-magi-income-sves btn-gradient shadow ml-2" id="aNonMagiIncomeSves" target="_blank" href="@Url.Action("Index", "DataExchange", new { screen = "SVES" })">SVES</a>
                        </div>
                    </div>
                    <div class="form-group col-lg-2">
                        <label class="bold">QIT Indicator</label>
                        <div class="form-inline">
                            <div class="custom-control custom-radio custom-control-inline">
                                @Html.RadioButtonFor(model => model.QitIndicator, true, new { id = "rdbtnQitIndicatorYes", @class = "custom-control-input" })
                                <label class="custom-control-label pt-1" for="rdbtnQitIndicatorYes">Yes</label>
                            </div>
                            <div class="custom-control custom-radio custom-control-inline">
                                @Html.RadioButtonFor(model => model.QitIndicator, false, new { id = "rdbtnQitIndicatorNo", @class = "custom-control-input" })
                                <label class="custom-control-label pt-1" for="rdbtnQitIndicatorNo">No</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="ddlSpousalImpoverishmentIndicator">Spousal Impoverishment Indicator</label>
                        @Html.DropDownListFor(model => model.SpousalImpoverishmentIndicator, DD.GetSpousalImpoverishmentIndicators(Model.SpousalImpoverishmentIndicator), "Select one...", new { @class = "form-control", id = "ddlSpousalImpoverishmentIndicator" })
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="txtbxSpousalAllocationAmount">Spousal Allocation Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">@CommonHtmlExtensions.IconHelper(Constants.Icons.IconDollarSign, new string[] { "input-group-text", "colorNavyBlue" })</div>
                            @Html.TextBoxFor(model => model.SpousalAllocationAmount, new { id = "txtbxSpousalAllocationAmount", data_toggle = "tooltip", title = "Spousal Allocation Amount", @class = "form-control allocation-amount validate-dollarAmountNonNegative", maxlength = 12 })
                        </div>
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="txtbxFamilyAllocationAmount">Family Allocation Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">@CommonHtmlExtensions.IconHelper(Constants.Icons.IconDollarSign, new string[] { "input-group-text", "colorNavyBlue" })</div>
                            @Html.TextBoxFor(model => model.FamilyAllocationAmount, new { id = "txtbxFamilyAllocationAmount", data_toggle = "tooltip", title = "Family Allocation Amount", @class = "form-control allocation-amount validate-dollarAmountNonNegative", maxlength = 12 })
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header text-white">
                        <h5>Personal Needs Allowance</h5>
                    </div>
                    <div class="card-body">
                        <fieldset class="ml-3 mb-2">
                            <legend>Budget Calculation fields</legend>
                            <div class="col-12 ml-1 w-100">
                                <div class="mt-1" id="divNonMagi-PNA">
                                    <div id="divNonMagi-pna-records" class="col-12">
                                        @if (Model.PersonalNeedsAllowances != null && Model.PersonalNeedsAllowances.Count > 0)
                                        {
                                            for (int ii = 0; ii < Model.PersonalNeedsAllowances.Count; ii++)
                                            {
                                                string indexPrefix = personalNeedsAllowancePrefix.Replace("{PersonalNeedsAllowanceIndex}", ii.ToString());
                                                @Html.Partial("NonMagiIncome/_PersonalNeedsAllowance", Model.PersonalNeedsAllowances[ii], new ViewDataDictionary() { { PortalConstants.ViewDataConstants.PersonalNeedsAllowancePrefix, indexPrefix }, { PortalConstants.ViewDataConstants.PersonalNeedsAllowanceIndex, ii } })
                                            }
                                        }
                                        else
                                        {
                                            string indexPrefix = personalNeedsAllowancePrefix.Replace("{PersonalNeedsAllowanceIndex}", "0");
                                            @Html.Partial("NonMagiIncome/_PersonalNeedsAllowance", new Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.PersonalNeedsAllowanceViewModel(), new ViewDataDictionary() { { PortalConstants.ViewDataConstants.PersonalNeedsAllowancePrefix, indexPrefix }, { PortalConstants.ViewDataConstants.PersonalNeedsAllowanceIndex, 0 } })
                                        }

                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col">
                                    <input type="hidden" id="hdnNonMagiPNACounter" value="@(Model.PersonalNeedsAllowances.Count == 0 ? 1 : Model.PersonalNeedsAllowances.Count)" />
                                    <button class="btn btn-info btn-gradient  shadow" id="btnAddPNARecord" name="btnAddPNARecord" type="button">Add</button>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="card-header text-white">
                <h5>HUB Verified SSA Income</h5>
            </div>

            <table class="table table-bordered m-0">
                <thead class="alert-warning">
                    <tr>
                        <th scope="col" class="col-2 pl-1">Name</th>
                        <th scope="col" class="col-2 pl-1">Relationship</th>
                        <th scope="col" class="col-2 pl-1">Income Month</th>
                        <th scope="col" class="col-2 pl-1">SSA Amount</th>
                        <th scope="col" class="col-2 pl-1">Payment in Suspension</th>
                    </tr>
                </thead>

                <tbody>
                    @foreach (var income in Model.HubVerifiedSsaIncomes)
                    {
                        <tr>
                            <td> @income.Name</td>
                            <td> @income.Relationship</td>
                            <td> @income.IncomeMonth</td>
                            <td>@(income.BenefitCredited.HasValue ? ("$" + income.BenefitCredited) : null)</td>
                            <td>@(income.PaymentSuspension.ToYesNoNull())</td>
                        </tr>
                    }

                </tbody>
            </table>
        </div>

        <div class="card">
            <div class="card-header text-white">
                <label class="m-auto">
                    If the applicant, spouse, parent or other has any money coming in, please add below:
                </label>
            </div>
            <div class="card-body">
                <div class="non-magi-income-section">
                    @for (int i = 0; i < Model.NonMagiIncomes.Count; i++)
                    {
                        string indexPrefix = namePrefix.Replace("{index}", i.ToString());
                        @Html.Partial("NonMagiIncome/_NonMagiIncome", Model.NonMagiIncomes[i], new ViewDataDictionary() { { PortalConstants.ViewDataConstants.nonMagiIncomeIndex, i }, { PortalConstants.ViewDataConstants.NamePrefix, indexPrefix }, { PortalConstants.ViewDataConstants.ApplicationId, Model.ApplicationId } })
                    }
                </div>
            </div>
            <div class="card-footer">
                <input type="hidden" id="hdnNonMagiIncomeCounter" value="@(Model.NonMagiIncomes.Count)" />
                <button class="btn btn-info btn-add-non-magi-income btn-gradient shadow" id="btnAddNonMagiIncome" type="button">Add Income</button>
            </div>
        </div>
    </div>
    @Html.Partial("_ValidationSummary")
}

<div id="divNonMagiIncome" style="display: none;">
    @Html.Partial("NonMagiIncome/_NonMagiIncome", new NonMagiIncomeViewModel(), new ViewDataDictionary() { { PortalConstants.ViewDataConstants.NamePrefix, namePrefix }, { PortalConstants.ViewDataConstants.nonMagiIncomeIndex, "{index}" } })
</div>

<div id="divNonMagiDetailIncome" style="display: none;">
    @Html.Partial("NonMagiIncome/_NonMagiIncomeDetail", new Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.ElderlyDisabledNonMagiIncomeDetailViewModel(), new ViewDataDictionary() { { PortalConstants.ViewDataConstants.NameDetailPrefix, nameDetailPrefix }, { PortalConstants.ViewDataConstants.nonMagiIncomeIndex, "{index}" }, { PortalConstants.ViewDataConstants.nonMagiIncomeDetailIndex, "{detailIndex}" }, { PortalConstants.ViewDataConstants.NetIncomeAmountClass, "{netIncomeAmountClass}" }, { "HeaderIncomeTypeId", String.Empty }, { "HeaderVaIndicator", String.Empty } })
</div>

<div id="divNonMagiPNA" style="display: none;">
    @Html.Partial("NonMagiIncome/_PersonalNeedsAllowance", new Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.PersonalNeedsAllowanceViewModel(), new ViewDataDictionary() { { PortalConstants.ViewDataConstants.PersonalNeedsAllowancePrefix, personalNeedsAllowancePrefix },  { PortalConstants.ViewDataConstants.PersonalNeedsAllowanceIndex, "{PersonalNeedsAllowanceIndex}" } })
</div>

@section modals{
    @Html.Partial("_DeleteConfirmationModal", new ViewDataDictionary {
                                                                       { PortalConstants.ViewDataConstants.DeleteModalSectionNameId, strIncomeHandle },
                                                                       { PortalConstants.ViewDataConstants.DeleteModalTitle, strIncomeHandle },
                                                                       { PortalConstants.ViewDataConstants.DeleteModalMessage, strIncomeHandle.ToLower() }
                                                                   })

    @Html.Partial("_DeleteConfirmationModal", new ViewDataDictionary {
                                                                       { PortalConstants.ViewDataConstants.DeleteModalSectionNameId, strIncomeDetailHandle },
                                                                       { PortalConstants.ViewDataConstants.DeleteModalTitle, strIncomeDetailHandleTitle },
                                                                       { PortalConstants.ViewDataConstants.DeleteModalMessage, strIncomeDetailHandleTitle.ToLower() }
                                                                   })

    @Html.Partial("_DeleteConfirmationModal", new ViewDataDictionary {
                                                                       { PortalConstants.ViewDataConstants.DeleteModalSectionNameId, strPersonalNeedsAllowance },
                                                                       { PortalConstants.ViewDataConstants.DeleteModalTitle, strPersonalNeedsAllowanceTitle },
                                                                       { PortalConstants.ViewDataConstants.DeleteModalMessage, strPersonalNeedsAllowanceTitle.ToLower() }
                                                                   })
}

@section scripts{
    <script src="~/Scripts/Custom/elderlyDisabledNonMagiIncome.js"></script>
    <script>
        $(document).ready(function () {
            InitializeAllCurrencyMask();

            InitializeFieldsMagiIncomeDetails();
            InitializeDatePickers();

			//  From and through dates masking
			$('.monthYearMask').inputmask({
				alias: 'monthYear'
            });

            //  From and through dates masking
            $('.dateStopMask').inputmask({
                alias: 'dateCalendar'
            });

            //Disable all fields if user is not authorized
            if ('@Model.ApplicationStatusId' == '@((int)Enums.enumApplicationStatus.Denied)') {
                $('form').find(':input:not(:disabled)').prop('disabled', true);
            }
            else {
                $('form').find(':input:not(:disabled)').prop('disabled', @(ViewBag.IsReadOnly));
            }

            $.validator.addClassRules({
                'validate-claimNumberFormat': {
                    'validate-regex': [ValidationRegex.IncomeClaimNumberFormat, 'claim number']
                },
                'validate-otherDescriptionFormat': {
                    'validate-regex': [ValidationRegex.IncomeOtherDescriptionFormat, 'other description']
                },
                'validate-vaOtherInfoFormat': {
                    'validate-regex': [ValidationRegex.IncomeVaOtherInfoFormat, 'other VA information']
                }
            });

            $(document.body).on('click', '.btn-add-non-magi-income', function ()
            {
                // Assign the id based on the hidden hdnNonMagiIncomeCounter field
                let count = parseInt($('#hdnNonMagiIncomeCounter').val());
                $('#hdnNonMagiIncomeCounter').val(count);
                $('.non-magi-income-section').append($('#divNonMagiIncome').html().replace(/{index}/g, count));
                $('#hdnNonMagiIncomeCounter').val(count + 1);
                InitializeCurrencyMask($(`#txtbx${count}AttestedIncomeAmount`), false);
                InitializeCurrencyMask($(`#txtbx${count}GrossIncomeAmount`), false);
                InitializeCurrencyMask($(`#txtbx${count}NetIncomeAmount`), false);
            });

			//Logic to copy the template for Income Detail and initialize fields for its use.
			$(document.body).on('click', '.btnAddDetailIncome', function () {
				// Assign the id based on the hidden hdnNonMagiIncomeDetailCounter field
				var indx = $(this).data("index");
				let count = parseInt($('#hdnNonMagiIncomeDetailCounter_' + indx).val());

                //SHOW OR HIDE Divs per Income Type
                let incomeTypeId = $('#ddl' + indx + 'IncomeTypeId').val();
                let classToAdd = (incomeTypeId == '2') ? 'mask-negative-dollar' : 'verify-amount';

				$('#divNonMagi-records-' + indx).append($('#divNonMagiDetailIncome').html().
                    replace(/{index}/g, indx).replace(/{detailIndex}/g, count).
                    replace(/{netIncomeAmountClass}/g, classToAdd));
				$('#hdnNonMagiIncomeDetailCounter_' + indx).val(count + 1);

				//Initialize Fields with mask
                InitializeCurrencyMask($(`#divRow_${indx}_${count} input.verify-amount`), false);
                const selector = '#txtbxIncomeMonth_' + indx + "_" + count;

				//  Initialize a month & year datepicker
                InitializeDatepicker(selector);

				InitializeDatepickerFullDate('#txtbxDateStop_' + indx + '_' + count, $('#txtbxDateStop_' + indx + '_' + count).data('datepicker-name'));

                //Make IncomeType readonly.
                $('#ddl' + indx + 'IncomeTypeId').attr("readonly", "readonly");
                $('#ddl' + indx + 'IncomeTypeId').css("pointer-events", "none");

                switch (incomeTypeId) {
                    case "@(Constants.ReferenceValues.IncomeType.JOB)": // Work Income
                        $('#divDetailType_' + incomeTypeId + '_' + indx + '_' + count).show();
                        // Make NET income as Readonly
                        $(`#txtbxNetAmount_${indx}_${count}`).attr('readonly', 'readonly');
                        break;
                    case "@(Constants.ReferenceValues.IncomeType.Veteran)": // Veteran Benefits
                        // Grab section div
                        let section = $(`#divRow_${indx}_${count}`);

                        //Show VA Check and VA Status only when VA Indicator is Pension.
						if ($('#ddl' + indx + 'VAIndicatorId').val() == "2") { // VA Indicator 2 = "Pension"
                            $('#divDetailType_' + incomeTypeId + '_' + indx + '_' + count).show();
                            // Net Amount is readonly on Pension
							$(`#txtbxNetAmount_${indx}_${count}`).attr('readonly', "readonly");
                            section.find('.gross-income-detail label').text("VA Check (Gross)");
						    section.find('.divMonthYear').removeClass('col-3');
						    section.find('.divMonthYear').addClass('col-2');
                        }
                        // Set Frequency as Monthly = 6
                        section.find('.detail-frequency option[value="6"]').attr('selected', 'selected').change();
                        //Hide Frequency Field
                        section.find('.divFrequency').hide();
                    break;
					default:
						$('#divDetailType_99_' + indx + '_' + count).show();
				}
			});

            // Remove non-magi income upon delete button click
            $(document.body).on('click', '.btn-delete-non-magi-income', function ()
            {
                let button = this;

                $('#divDelete' + '@strIncomeHandle' + 'ConfirmationDialog').modal({
                    backdrop: 'static',
                    keyboard: true
                });

                $('#btnConfirmDelete' + '@strIncomeHandle').off('click').click(function () {
                    var selection = $(button).closest('.non-magi-income-template');
                    selection.hide().children('.IncomeIsDeleted').val('True');
                    $('.tooltip').hide();
                    // If this row has a applicationNonMagiIncomeId, show the save required message
                    var applicationNonMagiIncomeId = selection.children('.IncomeApplicationNonMagiIncomeId').val();
                    if (applicationNonMagiIncomeId > 0) {
                        $('#divSavingDeleteRequiredMsg').show();
                    }
                });
			});

			$(document.body).on('blur', '.detail-gross-amount', function () {
				const [headerId, detailId] = getIndexes($(this));

                updateMonthlyIncome(headerId, detailId);
			});

			$(document.body).on('change', '.detail-frequency', function () {
				const [headerId, detailId] = getIndexes($(this));

                updateMonthlyIncome(headerId, detailId);
			});


            // Remove non-magi income detail upon delete button click
			$(document.body).on('click', '.btn-delete-income-detail', function ()
            {
                let button = this;
				var selection = $(button).closest('fieldset');
				const [headerId, detailId] = getIndexes($(button));

				selection.css('background-color', 'rgb(255,150,150)');

                $('#divDelete' + '@strIncomeDetailHandle' + 'ConfirmationDialog').modal({
                    backdrop: 'static',
					keyboard: true
				});

				$('#divDelete' + '@strIncomeDetailHandle' + 'ConfirmationDialog').on('hidden.bs.modal', function (e) {
					selection.css('background-color', 'unset');
				});

                $('#btnConfirmDelete' + '@strIncomeDetailHandle').off('click').click(function () {
					selection.hide().children('.IsEDNonMagiIncomeDetailDeleted').val('True');
                    $('.tooltip').hide();
                    // If this row has a applicationNonMagiIncomeId, show the save required message
					var applicationNonMagiIncomeId = selection.children('.IncomeApplicationNonMagiIncomeId').val();
                    if (applicationNonMagiIncomeId > 0) {
                        $('#divSavingDeleteRequiredMsg').show();
					}

					$('#hdn_IsDeleted_' + headerId + '_'+ detailId).val('true');
                });
            });

            // Show/Hide based on va-indicator changes
            $(document.body).on('change', '.select-va-indicator', function () {
                let value = Number($(this).val());

                // Show/Hide other VA info
                if (value == @(((byte)Enums.enumVAIndicator.Other).ToString())) {
                    $(this).closest('.row').parent().find('.va-other-information').show();
                }
                else {
                    $(this).closest('.row').parent().find(':text.va-other-information').val(null);
                    $(this).closest('.row').parent().find('div.va-other-information').hide();
                }
            });

            // Show/Hide income fields
           $(document.body).on('change', '.select-income-type', function ()
            {
                let value = Number($(this).val());

                // Show/Hide other income type
                if (value == @(Constants.ReferenceValues.IncomeType.OTHER))
                {
                    $(this).closest('.row').find('.other-income-type').show();
                }
                else
                {
                    $(this).closest('.row').find(':text.other-income-type').val(null);
					hideElementFromParentBySelector(this,'div.other-income-type');
                }

			    $(this).closest('.row').find(':text.attested-amount, :text.verify-amount').val(0);
                $(this).closest('.row').find(':text.claim-number, .select-member, .select-frequency').val(null);

                // Show/Hide income fields
                if (value > 0)
                {
                    $(this).closest('.row').find('div.claim-number, div.member, div.attested-amount, div.verify-amount, div.frequency').show();
                }
                else {
                    hideElementFromParentBySelector(this, 'div.claim-number, div.member, div.attested-amount, div.verify-amount, div.frequency');
                }

			   hideFieldByClass(this,'.work-income-details');
                // Show/Hide the VA UI elements
                if (value == @(Constants.ReferenceValues.IncomeType.Veteran))
                {
                    $(this).closest('.row').find('.va-indicator').show();
                    let vaIndicator = $(this).closest('.row').find('.select-va-indicator').val();
                    if (vaIndicator ==  @(((byte)Enums.enumVAIndicator.Other).ToString())) {
                        $(this).closest('.row').parent().find('.va-other-information').show();
                    }
                    else {
                        hideFieldByClass(this, '.va-other-information');
                    }
                } else if (value == @(Constants.ReferenceValues.IncomeType.JOB)) {
                    $(this).closest('.row').parent().find('.work-income-details').show();
					hideClaimNumber(this);
                } else {
					hideElementFromParentBySelector(this,'.va-indicator');
					hideFieldByClass(this,'.va-other-information');
                    $(this).closest('.row').parent().find(':text.va-other-information').val(null);
               }

			   //Show Claim Number for Black Lung Benefit, Railroad Retirement, SSA, VA.
               //Hide for all the rest.
               if (value == '@(Constants.ReferenceValues.IncomeType.Veteran)'
                   || value == '@(Constants.ReferenceValues.IncomeType.SOCIAL_SECURITY_BENEFITS)'
                   || value == '@(Constants.ReferenceValues.IncomeType.BlackLung)'
                   || value == '@(Constants.ReferenceValues.IncomeType.Railroad)'
                   )
               {
                   showClaimNumber(this);
               }
               else {
				   hideClaimNumber(this);
			   }
            });

            // If this page is being loaded with some pending deletes, show the Save Required banner:
            $('#divSavingDeleteRequiredMsg').@(showSaveDeleteRequired)();

            $(document.body).on('click', '#btnAddDetailIncome', function(){
                $('#divDetailsOnIncome').show();
            });
        });

        function hideClaimNumber(element) {
			$(element).closest('.row').find('div.claim-number').hide();
        }

        function showClaimNumber(element) {
			$(element).closest('.row').find('div.claim-number').show();
        }

        function hideElementFromParentBySelector(element, selector) {
			$(element).closest('.row').find(selector).hide();
        }

        function hideFieldByClass(incomeTypeElement, fieldToHideClass) {
			$(incomeTypeElement).closest('.row').parent().find(fieldToHideClass).hide();
		}

        // Save non-Magi income info
        SaveNonMagiIncomes = function () {
            let isValid = validator.form();

            if (isValid) {
                GenericFunctions.WaitMessageOpen('Please wait....');
                $(formSelector).submit();
            }
        };

        function InitializeDatePickers() {
            $('.datepickerMonthYear').each(function (index) {
				let id = $(this).attr('id');

				if (id.indexOf('{index}') == -1) {
					InitializeDatepicker(`#${id}`);
				}
            });

			$('.datepickerDateStop').each(function (index) {
				let id = $(this).attr('id');

				if (id.indexOf('{index}') == -1) {
					InitializeDatepickerFullDate(`#${id}`);
				}
            });

			//  Clear functionality for input fields
			$(document.body).on('click', '.btn-clear-input', function () {
				let dateInputId = `#${$(this).data('date-input-id')}`;

				//  Clear the value
				$(dateInputId).val(null);

				// Remove validation error from error container
				if ($(dateInputId).hasClass('error')) {
					$(dateInputId).removeClass('error');

					let errorContainerId = $(dateInputId).data('validation-errors-container');
					$(errorContainerId).children('em.error').remove();
				}

				$(this).blur();
            });

            //  Open the custom datepicker
            $(document.body).on('click', '.custom-datepicker-trigger', function () {
				let datepId = '#' + $(this).data('datepicker-id');
				//  Show the datepicker
				$(datepId).datepicker('show');
            });
        }

		function InitializeDatepickerFullDate(elementSelector) {
			//  Initialize a month & year datepicker
			$(elementSelector).datepicker({
				showOn: 'button',
				showButtonPanel: true,
                dateFormat: 'mm/dd/yy',
                defaultDate: 'now',
                onClose: function (dateText, inst) {
					$(this).datepicker('setDate', new Date(dateText));

					// Remove validation error from error container
					if ($(this).hasClass('error')) {
						$(this).removeClass('error');

						let errorContainerId = $(this).data('validation-errors-container');
						$(errorContainerId).children('em.error').remove();
					}
				}
            });

			// Date calendar masking
			$(elementSelector).inputmask({
				alias: 'dateCalendar'
			});
		}

        function InitializeDatepicker(elementSelector) {
			//  Initialize a month & year datepicker
			$(elementSelector).datepicker({
				changeMonth: true,
                changeYear: true,
				showOn: 'button',
				showButtonPanel: true,
				dateFormat: 'mm/yy',
                beforeShow: function (input, inst) {
					    $(inst.dpDiv).addClass('pickerMonthYearClass'); //  inst = the instance of the datepicker object. dpDiv = the datepicker div for display
				},
                onClose: function (dateText, inst) {
                        $(this).datepicker('setDate', new Date(inst.selectedYear, inst.selectedMonth, 1));
                        $(inst.dpDiv).removeClass('pickerMonthYearClass'); //  inst = the instance of the datepicker object. dpDiv = the datepicker div for display

					// Remove validation error from error container
					if ($(this).hasClass('error')) {
						$(this).removeClass('error');

						let errorContainerId = $(this).data('validation-errors-container');
						$(errorContainerId).children('em.error').remove();
					}
				}
            });

            // Mask for Month Year
			$(elementSelector).inputmask({
				alias: 'monthYear'
			});
        }

        InitializeAllCurrencyMask = function () {
			$(':text.attested-amount, :text.verify-amount, :text.allocation-amount').inputmask({ alias: 'nonNegativeCurrency', rightAlign: false, removeMaskOnSubmit: true, autoUnmask: true });
            $('input:text.mask-negative-dollar').each(function () {
                InitializeCurrencyMask(this, true)
            });

        }

        function InitializeFieldsMagiIncomeDetails() {
			//Disable Income Types for the ones that has Details.
			$('.elderly-disabled-non-magi-income .readonly').attr('readonly', "readonly");

            $('.detail-net-amount').each(function (index) {
                const [headerId, detailId] = getIndexes($(this));

                if (headerId != '{index}') { // Avoid logic on generic template
                    updateMonthlyIncome(headerId, detailId, true);

                    //Blocking all Income Type DropDowns with Details on it.
				    $('#ddl' + headerId + 'IncomeTypeId').css('pointer-events', 'none');

                    //If Income Type is VA, hide Frequency Field
                    if ($('#ddl' + headerId + 'IncomeTypeId').val() == "@(Constants.ReferenceValues.IncomeType.Veteran)") {
                        //Hide Frequency field
                        $('#divRow_' + headerId + '_' + detailId + ' .divFrequency').hide();

						if ($('#ddl' + headerId + 'VAIndicatorId').val() == "2") { // VA Indicator 2 = "Pension"
							$(`#txtbxNetAmount_${headerId}_${detailId}`).attr('readonly', "readonly");
                            $('#divDetailType_15_' + headerId + '_' + detailId).show();

                            //Make Month/Year field to fit in the space.
						    $('#divRow_' + headerId + '_' + detailId + ' .divMonthYear').removeClass('col-3');
							$('#divRow_' + headerId + '_' + detailId + ' .divMonthYear').addClass('col-2');
                        }
                    }
                    else if ($('#ddl' + headerId + 'IncomeTypeId').val() == "@(Constants.ReferenceValues.IncomeType.JOB)") {
						$(`#txtbxNetAmount_${headerId}_${detailId}`).attr('readonly', "readonly");
                    }
                }
            });
		}

        function updateMonthlyIncome(incomeId, nonMagiIncomeDetailIndex, isFirstLoad = false) {
			let incTypeId = $('#ddl' + incomeId + 'IncomeTypeId').val();
            const grossIncomeAmount = $("#txtbxGrossIncomeAmount_" + incomeId + "_" + nonMagiIncomeDetailIndex).val();
            if (incTypeId == @(Constants.ReferenceValues.IncomeType.JOB)) {
                const incomeFrequencyId = $("#ddlIncomeFrequencyId_" + incomeId + "_" + nonMagiIncomeDetailIndex).val();
                const monthlyIncome = calculateMonthlyIncome(incomeFrequencyId, grossIncomeAmount);
                const monthlyIncomeAmount = parseFloat(monthlyIncome) || 0;
                if (!isFirstLoad) { // Need this to keep whatever comes from the Backend
					$("#txtbxNetAmount_" + incomeId + "_" + nonMagiIncomeDetailIndex).val(monthlyIncomeAmount.toFixed(2));
				}
            }
            else if (incTypeId != @(Constants.ReferenceValues.IncomeType.Veteran)) { // All but Veterean, copy Gross into Net
				if (!isFirstLoad) { // Need this to keep whatever comes from the Backend
				    $("#txtbxNetAmount_" + incomeId + "_" + nonMagiIncomeDetailIndex).val(grossIncomeAmount);
				}
            }
        }

        function calculateMonthlyIncome(incomeFrequencyId, incomeAmount) {
            if (incomeAmount == 0)
            {
                return 0;

            }
            switch (Number(incomeFrequencyId)) {
                case 1: // Hourly
                    return (((incomeAmount * 160) - 65) / 2);
                case 2: // Daily
                    return (((incomeAmount * 20) - 65) / 2);
                case 3: // Weekly
                    return ((((incomeAmount * 52) / 12) - 65) / 2);
                case 4: // Biweekly
                    return ((((incomeAmount * 26) / 12) - 65) / 2);
                case 5: // Twice a month
                    return (((incomeAmount * 2) - 65) / 2);
                case 6: // Monthly
                    return ((incomeAmount - 65) / 2);
                case 7: // Quarterly
                    return (((incomeAmount / 3) - 65) / 2);
                case 16: // Semi-annually
                    return (((incomeAmount / 6) - 65) / 2);
                case 8: // Yearly
                    return (((incomeAmount / 12) - 65) / 2);
                default:
                    return incomeAmount;
            }
        }

        function getIndexes($element) {
			var headerDiv = $element.closest('.divRow');
			let headerId = headerDiv.data('header-index');
            let detailId = headerDiv.data('detail-index');
            return [headerId, detailId];
		}
    </script>
}