﻿using Cares.Api;
using Cares.Api.Infrastructure;
using Cares.Api.Infrastructure.Enums;
using Cares.Api.Infrastructure.Helper;
using Cares.Classes.Extensions;
using Cares.Controllers.Application;
using Cares.Data.DataAbstractionLayer;
using Cares.Infrastructure.Log;
using Cares.Models;
using Cares.Models.Application;
using Cares.Models.Enrollment;
using Cares.Models.Shared;
using Cares.Portal.Infrastructure;
using Cares.Portal.Infrastructure.VGSecurity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using static Cares.Infrastructure.Log.ScreenActionAttribute;
using ENUMS = Cares.Api.Infrastructure.Enums;

namespace Cares.Controllers.Enrollment
{
    public class EligEnrollController : WpBaseController
    {
        public const string EligEnrollScreenName = "Eligibility/Enrollment";

        #region Constants & validation messages
        public const string MoreThanOneDYSApplicantMsg = "There cannot be more than one applicant on a DYS application.";
        public const string MoreThanOneBCCApplicantMsg = "There cannot be more than one applicant on a BCC application.";
        public const string MoreThanOneHPEApplicantMsg = "There cannot be more than one applicant on a HPE application.";
        public const string MoreThanOnePEPApplicantMsg = "There cannot be more than one applicant on a PEP application.";
        public const string AwardPregnancytoMaleMsg = "Cannot award pregnancy category to a male.";
        #endregion

        public string ErrorMessages { get; set; }

        private HumanReadableErrorMessages humanReadableErrors = new HumanReadableErrorMessages();

        /// <summary>
        /// Indexes the specified person identifier.
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { EligEnrollScreenName }, ActionType = new string[] { ActionTypes.Read }, ActionDescription = "Loads the Eligibility/Enrollment screen.")]
        public ActionResult Index(string personId, string applicationId)
        {
            if (string.IsNullOrEmpty(applicationId))
            {
                return null;
            }

            string AuditorUserName = string.Empty;

            if (CaresSecurity.IsInRole(CaresSecurity.AuditorRolesAllowed))
            {
                AuditorUserName = Convert.ToString(Session[PortalConstants.SessionVariable.VG_UserName]);
            }

            AmericanIndianAlaskaNativeInformationDAL aianDal = new AmericanIndianAlaskaNativeInformationDAL(ProcessorKind.Worker);
            AIANMemberBO aian = aianDal.Get(applicationId, TokenId);
            EligibilityDAL eliDal = new EligibilityDAL(ProcessorKind.Worker);
            Eligibility eli = eliDal.Get(applicationId, AuditorUserName, TokenId);
            EligEnroll ee = new EligEnroll();
            bool anyApplyingInd = false;

            var personInfo = new PersonalInformation();
            var personInfoDAL = new PersonalInformationDAL(ProcessorKind.Worker);

            var conInfo = new ContactInformation();
            var conInfoDAL = new ContactInformationDAL(ProcessorKind.Worker);

            ee.PersonInfoTable = eli.PersonInfoTable;
            anyApplyingInd = eli.PersonInfoTable.Any(e => e.IsApplying != null && e.IsApplying.Equals("Yes"));

            if (anyApplyingInd)
            {
                ee.AnyApplyingInd = "true";
            }
            else
            {
                ee.AnyApplyingInd = "false";
            }

            var noDob = eli.PersonInfoTable.Any(e => e.DOB.Equals(string.Empty));

            if (ee.AnyApplyingInd == "true")
            {
                if (noDob == true)
                {
                    var applyingwithoutDob = from c in eli.PersonInfoTable where c.DOB.Equals(string.Empty) select c;

                    foreach (Models.Enrollment.PersonDetail pd in applyingwithoutDob)
                    {
                        if (pd.IsApplying.Equals("Yes"))
                        {
                            ee.ApplyingWithoutDob = "true";
                            break;
                        }
                    }
                }
            }

            int age = 0;
            //Set flags Override functionality
            for (int j = 0; j < eli.PersonInfoTable.Count; j++)
            {
                var appPersonId = "?applicationId=" + applicationId + "&personId=" + eli.PersonInfoTable[j].PersonId;

                personInfo = personInfoDAL.GetPersonalInformation(appPersonId, TokenId);

                //Set flag for POCR - Override functionality
                eli.PersonInfoTable[j].LivesWith19CareTaker = "false";

                if (!string.IsNullOrEmpty(personInfo.MoreAboutPersonDetail?.LiveWithUnder19Child))
                {
                    if (personInfo.MoreAboutPersonDetail.LiveWithUnder19Child == "true")
                    {
                        int.TryParse(eli.PersonInfoTable[j].Age, out age);

                        if (age >= 19)
                        {
                            eli.PersonInfoTable[j].LivesWith19CareTaker = "true";
                        }
                    }
                }

                if (personInfo.MoreAboutPersonDetail?.PastPregnancy == true)
                {
                    ee.PersonInfoTable[j].PastPregnancy = personInfo.MoreAboutPersonDetail.PastPregnancy;
                    ee.PersonInfoTable[j].NewBornDob = personInfo.MoreAboutPersonDetail.NewBornDob;
                }

                // Set the due date when is pregnant is true
                if (personInfo.MoreAboutPersonDetail?.IsPregnant == "true")
                {
                    ee.PersonInfoTable[j].DueDate = personInfo.MoreAboutPersonDetail.DueDate;
                }

                // set is IsAIANRace flag to true if there exists at least one race selected as AIAN
                // Start with false, then iterate through the race details.
                ee.PersonInfoTable[j].IsAIANRace = false;

                foreach (var raceDetail in personInfo.PersonRaceDetail)
                {
                    foreach (var race in raceDetail.RaceDetailList)
                    {
                        if (race.RaceId.Equals(Convert.ToString(Constants.ReferenceValues.Race.AIAN)))
                        {
                            // Found an AIAN race, set this to true and break.
                            ee.PersonInfoTable[j].IsAIANRace = true;
                            break;
                        }
                    }

                    if (ee.PersonInfoTable[j].IsAIANRace)
                    {
                        // Break the outer loop as well
                        break;
                    }
                }

                // set is IsAIAN flag to true if AIAN data filled in from AppendixB
                eli.PersonInfoTable[j].IsAIAN = false;

                if (aian.TribePersons?[j].isSelected != null)
                {
                    if (aian.TribePersons[j].FedRecTribe == "true" && aian.TribePersons[j].StateId == "1" && aian.TribePersons[j].TribeId != null)
                    {
                        ee.PersonInfoTable[j].IsAIAN = true;
                    }
                }
            }

            //Getting Application received date for Override functionality
            conInfo = conInfoDAL.GetContactInformation(applicationId, TokenId);

            if (conInfo.ApplicationDetail != null)
            {
                for (int i = 0; i < conInfo.ApplicationDetail.AppStatusList.Count; i++)
                {
                    if (conInfo.ApplicationDetail.AppStatusList[i].StatusId == Cares.Api.Infrastructure.Enums.ApplicationStatus.Received.Id.ToString())
                    {
                        ee.ReceivedDate = DateHelper.ConvertDateMMDDYY(conInfo.ApplicationDetail.AppStatusList[i].StatusDate);
                    }
                }
            }

            ee.ApplicationId = applicationId;
            ee.PageContactPersonId = personId;
            fillInAppStatusAndSubProgramCategory(ee);

            return View("Index", ee);
        }

        /// <summary>
        /// Save Enrollment.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="saveType">Type of the save.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { EligEnrollScreenName }, ActionType = new string[] { ActionTypes.Read, ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the enrollment.")]
        public ActionResult SaveEnrollment(EligEnroll formData, string SaveType)
        {
            performCommonValidation(formData.PageSubProgramCatogory);
            performAdditionalEligibilityValidation(formData.ApplicationId, formData.PageSubProgramCatogory);

            if (ModelState.IsValid)
            {
                bool success = false;
                ApplicationResponse objAppResp = new ApplicationResponse();
                EligibilityEnrollmentDAL objEEDal = new EligibilityEnrollmentDAL(ProcessorKind.Worker);

                for (int i = 0; i < formData.EligibilityRulesResult.Count; i++)
                {
                    if (!string.IsNullOrWhiteSpace(formData.EligibilityRulesResult[i].CategoryId))
                    {
                        var categoryId = formData.EligibilityRulesResult[i].CategoryId;
                        var programId = (string.IsNullOrWhiteSpace(formData.EligibilityRulesResult[i].ProgramId) ? "0" : formData.EligibilityRulesResult[i].ProgramId);

                        //  Ensure the correct program id is set, especially during override.
                        //  If in CHIP Category, but Program is not listed as CHIP
                        if (ENUMS.ProgramSubCategory.ChipProgramCategories.Contains(categoryId) && programId != Constants.CHIP)
                        {
                            formData.EligibilityRulesResult[i].ProgramId = Constants.CHIP;
                        }
                        //  IF APTC, but Program is not listed as APTC
                        else if (categoryId == ENUMS.enumProgramSubCategory._99APTC.ToString() && programId != Constants.APTC)
                        {
                            formData.EligibilityRulesResult[i].ProgramId = Constants.APTC;
                        }
                        //  If not APTC and not a CHIP category, default to Medicaid.
                        else if (categoryId != ENUMS.enumProgramSubCategory._99APTC.ToString() && !ENUMS.ProgramSubCategory.ChipProgramCategories.Contains(categoryId))
                        {
                            formData.EligibilityRulesResult[i].ProgramId = Constants.MEDICAID;
                        }
                    }
                }

                formData.UpdatedBy = Convert.ToString(Session[PortalConstants.SessionVariable.VG_UserName]);

                objAppResp = objEEDal.Save(formData, SaveType, TokenId);

                var appStatusId = string.Empty;
                var appStatusDesc = string.Empty;

                if (objAppResp != null)
                {
                    if (!string.IsNullOrEmpty(objAppResp.responseStatus))
                    {
                        if (objAppResp.responseStatus.Equals(Constants.SUCCESS))
                        {
                            success = true;
                            appStatusId = objAppResp.AppStatusId;
                            appStatusDesc = objAppResp.AppStatusDesc;
                        }
                        else if (objAppResp.errorDesc.Contains("001|"))
                        {
                            string errorMessage = string.Empty;
                            string[] splitErrMsg = objAppResp.errorDesc.Split('|');

                            errorMessage = splitErrMsg[1] + "<ol>";

                            for (int i = 2; i < splitErrMsg.Length - 1; i++)
                            {
                                errorMessage = errorMessage + "<li>" + splitErrMsg[i] + "</li>";
                            }

                            errorMessage = errorMessage + "</ol>";
                            ModelState.AddModelError("Overlapping Enrollment", errorMessage);
                        }
                    }
                }

                if (success)
                {
                    // If subprogram category is PEP & application status be "approved" or program description is "Already Enrolled" then send automatic-email.
                    if (formData.PageSubProgramCatogory == Constants.SubProgramCategories.PEP)
                    {
                        // Call send email.
                        SendEmail(formData.ApplicationId, formData.PersonInfoTable[0].PersonId, formData.EligibilityRulesResult[0].ProgramId);
                    }

                    ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

                    return Json(new { success = true, currentView = "F_EligEnroll", AppStatusId = appStatusId, AppStatusDesc = appStatusDesc });
                }
                else
                {
                    ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

                    return Json(new { success = false, response = ErrorMessages, currentView = "F_EligEnroll" });
                }
            }
            else
            {
                ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

                return Json(new { success = false, response = ErrorMessages, currentView = "F_EligEnroll" });
            }
        }

        /// <summary>
        /// Send on-demand Pep email.
        /// </summary>
        /// <param name="emailRequest">The pep info view model.</param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult SendPepEmail(PepEmail emailRequest)
        {
            if (string.IsNullOrEmpty(emailRequest.DeterminerEmail))
            {
                return Json(new { success = false, message = "Email is required." });
            }

            SendEmail(emailRequest.ApplicationId, emailRequest.PersonId, emailRequest.ProgramId, emailRequest.DeterminerEmail);

            return Json(new { success = true });
        }

        /// <summary>
        /// Save Eligibility
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <param name="personId">The person identifier.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { EligEnrollScreenName }, ActionType = new string[] { ActionTypes.Read, ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the eligibility.")]
        public ActionResult SaveEligibility(EligEnroll formData, string personId)
        {
            performCommonValidation(formData.PageSubProgramCatogory);

            string ineligibilityRequired = "Select Ineligibility Reason.";
            ApplicationResponse objAppResp = new ApplicationResponse();
            EligibilityDAL objEEDal = new EligibilityDAL(ProcessorKind.Worker);

            //check for valid model
            for (int i = 0; i < formData.EligibilityRulesResult.AsEmptyListIfNull().Count(); i++)
            {
                var person = formData.EligibilityRulesResult[i];

                if (personId == person.PersonId)
                {
                    if (string.IsNullOrEmpty(person.IneligibilityReasonId))
                    {
                        ModelState.AddModelError("EligibilityRulesResult[" + i + "].IneligibilityReasonId", ineligibilityRequired);
                    }
                }
            }

            if (ModelState.IsValid)
            {
                formData.UpdatedBy = Convert.ToString(Session[PortalConstants.SessionVariable.VG_UserName]);
                objAppResp = objEEDal.Save(formData, personId, TokenId);

                return Json(new { success = true, currentView = "F_EligEnroll" });
            }
            else
            {
                ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

                return Json(new { success = false, response = ErrorMessages, currentView = "F_EligEnroll" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// Check for eligibility.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { EligEnrollScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Checks for the eligibility.")]
        public ActionResult EligEnroll(string applicationId)
        {
            // Get the SubProgramCategory for validation checks. Easiest done this way:
            EligEnroll ee = new EligEnroll();
            ee.ApplicationId = applicationId;
            fillInAppStatusAndSubProgramCategory(ee);
            performCommonValidation(ee.PageSubProgramCatogory);
            performAdditionalEligibilityValidation(applicationId, ee.PageSubProgramCatogory);

            if (ModelState.IsValid)
            {
                EligibilityEnrollmentDAL eliDal = new EligibilityEnrollmentDAL(ProcessorKind.Worker);
                var userName = Convert.ToString(Session[PortalConstants.SessionVariable.VG_UserName]);
                var appUpdatedBy = "?applicationId=" + applicationId + "&updatedBy=" + userName;

                eliDal.RunTaxHHRules(appUpdatedBy, TokenId);

                return RedirectToAction("InRuleOutput", new { applicationId = applicationId });
            }
            else
            {
                ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

                return Json(new { success = false, response = ErrorMessages, currentView = "F_EligEnroll" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// Perform any Eligibility specific validations.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// /// <param name="subProgramCategory">The subProgramCategory identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { EligEnrollScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Check for any Eligibility specific validations based the roles.")]
        private void performAdditionalEligibilityValidation(string applicationId, Constants.SubProgramCategories subProgramCategory)
        {
            Eligibility eliInfo = new Eligibility();
            var eliInfoDal = new EligibilityDAL(ProcessorKind.Worker);
            var userName = Convert.ToString(Session[PortalConstants.SessionVariable.VG_UserName]);

            eliInfo = eliInfoDal.Get(applicationId, userName, TokenId);

            // Display error message if there is more than one applicant on a DYS/BCC/HPE/PEP application when determining eligibility.
            if (eliInfo.PersonInfoTable.Count > 1)
            {
                if (subProgramCategory == Constants.SubProgramCategories.DYS)
                {
                    ModelState.AddModelError(string.Empty, MoreThanOneDYSApplicantMsg);
                }
                else if (subProgramCategory == Constants.SubProgramCategories.BCC)
                {
                    ModelState.AddModelError(string.Empty, MoreThanOneBCCApplicantMsg);
                }
                else if (subProgramCategory == Constants.SubProgramCategories.HPE)
                {
                    ModelState.AddModelError(string.Empty, MoreThanOneHPEApplicantMsg);
                }
                else if (subProgramCategory == Constants.SubProgramCategories.PEP)
                {
                    ModelState.AddModelError(string.Empty, MoreThanOnePEPApplicantMsg);
                }
            }
        }

        /// <summary>
        /// Gets Enrollment/Eligibility result summary for an application.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { EligEnrollScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Gets Enrollment/Eligibility result summary for an application.")]
        public ActionResult InRuleOutput(string applicationId)
        {
            EligEnroll eli = new EligEnroll();
            EligibilityEnrollmentDAL eliDal = new EligibilityEnrollmentDAL(ProcessorKind.Worker);
            var userName = Convert.ToString(Session[PortalConstants.SessionVariable.VG_UserName]);
            var appUpdatedBy = "?applicationId=" + applicationId;

            if (CaresSecurity.IsInRole(CaresSecurity.AuditorRolesAllowed))
            {
                appUpdatedBy = appUpdatedBy + "&updatedBy=" + userName;
            }

            eli = eliDal.GetRulesResult(appUpdatedBy, TokenId);

            if (eli.EligibilityRulesResult == null)
            {
                eli.EligibilityRulesResult = new List<EligibilityCombineResult>
                {
                    new EligibilityCombineResult()
                };
            }

            if (eli.PersonEligibityList == null)
            {
                eli.PersonEligibityList = new List<IncomeEligibility>
                {
                    new IncomeEligibility()
                };
            }

            for (int i = 0; i < eli.EligibilityRulesResult.Count; i++)
            {
                eli.EligibilityRulesResult[i].ExistingStartDate = eli.EligibilityRulesResult[i].StartDate;
            }

            fillInAppStatusAndSubProgramCategory(eli);

            return View("EditorTemplates/EligibilityRulesResultList", eli);
        }

        /// <summary>
        /// Send Email.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="programId">The program identifier.</param>
        /// <param name="email">The email identifier.</param>
        /// <returns></returns>
        public void SendEmail(string applicationId, string personId, string programId, string email = "")
        {
            var emailSubject = string.Empty;
            var emailData = PresumptiveEligibilityDAL.GetPepEmailInfo(applicationId, personId);

            // Set subject and status based on conditions.
            if (emailData.AppStatus == 20)  // 20 = Approved.
            {
                emailSubject = "PEP Presumptive Eligibility Approval.";
                emailData.Status = EnrollmentEmailStatusEnum.Approved;
            }
            else if (programId == "4")      // 4 = Already Enrolled.
            {
                emailSubject = "Already Enrolled in Medicaid.";
                emailData.Status = EnrollmentEmailStatusEnum.AlreadyEnrolled;
            }
            else
            {
                return;
            }

            // Override the determiner email, if provided from the UI.
            if (!string.IsNullOrEmpty(email))
            {
                emailData.DeterminerEmail = email;
            }

            Emailer mailer = new Emailer(ProcessorKind.Worker);

            // Renders the pep email partial view.
            string emailBody = RenderView("~/views/EligEnroll/EnrollmentCompleteEmailTemplate.cshtml", emailData, ControllerContext);

            // Call email processor.
            mailer.ProcessEnrollmentComplete(emailData.DeterminerEmail, emailSubject, emailBody, TokenId);

            // Insert Notes.
            NotesDal.AddNote(Convert.ToInt64(applicationId), Convert.ToInt64(personId), enumNoteType.GENERAL_NOTE, "Billable notice emailed to " + emailData.DeterminerEmail, CallingUsername, TokenId);
        }

        /// <summary>
        /// Used to determine if certain UI elements need to be locked down for the application.
        /// This returns true for things like if app is in terminal status, or in re-open, but not admin.
        /// </summary>
        /// <param name="appStatus">The application status.</param>
        /// <returns></returns>
        public static bool CheckForApplicationLockDown(string appStatus)
        {
            // If in terminal status, lock down edits
            if (ApplicationHelper.IsEnrollmentComplete(appStatus))
            {
                return true;
            }
            else
            {
                // If in reopen, lock down for all but admins
                if (appStatus == Cares.Api.Infrastructure.Enums.ApplicationStatus.ReOpen.Id.ToString())
                {
                    if (CaresSecurity.IsInRole(CaresSecurity.Admin) == false)
                    {
                        return true;
                    }
                }
            }

            return false;
        }
    }
}