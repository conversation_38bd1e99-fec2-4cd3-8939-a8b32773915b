
USE [Staging]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_POPULATE_RENEWAL_NORESPONSE_INFORMATICA_REPORT] @ReportingDate DATE = NULL
AS

-- ======================================================================================================
-- Author:		<PERSON><PERSON><PERSON> Kasula
-- Create date: 08/11/2025
-- Description: Generate a Renewal No Response report for the informatica team. 
-- ======================================================================================================


BEGIN
	SET NOCOUNT ON;

SET @ReportingDate = ISNULL(@ReportingDate, GETDATE());



DROP TABLE IF EXISTS #RENEWALS, #NVRA, #LIABILITY, #FORMS, #SPONSOR


----Initial Pull

SELECT 
DISTINCT	AN.APPLICATION_ID,
			AD.PERSON_ID, 
			AN.NOTES_REF_ID,
			AN.NOTES_DATE
INTO		dbo.#RENEWALS	
FROM		Db4_ee.dbo.APPLICATION_NOTES AN WITH(NOLOCK) 
INNER JOIN  Db4_ee.dbo.APPLICATION_NOTES_DETAIL AD WITH(NOLOCK) ON AN.APPLICATION_NOTES_ID = AD.APPLICATION_NOTES_ID
WHERE		NOTES_REF_ID IN (117, 118, 119, 120, 121, 122)
			AND YEAR(AD.CREATED_DATE) = YEAR(@ReportingDate) 
			AND MONTH(AD.CREATED_DATE) = MONTH(@ReportingDate) 

----To get Renwals & No Response

SET ANSI_WARNINGS OFF;

SELECT
        APPLICATION_ID,
        PERSON_ID,
        MAX(CASE WHEN NOTES_REF_ID IN (117,118,119) THEN NOTES_DATE END) AS REVIEW_SENT_DATE,
        MAX(CASE WHEN NOTES_REF_ID IN (120,121,122) THEN NOTES_DATE END) AS TERMINATION_SENT_DATE
    INTO #RENEWALS_NORESPONSE
    FROM #RENEWALS
    GROUP BY APPLICATION_ID, PERSON_ID

SET ANSI_WARNINGS OFF;

-----To get the Liability details

SELECT  DISTINCT R.APPLICATION_ID,
		R.PERSON_ID,
		HAS_TRANSFER_PENALTY AS LIABILITY_BLOCK_INDICATOR
INTO	dbo.#LIABILITY	
FROM	#RENEWALS R
INNER  JOIN	Db4_ee.dbo.PERSON_LIABILITY PL WITH(NOLOCK) ON PL.PERSON_ID = R.PERSON_ID 
			AND PL.ORIGINATING_APPLICATION_ID = R.APPLICATION_ID
			AND pl.LIABILITY_START_DATE <= @ReportingDate
			AND(pl.LIABILITY_END_DATE IS NULL OR pl.LIABILITY_END_DATE >= @ReportingDate)
			AND pl.LIABILITY_START_DATE < ISNULL(pl.LIABILITY_END_DATE, '9999-12-31')  
WHERE  HAS_TRANSFER_PENALTY = 1

------To get NVRA details

SELECT  A.APPLICATION_ID, 
		'Y' AS NVRA
INTO  #NVRA 
FROM  Db4_ee.dbo.APPLICATION A	
INNER JOIN Db4_ee.dbo.APPLICATION_NOTES AN WITH(NOLOCK) ON AN.APPLICATION_ID = A.APPLICATION_ID
WHERE AN.NOTES_REF_ID = 123

-------To get the Forms details

SELECT R.APPLICATION_ID,
	   R.PERSON_ID,
	   CASE WHEN MAX(CASE WHEN EDL.ref_ED_FORM_NAME_ID = 40 THEN 1 ELSE 0 END) = 1 THEN 'Y' ELSE 'N' END AS FORM_214R_SENT,
       CASE WHEN MAX(CASE WHEN EDL.ref_ED_FORM_NAME_ID = 11 THEN 1 ELSE 0 END) = 1 THEN 'Y' ELSE 'N' END AS FORM_240R_SENT,
       CASE WHEN MAX(CASE WHEN EDL.ref_ED_FORM_NAME_ID = 32 THEN 1 ELSE 0 END) = 1 THEN 'Y' ELSE 'N' END AS FORM_225R_SENT
INTO	#FORMS
FROM #RENEWALS R
LEFT JOIN Db4_ee.dbo.ELDERLY_DISABLED_LETTER_FINAL EDL WITH (NOLOCK) ON  R.PERSON_ID = EDL.PERSON_ID
       AND R.APPLICATION_ID = EDL.APPLICATION_ID
GROUP BY	R.APPLICATION_ID, R.PERSON_ID

-------To get Sponsor details

SELECT  R.PERSON_ID,
		CASE WHEN S.SPONSOR_ROLE_ID = 1 AND A.SUB_PROGRAM_CATEGORY_ID IN (7,8)  THEN 'Y'
					  ELSE 'N' END AS NOTICE_TO_SPONSOR
INTO  #SPONSOR
FROM  #RENEWALS R
INNER JOIN Db4_ee.dbo.APPLICATION A	WITH(NOLOCK) ON A.APPLICATION_ID = R.APPLICATION_ID
INNER JOIN Db4_ee.dbo.SPONSOR S		WITH(NOLOCK) ON R.PERSON_ID = S.PERSON_ID
WHERE S.SPONSOR_ROLE_ID = 1

------- Main Pull
	
      MERGE [staging].[dbo].[RENEWAL_NORESPONSE_RECIPIENTS] AS trgt
	  USING
			(
			     SELECT DISTINCT
				 WD.WORKER_COUNTY_NUMBER AS DISTRICT_OFFICE_NUMBER,
				 WD.DO_NAME AS DISTRICT_OFFICE_NAME,
				 WD.WORKER_NUMBER AS REVIEWER_NUMBER,
				 WD.WORKER_FIRST_NAME + ' ' + WD.WORKER_LAST_NAME AS REVIEWER_NAME,
			     ISNULL(EDP.ELDERLY_DISABLED_PROGRAM_CODE, '')  AS PROGRAM_CODE,
				 PD.MEDICAID_IDENTIFICATION_NO AS MEDICAID_ID,
				 P.SSN AS SSN,
				 A.Application_ID AS APPLICATION_ID,
				 P.Person_ID AS PERSON_ID,
				 P.Last_Name AS LAST_NAME,
				 P.First_Name AS FIRST_NAME,
				 LEFT(P.Middle_Name,1) AS MIDDLE_INITIAL,
				 CASE WHEN FORMAT(CAST(@ReportingDate AS DATE), 'MMddyyyy') BETWEEN T.BENEFICIARY_PART_A_THIRD_PARTY_START_DATE
					  AND T.BENEFICIARY_PART_A_THIRD_PARTY_END_DATE THEN 'Y' ELSE 'N' 
					  END AS BUY_IN_STATUS,
				 CASE WHEN L.LIABILITY_BLOCK_INDICATOR = 1  THEN 'Y' ELSE 'N' 				
					  END  AS LIABILITY_BLOCK_INDICATOR,
				 CASE WHEN WR.DUE_DATE <= EOMONTH(@ReportingDate) THEN WRT.WORKER_REMINDER_TYPE_DESCRIPTION ELSE ' ' 
					  END AS WORKER_ALERT_CODE,
				 CASE WHEN WR.DUE_DATE <= EOMONTH(@ReportingDate) THEN WR.DUE_DATE ELSE '' 
				      END AS WORKER_ALERT_DATE,
				 CASE WHEN A.SUB_PROGRAM_CATEGORY_ID = 1 THEN DATEADD(MM, -1, VE.CANCEL_DATE) 
					  ELSE VE.DO_Review_Date END AS REVIEW_DATE, 
				 FORMAT(VE.CANCEL_DATE, 'MMddyyyy') AS TERMINATION_DATE,
				 CASE WHEN VE.IS_REVIEW_RECEIVED = 1  THEN 'Y' ELSE 'N' 				
					  END  AS REVIEW_RECEIVED_IND,
				 S.NOTICE_TO_SPONSOR AS  NOTICE_TO_SPONSOR,
				 CASE WHEN AED.ELDERLY_DISABLED_PROGRAM_ID IN (2,32,33,34,37) AND A.SUB_PROGRAM_CATEGORY_ID = 8 THEN 'Y'
					  ELSE 'N' END AS NOTICE_TO_FACILITY,
				 'PAPER' AS REVIEW_TYPE,
				  CASE WHEN A.SUB_PROGRAM_CATEGORY_ID = 8 THEN 'E&D' 
						WHEN A.SUB_PROGRAM_CATEGORY_ID = 7 THEN 'MSP'
						WHEN A.SUB_PROGRAM_CATEGORY_ID = 1 THEN 'SOBRA'
					END AS  PROGRAM_CATEGORY,
				  PSC.PROGRAM_SUB_CATEGORY_DESC AS AID_CATEGORY,
				  CASE WHEN A.SUB_PROGRAM_CATEGORY_ID = 8 THEN EDP.ELDERLY_DISABLED_PROGRAM_NAME
						ELSE ' ' END AS  [PROGRAM_NAME],
				  F.FORM_214R_SENT AS FORM_214R_SENT,
				  F.FORM_240R_SENT AS FORM_240R_SENT,
				  F.FORM_225R_SENT AS FORM_225R_SENT,
				  NV.NVRA AS NVRA,
				  R.REVIEW_SENT_DATE AS REVIEW_SENT_DATE,
				  R.TERMINATION_SENT_DATE AS TERMINATION_SENT_DATE                       
        FROM #RENEWALS_NORESPONSE R
		INNER JOIN Db4_ee.dbo.APPLICATION A				WITH(NOLOCK) ON A.APPLICATION_ID = R.APPLICATION_ID
		LEFT JOIN #NVRA NV						    WITH(NOLOCK) ON NV.APPLICATION_ID = R.APPLICATION_ID 
		LEFT JOIN #LIABILITY L						WITH(NOLOCK) ON L.APPLICATION_ID = R.APPLICATION_ID AND L.PERSON_ID = R.PERSON_ID
		LEFT JOIN #FORMS F							WITH(NOLOCK) ON F.APPLICATION_ID = R.APPLICATION_ID
		LEFT JOIN Db4_ee.dbo.APPLICATION_ENROLLMENT VE	WITH(NOLOCK) ON R.APPLICATION_ID = VE.APPLICATION_ID AND VE.PERSON_ID = R.PERSON_ID  
        LEFT JOIN Db4_ee.dbo.PERSON P			            WITH(NOLOCK) ON P.Person_ID = R.Person_ID 
		LEFT JOIN Db4_ee.dbo.PERSON_DETAIL PD				WITH(NOLOCK) ON PD.Person_ID = P.Person_ID
		LEFT JOIN Db4_ee.dbo.APPLICATION_ELIGIBILITY VEE	WITH(NOLOCK) ON R.APPLICATION_ID = VEE.APPLICATION_ID AND VEE.PERSON_ID = R.PERSON_ID  		
		LEFT JOIN Db4_ee.dbo.REF_PROGRAM_SUB_CATEGORY PSC  WITH(NOLOCK) ON PSC.PROGRAM_SUB_CATEGORY_ID = VEE.PROGRAM_SUB_CATEGORY_ID
		LEFT JOIN Db4_ee.dbo.APPLICATION_ELDERLY_DISABLED_DETAIL AED WITH(NOLOCK) ON AED.APPLICATION_ID = R.APPLICATION_ID
        LEFT JOIN Db4_ee.dbo.REF_ELDERLY_DISABLED_PROGRAM EDP  WITH(NOLOCK) ON EDP.ELDERLY_DISABLED_PROGRAM_ID = AED.ELDERLY_DISABLED_PROGRAM_ID
		LEFT JOIN Db4_ee.dbo.vw_WORKER_REMINDER WR			WITH(NOLOCK) ON WR.APPLICATION_ID = R.APPLICATION_ID AND WR.PERSON_ID = R.PERSON_ID
		LEFT JOIN Db4_ee.dbo.REF_WORKER_REMINDER_TYPE WRT  WITH(NOLOCK) ON WR.WORKER_REMINDER_TYPE_ID = WRT.WORKER_REMINDER_TYPE_ID
		LEFT JOIN #SPONSOR S						WITH(NOLOCK) ON R.PERSON_ID = S.PERSON_ID
		LEFT JOIN Db4_ee.dbo.TBQ_NOTIFICATION T			WITH(NOLOCK) ON T.STATE_BENEFICIARY_SSN = P.SSN
		CROSS APPLY Db4_ee.dbo.fn_GetWorkerDOInfo(R.APPLICATION_ID) AS WD
    
) AS src
    ON (trgt.APPLICATION_ID = src.APPLICATION_ID AND trgt.PERSON_ID = src.PERSON_ID)

    WHEN MATCHED AND
	(
    ISNULL(CAST(trgt.DISTRICT_OFFICE_NUMBER AS NVARCHAR(20)),'') <> ISNULL(CAST(src.DISTRICT_OFFICE_NUMBER AS NVARCHAR(20)),'')
    OR ISNULL(trgt.DISTRICT_OFFICE_NAME,'')        <> ISNULL(src.DISTRICT_OFFICE_NAME,'')
    OR ISNULL(CAST(trgt.REVIEWER_NUMBER AS NVARCHAR(20)),'') <> ISNULL(CAST(src.REVIEWER_NUMBER AS NVARCHAR(20)),'')
    OR ISNULL(trgt.REVIEWER_NAME,'')               <> ISNULL(src.REVIEWER_NAME,'')
    OR ISNULL(trgt.PROGRAM_CODE,'')                <> ISNULL(src.PROGRAM_CODE,'')
    OR ISNULL(trgt.MEDICAID_ID,'')                 <> ISNULL(src.MEDICAID_ID,'')
    OR ISNULL(trgt.SSN,'')                         <> ISNULL(src.SSN,'')
    OR ISNULL(trgt.LAST_NAME,'')                   <> ISNULL(src.LAST_NAME,'')
    OR ISNULL(trgt.FIRST_NAME,'')                  <> ISNULL(src.FIRST_NAME,'')
    OR ISNULL(trgt.MIDDLE_INITIAL,'')              <> ISNULL(src.MIDDLE_INITIAL,'')
    OR ISNULL(trgt.BUY_IN_STATUS,'')               <> ISNULL(src.BUY_IN_STATUS,'')
    OR ISNULL(trgt.LIABILITY_BLOCK_INDICATOR,'')   <> ISNULL(src.LIABILITY_BLOCK_INDICATOR,'')
    OR ISNULL(trgt.WORKER_ALERT_CODE,'')           <> ISNULL(src.WORKER_ALERT_CODE,'')
    OR ISNULL(CAST(trgt.WORKER_ALERT_DATE AS DATE), '9999-12-31') <> ISNULL(CAST(src.WORKER_ALERT_DATE AS DATE), '9999-12-31')
	OR ISNULL(CAST(trgt.REVIEW_DATE       AS DATE), '9999-12-31') <> ISNULL(CAST(src.REVIEW_DATE       AS DATE), '9999-12-31')
    OR ISNULL(trgt.TERMINATION_DATE, '')           <> ISNULL(src.TERMINATION_DATE, '')    
	OR ISNULL(trgt.REVIEW_RECEIVED_IND,'')         <> ISNULL(src.REVIEW_RECEIVED_IND,'')
    OR ISNULL(trgt.NOTICE_TO_SPONSOR,'')           <> ISNULL(src.NOTICE_TO_SPONSOR,'')
    OR ISNULL(trgt.NOTICE_TO_FACILITY,'')          <> ISNULL(src.NOTICE_TO_FACILITY,'')
    OR ISNULL(trgt.REVIEW_TYPE,'')                 <> ISNULL(src.REVIEW_TYPE,'')
    OR ISNULL(trgt.PROGRAM_CATEGORY,'')            <> ISNULL(src.PROGRAM_CATEGORY,'')
    OR ISNULL(trgt.AID_CATEGORY,'')                <> ISNULL(src.AID_CATEGORY,'')
    OR ISNULL(trgt.[PROGRAM_NAME],'')              <> ISNULL(src.[PROGRAM_NAME],'')
    OR ISNULL(trgt.FORM_214R_SENT,'')              <> ISNULL(src.FORM_214R_SENT,'')
    OR ISNULL(trgt.FORM_240R_SENT,'')              <> ISNULL(src.FORM_240R_SENT,'')
    OR ISNULL(trgt.FORM_225R_SENT,'')              <> ISNULL(src.FORM_225R_SENT,'')
    OR ISNULL(trgt.NVRA,'')                        <> ISNULL(src.NVRA,'')
    OR ISNULL(CAST(trgt.REVIEW_SENT_DATE  AS DATE), '9999-12-31') <> ISNULL(CAST(src.REVIEW_SENT_DATE  AS DATE), '9999-12-31')
	OR ISNULL(CAST(trgt.TERMINATION_SENT_DATE AS DATE), '9999-12-31') <> ISNULL(CAST(src.TERMINATION_SENT_DATE AS DATE), '9999-12-31')
    )
	
	THEN
        UPDATE SET
             trgt.DISTRICT_OFFICE_NUMBER    = src.DISTRICT_OFFICE_NUMBER
            ,trgt.DISTRICT_OFFICE_NAME      = src.DISTRICT_OFFICE_NAME
            ,trgt.REVIEWER_NUMBER           = src.REVIEWER_NUMBER
            ,trgt.REVIEWER_NAME             = src.REVIEWER_NAME
            ,trgt.PROGRAM_CODE              = src.PROGRAM_CODE
            ,trgt.MEDICAID_ID               = src.MEDICAID_ID
            ,trgt.SSN                       = src.SSN
            ,trgt.LAST_NAME                 = src.LAST_NAME
            ,trgt.FIRST_NAME                = src.FIRST_NAME
            ,trgt.MIDDLE_INITIAL            = src.MIDDLE_INITIAL
            ,trgt.BUY_IN_STATUS             = src.BUY_IN_STATUS
            ,trgt.LIABILITY_BLOCK_INDICATOR = src.LIABILITY_BLOCK_INDICATOR
            ,trgt.WORKER_ALERT_CODE         = src.WORKER_ALERT_CODE
            ,trgt.WORKER_ALERT_DATE         = src.WORKER_ALERT_DATE
            ,trgt.REVIEW_DATE               = src.REVIEW_DATE
            ,trgt.TERMINATION_DATE          = src.TERMINATION_DATE
            ,trgt.REVIEW_RECEIVED_IND       = src.REVIEW_RECEIVED_IND
            ,trgt.NOTICE_TO_SPONSOR         = src.NOTICE_TO_SPONSOR
            ,trgt.NOTICE_TO_FACILITY        = src.NOTICE_TO_FACILITY
            ,trgt.REVIEW_TYPE               = src.REVIEW_TYPE
            ,trgt.PROGRAM_CATEGORY          = src.PROGRAM_CATEGORY
            ,trgt.AID_CATEGORY              = src.AID_CATEGORY
            ,trgt.[PROGRAM_NAME]            = src.[PROGRAM_NAME]
            ,trgt.FORM_214R_SENT            = src.FORM_214R_SENT
            ,trgt.FORM_240R_SENT            = src.FORM_240R_SENT
            ,trgt.FORM_225R_SENT            = src.FORM_225R_SENT
            ,trgt.NVRA                      = src.NVRA
            ,trgt.REVIEW_SENT_DATE          = src.REVIEW_SENT_DATE
            ,trgt.TERMINATION_SENT_DATE     = src.TERMINATION_SENT_DATE
            ,trgt.UPDATED_DATE              = CAST(GETDATE() AS DATE)

    WHEN NOT MATCHED BY TARGET THEN
        INSERT (
             [DISTRICT_OFFICE_NUMBER]
            ,[DISTRICT_OFFICE_NAME]
            ,[REVIEWER_NUMBER]
            ,[REVIEWER_NAME]
            ,[PROGRAM_CODE]
            ,[MEDICAID_ID]
            ,[SSN]
            ,[APPLICATION_ID]
            ,[PERSON_ID]
            ,[LAST_NAME]
            ,[FIRST_NAME]
            ,[MIDDLE_INITIAL]
            ,[BUY_IN_STATUS]
            ,[LIABILITY_BLOCK_INDICATOR]
            ,[WORKER_ALERT_CODE]
            ,[WORKER_ALERT_DATE]
            ,[REVIEW_DATE]
            ,[TERMINATION_DATE]
            ,[REVIEW_RECEIVED_IND]
            ,[NOTICE_TO_SPONSOR]
            ,[NOTICE_TO_FACILITY]
            ,[REVIEW_TYPE]
            ,[PROGRAM_CATEGORY]
            ,[AID_CATEGORY]
            ,[PROGRAM_NAME]
            ,[FORM_214R_SENT]
            ,[FORM_240R_SENT]
            ,[FORM_225R_SENT]
            ,[NVRA]
            ,[REVIEW_SENT_DATE]
            ,[TERMINATION_SENT_DATE]
            ,[CREATED_DATE]
            ,[UPDATED_DATE]
        )
        VALUES (
             src.DISTRICT_OFFICE_NUMBER
            ,src.DISTRICT_OFFICE_NAME
            ,src.REVIEWER_NUMBER
            ,src.REVIEWER_NAME
            ,src.PROGRAM_CODE
            ,src.MEDICAID_ID
            ,src.SSN
            ,src.APPLICATION_ID
            ,src.PERSON_ID
            ,src.LAST_NAME
            ,src.FIRST_NAME
            ,src.MIDDLE_INITIAL
            ,src.BUY_IN_STATUS
            ,src.LIABILITY_BLOCK_INDICATOR
            ,src.WORKER_ALERT_CODE
            ,src.WORKER_ALERT_DATE
            ,src.REVIEW_DATE
            ,src.TERMINATION_DATE
            ,src.REVIEW_RECEIVED_IND
            ,src.NOTICE_TO_SPONSOR
            ,src.NOTICE_TO_FACILITY
            ,src.REVIEW_TYPE
            ,src.PROGRAM_CATEGORY
            ,src.AID_CATEGORY
            ,src.[PROGRAM_NAME]
            ,src.FORM_214R_SENT
            ,src.FORM_240R_SENT
            ,src.FORM_225R_SENT
            ,src.NVRA
            ,src.REVIEW_SENT_DATE
            ,src.TERMINATION_SENT_DATE
            ,CAST(GETDATE() AS DATE)
            ,CAST(GETDATE() AS DATE)
        );
END
GO