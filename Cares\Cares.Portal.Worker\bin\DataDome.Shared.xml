<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DataDome.Shared</name>
    </assembly>
    <members>
        <member name="T:DataDome.Api.ApiFields">
            <summary>
            Names of the form fields those should be sent to the API.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.Key">
            <summary>
            The customer key defined in the settings (license key).
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.RequestModuleName">
            <summary>
            Name of the connector application / module, that processes requests.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.ModuleVersion">
            <summary>
            Version of the connector application/module, that processes requests.
            </summary>
            <remarks>
            Version should be in form of "Major.Minor", e.g. "2.1".
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.Request">
            <summary>
            This is a path and query part of the Request-URI.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec5.html#sec5.1.2">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec5.html#sec5.1.2
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.UserAgent">
            <summary>
            The User-Agent request-header field, containing information about the user agent.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.43">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.43
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.Ip">
            <summary>
            IP address of the host originating the request.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.Port">
            <summary>
            IP port of the TCP/IP connection originating the request (from the final client).
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.Host">
            <summary>
            The Host request-header field specifies the Internet host.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.23">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.23
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.ServerHostname">
            <summary>
            The virtualhost.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.Referer">
            <summary>
            The Referer request-header field allows that the client to specify.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.36">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.36
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.Protocol">
            <summary>
            This is a scheme part of the Request-URI (HTTP or HTTPS)
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec5.html#sec5.1.2">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec5.html#sec5.1.2
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.TimeRequest">
            <summary>
            This microtimestamp at the hit request.
            </summary>
            <remarks>
            This is a difference between current time and epoch (1970-01-01 00:00:00.000000)
            in microseconds.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.HeadersList">
            <summary>
            List all headers name sent by the client, in received order, comma separated.
            (ex : Host,Connection,Pragma,Cookie,Cache-Control,User-Agent).
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.Accept">
            <summary>
            The Accept request-header field. Can be used to specify certain media types which are
            acceptable for the response.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.1">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.1
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.AcceptEncoding">
            <summary>
            The Accept-Encoding request-header field. Is similar to Accept, but restricts the
            content-codings that are acceptable in the response.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.3">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.3
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.AcceptLanguage">
            <summary>
            The Accept-Language request-header field. Is similar to Accept, but restricts the
            set of natural languages that are preferred as a response to the request.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.4">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.4
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.AcceptCharset">
            <summary>
            The Accept-Charset request-header field. Can be used to indicate what character sets
            are acceptable for the response.
            </summary>
            <remarks>
            <see href="https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.2">
            RFC https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.2
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.Origin">
            <summary>
            Origin header.
            </summary>
            <remarks>
            The Origin request header indicates where a fetch originates from. It doesn't include
            any path information, but only the server name. It is sent with CORS requests, as well
            as with POST requests. It is similar to the Referer header, but, unlike this header,
            it doesn't disclose the whole path.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.XForwardedForIP">
            <summary>
            HTTP extension header field that allows is used to disclose information about the
            client that initiated the request and subsequent proxies in a chain of proxies.
            </summary>
            <remarks>
            Actually this header field is descibed by 
            <see href="http://tools.ietf.org/html/rfc7239#section-5.2">
            RFC http://tools.ietf.org/html/rfc7239#section-5.2 </see>. But there is a most widely
            used non-standard version of this field called "X-Forwarded-For".
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.XRequestedWith">
            <summary>
            Mainly used to identify Ajax requests. Most JavaScript frameworks send this field with
            value of XMLHttpRequest.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.Connection">
            <summary>
            The Connection general header controls whether or not the network connection stays
            open after the current transaction finishes. If the value sent is keep-alive, the
            connection is persistent and not closed, allowing for subsequent requests to the same
            server to be done.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.Pragma">
            <summary>
            The Pragma HTTP/1.0 general header is an implementation-specific header that may have
            various effects along the request-response chain. It is used for backwards
            compatibility with HTTP/1.0 caches where the Cache-Control HTTP/1.1 header is not yet
            present.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.CacheControl">
            <summary>
            The Pragma HTTP/1.0 general header is an implementation-specific header that may have
            various effects along the request-response chain. It is used for backwards
            compatibility with HTTP/1.0 caches where the Cache-Control HTTP/1.1 header is not yet
            present.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.ServerName">
            <summary>
            Machine name of the request accepting server.
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.ApiConnectionState">
            <summary>
            Diagnostic information about network connection used to send the API request.
            </summary>
            <remarks>
            Possible states are:
            <list type="bullet">
            <item>
                <term>Reused</term>
                <description>Already existing connection in the pool was reused.</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.ClientID">
            <summary>
            Custom client identifier.
            </summary>
            <remarks>
            <para>
            This identifier is taken from cookies or generated automatically. Generated identifier
            is Base64-encoded buffer containing these uint values:
            </para>
            <list type="bullet">
            <item>
                <term>0-3 (128 bits)</term>
                <description>Remote IP (IPv4 with right padding or IPv6)</description>
            </item>
            <item>
                <term>4 (32 bits)</term>
                <description>Remote port.</description>
            </item>
            <item>
                <term>5-8 (128 bits)</term>
                <description>Local IP (IPv4 with right padding or IPv6)</description>
            </item>
            <item>
                <term>9 (32 bits)</term>
                <description>Local port.</description>
            </item>
            <item>
                <term>10 (32 bits)</term>
                <description>Timestamp (UNIX time - milliseconds from 1970-01-01).</description>
            </item>
            <item>
                <term>11 (32 bits)</term>
                <description>Random number.</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.Method">
            <summary>
            HTTP method (GET/POST/OPTION).
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.PostParamLen">
            <summary>
            The length of the PostParam string (first chunk only).
            </summary>
        </member>
        <member name="F:DataDome.Api.ApiFields.CookiesLen">
            <summary>
            The length of the cookie string.
            </summary>
            <remarks>
            <see href="https://tools.ietf.org/html/rfc6265#section-5.4">
            RFC https://tools.ietf.org/html/rfc6265#section-5.4
            </see>.
            </remarks>
        </member>
        <member name="F:DataDome.Api.ApiFields.AuthorizationLen">
            <summary>
            The length of the Authorisation string
            </summary>
        </member>
        <member name="T:DataDome.Api.ApiResult">
            <summary>
            API response described.
            </summary>
        </member>
        <member name="P:DataDome.Api.ApiResult.StatusCode">
            <summary>
            HTTP status code returned by API server.
            </summary>
        </member>
        <member name="P:DataDome.Api.ApiResult.Description">
            <summary>
            Status description returned by API server.
            </summary>
        </member>
        <member name="P:DataDome.Api.ApiResult.Headers">
            <summary>
            Additional API Server response information.
            </summary>
        </member>
        <member name="P:DataDome.Api.ApiResult.RequestHeaders">
            <summary>
            Additional API Server request information.
            </summary>
        </member>
        <member name="P:DataDome.Api.ApiResult.Content">
            <summary>
            Addition response content from API server.
            </summary>
        </member>
        <member name="P:DataDome.Api.ApiResult.IsRewrite">
            <summary>
            If this property returns true, then incoming request should be rewritten using
            <see cref="P:DataDome.Api.ApiResult.StatusCode"/> and <see cref="P:DataDome.Api.ApiResult.Description"/> we have in the result.
            </summary>
        </member>
        <member name="M:DataDome.Api.ApiResult.ToString">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Api.IDictionaryExtensions">
            <summary>
            Useful method extensions to for dictionaries.
            </summary>
        </member>
        <member name="M:DataDome.Api.IDictionaryExtensions.WriteForm(System.Collections.Generic.IDictionary{System.String,System.String},System.IO.Stream,System.Text.Encoding)">
            <summary>
            Render dictionary to the stream. Encoded as HTTP form (key1=value1&amp;key2=value2).
            </summary>
            <param name="dictionary">
            Dictionary to be rendered.
            </param>
            <param name="stream">
            Target stream.
            </param>
            <param name="encoding">
            Text encoding.
            </param>
        </member>
        <member name="T:DataDome.Api.RequestHelper">
            <summary>
            Helpful tools to collect data for API request.
            </summary>
        </member>
        <member name="T:DataDome.Api.IApiService">
            <summary>
            This interface is responsible for communication with API server.
            </summary>
            <remarks>
            It can be replaced with some stub for testing purposes. Or may be clustered implementation,
            that will choose API server depends on some circumstances.
            </remarks>
        </member>
        <member name="M:DataDome.Api.IApiService.Request(DataDome.Web.IConnectorRequest)">
            <summary>
            Request processing routine.
            </summary>
            <param name="request">
            Request to be handled by Connector and dispatched to API server.
            </param>
            <returns>
            Returns the <see cref="T:DataDome.Api.ApiResult">result</see> of the request processing by API server.
            </returns>
        </member>
        <member name="T:DataDome.Api.RequestRules">
            <summary>
            This <see cref="T:System.Collections.Generic.IDictionary`2"/> implementation is responsible for
            preliminary filtering of API request value.
            </summary>
            <remarks>
            It skips null values, excluded keys, trim data with extra length.
            </remarks>
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#IDictionary{System#String,System#String}#Add(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#IDictionary{System#String,System#String}#ContainsKey(System.String)">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Api.RequestRules.System#Collections#Generic#IDictionary{System#String,System#String}#Keys">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#IDictionary{System#String,System#String}#Remove(System.String)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#IDictionary{System#String,System#String}#TryGetValue(System.String,System.String@)">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Api.RequestRules.System#Collections#Generic#IDictionary{System#String,System#String}#Values">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Api.RequestRules.System#Collections#Generic#IDictionary{System#String,System#String}#Item(System.String)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String,System#String}}#Add(System.Collections.Generic.KeyValuePair{System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String,System#String}}#Clear">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String,System#String}}#Contains(System.Collections.Generic.KeyValuePair{System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String,System#String}}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.String}[],System.Int32)">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Api.RequestRules.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String,System#String}}#Count">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Api.RequestRules.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String,System#String}}#IsReadOnly">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String,System#String}}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,System#String}}#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Api.RequestRules.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Api.WebRequestApiService">
            <summary>
            Implementation for <see cref="T:DataDome.Api.IApiService"/> interface that is sending HTTP requests to
            API server as it is expected for main implementation. But also works for test API server
            (in DataDome.Web.Api project).
            </summary>
        </member>
        <member name="M:DataDome.Api.WebRequestApiService.ConfigureServicePoint">
            <summary>
            Configure <see cref="T:System.Net.ServicePoint"/> that is used too handle and anage pool of
            <see cref="T:System.Net.HttpWebRequest">requests</see> to some particular endpoint (API Server URL).
            </summary>
        </member>
        <member name="M:DataDome.Api.WebRequestApiService.CreateRequest">
            <summary>
            Configure HTTP request.
            </summary>
            <returns>
            Returns configured <see cref="T:System.Net.HttpWebRequest">HTTP request</see>.
            </returns>
        </member>
        <member name="M:DataDome.Api.WebRequestApiService.CollectXForwardedFor(System.Collections.Generic.IDictionary{System.String,System.String},DataDome.Web.IConnectorRequest)">
            <summary>
            Apply custom filtering rules for data that will be send to API server.
            If X-Forwarded contains same IP as remote endpoint, we can skip it.
            </summary>
            <param name="dictionary">
            Dictionary with data collected.
            </param>
            <param name="request"></param>
        </member>
        <member name="M:DataDome.Api.WebRequestApiService.CollectData(DataDome.Web.IConnectorRequest)">
            <summary>
            Collect data that should be send to API server.
            </summary>
            <param name="request">
            HTTP request that was intercepted by connector.
            </param>
            <returns>
            Returns dictionary of collected values.
            </returns>
        </member>
        <member name="M:DataDome.Api.WebRequestApiService.DataDome#Api#IApiService#Request(DataDome.Web.IConnectorRequest)">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Configuration.IConnectorSettings">
            <summary>
            This is a basic interface to get connector's settings.
            </summary>
            <remarks>
            We can implement this interface in a different ways. For exaple, it can be defined in code
            by using <see cref="T:DataDome.Configuration.ManualConnectorSettings"/> implementation. Or settings may be stored
            in &lt;appSettings&gt; section of a web.config file, like in
            <see cref="T:DataDome.Configuration.WebConfigConnectorSettings"/>. May be sometimes we decide to take
            configuration from API server.
            </remarks>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.ApiUrl">
            <summary>
            API endpoint <see cref="T:System.Uri">URL</see>.
            </summary>
            <remarks>
            All collected data will be send to this API URL.
            </remarks>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.ApiDomain">
            <summary>
            API endpoint <see cref="T:System.String">Domain</see>.
            </summary>
            <remarks>
            All collected data will be send with this Domain.
            </remarks>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.ApiProtocol">
            <summary>
            API endpoint <see cref="T:System.String">Protocol</see>.
            </summary>
            <remarks>
            All collected data will be send with this protocol.
            </remarks>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.Timeout">
            <summary>
            Timeout to wait for API connection.
            </summary>
            <remarks>
            If API request exceeds this timeout, it should pass a request to main site.
            </remarks>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.Pattern">
            <summary>
            Request matching pattern.
            </summary>
            <remarks>
            Only requests with matched URLs should be delegated to API for verification.
            </remarks>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.ExclusionPattern">
            <summary>
            Returns true if pattern is inclusion regex
            </summary>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.LicenseKey">
            <summary>
            License key.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.CookieName">
            <summary>
            Cookie name.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.DataExclude">
            <summary>
            Parameters to be excluded from sending to API Server.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.TraceOutput">
            <summary>
            True if tracing output is enabled.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.IConnectorSettings.ServerName">
            <summary>
            Get executing server's name.
            </summary>
        </member>
        <member name="M:DataDome.Configuration.IConnectorSettings.GetTimestamp">
            <summary>
            Get current UTC timestamp.
            </summary>
            <returns>
            Returns current UTC timestamp.
            </returns>
        </member>
        <member name="T:DataDome.Configuration.InvalidSettingException">
            <summary>
            This exception is thrown when some Connector's setting is wrong.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.InvalidSettingException.Name">
            <summary>
            Name of the setting that caused exception.
            </summary>
        </member>
        <member name="M:DataDome.Configuration.InvalidSettingException.#ctor(System.String,System.Exception)">
            <summary>
            Construct exception.
            </summary>
            <param name="name">
            <see cref="P:DataDome.Configuration.InvalidSettingException.Name"/> value.
            </param>
            <param name="innerException">
            Inner exception that describes, what kind of error happened.
            </param>
        </member>
        <member name="T:DataDome.Configuration.ManualConnectorSettings">
            <summary>
            This implementation for <see cref="T:DataDome.Configuration.IConnectorSettings"/> allows to manage Connector's
            settings manually in code.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.ApiUrl">
            <summary>
            
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.ApiUrl"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.ApiProtocol">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.ApiProtocol"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.ApiDomain">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.ApiDomain"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.Timeout">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.Timeout"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.Pattern">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.Pattern"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.ExclusionPattern">
            <summary>
            Returns true if pattern is inclusion regex
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.LicenseKey">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.LicenseKey"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.CookieName">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.CookieName"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataExclude">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.DataExclude"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.TraceOutput">
            <summary>
            Property to store manually managed <see cref="P:DataDome.Configuration.IConnectorSettings.TraceOutput"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.ServerName">
            <summary>
            Property to store server name <see cref="P:DataDome.Configuration.IConnectorSettings.ServerName"/>.
            </summary>
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#ApiUrl">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#Timeout">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#Pattern">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#LicenseKey">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#CookieName">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#DataExclude">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#TraceOutput">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#ServerName">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Configuration.ManualConnectorSettings.DataDome#Configuration#IConnectorSettings#GetTimestamp">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Configuration.WebConfigConnectorSettings">
            <summary>
            This implementation of <see cref="T:DataDome.Configuration.IConnectorSettings"/> interface is responsible for
            peeking settings from &lt;appSettings&gt; section of the web.config file.
            </summary>
        </member>
        <member name="M:DataDome.Configuration.WebConfigConnectorSettings.GetKeyName(System.String)">
            <summary>
            Get full key name of the setting.
            </summary>
            <param name="name">
            Local name.
            </param>
            <remarks>
            To distinguish application settings for different application modules, it's a good
            practice to use prefixes, for instance full key name "connector:api-url", where
            "connector" is a prefix and "api-url" is a setting name.
            </remarks>
            <returns>
            Returns full key name.
            </returns>
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#ApiUrl">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#ApiDomain">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#ApiProtocol">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#Timeout">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#Pattern">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#ExclusionPattern">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#LicenseKey">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#CookieName">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#DataExclude">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#TraceOutput">
            <ineritdoc />
        </member>
        <member name="P:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#ServerName">
            <ineritdoc />
        </member>
        <member name="M:DataDome.Configuration.WebConfigConnectorSettings.DataDome#Configuration#IConnectorSettings#GetTimestamp">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Configuration.WebConfigConnectorSettings.#ctor(System.Collections.Specialized.NameValueCollection,System.String)">
            <summary>
            Construct.
            </summary>
            <param name="settings">
            Setting dictionary.
            </param>
            <param name="prefix">
            Settings prefix, more details <see cref="M:DataDome.Configuration.WebConfigConnectorSettings.GetKeyName(System.String)">here</see>.
            </param>
        </member>
        <member name="M:DataDome.Configuration.WebConfigConnectorSettings.#ctor(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Construct using default prefix.
            </summary>
            <param name="settings">
            Setting dictionary.
            </param>
        </member>
        <member name="M:DataDome.Configuration.WebConfigConnectorSettings.#ctor">
            <summary>
            Construct using default prefix and default settings source.
            </summary>
        </member>
        <member name="T:DataDome.Diagnostics.DebugStream">
            <summary>
            Proxy stream using for debugging purposes. It can be used to wrap another stream and
            collect data written to it.
            </summary>
        </member>
        <member name="P:DataDome.Diagnostics.DebugStream.CanRead">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Diagnostics.DebugStream.CanSeek">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Diagnostics.DebugStream.CanWrite">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Diagnostics.DebugStream.Length">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Diagnostics.DebugStream.Flush">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Diagnostics.DebugStream.Position">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Diagnostics.DebugStream.Read(System.Byte[],System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Diagnostics.DebugStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Diagnostics.DebugStream.SetLength(System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Diagnostics.DebugStream.Write(System.Byte[],System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Diagnostics.LogSource">
            <summary>
            Custom trace source.
            </summary>
            <remarks>
            <para>
            .NET diagnostics ia a bit tricky, so to simplify its usage we created custom trace source
            with some useful tools.
            </para>
            <para>
            To use this source just create its static instance:
            </para>
            <example>
            private static readonly LogSource _Log = new LogSource();
            </example>
            </remarks>
        </member>
        <member name="T:DataDome.Web.RegexUrlExcludeMatcher">
            <summary>
            Default matching implementation for <see cref="T:DataDome.Web.IRequestExcludeMatcher"/> interface.
            </summary>
        </member>
        <member name="M:DataDome.Web.RegexUrlExcludeMatcher.DataDome#Web#IRequestExcludeMatcher#ExcludeMatch(DataDome.Web.IConnectorRequest)">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Web.EmptyRequestMatcher">
            <summary>
            Request <see cref="T:DataDome.Web.IRequestMatcher">matching</see> implementation that always return true.
            </summary>
        </member>
        <member name="M:DataDome.Web.EmptyRequestMatcher.DataDome#Web#IRequestMatcher#Match(DataDome.Web.IConnectorRequest)">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Web.HttpWebResponseExtensions">
            <summary>
            Useful method extensions for <see cref="T:System.Net.HttpWebResponse"/>.
            </summary>
        </member>
        <member name="T:DataDome.Web.IConnectorRequest">
            <summary>
            This interface is responsible for unification of HTTP Modules, OWIN and probably other
            implementations if requests, handled by connector.
            </summary>
        </member>
        <member name="P:DataDome.Web.IConnectorRequest.Id">
            <summary>
            Unique request identifier.
            </summary>
        </member>
        <member name="P:DataDome.Web.IConnectorRequest.ClientId">
            <summary>
            <see cref="P:DataDome.Web.IConnectorRequest.ClientId">Client identifier</see>.
            </summary>
        </member>
        <member name="P:DataDome.Web.IConnectorRequest.Url">
            <summary>
            Requested URL.
            </summary>
        </member>
        <member name="P:DataDome.Web.IConnectorRequest.Local">
            <summary>
            Local requested endpoint.
            </summary>
        </member>
        <member name="P:DataDome.Web.IConnectorRequest.Remote">
            <summary>
            Remote request endpoint.
            </summary>
        </member>
        <member name="P:DataDome.Web.IConnectorRequest.XForwardedFor">
            <summary>
            X Forwarded For
            </summary>
        </member>
        <member name="M:DataDome.Web.IConnectorRequest.GetCookie(System.String)">
            <summary>
            Get cookie value.
            </summary>
            <param name="name">
            Cookie name.
            </param>
            <returns>
            Returns cookie.
            </returns>
        </member>
        <member name="M:DataDome.Web.IConnectorRequest.SetCookie(System.Web.HttpCookie)">
            <summary>
            Set cookie.
            </summary>
            <param name="cookie">
            Cookie to be added.
            </param>
        </member>
        <member name="M:DataDome.Web.IConnectorRequest.Collect(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Collect data from request into dictionary,
            </summary>
            <param name="dictionary">
            Dictionary to collect request data into.
            </param>
        </member>
        <member name="M:DataDome.Web.IConnectorRequest.Dump">
            <summary>
            Dump request for debugging purposes.
            </summary>
            <returns>
            Returns string representation of the dump.
            </returns>
        </member>
        <member name="T:DataDome.Web.IRequestExcludeMatcher">
            <summary>
            This interface is responsible for request matching. Only request, which conform
            maching algorightm allowed to be delegated to API server.
            </summary>
            <remarks>
            This interface matches a whole HTTP request, so it can be suitable to check URL/MIME or
            any other part of HTTP request.
            </remarks>
        </member>
        <member name="M:DataDome.Web.IRequestExcludeMatcher.ExcludeMatch(DataDome.Web.IConnectorRequest)">
            <summary>
            Matching routine.
            </summary>
            <param name="request">
            <see cref="T:DataDome.Web.IConnectorRequest">Request</see> to be checked for unmatching using algorithm
            implemented by particular matcher.
            </param>
            <returns>
            Return true if request unconforms to internal matching conditions.
            </returns>
        </member>
        <member name="T:DataDome.Web.IRequestMatcher">
            <summary>
            This interface is responsible for request matching. Only request, which conform
            maching algorightm allowed to be delegated to API server.
            </summary>
            <remarks>
            This interface matches a whole HTTP request, so it can be suitable to check URL/MIME or
            any other part of HTTP request.
            </remarks>
        </member>
        <member name="M:DataDome.Web.IRequestMatcher.Match(DataDome.Web.IConnectorRequest)">
            <summary>
            Matching routine.
            </summary>
            <param name="request">
            <see cref="T:DataDome.Web.IConnectorRequest">Request</see> to be checked for matching using algorithm
            implemented by particular matcher.
            </param>
            <returns>
            Return true if request conforms to internal matching conditions.
            </returns>
        </member>
        <member name="T:DataDome.Web.NetHelper">
            <summary>
            Useful network-related tools.
            </summary>
        </member>
        <member name="M:DataDome.Web.NetHelper.ParseEndpoint(System.String,System.Nullable{System.Int32})">
            <summary>
            Parse <see cref="T:System.Net.IPEndPoint"/> class from string representation of IP address.
            </summary>
            <param name="address">
            IP Address.
            </param>
            <param name="port">
            Port.
            </param>
            <returns>
            Returns <see cref="T:System.Net.IPEndPoint"/> instance.
            </returns>
        </member>
        <member name="M:DataDome.Web.NetHelper.ParseEndpoint(System.String,System.String)">
            <summary>
            Parse <see cref="T:System.Net.IPEndPoint"/> class from string representation of IP address and port.
            </summary>
            <param name="address">
            IP Address.
            </param>
            <param name="port">
            Port.
            </param>
            <returns>
            Returns <see cref="T:System.Net.IPEndPoint"/> instance.
            </returns>
        </member>
        <member name="M:DataDome.Web.NetHelper.RemoveForwardedIp(System.String,System.String)">
            <summary>
            This helper method removes client's IP from proxy chain if it exists.
            </summary>
            <param name="forwarded">
            Host header values that contains chain information.
            Should not be url encoded
            </param>
            <param name="client">
            Client IP address.
            </param>
            <returns>
            Returns result.
            </returns>
        </member>
        <member name="T:DataDome.Web.RegexUrlMatcher">
            <summary>
            Default matching implementation for <see cref="T:DataDome.Web.IRequestMatcher"/> interface.
            </summary>
        </member>
        <member name="M:DataDome.Web.RegexUrlMatcher.DataDome#Web#IRequestMatcher#Match(DataDome.Web.IConnectorRequest)">
            <inheritdoc />
        </member>
    </members>
</doc>
