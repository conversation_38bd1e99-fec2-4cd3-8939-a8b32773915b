﻿using Cares.Api.Infrastructure;
using Cares.Api.Infrastructure.WebAPI;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Api.Messages.Representatives;
using System;
using System.Threading.Tasks;

namespace Cares.Api.ElderlyDisabled
{
    public class ElderlyDisabledApi : CaresApiBase
    {
        /// <summary>
        /// Constructor. It should only call AppTier, configure it for that
        /// </summary>
        public ElderlyDisabledApi() : base(ProcessorKind.AppTier) { }

        /// <summary>
        /// Gets an E&D application DTO by app Id
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledApplicationDto> GetApplication(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledApplicationDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetApplication, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves an E&D application
        /// </summary>
        /// <param name="app">The app.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SaveApplication(ElderlyDisabledApplicationDto app, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledApplicationDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_SaveApplication, parameters, app, tokenId, username);
            return result;
        }

        /// <summary>
        /// Gets Spouse data
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledSpouseDto> GetSpouseInfo(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledSpouseDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetSpouseInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Gets Eligibility Enrollment for Elderly Disabled
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledEligibilityDeterminationsDto> GetElderlyDisabledEligibilityEnrollment(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledEligibilityDeterminationsDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetElderlyDisabledEligibilityEnrollment, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves Spouse data
        /// </summary>
        /// <param name="spouseData">The spouse data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SaveSpouseInfo(ElderlyDisabledSpouseDto spouseData, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledSpouseDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_SaveSpouseInfo, parameters, spouseData, tokenId, username);
            return result;
        }

        /// <summary>
        /// Gets Household Members
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledHouseholdMembersDto> GetHouseholdMembers(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledHouseholdMembersDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetHouseholdMembers, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves household info
        /// </summary>
        /// <param name="householdData">The household data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveHouseholdMembers(ElderlyDisabledHouseholdMembersDto householdData, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledHouseholdMembersDto, BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_SaveHouseholdMembers, parameters, householdData, tokenId, username);
            return result;
        }

        /// <summary>
        /// Deletes household member
        /// </summary>
        /// <param name="applicationNonMagiNoSsnPersonId">The application non-magi no ssn person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> DeleteHouseholdMember(int applicationNonMagiNoSsnPersonId, string username, Guid tokenId)
        {
            string parameters = $"?applicationNonMagiNoSsnPersonId={applicationNonMagiNoSsnPersonId}&username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierGet<BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_DeleteHouseholdMember, parameters, tokenId, username);
            return result;
        }

        /// <summary>
        /// Gets property info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledPropertyDto> GetPropertyInformation(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledPropertyDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetPropertyInformation, parameters, tokenId, username);
        }

        /// <summary>
        /// Save Property Info
        /// </summary>
        /// <param name="propertyInfo">The property information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SavePropertyInformation(ElderlyDisabledPropertyDto propertyInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledPropertyDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_SavePropertyInformation, parameters, propertyInfo, tokenId, username);
            return result;
        }

        /// <summary>
        /// Gets resource info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledResourceDto> GetResourceInformation(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledResourceDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetResourceInformation, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves resource info.
        /// </summary>
        /// <param name="resourceData">The resource data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveResourceInformation(ElderlyDisabledResourceDto resourceData, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledResourceDto, BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_SaveResourceInformation, parameters, resourceData, tokenId, username);
            return result;
        }

        /// <summary>
        /// Getting medical insurance information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledMedicalInsuranceDto> GetMedicalInsuranceInfo(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledMedicalInsuranceDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetMedicalInsuranceInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Save medical insurance information
        /// </summary>
        /// <param name="medicalInsuranceInfo">The medical insurance info.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveMedicalInsuranceInfo(ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            return await ProcessAppTierPost<ElderlyDisabledMedicalInsuranceDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_SaveMedicalInsuranceInfo, parameters, medicalInsuranceInfo, tokenId, username);
        }

        /// <summary>
        /// Gets E&D Veteran info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledVeteranDto> GetVeteranDetails(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledVeteranDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetVeteranDetails, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves E&D Veteran info
        /// </summary>
        /// <param name="veteranInfo">The veteran info.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SaveElderlyDisabledVeteranInfo(ElderlyDisabledVeteranDto veteranInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            return await ProcessAppTierPost<ElderlyDisabledVeteranDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_SaveElderlyDisabledVeteranInfo, parameters, veteranInfo, tokenId, username);
        }

        /// <summary>
        /// Gets Life Insurance Info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledLifeInsuranceDto> GetLifeInsuranceInfo(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledLifeInsuranceDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetLifeInsuranceInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves Life Insurance Info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveLifeInsuranceInfo(ElderlyDisabledLifeInsuranceDto applicationInsuranceInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            return await ProcessAppTierPost<ElderlyDisabledLifeInsuranceDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_SaveLifeInsuranceInfo, parameters, applicationInsuranceInfo, tokenId, username);
        }

        /// <summary>
        /// Gets personal property info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledPersonalPropertyDto> GetPersonalPropertyInfo(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledPersonalPropertyDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetPersonalPropertyInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves personal property info.
        /// </summary>
        /// <param name="personalPropertyData">The personal property data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SavePersonalPropertyInfo(ElderlyDisabledPersonalPropertyDto personalPropertyData, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledPersonalPropertyDto, BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_SavePersonalPropertyInfo, parameters, personalPropertyData, tokenId, username);
            return result;
        }

        /// <summary>
        ///  Auth rep save
        /// </summary>
        /// <param name="representativeData">The representative data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveRepresentativeInfo(RepresentativeInfo representativeData, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<RepresentativeInfo, BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledUserApi_SaveRepresentativeInfo,
                parameters, representativeData, tokenId, username);
            return result;
        }

        /// <summary>
        /// Get auth rep info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<RepresentativeInfo> GetRepresentativeInfo(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<RepresentativeInfo>(AppTierWebServiceNames.ElderlyDisabledUserApi_GetRepresentativeInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Gets the application information bar.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledApplicationInfoBarDto> GetApplicationInfoBar(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledApplicationInfoBarDto>(AppTierWebServiceNames.ElderlyDisabledUserApi_GetAppInfoBarInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Gets person liability info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledLiabilityDto> GetPersonLiabilityInfo(long applicationId, long personId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&personId={personId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledLiabilityDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetPersonLiabilityInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Upsert Liability Test data
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiResponse> UpsertLiabilityTestData(ElderlyDisabledLiabilityDto personLiabilityInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            return await ProcessAppTierPost<BaseApiResponse, ElderlyDisabledLiabilityDto>(AppTierWebServiceNames.ElderlyDisabledApi_UpsertLiabilityTestData, parameters, personLiabilityInfo, tokenId, username);
        }

        /// <summary>
        /// Upsert on a single liability segment
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<ElderlyDisabledLiabilityDetailDto> UpsertLiabilitySegment(ElderlyDisabledLiabilityDetailDto personLiabilityInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            return await ProcessAppTierPost<ElderlyDisabledLiabilityDetailDto, ElderlyDisabledLiabilityDetailDto>(AppTierWebServiceNames.ElderlyDisabledApi_UpsertLiabilitySegment, parameters, personLiabilityInfo, tokenId, username);
        }

        /// <summary>
        /// Delete liability segment
        /// </summary>
        /// <param name="personLiabilityId"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiResponse> DeleteLiabilitySegment(long personLiabilityId, string username, Guid tokenId)
        {
            string parameters = $"?personLiabilityId={personLiabilityId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_DeleteLiabilitySegment, parameters, tokenId, username);
        }

        /// <summary>
        /// Get renew application.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> GetRenewApplication(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_GetRenewApplication, parameters, tokenId, username);
        }

        /// <summary>
        /// Gets Non-Magi Income
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledNonMagiIncomesDto> GetNonMagiIncomes(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledNonMagiIncomesDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetNonMagiIncomes, parameters, tokenId, username);
        }

        /// <summary>
        /// Gets Qit and Allocation Info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledQitAndAllocationDto> GetQitAndAllocation(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledQitAndAllocationDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetQitAndAllocation, parameters, tokenId, username);
        }



        /// <summary>
        /// Save Non-Magi Incomes
        /// </summary>
        /// <param name="nonMagiIncomeData">The non-magi income data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveNonMagiIncomes(ElderlyDisabledNonMagiIncomesDto nonMagiIncomeData, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledNonMagiIncomesDto, BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_SaveApplicationNonMagiIncome, parameters, nonMagiIncomeData, tokenId, username);
            return result;
        }

        /// <summary>
        /// Save Allocation Info
        /// </summary>
        /// <param name="allocationData">The allocation data.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SaveAllocationInfo(ElderlyDisabledQitAndAllocationDto allocationData, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledQitAndAllocationDto, BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_SaveAllocationInfo, parameters, allocationData, tokenId, username);
            return result;
        }

        /// <summary>
        /// Calculates the Net VA Values
        /// </summary>
        /// <param name="appId">The app id.</param>
        /// <param name="appNonMagiIncomeId">The app non magi income id.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> CalculateVANetValues(long appId, long appNonMagiIncomeId, string username, Guid tokenId)
        {
            string parameters = $"?appId={appId}&applicationNonMagiIncomeId={appNonMagiIncomeId}&username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierGet<BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_CalculateNetVAIncome, parameters, tokenId, username);
            return result;
        }

        /// <summary>
        /// Gets the eligibility.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledEligibilityDeterminationsDto> GetEligibility(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledEligibilityDeterminationsDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetEligibilityDeterminations, parameters, tokenId, username);
        }

        /// <summary>
        /// Gets the eligibility.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledApplicationDetailQitLienDto> GetApplicationDetailQitLien(long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledApplicationDetailQitLienDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetApplicationDetailQitLien, parameters, tokenId, username);
        }

        /// <summary>
        /// Saves the eligibility information.
        /// </summary>
        /// <param name="eligibility">The eligibility.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> SaveEligibilityInformation(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledEligibilityDeterminationsDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_SaveEligibilityDeterminations, parameters, eligibility, tokenId, username);
            return result;
        }

        /// <summary>
        /// Enrolls MPS for an E&D app.  Returns the MSP app ID
        /// </summary>
        /// <param name="eligibility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiResponse> EnrollMsp(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledEligibilityDeterminationsDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_EnrollMsp, parameters, eligibility, tokenId, username);
            return result;
        }

        /// <summary>
        /// Enrolls the complete.
        /// </summary>
        /// <param name="eligibility">The eligibility.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> EnrollComplete(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledEligibilityDeterminationsDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_EnrollComplete, parameters, eligibility, tokenId, username);
            return result;
        }

        /// <summary>
        /// Truncate the liability segments
        /// </summary>
        /// <param name="eligibility">The elderly disabled eligibility determination dto.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiResponse> TruncateLiabilitySegments(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ElderlyDisabledEligibilityDeterminationsDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_TruncateLiabilitySegments, parameters, eligibility, tokenId, username);
            return result;
        }

        /// <summary>
        /// Gets the primary sponsor info.
        /// NOTE: THIS RETURNS CaresError.NotFound if there isn't one
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<RepresentativeInfoDto> GetPrimarySponsorInfo(long applicationId, long personId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&personId={personId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<RepresentativeInfoDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetPrimarySponsorInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Gets the elderly disabled non-magi income info for Elig/Enroll screen.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ElderlyDisabledNonMagiIncomesDto> GetElderlyDisabledNonMagiIncomeInfo(long applicationId, long personId, string username, Guid tokenId)
        {
            string parameters = $"?applicationId={applicationId}&personId={personId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ElderlyDisabledNonMagiIncomesDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetElderlyDisabledNonMagiIncomeInfo, parameters, tokenId, username);
        }

        /// <summary>
        /// Called by the front end, for dashboard display, or other (displaying error to user who tries to navigate to a partial import)
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<StagingExpediteImportDetailsDto> GetImportExpediteDetails(int expediteImportId, long applicationId, string username, Guid tokenId)
        {
            string parameters = $"?expediteImportId={expediteImportId}&applicationId={applicationId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<StagingExpediteImportDetailsDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetImportExpediteDetails, parameters, tokenId, username);
        }

        /// <summary>
        /// Returns Expedite Facility data
        /// </summary>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ExpediteFacilityProvidersDto> GetExpediteFacilityProviders(string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ExpediteFacilityProvidersDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetExpediteFacilityProviders, parameters, tokenId, username);
        }

        /// <summary>
        /// Returns Expedite Facility data by a search text
        /// </summary>
        /// <param name="searchText">The search text.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ExpediteFacilityProvidersDto> GetExpediteFacilityProvidersByString(string searchText, string username, Guid tokenId)
        {
            string parameters = $"?searchText={searchText}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ExpediteFacilityProvidersDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetExpediteFacilityByString, parameters, tokenId, username);
        }

        /// <summary>
        /// Returns Expedite Facility data by a search text
        /// </summary>
        /// <param name="searchText">The search text.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ExpediteFacilityDto> GetExpediteFacilityById(int expediteFacilityId, string username, Guid tokenId)
        {
            string parameters = $"?expediteFacilityId={expediteFacilityId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ExpediteFacilityDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetExpediteFacilityById, parameters, tokenId, username);
        }

        /// <summary>
        /// Inserts the expedite facility information.
        /// </summary>
        /// <param name="expediteFacilityInfo">The expedite facility information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> InsertExpediteFacility(ExpediteFacilityDto expediteFacilityInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ExpediteFacilityDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_InsertExpediteFacility, parameters, expediteFacilityInfo, tokenId, username);
            return result;
        }

        /// <summary>
        /// Inserts the financial institution information.
        /// </summary>
        /// <param name="financialInstitutionInfo">The financial institution information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> InsertFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<FinancialInstitutionDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_InsertFinancialInstitution, parameters, financialInstitutionInfo, tokenId, username);

            return result;
        }

        /// <summary>
        /// Updates Expedite Facility data.
        /// </summary>
        /// <param name="expediteFacilityInfo">The expedite facility information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> UpdateExpediteFacility(ExpediteFacilityDto expediteFacilityInfo, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ExpediteFacilityDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_UpdateExpediteFacility, parameters, expediteFacilityInfo, tokenId, username);
            return result;
        }

        /// <summary>
        /// Updates Financial Institution data.
        /// </summary>
        /// <param name="financialInstitutionInfo">The financial institution information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<BaseApiMessage> UpdateFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, bool mergeFlag, string username, Guid tokenId)
        {
            string parameters = $"?MergeFlag={mergeFlag}&username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<FinancialInstitutionDto, BaseApiResponse>(AppTierWebServiceNames.ElderlyDisabledApi_UpdateFinancialInstitution, parameters, financialInstitutionInfo, tokenId, username);
            return result;
        }

        /// <summary>
        /// Returns all Providers from the REF_NH_PROVIDER table
        /// </summary>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<ProvidersDto> GetProviders(string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ProvidersDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetProviders, parameters, tokenId, username);
        }

        /// <summary>
        /// ONLY sets the PROVIDER_ID for a given Expedite Facility
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public async Task<BaseApiMessage> SetExpediteFacilityProvider(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            string parameters = $"?username={username}&tokenId={tokenId}";
            var result = await ProcessAppTierPost<ExpediteFacilityDto, BaseApiMessage>(AppTierWebServiceNames.ElderlyDisabledApi_SetExpediteFacilityProvider, parameters, expediteFacility, tokenId, username);
            return result;
        }

        /// <summary>
        /// Returns Financial Institution data by a search text
        /// </summary>
        /// <param name="searchText">The search text.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<FinancialInstitutionsDto> GetFinancialInstitutionsByString(string searchText, string username, Guid tokenId)
        {
            string parameters = $"?searchText={searchText}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<FinancialInstitutionsDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetFinancialInstitutionsByString, parameters, tokenId, username);
        }

        /// <summary>
        /// Returns single Financial Institution data by BankId or Return All.
        /// </summary>
        /// <param name="bankId">The bank id.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<FinancialInstitutionsDto> GetFinancialInstitutions(int? bankId, string username, Guid tokenId)
        {
            string parameters = $"?BankId={bankId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<FinancialInstitutionsDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetFinancialInstitutions, parameters, tokenId, username);
        }

        /// <summary>
        /// Get the last Exparte Application info for a person.
        /// </summary>
        /// <param name="personId">The person id.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public async Task<ExparteApplicationInfoDto> GetExparteAppInfoByPersonId(int personId, string username, Guid tokenId)
        {
            string parameters = $"?personId={personId}&username={username}&tokenId={tokenId}";
            return await ProcessAppTierGet<ExparteApplicationInfoDto>(AppTierWebServiceNames.ElderlyDisabledApi_GetExparteInfoByPersonId, parameters, tokenId, username);
        }
    }
}