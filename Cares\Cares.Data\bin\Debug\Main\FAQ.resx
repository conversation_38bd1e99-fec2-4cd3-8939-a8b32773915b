﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="answer10_1" xml:space="preserve">
    <value>On the Alabamacares.Alabama.gov Login page:</value>
  </data>
  <data name="answer10_2" xml:space="preserve">
    <value>Enter your user name and password to log into your account</value>
  </data>
  <data name="answer10_3" xml:space="preserve">
    <value>From the "Dashboard" screen, click the "Finish Incomplete Application" button beside the application information.</value>
  </data>
  <data name="answer11_1" xml:space="preserve">
    <value>Information that you have entered into a particular field can be corrected while you are still in that field by simply using the backspace key to remove anything that you entered.  After doing this you can retype your information correctly.  There are several section summary screens that will allow remind you to make sure all previous information is correct before moving on.  At these summary screens you there may be ways to delete and to add information.  At any time, while filling out the application, you may scroll to any previous screen and press the Edit button to modify data, however there are several screens that do not allow editting after you get to a certain point.</value>
  </data>
  <data name="answer12_1" xml:space="preserve">
    <value>Open the Help Center by clicking the ? in the top right of the website.  For questions or help filling out the on-line application, call the ALL Kids toll-free number at [CHIPPHONE] Monday-Friday to speak with a Customer Service representative.  Spanish is available if needed.  You may leave a message if needed.  Email is also available for help by clicking the link in the Help Center(?) section under Contact Us.</value>
  </data>
  <data name="answer13_1" xml:space="preserve">
    <value>Under most programs you will be eligible for one year from the date your application is awarded. You will be sent a “renewal” notice prior to the one year anniversary and you will return the renewal to the program and you will have to be re-determined as to whether you and/or your children are still eligible for that program. If it is determined that you are no longer eligible for that program, your renewal will be forwarded to the agency you and/or your children may be eligible for.</value>
  </data>
  <data name="answer14_1" xml:space="preserve">
    <value>The information on the Review and Sign page means that everything you have entered into the on-line application will be used to verify (check) income, insurance, or other information. The agencies check with other state and federal agencies, banks, employers, etc., to make sure the information you entered is correct. Also, if you are not satisfied by the decision reached on your application, you may request a fair hearing and the agency will look at the application again. By checking the box on the Review and Sign page, you are agreeing that everything you entered is true. If you knowingly entered information that is not true or left out information, you may have committed a crime and that crime is punishable under state and/or federal law.</value>
  </data>
  <data name="answer15_1" xml:space="preserve">
    <value>The website will tell you immediately if it looks like you and/or your children may be eligible for one or more of the programs. </value>
  </data>
  <data name="answer15_2" xml:space="preserve">
    <value>[NOTE]: The computer cannot make the decision as to whether you and/or your children are eligible for these health care programs if any documentation is required, information from other agencies need to be verified, discrepancies are noted in the entered data, etc...  It may take up to 8 weeks to process the application due to these issues.</value>
  </data>
  <data name="answer16_1" xml:space="preserve">
    <value>A “household” is made up of children under age 19, their parent(s), guardian(s), and/or caretaker(s) who all live in the home.  For the MLIF program, the caretaker must be related to the child and the relationship must be verified(must have proof).  For all other programs the caretaker does not have to be related to the children living in the home.</value>
  </data>
  <data name="answer17_1" xml:space="preserve">
    <value>Household income includes income from a job or self-employment, and other income, such as:</value>
  </data>
  <data name="answer17_10" xml:space="preserve">
    <value>Miner's Benefits</value>
  </data>
  <data name="answer17_11" xml:space="preserve">
    <value>Black Lung Benefits</value>
  </data>
  <data name="answer17_12" xml:space="preserve">
    <value>Cash Contributions (from relatives, others)</value>
  </data>
  <data name="answer17_13" xml:space="preserve">
    <value>Rental Income (land, buildings, from roomer)</value>
  </data>
  <data name="answer17_14" xml:space="preserve">
    <value>Personal Loans (from relatives, others)</value>
  </data>
  <data name="answer17_15" xml:space="preserve">
    <value>Unemployment Compensation</value>
  </data>
  <data name="answer17_16" xml:space="preserve">
    <value>Insurance Annuity or Proceeds</value>
  </data>
  <data name="answer17_17" xml:space="preserve">
    <value>Government Payments on Land</value>
  </data>
  <data name="answer17_18" xml:space="preserve">
    <value>Royalties</value>
  </data>
  <data name="answer17_2" xml:space="preserve">
    <value>Social Security</value>
  </data>
  <data name="answer17_20" xml:space="preserve">
    <value>Interest on Savings</value>
  </data>
  <data name="answer17_21" xml:space="preserve">
    <value>Legal Settlements</value>
  </data>
  <data name="answer17_22" xml:space="preserve">
    <value>Sheltered Workshop Earnings</value>
  </data>
  <data name="answer17_23" xml:space="preserve">
    <value>Lump Sums</value>
  </data>
  <data name="answer17_24" xml:space="preserve">
    <value>Dividends</value>
  </data>
  <data name="answer17_25" xml:space="preserve">
    <value>School Grants or Loans</value>
  </data>
  <data name="answer17_26" xml:space="preserve">
    <value>Coal, Oil, Gravel Rights and Timber Leases</value>
  </data>
  <data name="answer17_27" xml:space="preserve">
    <value>[NOTE]: If you receive money from someone else, that money is also counted as income.</value>
  </data>
  <data name="answer17_3" xml:space="preserve">
    <value>SSI</value>
  </data>
  <data name="answer17_4" xml:space="preserve">
    <value>Public Assistance</value>
  </data>
  <data name="answer17_5" xml:space="preserve">
    <value>Railroad Retirement</value>
  </data>
  <data name="answer17_6" xml:space="preserve">
    <value>Veterans Benefits, Pensions, Compensation or Insurance</value>
  </data>
  <data name="answer17_7" xml:space="preserve">
    <value>Federal Civil Service Annuity</value>
  </data>
  <data name="answer17_8" xml:space="preserve">
    <value>State Retirement/Pension</value>
  </data>
  <data name="answer17_9" xml:space="preserve">
    <value>Private Pension</value>
  </data>
  <data name="answer18_1" xml:space="preserve">
    <value>If you have medical bills in the 3 months before you submit an application for Medicaid, you may be eligible for Medicaid to pay those bills.  Your income from those months must have been within the Medicaid income limits and the medical services received must have been Medicaid covered services.</value>
  </data>
  <data name="answer19_1" xml:space="preserve">
    <value>No.  You must have a signed statement from a doctor or health care clinic verifying (proving) that you are pregnant and the date your baby is due.</value>
  </data>
  <data name="answer1_1" xml:space="preserve">
    <value>SOBRA, ALL Kids and Medicaid for Low Income Families (MLIF) are programs that pay for health care coverage for children under age 19 in low income families.  SOBRA and MLIF pay for health care coverage for pregnant women.  MLIF also pays for health care coverage for low income adults with children under age 19.  Plan First is a program for low income females, aged 19 - 55, for family planning (birth control) services only.</value>
  </data>
  <data name="answer20_1" xml:space="preserve">
    <value>Go to a health care clinic or your local health department to get proof of your pregnancy and the date your baby is due.</value>
  </data>
  <data name="answer21_1" xml:space="preserve">
    <value>The Eligibility Results screen will let you know if someone is not eligible for any program.  This screen will provide a customer service phone number for any questions you may have at that time.  If you still wish to apply anyway, you may [DOWNLOAD_APP_URL] to download a blank application, fill it out and mail it in.</value>
  </data>
  <data name="answer2_1" xml:space="preserve">
    <value>SOBRA - Low income children under age 19 and pregnant women.</value>
  </data>
  <data name="answer2_2" xml:space="preserve">
    <value>ALL Kids - Low income children under age 19 whose income is higher than the SOBRA income limits.</value>
  </data>
  <data name="answer2_3" xml:space="preserve">
    <value>MLIF – Children under age 19 and adults with children under age 19 with very low income.</value>
  </data>
  <data name="answer2_4" xml:space="preserve">
    <value>Plan First – Low income females aged 19 – 55 for family planning services only.</value>
  </data>
  <data name="answer2_5" xml:space="preserve">
    <value>[NOTE]: Your household size and household income will be the deciding factors as to which of the programs you and/or your children may be eligible for.</value>
  </data>
  <data name="answer3_1" xml:space="preserve">
    <value>SOBRA, MLIF and Plan First - There is no cost for children under age 18. There are small co-pays for some services. ALL Kids - Cost is based on family size and income. Families pay from $52 to $104 per year, per child. Small co-pays may be required at time of service. There are no co-pays for preventive services like regular check-ups, immunizations, dental cleanings and vision exams.</value>
  </data>
  <data name="answer4_1" xml:space="preserve">
    <value>For SOBRA and Medicaid for Low Income Families (MLIF), you may have other health insurance and still be eligible. For ALL Kids and Plan First, you cannot have other health insurance and be eligible. There are rules for the ALL Kids program regarding lost or cancelled health insurance. For questions, call the ALL Kids toll-free number at [CHIPPHONE].</value>
  </data>
  <data name="answer4_2" xml:space="preserve">
    <value>[NOTE]: If you have Medicaid, Blue Cross, CHAMPUS or any other health insurance, please list those.</value>
  </data>
  <data name="answer5_1" xml:space="preserve">
    <value>No. You cannot transfer Medicaid from one state to another. Each state’s Medicaid program is different. You may be eligible for Medicaid in Alabama based on your income and family size. You may submit an on-line application to find out if you might be eligible for Medicaid or any of the other health care programs in Alabama. If you were eligible for Medicaid in another state, you must get a copy of your termination letter from that state to send to Alabama Medicaid.</value>
  </data>
  <data name="answer5_2" xml:space="preserve">
    <value>[NOTE]: If you were eligible for the Social Security Administration’s SSI program in another state, you will need to contact the Social Security Administration and let them know you have moved. If you are still eligible for the SSI program, you will automatically receive Medicaid in Alabama.</value>
  </data>
  <data name="answer6_1" xml:space="preserve">
    <value>Create a user account by entering a user name and password (remember your password or write it down).</value>
  </data>
  <data name="answer6_3" xml:space="preserve">
    <value>Fill in all the blanks for each question on every page.</value>
  </data>
  <data name="answer6_4" xml:space="preserve">
    <value>Once completed, click the “Submit Application” button. (The application will tell you which program you and/or your children may be eligible for. You will be able to check your answers before you submit the application.)</value>
  </data>
  <data name="answer6_6" xml:space="preserve">
    <value>Enter your user name and password and visit your account dashboard to update your contact information, view the status of your last application, or view enrollment information for your children.</value>
  </data>
  <data name="answer7_1" xml:space="preserve">
    <value>If you do not have a Social Security number, you may leave it blank.  This may slow down the process of determining eligibility.</value>
  </data>
  <data name="answer8_1" xml:space="preserve">
    <value>It should take about 30 minutes to complete this application.  If you are unable to complete the application at this time, you will have 30 days to come back to the application and finish it.  The website will automatically delete the application if it is not completed within 30 days.  You will have to start a new application if you wait longer than 30 days from the time you start.</value>
  </data>
  <data name="answer9_1" xml:space="preserve">
    <value>You may click on the "Sign Out" link at the top of the screen.  You will have 30 days to come back to your application. After 30 days, the website will automatically delete the application and you will have to start a new one.</value>
  </data>
  <data name="faqButton" xml:space="preserve">
    <value>Standard FAQs</value>
  </data>
  <data name="PaperApplication" xml:space="preserve">
    <value>Download Paper Application</value>
  </data>
  <data name="question1" xml:space="preserve">
    <value>What are these programs for?</value>
  </data>
  <data name="question10" xml:space="preserve">
    <value>How do I get back into my application, if I don’t finish it?</value>
  </data>
  <data name="question11" xml:space="preserve">
    <value>What if I make a mistake?</value>
  </data>
  <data name="question12" xml:space="preserve">
    <value>What if I have trouble with the on-line application?</value>
  </data>
  <data name="question13" xml:space="preserve">
    <value>How long will I or my children be covered by these programs?</value>
  </data>
  <data name="question14" xml:space="preserve">
    <value>What does the Review and Sign page mean?</value>
  </data>
  <data name="question15" xml:space="preserve">
    <value>How long does it take to find out if I or my children are eligible for any of these programs?</value>
  </data>
  <data name="question16" xml:space="preserve">
    <value>What is a household?</value>
  </data>
  <data name="question17" xml:space="preserve">
    <value>What is household income?</value>
  </data>
  <data name="question18" xml:space="preserve">
    <value>What if I already have medical bills?</value>
  </data>
  <data name="question19" xml:space="preserve">
    <value>Can I use my ultrasound picture as proof of pregnancy?</value>
  </data>
  <data name="question2" xml:space="preserve">
    <value>Who are these programs for?</value>
  </data>
  <data name="question20" xml:space="preserve">
    <value>How can I get proof of pregnancy if the doctor only sees Medicaid patients?</value>
  </data>
  <data name="question21" xml:space="preserve">
    <value>What if the computer says I or my children are not eligible?</value>
  </data>
  <data name="question3" xml:space="preserve">
    <value>How much do these programs cost?</value>
  </data>
  <data name="question4" xml:space="preserve">
    <value>If I have other insurance, would I still be eligible for these programs?</value>
  </data>
  <data name="question5" xml:space="preserve">
    <value>I was on Medicaid in another state, can I transfer my Medicaid?</value>
  </data>
  <data name="question6" xml:space="preserve">
    <value>How does the on-line application work?</value>
  </data>
  <data name="question7" xml:space="preserve">
    <value>What if I don’t have a Social Security number?</value>
  </data>
  <data name="question8" xml:space="preserve">
    <value>How long will it take me to fill out the on-line application?</value>
  </data>
  <data name="question9" xml:space="preserve">
    <value>What if I cannot finish the on-line application?</value>
  </data>
</root>