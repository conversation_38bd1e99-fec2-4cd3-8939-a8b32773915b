﻿using AlabamaConnectExpress.BLL.PresumptiveEligibility;
using Cares.Controllers.Application;
using Cares.Infrastructure.Log;
using Cares.Portal.Infrastructure;
using Cares.Portal.Infrastructure.VGSecurity;
using Cares.Portal.Worker.Models.Models;
using Cares.Portal.Worker.Models.Models.PresumptiveEligibility.ViewModel;
using System;
using System.Threading.Tasks;
using System.Web.Mvc;
using static Cares.Infrastructure.Log.ScreenActionAttribute;
using ENUMS = Cares.Api.Infrastructure.Enums;

namespace AlabamaConnectExpress.Controllers.PresumptiveEligibility
{
    public class PresumptiveEligibilityController : WpBaseController
    {
        #region constants
        public const string pepAdminScreenName = "PEP Admin Landing";
        public const string pepProviderScreenName = "Add Provider";
        public const string pepUpdateProviderScreenName = "Update Provider";
        public const string pepDeterminerScreenName = "Add Determiner";
        public const string AddPepProviderFormName = "F_AddPepProvider";
        public const string AddPepDeterminerFormName = "F_AddPepDeterminer";
        private const string _determiner = "Determiner";
        const string _provider = "Provider";

        #endregion

        private HumanReadableErrorMessages humanReadableErrors = new HumanReadableErrorMessages();

        #region PEP Admin
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { pepAdminScreenName }, ActionType = new string[] { ActionTypes.Read },
          ActionDescription = "Gets PEP Admin landing page")]
        public async Task<ActionResult> PepAdminPage(string partial = "")
        {
            // Only Medicaid Admin and PEP Eligibility worker can access PEP admin screen.
            if (!(CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN) || CaresSecurity.IsInRole(CaresSecurity.UserRoles.PEP_ELIG)))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }
            ViewBag.PartialV = partial;
            PresumptiveEligibilityBll edBll = new PresumptiveEligibilityBll(this);
            PepLandingViewModel vm = await edBll.GetPepLandingPage(CallingUsername, TokenId);
            return View("~/Views/PresumptiveEligibility/PepAdminLandingPage.cshtml", vm);
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { pepAdminScreenName }, ActionDescription = "Toggle View for PEP Admin page")]
        public async Task<ActionResult> pepAdminToggleView(string selectedValue)
        {
            if (selectedValue == _provider)
            {
                // Medicaid Admin and PEP Eligibility worker can access PEP Provider admin screen.
                if (!(CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN) || CaresSecurity.IsInRole(CaresSecurity.UserRoles.PEP_ELIG)))
                {
                    return RedirectToErrorPage(new BaseViewModel()
                    {
                        CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                    });
                }
                PresumptiveEligibilityBll edBll = new PresumptiveEligibilityBll(this);
                PepLandingViewModel vm = await edBll.GetPepLandingPage(CallingUsername, TokenId);//TODO: GET PROVIDERS
                return PartialView("~/Views/PresumptiveEligibility/_Provider.cshtml", vm);
            }
            else if (selectedValue == _determiner)
            {
                // Medicaid Admin worker can access PEP Provider admin screen.
                if (!(CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN) || CaresSecurity.IsInRole(CaresSecurity.UserRoles.PEP_ELIG)))
                {
                    return RedirectToErrorPage(new BaseViewModel()
                    {
                        CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                    });
                }
                PresumptiveEligibilityBll edBll = new PresumptiveEligibilityBll(this);
                PepDeterminersViewModel vm = await edBll.GetPepDeterminers(CallingUsername, TokenId);//TODO: GET DETERMINERS
                return PartialView("~/Views/PresumptiveEligibility/_Determiner.cshtml", vm);
            }
            return null;
        }
        #endregion PEP Admin

        #region Add PEP Provider.
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { pepProviderScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Add PEP Provider page.")]
        public async Task<ActionResult> AddPEPProvider(int pepProviderId = 0)
        {
            var provider = new PepProviderViewModel();
            // Medicaid admin role have access to add PEP Provider view/page.
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            if(pepProviderId > 0)
			{
                PresumptiveEligibilityBll pepBll = new PresumptiveEligibilityBll(this);
                provider = await pepBll.GetProviderById(pepProviderId, CallingUsername, TokenId);
            }
			else
			{
                //Force state Alabama
                provider.ProviderAddress.StateId = (short)ENUMS.enumState.Alabama;
                provider.ProviderStatusId = (int)ENUMS.enumProviderDeterminerStatus.Active;
			}

            return View(provider);
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { pepProviderScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Add PEP Provider page.")]
        public async Task<ActionResult> AddPEPProvider(PepProviderViewModel formData)
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return DisplayNotAuthorizedPage();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    PresumptiveEligibilityBll pepBll = new PresumptiveEligibilityBll(this);
                    var result = await pepBll.SaveProvider(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
					{
						return Json(new { success = true });
					}
				}
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return Json(new { success = false });
            }

            var ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = ErrorMessages, currentView = AddPepProviderFormName });
        }
        #endregion Add PEP Provider

        #region Add PEP Determiner
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { pepDeterminerScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Add PEP Determiner page.")]
        public async Task<ActionResult> AddPEPDeterminer(int pepDeterminerId = 0)
        {
            var determiner = new PepDeterminerViewModel();
            // Medicaid admin role have access to add PEP Determiner view/page.
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            if (pepDeterminerId > 0)
            {
                PresumptiveEligibilityBll pepBll = new PresumptiveEligibilityBll(this);
                determiner = await pepBll.GetDeterminerById(pepDeterminerId, CallingUsername, TokenId);

                determiner.ProviderId = (int)determiner.Provider.ProviderId;
            }
            else
            {
                //Force state Alabama
                determiner.DeterminerStatusId = (int)ENUMS.enumProviderDeterminerStatus.Active;
            }

            return View(determiner);
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { pepDeterminerScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Add PEP Determiner page.")]
        public async Task<ActionResult> AddPEPDeterminer(PepDeterminerViewModel formData)
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return DisplayNotAuthorizedPage();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    PresumptiveEligibilityBll pepBll = new PresumptiveEligibilityBll(this);

                    var result = await pepBll.SaveDeterminer(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        return Json(new { success = true });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return Json(new { success = false });
            }

            var ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = ErrorMessages, currentView = AddPepDeterminerFormName });
        }

        #endregion Add PEP Determiner

        /// <summary>
        /// Get the facility by string.
        /// </summary>
        /// <param name="searchText">The search text</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { pepDeterminerScreenName }, ActionType = new string[] { ActionTypes.Read },
          ActionDescription = "Gets the PEP Providers List by a string")]
        public async Task<JsonResult> GetProviderByString(string searchText)
        {
            PresumptiveEligibilityBll edBll = new PresumptiveEligibilityBll(this);
            PepProvidersViewModel result = await edBll.GetProviderByString(searchText, CallingUsername, TokenId);

            return Json(new { success = true, providers = result.Providers }, JsonRequestBehavior.AllowGet);
        }
    }
}