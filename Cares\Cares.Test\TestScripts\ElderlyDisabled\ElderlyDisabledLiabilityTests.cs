﻿using Cares.Api.Infrastructure.Enums;
using Cares.Api.Messages.Applications;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Controllers.Enrollment;
using Cares.Data;
using Cares.Data.DataAbstractionLayer;
using Cares.Portal.Infrastructure.VGSecurity;
using Cares.Services.AppTier.BLL.ElderlyDisabled;
using Cares.Services.AppTier.Controllers;
using Cares.Test.Helpers;
using Cares.Test.Portal.Mocking;
using Cares.Test.TestScripts.ElderlyDisabled;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using static Cares.Api.Infrastructure.Constants;

namespace Cares.Test.AppTier
{
    [TestClass]
    public class ElderlyDisabledLiabilityTests : EandDBaseTest
    {
        private ElderlyDisabledApiController _enDApiController = null;

        [TestInitialize]
        public void Initialize()
        {
            _enDApiController = new ElderlyDisabledApiController();
        }

        [TestMethod]
        [TestCategory(TestCategoryNames.DevTesting)]
        [Ignore]
        public void GetLiabilityTestData_DevTest()
        {
            long appId = 3983565;
            long personId = 6141756;
            ElderlyDisabledLiabilityDto liabilityInfo = _enDApiController.GetPersonLiabilityInfo(appId, personId, automationUserName, CommonTokenId);
            Assert.IsTrue(liabilityInfo.IsSuccessful);
        }

        [TestMethod]
        [TestCategory(TestCategoryNames.ElderlyDisabled)]
        [TestCategory(TestCategoryNames.Regression)]
        public void GetLiabilityTestData_Save()
        {
            Random rand = new Random();
            _whatToLoad = WhatToLoad.Liability;
            var theApp = LoadBasicEandDApp();
            long appId = theApp.ApplicationId;
            long personId = theApp.ContactPersonId;

            ElderlyDisabledLiabilityDto liabilityInfo = _enDApiController.GetPersonLiabilityInfo(appId, personId, automationUserName, CommonTokenId);
            Assert.IsTrue(liabilityInfo.IsSuccessful);

            liabilityInfo.UpdatedBy = "Unknown";
            liabilityInfo.UpdatedDate = DateTime.Now;
            ElderlyDisabledLiabilityTestDto newTest1 = new ElderlyDisabledLiabilityTestDto()
            {
                OriginatingApplicationId = liabilityInfo.ApplicationId,
                PersonId = liabilityInfo.ContactPersonId,
                LiabilityTestTypeId = enumLiabilityTestType.Infrequent_Irregular,
                Amount = rand.Next(99999),
                EffectiveDate = DateTime.Today.AddDays(-(120+rand.Next(120))),
                Description = "Desc:"+rand.Next(100).ToString(),
                EndDate = DateTime.Today.AddDays(-rand.Next(120))
            };
            liabilityInfo.LiabilityTests.Add(newTest1);
            ElderlyDisabledLiabilityTestDto newTest2 = new ElderlyDisabledLiabilityTestDto()
            {
                OriginatingApplicationId = liabilityInfo.ApplicationId,
                PersonId = liabilityInfo.ContactPersonId,
                LiabilityTestTypeId = enumLiabilityTestType.Other,
                Amount = rand.Next(99999),
                EffectiveDate = DateTime.Today.AddDays(-(120 + rand.Next(120))),
                Description = "Desc:" + rand.Next(100).ToString(),
                EndDate = DateTime.Today.AddDays(-rand.Next(120))
            };
            liabilityInfo.LiabilityTests.Add(newTest2);
            ElderlyDisabledLiabilityTestDto newTest3 = new ElderlyDisabledLiabilityTestDto()
            {
                OriginatingApplicationId = liabilityInfo.ApplicationId,
                PersonId = liabilityInfo.ContactPersonId,
                LiabilityTestTypeId = enumLiabilityTestType.VA_Aid_Attendance,
                Amount = rand.Next(99999),
                EffectiveDate = DateTime.Today.AddDays(-(120 + rand.Next(120))),
                Description = "Desc:" + rand.Next(100).ToString(),
                EndDate = DateTime.Today.AddDays(-rand.Next(120))
            };
            liabilityInfo.LiabilityTests.Add(newTest3);

            // Save
            var response = _enDApiController.UpsertLiabilityTestData(liabilityInfo, automationUserName, CommonTokenId);
            Assert.IsTrue(response.IsSuccessful);

            ElderlyDisabledLiabilityDto liabilityInfo2 = _enDApiController.GetPersonLiabilityInfo(appId, personId, automationUserName, CommonTokenId);
            Assert.IsTrue(liabilityInfo2.IsSuccessful);
            Assert.AreEqual(liabilityInfo.LiabilityTests.Count, liabilityInfo2.LiabilityTests.Count);
            var test1Match = liabilityInfo2.LiabilityTests.FirstOrDefault(t => t.Amount == newTest1.Amount);
            Assert.IsNotNull(test1Match);
            CompareHelpers.CompareTwoItems(newTest1, test1Match);
            var test2Match = liabilityInfo2.LiabilityTests.FirstOrDefault(t => t.Amount == newTest2.Amount);
            Assert.IsNotNull(test2Match);
            CompareHelpers.CompareTwoItems(newTest2, test2Match);
            var test3Match = liabilityInfo2.LiabilityTests.FirstOrDefault(t => t.Amount == newTest3.Amount);
            Assert.IsNotNull(test3Match);
            CompareHelpers.CompareTwoItems(newTest3, test3Match);

            // UPDATE
            test1Match.Amount += 1;
            test1Match.Description += ".1";
            test1Match.EffectiveDate = test1Match.EffectiveDate.Value.AddDays(-1);
            test1Match.EndDate = test1Match.EndDate.Value.AddDays(-1);
            response = _enDApiController.UpsertLiabilityTestData(liabilityInfo2, automationUserName, CommonTokenId);
            Assert.IsTrue(response.IsSuccessful);
            // Assert
            ElderlyDisabledLiabilityDto liabilityInfo3 = _enDApiController.GetPersonLiabilityInfo(appId, personId, automationUserName, CommonTokenId);
            Assert.IsTrue(liabilityInfo3.IsSuccessful);
            var test1v2Match = liabilityInfo3.LiabilityTests.FirstOrDefault(t => t.Amount == test1Match.Amount);
            Assert.IsNotNull(test1v2Match);
            CompareHelpers.CompareTwoItems(test1Match, test1v2Match);

            // Delete
            liabilityInfo3.LiabilityTests.Remove(test1v2Match);
            response = _enDApiController.UpsertLiabilityTestData(liabilityInfo3, automationUserName, CommonTokenId);
            Assert.IsTrue(response.IsSuccessful);
            // Assert
            ElderlyDisabledLiabilityDto liabilityInfo4 = _enDApiController.GetPersonLiabilityInfo(appId, personId, automationUserName, CommonTokenId);
            Assert.IsTrue(liabilityInfo4.IsSuccessful);
            Assert.AreEqual(liabilityInfo3.LiabilityTests.Count, liabilityInfo4.LiabilityTests.Count);
        }
    }
}
