﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Hub {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class RemoteIDProofing {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal RemoteIDProofing() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Hub.RemoteIDProofing", typeof(RemoteIDProofing).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The answer number can not be over two characters long..
        /// </summary>
        public static string AnswerNumberToLong {
            get {
                return ResourceManager.GetString("AnswerNumberToLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The answer number must be at least one character long..
        /// </summary>
        public static string AnswerNumberToShort {
            get {
                return ResourceManager.GetString("AnswerNumberToShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s birth date must be exactly ten characters long..
        /// </summary>
        public static string BirthdateSizeIncorrect {
            get {
                return ResourceManager.GetString("BirthdateSizeIncorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s city name must be between one and 38 characters long..
        /// </summary>
        public static string CityNameIncorrectLength {
            get {
                return ResourceManager.GetString("CityNameIncorrectLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s given name can not contain spaces or special characters..
        /// </summary>
        public static string GivenNameContainsIllegalChars {
            get {
                return ResourceManager.GetString("GivenNameContainsIllegalChars", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to request.PrimaryRequest.Person.PersonName.PersonGivenName.Length &gt; 50.
        /// </summary>
        public static string GivenNameIncorrectLength {
            get {
                return ResourceManager.GetString("GivenNameIncorrectLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s language preference must be either &apos;eng&apos; or &apos;spa&apos;..
        /// </summary>
        public static string LanguagePreferenceInvalid {
            get {
                return ResourceManager.GetString("LanguagePreferenceInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s middle name can not contain spaces or special characters..
        /// </summary>
        public static string MiddleNameContainsIllegalChars {
            get {
                return ResourceManager.GetString("MiddleNameContainsIllegalChars", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s middle name can not be over 50 characters long..
        /// </summary>
        public static string MiddleNameToLong {
            get {
                return ResourceManager.GetString("MiddleNameToLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s telephone number can not be over 13 characters long..
        /// </summary>
        public static string PhoneNumberToLong {
            get {
                return ResourceManager.GetString("PhoneNumberToLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s postal code must be exactly five characters long..
        /// </summary>
        public static string PostalCodeIncorrectLength {
            get {
                return ResourceManager.GetString("PostalCodeIncorrectLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s postal extension code must be exactly four characters long..
        /// </summary>
        public static string PostalExtensionCodeIncorrectLength {
            get {
                return ResourceManager.GetString("PostalExtensionCodeIncorrectLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s level of proofing code must be &apos;LevelTwo&apos;, &apos;LevelThree&apos;, or &apos;OptionThree&apos;..
        /// </summary>
        public static string ProofingCodeInvalid {
            get {
                return ResourceManager.GetString("ProofingCodeInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The question number can not be at over two characters long..
        /// </summary>
        public static string QuestionNumberToLong {
            get {
                return ResourceManager.GetString("QuestionNumberToLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The question number must be at least one character long..
        /// </summary>
        public static string QuestionNumberToShort {
            get {
                return ResourceManager.GetString("QuestionNumberToShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The session identification can not be over 70 characters long..
        /// </summary>
        public static string SessionIDToLong {
            get {
                return ResourceManager.GetString("SessionIDToLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The session identification must be at least one character long..
        /// </summary>
        public static string SessionIDToShort {
            get {
                return ResourceManager.GetString("SessionIDToShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s SSN must have exactly 9 numbers..
        /// </summary>
        public static string SSNLengthIncorrect {
            get {
                return ResourceManager.GetString("SSNLengthIncorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s state abbreviation must be exactly two chracters long..
        /// </summary>
        public static string StateAbbrevIncorrectLength {
            get {
                return ResourceManager.GetString("StateAbbrevIncorrectLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s street name must be between one and 68 characters long..
        /// </summary>
        public static string StreetNameIncorrectLength {
            get {
                return ResourceManager.GetString("StreetNameIncorrectLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s subscriber number must be exactly seven characters long..
        /// </summary>
        public static string SubscriberNumberLengthInvalid {
            get {
                return ResourceManager.GetString("SubscriberNumberLengthInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s suffix may only be &apos;SR&apos;, &apos;JR&apos;, &apos;2&apos;, &apos;II&apos;, &apos;3&apos;, &apos;III&apos;, &apos;4&apos;, or &apos;IV&apos;.
        /// </summary>
        public static string SuffixIncorrect {
            get {
                return ResourceManager.GetString("SuffixIncorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s surname can not contain spaces or special characters..
        /// </summary>
        public static string SurNameContainsIllegalChars {
            get {
                return ResourceManager.GetString("SurNameContainsIllegalChars", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s surname must be between one and 50 characters long..
        /// </summary>
        public static string SurNameIncorrectLength {
            get {
                return ResourceManager.GetString("SurNameIncorrectLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s surname may only have an apostrophe following the letters &apos;O&apos;, &apos;D&apos;, and &apos;I&apos;..
        /// </summary>
        public static string SurNameMisplacedApostrophe {
            get {
                return ResourceManager.GetString("SurNameMisplacedApostrophe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s surname may only contain a maximum of one apostrophe..
        /// </summary>
        public static string SurNameToManyApostrophes {
            get {
                return ResourceManager.GetString("SurNameToManyApostrophes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The person&apos;s surname may only contain a maximum of one hyphen..
        /// </summary>
        public static string SurNameToManyHyphens {
            get {
                return ResourceManager.GetString("SurNameToManyHyphens", resourceCulture);
            }
        }
    }
}
