﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Insurance {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ChipSpecific {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ChipSpecific() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Insurance.ChipSpecific", typeof(ChipSpecific).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ALL Kids Program Questions.
        /// </summary>
        public static string chipSpecificQuestions {
            get {
                return ResourceManager.GetString("chipSpecificQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did [NAME], (DOB: [DOB]) have health coverage through a job that ended in the last 3 months?.
        /// </summary>
        public static string coverageEndedLast3Months {
            get {
                return ResourceManager.GetString("coverageEndedLast3Months", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Why did that coverage end?.
        /// </summary>
        public static string coverageEndedWhy {
            get {
                return ResourceManager.GetString("coverageEndedWhy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB: [DOB]) offered the Alabama state employee health benefit plan through a job or a family member&apos;s job?.
        /// </summary>
        public static string stateEmployeeCoverage {
            get {
                return ResourceManager.GetString("stateEmployeeCoverage", resourceCulture);
            }
        }
    }
}
