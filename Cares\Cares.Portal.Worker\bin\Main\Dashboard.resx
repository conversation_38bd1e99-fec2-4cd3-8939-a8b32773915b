﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="amountDueInfo" xml:space="preserve">
    <value>ALL Kids Premium Due</value>
  </data>
  <data name="appEnrollStatus" xml:space="preserve">
    <value>Application &amp; Enrollment Status</value>
  </data>
  <data name="createNewApplication" xml:space="preserve">
    <value>Create New Application</value>
  </data>
  <data name="appStatus" xml:space="preserve">
    <value>Application Status</value>
  </data>
  <data name="cancelDate" xml:space="preserve">
    <value>Cancel Date</value>
  </data>
  <data name="ced" xml:space="preserve">
    <value>Contract Effective Date</value>
  </data>
  <data name="dueDate" xml:space="preserve">
    <value>Due Date </value>
  </data>
  <data name="editMyAccount" xml:space="preserve">
    <value>Make changes to My Account</value>
  </data>
  <data name="enrollStatus" xml:space="preserve">
    <value>Enrollment Status</value>
  </data>
  <data name="header" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="finishApplication" xml:space="preserve">
    <value>Finish Application</value>
  </data>
  <data name="messages" xml:space="preserve">
    <value>Messages and Announcements</value>
  </data>
  <data name="noMessages" xml:space="preserve">
    <value>You have no messages or announcements</value>
  </data>
  <data name="payOnline" xml:space="preserve">
    <value>Pay Online</value>
  </data>
  <data name="program" xml:space="preserve">
    <value>Program</value>
  </data>
  <data name="renewal" xml:space="preserve">
    <value>Renewal</value>
  </data>
  <data name="source" xml:space="preserve">
    <value>Application Source</value>
  </data>
  <data name="startDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="submitDate" xml:space="preserve">
    <value>Submit Date</value>
  </data>
  <data name="totalAmtDue" xml:space="preserve">
    <value>Total Amount Due</value>
  </data>
  <data name="viewDetail" xml:space="preserve">
    <value>View Detail</value>
  </data>
  <data name="appID" xml:space="preserve">
    <value>Application ID</value>
  </data>
  <data name="renewApplicaiton" xml:space="preserve">
    <value>Renew Application</value>
  </data>
  <data name="premiumDueNotice" xml:space="preserve">
    <value>All payments made will be reflected the next day</value>
  </data>
  <data name="status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="nonPaymentDenialMessage" xml:space="preserve">
    <value>Denied due to non-payment of premium: </value>
  </data>
  <data name="sosMessage1" xml:space="preserve">
    <value>Medicaid will be mailing you a paper voter registration application in the near future. But, if you have a valid Alabama driver’s license or State ID, you can also register to vote online right now by clicking</value>
  </data>
  <data name="sosMessage2" xml:space="preserve">
    <value>You should be registered to vote at your address, so you should submit a registration application to update your address if you are not sure if your voter registration is current.</value>
  </data>
  <data name="sosMessage3" xml:space="preserve">
    <value>If you have a question about voter registration, you can call the Alabama Secretary of State toll-free at 1-800-274-8683</value>
  </data>
  <data name="statusAction" xml:space="preserve">
    <value>Status/Action</value>
  </data>
  <data name="approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="denied" xml:space="preserve">
    <value>Denied</value>
  </data>
</root>