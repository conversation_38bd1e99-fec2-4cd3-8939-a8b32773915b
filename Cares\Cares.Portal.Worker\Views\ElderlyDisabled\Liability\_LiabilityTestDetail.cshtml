﻿@model Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.ElderlyDisabledLiabilityTestViewModel
@using Cares.Portal.Infrastructure
@using Cares.Api.Infrastructure.Enums
@using Constants = Cares.Api.Infrastructure.Constants;

@{
    byte testTypeId = Convert.ToByte(ViewData[PortalConstants.ViewDataConstants.LiabilityTestType].ToString());
    LiabilityTestTypeType testType = LiabilityTestTypeType.GetLiabilityTestTypeTypeById(testTypeId);
    var delete = @CommonHtmlExtensions.IconHelper(Cares.Api.Infrastructure.Constants.Icons.IconDelete);
    string liabilityTestIndex = ViewData[PortalConstants.ViewDataConstants.LiabilityTestIndex].ToString();
    var liabilityTestDetailPrefix = ViewData[PortalConstants.ViewDataConstants.NamePrefix].ToString();
    string amountDescription = testType.Description;
    bool showDescription = true;
    string strDateStoppedPickerClass = "datepicker";
    string effDatePickerClass = "liabilityDatepickerMonthYear";
    bool isTemplate = (ViewData[PortalConstants.ViewDataConstants.IsTemplate].ToString() == "True");
}

@if (!isTemplate)
{
    liabilityTestDetailPrefix = liabilityTestDetailPrefix.Replace("{index}", liabilityTestIndex.ToString());
}
else
{
    strDateStoppedPickerClass = string.Empty;
    effDatePickerClass = "datePickReplace";
}


@switch (testType.Id)
{
    case (byte)enumLiabilityTestType.Infrequent_Irregular:
        showDescription = true;
        break;
    case (byte)enumLiabilityTestType.Other:
        showDescription = true;
        amountDescription += " Amount";
        break;
    case (byte)enumLiabilityTestType.VA_Aid_Attendance:
        showDescription = false;
        break;
}


<div class="row m-1 divRow" id="divLTRow_@(testType.ShortName)_@(liabilityTestIndex)" data-header-index="@liabilityTestIndex">
    @Html.HiddenFor(model => model.PersonLiabilityTestId, new { Name = liabilityTestDetailPrefix + Html.NameFor(n => n.PersonLiabilityTestId), @class = "hdnPersonLiabilityTestId" })
    @Html.HiddenFor(model => model.LiabilityTestTypeId, new { Name = liabilityTestDetailPrefix + Html.NameFor(n => n.LiabilityTestTypeId) })
    @Html.HiddenFor(model => model.OriginatingApplicationId, new { Name = liabilityTestDetailPrefix + Html.NameFor(n => n.OriginatingApplicationId) })
    @Html.HiddenFor(model => model.PersonId, new { Name = liabilityTestDetailPrefix + Html.NameFor(n => n.PersonId) })
    @Html.HiddenFor(model => model.IsDeleted, new { Name = liabilityTestDetailPrefix + Html.NameFor(n => n.IsDeleted), id = "hdn_IsLT_" + testType.ShortName + "_Deleted_" + liabilityTestIndex, @class = "hdnLiabilityTestIsDeleted" })

    <div class="col-2">
        <label for="txtbxLT_@(testType.ShortName)_EffectiveDate_@(liabilityTestIndex)" class="form-label required">Month/Year</label>
        <div class="input-group ">
            @Html.TextBoxFor(model => model.EffectiveDate,
                    "{0:MM/yyyy}",
                    new
                    {
                        id = "txtbxLT_" + testType.ShortName + "_EffectiveDate_" + liabilityTestIndex,
                        @Name = liabilityTestDetailPrefix + "EffectiveDate",
                        placeholder = "MM/YYYY",
                        @class = "form-control monthYearMask validate-dateFormat required " + effDatePickerClass,
                        data_validation_errors_container = "#divLT_" + testType.ShortName + "_EffectiveDateValidationErrors_" + liabilityTestIndex,
                        oncopy = "return false",
                        onpaste = "return false",
                    })
            <button type="button" data-date-input-id="txtbxLT_@(testType.ShortName)_EffectiveDate_@(liabilityTestIndex)" data-toggle="tooltip" data-original-title="Clear Date"
                    class="btn btn-clear-input btn-outline-secondary py-0">
                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconCross, new string[] { "align-middle" })
            </button>
            <button type="button" id="btnLT_@(testType.ShortName)_EffectiveDate_@(liabilityTestIndex)" tabindex="-1" data-toggle="tooltip" data-original-title="Select Date" data-datepicker-id="txtbxLT_@(testType.ShortName)_EffectiveDate_@(liabilityTestIndex)"
                    class="btn btn-outline-secondary liability-datepicker-trigger py-0">
                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconCalendar, new string[] { "align-middle" })
            </button>
            @*Container for client-side validations for layout purposes. NOTE: Use of the class is optional based on the design.*@
            <div id="divLT_@(testType.ShortName)_EffectiveDateValidationErrors_@(liabilityTestIndex)" class="error-js-container"></div>
        </div>
    </div>

    <div class="form-group col-2 verify-amount liabilityTest-amount">
        <label for="txtbxLT_@(testType.ShortName)_Amount_@(liabilityTestIndex)">@amountDescription</label>
        <div class="input-group">
            <div class="input-group-prepend">
                <span class="input-group-text border-0">
                    @CommonHtmlExtensions.IconHelper(Constants.Icons.IconDollarSign, new string[] { "colorNavyBlue", "align-middle" })
                </span>
            </div>
            @Html.TextBoxFor(model => model.Amount, new
               {
                   id = "txtbxLT_" + testType.ShortName + "_Amount_" + liabilityTestIndex,
                   data_toggle = "tooltip",
                   title = "Amount",
                   Name = liabilityTestDetailPrefix + Html.NameFor(d => d.Amount),
                   @class = "form-control liability-amount verify-amount validate-dollarAmountNonNegative",
                   maxlength = 12
               })
        </div>
    </div>

    @if (showDescription)
    {
        <div class="form-group col-4">
            <label for="@("txtbxLT_" + testType.ShortName + "_Amount_" + liabilityTestIndex)">Description</label>
            <div class="input-group">
                @Html.TextBoxFor(model => model.Description, new
                   {
                       id = "txtbxLT_" + testType.ShortName + "_Description_" + liabilityTestIndex,
                       data_toggle = "tooltip",
                       title = "Description",
                       Name = liabilityTestDetailPrefix + Html.NameFor(d => d.Description),
                       @class = "form-control JS_EDLiabilityRemark validate-liabilityRemarkFormat",
                       maxlength = 250
                   })
            </div>
        </div>
    }

    <div class="form-group col-2 ">
        <label for="@("txtbxLT_" + testType.ShortName + "_EndDate_" + liabilityTestIndex)" class="form-label">Date Stopped</label>
        @Html.TextBoxFor(model => model.EndDate,
                "{0:MM/dd/yyyy}",
                new
                {
                    id = "txtbxLT_" + testType.ShortName + "_EndDate_" + liabilityTestIndex,
                    @Name = liabilityTestDetailPrefix + "EndDate",
                    placeholder = "MM/DD/YYYY",
                    @class = "form-control JS_date " + strDateStoppedPickerClass,
                    oncopy = "return false",
                    onpaste = "return false"
                })
    </div>

    <div class="col-1 order-12 pull-right pl-3 align-items-end d-flex">
        <div class="form-group">
            <button type="button" class="btn btn-sm btn-ed-circle btn-delete-phone btn-gradient btn-delete-liability-test" data-header-index="@liabilityTestIndex" data-detail-index="@liabilityTestIndex">@delete</button>
        </div>
    </div>
</div>
