﻿@model Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.ElderlyDisabledLiabilityViewModel
@using DD = Cares.Classes.DropDownValues
@using Cares.Api.Infrastructure.Enums
@using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel
@using Cares.Portal.Infrastructure
@using GlobalRes = Cares.Portal.Worker.Resources.Shared.Global
@using Cares.Api.Infrastructure

@{
    byte testTypeId = Convert.ToByte(ViewData[PortalConstants.ViewDataConstants.LiabilityTestType].ToString());
    LiabilityTestTypeType testType = LiabilityTestTypeType.GetLiabilityTestTypeTypeById(testTypeId);
    List<ElderlyDisabledLiabilityTestViewModel> theList = new List<ElderlyDisabledLiabilityTestViewModel>();
    string listName = "LiabilityTests";
}

@switch (testType.Id)
{
    case (byte)enumLiabilityTestType.Infrequent_Irregular:
        theList = Model.Infrequent_IrregularLiabilityTests;
        listName = "Infrequent_IrregularLiabilityTests";
        break;
    case (byte)enumLiabilityTestType.Other:
        theList = Model.OtherLiabilityTests;
        listName = "OtherLiabilityTests";
        break;
    case (byte)enumLiabilityTestType.VA_Aid_Attendance:
        theList = Model.VA_Aid_AttendanceLiabilityTests;
        listName = "VA_Aid_AttendanceLiabilityTests";
        break;
}

<div id="divLiabilityTests@(testType.ShortName) " class="card property-parcel-template shadow">
    <div class="card-header text-white d-flex justify-content-between align-items-center py-1">
        <label>Budget Calculation Fields for @testType.Description</label>
    </div>
    <div class="card-body p-3">
        <div id="divLiabilityTest_@(testType.ShortName)_Records">
            @for (int index = 0; index < theList.Count; index++)
            {
                string prefix = listName + "[{index}].";
                @Html.Partial("Liability/_LiabilityTestDetail", theList[index],
                    new ViewDataDictionary {
                        { PortalConstants.ViewDataConstants.LiabilityTestType, testTypeId},
                        { PortalConstants.ViewDataConstants.LiabilityTestIndex, index},
                        { PortalConstants.ViewDataConstants.NamePrefix, prefix },
                        { PortalConstants.ViewDataConstants.IsTemplate, false } })
            }
        </div>
    </div>
    <div class="card-footer">
        <input type="hidden" id="hdnLiabilityTests_@(testType.ShortName)_Counter" class="LiabilityTestListCounter" value="@(theList.Count)" />
        <button class="btn btn-info btn-add-liability-test btn-gradient shadow" data-short-name="@(testType.ShortName)" id="btnAddLiabilityTests_@(testType.ShortName)" type="button">Add</button>
    </div>
</div>

@* Empty template for JS cloning *@
<div id="divNewLiabilityTest_@(testType.ShortName)" style="display:none;">
    @Html.Partial("Liability/_LiabilityTestDetail", new ElderlyDisabledLiabilityTestViewModel()
        {
            LiabilityTestTypeId = testType.Type
        },
        new ViewDataDictionary {
            { PortalConstants.ViewDataConstants.LiabilityTestType, testTypeId},
            { PortalConstants.ViewDataConstants.LiabilityTestIndex, "{index}"},
            { PortalConstants.ViewDataConstants.NamePrefix, listName + "[{index}]." },
            { PortalConstants.ViewDataConstants.IsTemplate, true }  })
</div>
