﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class OtherAddresses {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal OtherAddresses() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.OtherAddresses", typeof(OtherAddresses).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter [NAME]&apos;s, (DOB:[DOB]) home address.
        /// </summary>
        public static string enterPersonsHomeAddress {
            get {
                return ResourceManager.GetString("enterPersonsHomeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter [NAME]&apos;s, (DOB:[DOB]) mailing address.
        /// </summary>
        public static string enterPersonsMailingAddress {
            get {
                return ResourceManager.GetString("enterPersonsMailingAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Address Information.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB:[DOB]) temporarily living outside Alabama?.
        /// </summary>
        public static string livingOutsideAlabama {
            get {
                return ResourceManager.GetString("livingOutsideAlabama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Home address.
        /// </summary>
        public static string noHomeAddress {
            get {
                return ResourceManager.GetString("noHomeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other address.
        /// </summary>
        public static string otherAddress {
            get {
                return ResourceManager.GetString("otherAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s [NAME]&apos;s, (DOB:[DOB]) home address?.
        /// </summary>
        public static string personsHomeAddress {
            get {
                return ResourceManager.GetString("personsHomeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s [NAME]&apos;s, (DOB:[DOB]) mailing address?.
        /// </summary>
        public static string personsMailingAddress {
            get {
                return ResourceManager.GetString("personsMailingAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Where will [NAME], (DOB:[DOB]) live in Alabama?.
        /// </summary>
        public static string whereWillPersonLive {
            get {
                return ResourceManager.GetString("whereWillPersonLive", resourceCulture);
            }
        }
    }
}
