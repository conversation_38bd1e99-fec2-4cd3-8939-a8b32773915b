﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="answer1_1" xml:space="preserve">
    <value>In order to protect your information, we have to take necessary steps to verify your identity, and to avoid any unauthorized person creating an account under your name.</value>
  </data>
  <data name="answer1_2" xml:space="preserve">
    <value>Here's how it works:</value>
  </data>
  <data name="answer1_3" xml:space="preserve">
    <value>You’ll be asked three to five questions that can only be answered by you.</value>
  </data>
  <data name="answer1_4" xml:space="preserve">
    <value>You’ll answer from a list of possible choices.</value>
  </data>
  <data name="answer1_5" xml:space="preserve">
    <value>When you answer enough questions correctly, you’ll be able to submit an application online.</value>
  </data>
  <data name="answer1_6" xml:space="preserve">
    <value>You have to verify your identity one time only.</value>
  </data>
  <data name="answer1_7" xml:space="preserve">
    <value>If your identity can’t be verified online, you’ll be told what to do next.</value>
  </data>
  <data name="answer1_8" xml:space="preserve">
    <value>If you do not complete the identity verification process, the process will begin again.</value>
  </data>
  <data name="answer1_9" xml:space="preserve">
    <value>Please do not call Medicaid/ALLKids helpdesk.</value>
  </data>
  <data name="answer2_1" xml:space="preserve">
    <value>A reference number is generated when we are unable to verify your identity online.</value>
  </data>
  <data name="answer2_2_1" xml:space="preserve">
    <value>If a reference number is provided, you will need to contact the Experian call center at </value>
  </data>
  <data name="answer2_2_2" xml:space="preserve">
    <value>**************</value>
  </data>
  <data name="answer2_2_3" xml:space="preserve">
    <value> and provide them with the reference number.</value>
  </data>
  <data name="answer2_3" xml:space="preserve">
    <value>A call center agent will ask you a few questions to verify your identity.</value>
  </data>
  <data name="answer2_4" xml:space="preserve">
    <value>Once you successfully verify your identity over the phone, you will be allowed to submit an application online on your next login.</value>
  </data>
  <data name="answer2_5" xml:space="preserve">
    <value>Experian call center hours</value>
  </data>
  <data name="answer2_6" xml:space="preserve">
    <value>Monday – Friday: 7:30 AM – 9:00 PM</value>
  </data>
  <data name="answer2_7" xml:space="preserve">
    <value>Saturday: 9:00 AM – 7:00 PM</value>
  </data>
  <data name="answer2_8" xml:space="preserve">
    <value>Sunday: 10:00 AM – 7:00 PM</value>
  </data>
  <data name="answer2_9" xml:space="preserve">
    <value>(Central Standard Time)</value>
  </data>
  <data name="answer3_1" xml:space="preserve">
    <value>In that case, you will be able to view the latest information we have on file for your household.</value>
  </data>
  <data name="answer3_2" xml:space="preserve">
    <value>You may not be able to change some of the personal information for your household members online.</value>
  </data>
  <data name="answer3_3" xml:space="preserve">
    <value>Please submit a paper application if any personal information needs to be updated.</value>
  </data>
  <data name="question1" xml:space="preserve">
    <value>Why do I need to verify my identity?</value>
  </data>
  <data name="question2" xml:space="preserve">
    <value>What do I need to do with the reference number which was provided?</value>
  </data>
  <data name="question3" xml:space="preserve">
    <value>What if I have been identified as an existing user?</value>
  </data>
  <data name="ridpButton" xml:space="preserve">
    <value>Identity Verification</value>
  </data>
</root>