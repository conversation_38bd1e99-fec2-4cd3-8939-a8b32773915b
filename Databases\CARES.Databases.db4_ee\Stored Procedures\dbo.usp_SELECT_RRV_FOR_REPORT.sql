USE [Db4_ee]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_SELECT_RRV_FOR_REPORT]
	@CancelDate DATE,
	@ProgramId TINYINT
AS
-- =============================================
-- Author:		<PERSON>
-- Create date:	08/28/2017
-- Description:	Select all needed information for rrv report for either allKids or Medicaid
-- Modified By: <PERSON><PERSON> on 07/20/2020 Get the income from Non_Magi_app_income table for MSP applications.
-- Modified By: <PERSON><PERSON> on 07/30/2020 Get verified income for MSP apps.
-- Modified By: <PERSON><PERSON> on 08/04/2020 - Clean and changes logic for income pull for MSP apps.
-- Modified By: <PERSON> on 08/20/2020 - Fixed an issue with Alias name mismatch.
-- Modified By: <PERSON><PERSON> on 08/20/2020 - Added a missed filter on SSA income pull and CleanedUp the unnecessary code.
-- Modified By: <PERSON> on 04/05/2021 - ENH 172621:Technical : Add columns to reports for Notice Mail out date process
-- Modified By: <PERSON> on 06/08/2022 - Bug 199440:RRV Month printing as the New Effective date on CHIP Renewals
--								as well as	  - Bug 199542:Technical : ACHN Information populating for MSP applicants in Auto-renewal file
-- Modified By: Vinith Golla on 06/11/2025 - PBI #244867 - Drop temp table [dbo].[#SSAIncome] that was missed (existing, not part of the current PBI).
-- Modified By: Vinith Golla on 06/13/2025 - PBI #244867 -	1) Add coverage period columns for each member (Start Date, Cancel Date) instead of effective (All kids data).
--														 -	2) Add outstanding balance amount (Past Due) for each member from the triggered application (All kids data).
-- Modified By: Geoffrey Goeters on 08/25/2025 - Adding prior enrollment dates for All Kids data to accompany prior balance
-- Modified BY: Geoffrey Goeters on 09/16/2025 - Adding additional join to account for cases where the enrollee isn't on the trigger app, and so the AllKidsPerson is
--												  showing null (additional join will pull the latest current enrollment at the time of the report
-- =============================================
BEGIN
	SET NOCOUNT ON;

	DROP TABLE IF EXISTS	[dbo].[#RRVRenewals],
							[dbo].[#ContactAddress],
							[dbo].[#WithAddress],
							[dbo].[#IncomeInfo],
							[dbo].[#AppIds],
							[dbo].[#PersonalIncomeInfo],
							[dbo].[#SummedIncome],
							[dbo].[#SSAIncome],
							[dbo].[#LatestCurrentEnrollment];

    CREATE TABLE [dbo].[#RRVRenewals]
    (
	   ContactFullName VARCHAR(200),
	   ContactId BIGINT,
	   ApplicationId BIGINT,
	   AllkidsPerson VARCHAR(500),
	   MedicaidRenewedPerson VARCHAR(500)
    );

	CREATE INDEX RRVRenewalsContactId ON [dbo].[#RRVRenewals]
    (
	   ContactId
    );

	CREATE INDEX RRVRenewalsApplicationId ON [dbo].[#RRVRenewals]
    (
	   ApplicationId
    );

	CREATE TABLE [dbo].[#ContactAddress]
    (
		rowId BIGINT,
		ContactId BIGINT,
		ContactAddressLine1 VARCHAR(500),
		ContactAddressLine2 VARCHAR(500),
		ContactCityStateZip VARCHAR(100),
		ApplicationId BIGINT,
		PregnancyProvider VARCHAR(50),
		PregnancyTelephone VARCHAR(15)
    );

	CREATE INDEX ContactAddressApplicationId ON [dbo].[#ContactAddress]
    (
	   ApplicationId
    );

	CREATE TABLE [dbo].[#WithAddress]
    (

		ContactFullName VARCHAR(200),
		ContactAddressLine1 VARCHAR(500),
		ContactAddressLine2 VARCHAR(500),
		ContactCityStateZip VARCHAR(100),
		ApplicationId BIGINT,
		AllkidsPerson VARCHAR(500),
		MedicaidRenewedPerson VARCHAR(500),
		PregnancyProvider VARCHAR(50),
		PregnancyTelephone VARCHAR(15),
		ContactId BIGINT
    );

	CREATE INDEX WithAddressApplicationId ON [dbo].[#WithAddress]
    (
	   ApplicationId
    );

	CREATE TABLE [dbo].[#AppIds]
    (
		ApplicationId BIGINT
    );

	CREATE INDEX AppIdsApplicationId ON [dbo].[#AppIds]
    (
	   ApplicationId
    );

	CREATE TABLE [dbo].[#PersonalIncomeInfo]
    (
		ApplicationId BIGINT,
		FullName VARCHAR(200),
		PersonId BIGINT,
		CanCompute BIT,
		MonthlyIncome DECIMAL(18,2),
		StringIncome VARCHAR(200)
    );

	CREATE TABLE [dbo].[#SummedIncome]
    (
		PersonId BIGINT,
		SummedIncome DECIMAL(18,2)
    );

	CREATE INDEX SummedIncomePersonId ON [dbo].[#SummedIncome]
    (
	   PersonId
    );

	CREATE TABLE [dbo].[#IncomeInfo]
    (
		ApplicationId BIGINT,
		MedicaidPersonIncome VARCHAR(200)
    );

	CREATE INDEX IncomeInfoApplicationId ON [dbo].[#IncomeInfo]
    (
	   ApplicationId
    );

	CREATE TABLE [dbo].[#AppsWithMSP]
    (
		ApplicationId BIGINT
	);

	SELECT PERSON_ID, START_DATE, CANCEL_DATE, CURRENT_AMOUNT
	INTO [dbo].[#LatestCurrentEnrollment]
	FROM (
		SELECT aen.PERSON_ID, AEN.START_DATE, AEN.CANCEL_DATE, AEN.CURRENT_AMOUNT, ROW_NUMBER() OVER(PARTITION BY aen.PERSON_ID ORDER BY aen.CANCEL_DATE DESC) AS rn
		FROM dbo.APPLICATION_ENROLLMENT AS aen WITH(NOLOCK)
		JOIN dbo.APPLICATION_ELIGIBILITY AS ael WITH(NOLOCK) ON ael.APPLICATION_ID = aen.APPLICATION_ID AND ael.PERSON_ID = aen.PERSON_ID
		JOIN dbo.APPLICATION_RENEWAL_MEMBER AS arm WITH(NOLOCK) ON arm.PERSON_ID = aen.PERSON_ID
		WHERE GETDATE() BETWEEN aen.START_DATE and AEN.CANCEL_DATE AND aen.START_DATE < aen.CANCEL_DATE
			 AND @CancelDate = ARM.CANCEL_DATE
		) AS latestCurrentEnrollment
	WHERE rn = 1

	INSERT	INTO [dbo].[#RRVRenewals]
	SELECT  dbo.fnProperCase(Contact.FIRST_NAME + ' ' + IIF(Contact.MIDDLE_NAME IS NULL, '', Contact.MIDDLE_NAME + ' ') + Contact.LAST_NAME) AS ContactFullName,
			Contact.PERSON_ID AS ContactId,
			ARM.RENEWED_APPLICATION_ID AS ApplicationId,
			dbo.fnProperCase(PB.FIRST_NAME + ' ' + IIF(PB.MIDDLE_NAME IS NULL, '', PB.MIDDLE_NAME + ' ') + PB.LAST_NAME) + '|' +
			CONVERT(VARCHAR(10), PB.DOB, 101) + '|' +
			CONVERT(VARCHAR(10), AE.START_DATE, 101) + '|' +
			CONVERT(VARCHAR(10), AE.CANCEL_DATE, 101) + '|' +
			CONVERT(VARCHAR(10), ISNULL(TAE.START_DATE, lce.START_DATE), 101) + '|' + 
			CONVERT(VARCHAR(10), ISNULL(TAE.CANCEL_DATE, lce.CANCEL_DATE),101) + '|' +
			ISNULL(refFeeCode.FEE_CODE_DESC, 'No Fee') + '|' +
			'$' + CAST(ISNULL(AE.CURRENT_AMOUNT, 0) AS VARCHAR(10)) + '|' +
			'$' + CAST(COALESCE(TAE.CURRENT_AMOUNT, lce.CURRENT_AMOUNT, 0) AS VARCHAR(10)) + '|'AS AllkidsPerson,
			dbo.fnProperCase(PB.FIRST_NAME + ' ' + IIF(PB.MIDDLE_NAME IS NULL, '', PB.MIDDLE_NAME + ' ') + PB.LAST_NAME) + ' | ' +
			'XXX-XX-' + RIGHT(PB.SSN, 4) + ' | ' + CONVERT(VARCHAR(10), PB.DOB, 101) + '  ;  ' AS MedicaidRenewedPerson
	FROM	[dbo].[RENEWAL_REDETERMINATION_VERIFICATION] AS RRV WITH (NOLOCK)
			INNER JOIN [dbo].[APPLICATION_RENEWAL_MEMBER] AS ARM WITH (NOLOCK) ON ARM.RENEWAL_MEMBER_ID = RRV.RENEWAL_MEMBER_ID
			INNER JOIN [dbo].[APPLICATION] AS App WITH (NOLOCK) ON App.APPLICATION_ID = ARM.RENEWED_APPLICATION_ID
			INNER JOIN [dbo].[sysnl_donotmodify$$PERSON] AS Contact WITH (NOLOCK) ON App.CONTACT_PERSON_ID = Contact.PERSON_ID
			INNER JOIN [dbo].[sysnl_donotmodify$$PERSON] AS PB WITH (NOLOCK) ON ARM.PERSON_ID = PB.PERSON_ID
			INNER JOIN [dbo].[APPLICATION_ENROLLMENT] AS AE WITH (NOLOCK) ON AE.PERSON_ID = ARM.PERSON_ID AND AE.APPLICATION_ID = ARM.RENEWED_APPLICATION_ID
			LEFT JOIN [dbo].[REF_FEE_CODE] AS refFeeCode WITH (NOLOCK) ON refFeeCode.FEE_CODE_ID = AE.FEE_CODE_ID
			INNER JOIN [dbo].[APPLICATION_ELIGIBILITY] AS AEL WITH (NOLOCK) ON AEL.PERSON_ID = ARM.PERSON_ID AND AEL.APPLICATION_ID = ARM.RENEWED_APPLICATION_ID
			LEFT JOIN [dbo].[APPLICATION_ENROLLMENT] AS TAE WITH (NOLOCK) ON TAE.PERSON_ID = ARM.PERSON_ID AND TAE.APPLICATION_ID = ARM.TRIGGER_APPLICATION_ID
			LEFT JOIN [dbo].[#LatestCurrentEnrollment] AS lce ON lce.PERSON_ID = ARM.PERSON_ID
	WHERE	@CancelDate = ARM.CANCEL_DATE
			AND AEL.PROGRAM_ID = @ProgramId

	INSERT INTO [dbo].[#ContactAddress]
	SELECT *
	FROM
	(
		SELECT  ROW_NUMBER() OVER (PARTITION BY RR.ApplicationId ORDER BY ADDR.ADDRESS_TYPE_ID) AS rowId,
				RR.ContactId,
				dbo.fnProperCase(ADDR.ADDRESS_LINE1) AS ContactAddressLine1,
				dbo.fnProperCase(ADDR.ADDRESS_LINE2) AS ContactAddressLine2,
				dbo.fnProperCase(ADDR.CITY) + ',' + refStatePerson.STATE_ABREV + ',' + ADDR.ZIPCODE AS ContactCityStateZip,
				RR.ApplicationId,
				ISNULL(PROVIDER, 'ANY MEDICAID PROVIDER') AS PregnancyProvider,
				ISNULL(TELEPHONE, '') AS PregnancyTelephone
		FROM    [dbo].[#RRVRenewals] AS RR
				INNER JOIN [dbo].[vw_ADDRESS] AS ADDR ON ADDR.PERSON_ID = rr.ContactId
				INNER JOIN [dbo].[REF_STATE] AS refStatePerson  WITH (NOLOCK) ON refStatePerson.STATE_ID = ADDR.STATE_ID
				LEFT JOIN [dbo].[REF_COUNTY_PROVIDER] AS RCP WITH (NOLOCK) ON RCP.COUNTY_ID = addr.COUNTY_ID
	) AS v
	WHERE v.rowId = 1

	INSERT INTO [dbo].[#AppsWithMSP]
	SELECT
	DISTINCT    AEN.APPLICATION_ID as ApplicationId
	FROM        dbo.vw_ELIGIBILITY_ENROLLMENT AEN WITH(NOLOCK)
		        INNER JOIN [dbo].[#ContactAddress] CA WITH(NOLOCK) ON AEN.APPLICATION_ID = CA.ApplicationId
	WHERE       AEN.PROGRAM_SUB_CATEGORY_ID IN (80		-- 92 D.O. SLMB Only.
                                               ,81		-- 93 D.O. Qualified Individual (QI-1).
                                               ,82		-- 95 D.O. QMB Only.
                                               ,83)		-- 97 Qualified Disabled Working Individual.

	Update [dbo].[#ContactAddress] set PregnancyProvider = NULL, PregnancyTelephone = NULL where ApplicationId in
	(
		SELECT	AEN.APPLICATION_ID as ApplicationId
		FROM	dbo.VW_ELIGIBILITY_ENROLLMENT AEN WITH(NOLOCK)
		        INNER JOIN dbo.#AppsWithMSP AWM ON AEN.APPLICATION_ID = AWM.ApplicationId
		GROUP BY AEN.APPLICATION_ID
		HAVING COUNT(DISTINCT AEN.PERSON_ID) = 1
	)

	DROP TABLE IF EXISTS [dbo].[#AppsWithMSP];

	INSERT	INTO [dbo].[#WithAddress]
	SELECT  ContactFullName,
			ContactAddressLine1,
			ContactAddressLine2,
			ContactCityStateZip,
			RR.ApplicationId,
			AllkidsPerson,
			MedicaidRenewedPerson,
			PregnancyProvider,
			PregnancyTelephone,
			CA.ContactId
	FROM    [dbo].[#RRVRenewals] AS RR
			INNER JOIN [dbo].[#ContactAddress] AS CA ON CA.ApplicationId = RR.ApplicationId

	DROP TABLE IF EXISTS [dbo].[#RRVRenewals], [dbo].[#ContactAddress];

	INSERT	INTO [dbo].[#AppIds]
	SELECT	DISTINCT ApplicationId
	FROM	[dbo].[#WithAddress]

	INSERT	INTO [dbo].[#PersonalIncomeInfo]
	SELECT  ApplicationId,
			dbo.fnProperCase(PB.FIRST_NAME + ' ' + IIF(PB.MIDDLE_NAME IS NULL, '', PB.MIDDLE_NAME + ' ') + PB.LAST_NAME) AS FullName,
		    PB.PERSON_ID AS PersonId,
			IIF(AID.INCOME_FREQUENCY_ID IN (1,2) AND WEEKLY_WORK_LOAD IS NULL, 0, 1) AS CanCompute,
			dbo.fnConvertToMonthlyIncome(
			CAST(IIF(AID.INCOME_FREQUENCY_ID IS NULL OR AID.INCOME_FREQUENCY_ID = 9, 8, AID.INCOME_FREQUENCY_ID) AS INT),
			INCOME_AMOUNT,
			CAST(WEEKLY_WORK_LOAD AS INT)) AS MonthlyIncome,
			'$' + CAST(INCOME_AMOUNT AS VARCHAR(10)) + '    ' + ISNULL(RIF.INCOME_FREQUENCY_DESC, 'Once') AS StringIncome
	FROM	[dbo].[APPLICATION_INCOME] AS AI WITH (NOLOCK)
			INNER JOIN [dbo].[APPLICATION_INCOME_DETAIL] AS AID WITH (NOLOCK) ON AI.APP_INCOME_ID = AID.APP_INCOME_ID
			INNER JOIN [dbo].[#AppIds] AS A ON A.ApplicationId = AI.APPLICATION_ID
			INNER JOIN [dbo].[sysnl_donotmodify$$PERSON] AS PB WITH (NOLOCK) ON AI.PERSON_ID = PB.PERSON_ID
			LEFT JOIN [dbo].[REF_INCOME_FREQUENCY] AS RIF WITH (NOLOCK) ON AID.INCOME_FREQUENCY_ID = RIF.INCOME_FREQUENCY_ID

	--For MSP Applications, get the income from Hub tables.

	CREATE TABLE [dbo].[#SSAIncome]
	(
		APPLICATION_ID INT,
		PERSON_ID INT,
		MONTHLY_INCOME DECIMAL(18,2)
	)

	INSERT
	INTO	[dbo].[#SSAIncome](APPLICATION_ID, PERSON_ID, MONTHLY_INCOME)
	SELECT	SSA.APPLICATION_ID,
			SSA.PERSON_ID,
			SSA.BENEFIT_CREDITED_AMT
	FROM	(
				SELECT	ROW_NUMBER() OVER (PARTITION BY  APP.APPLICATION_ID, APP.CONTACT_PERSON_ID ORDER BY MCI.REQUEST_MONTH_INFO_TYPE ASC) AS RowId,
						SCR.APPLICATION_ID,
						SCR.PERSON_ID,
						MCI.BENEFIT_CREDITED_AMT
				FROM	[dbo].[#AppIds] AS A
						INNER JOIN [dbo].[APPLICATION] AS APP WITH (NOLOCK) ON A.ApplicationId = APP.APPLICATION_ID
						INNER JOIN  [dbo].FH_SSA_COMPOSITE_RESPONSE AS SCR WITH (NOLOCK) ON APP.APPLICATION_ID = SCR.APPLICATION_ID
																							AND APP.CONTACT_PERSON_ID = SCR.PERSON_ID
						INNER JOIN [dbo].FH_MONTHLY_INCOME_INFORMATION AS MCI WITH (NOLOCK) ON SCR.FH_SSA_COMPOSITE_RESPONSE_ID = MCI.FH_SSA_COMPOSITE_RESPONSE_ID
				WHERE	APP.SUB_PROGRAM_CATEGORY_ID = 7 -- MSP App Only
						AND SCR.REQUEST_TYPE = 2
						AND SCR.MONTH_TITLE2_INCOME_INFO_IND IS NOT NULL
						AND SCR.MONTH_TITLE2_INCOME_INFO_IND = 1
			) AS SSA
	WHERE	SSA.RowId = 1

	CREATE INDEX IX_SSAIncomePersonId ON [dbo].[#SSAIncome]
    (
	   PERSON_ID
    );

	INSERT	INTO #PersonalIncomeInfo(ApplicationId, FullName, PersonId, CanCompute, MonthlyIncome)
	SELECT	SSA.APPLICATION_ID,
			dbo.fnProperCase(P.FIRST_NAME + ' ' + IIF(P.MIDDLE_NAME IS NULL, '', P.MIDDLE_NAME + ' ') + P.LAST_NAME) AS FullName,
			SSA.PERSON_ID,
			1,
			SSA.MONTHLY_INCOME
	FROM	[dbo].[#SSAIncome] AS SSA
			INNER JOIN [dbo].[PERSON] AS P WITH (NOLOCK) ON SSA.PERSON_ID = P.PERSON_ID

	INSERT	INTO #PersonalIncomeInfo(ApplicationId, FullName, PersonId, CanCompute, MonthlyIncome)
	SELECT	APP.APPLICATION_ID,
			dbo.fnProperCase(P.FIRST_NAME + ' ' + IIF(P.MIDDLE_NAME IS NULL, '', P.MIDDLE_NAME + ' ') + P.LAST_NAME) AS FullName,
			APP.CONTACT_PERSON_ID,
			1,
			[dbo].[fnGetEquifaxIncome](APP.APPLICATION_ID, APP.CONTACT_PERSON_ID)
	FROM	[dbo].[#AppIds] AS A
			INNER JOIN [dbo].[APPLICATION] AS APP WITH (NOLOCK) ON A.ApplicationId = APP.APPLICATION_ID
			INNER JOIN [dbo].[PERSON] AS P WITH (NOLOCK) ON APP.CONTACT_PERSON_ID = P.PERSON_ID
	WHERE	APP.SUB_PROGRAM_CATEGORY_ID = 7 -- MSP App Only

	DROP TABLE IF EXISTS	[dbo].[#AppIds],
							[dbo].[#SSAIncome];

	INSERT	INTO [dbo].[#SummedIncome]
	SELECT	DISTINCT PersonId,
			SUM(MonthlyIncome) AS SummedIncome
	FROM	[dbo].[#PersonalIncomeInfo]
	WHERE   CanCompute = 1
	GROUP BY PersonId

	INSERT INTO [dbo].[#IncomeInfo]
	SELECT DISTINCT ApplicationId,
		   FullName + '    ' + IIF(CanCompute = 1, '$' + CAST(SummedIncome AS VARCHAR(10)), StringIncome) + '  ;  ' AS MedicaidPersonIncome
	FROM   [dbo].[#PersonalIncomeInfo] AS PII
		   LEFT JOIN [dbo].[#SummedIncome] AS SI ON PII.PersonId = SI.PersonId

	DROP TABLE IF EXISTS	[dbo].[#PersonalIncomeInfo],
							[dbo].[#SummedIncome];

    SELECT	DISTINCT GETDATE() AS CurrentDate,
			ContactFullName,
			ContactAddressLine1,
			ContactAddressLine2,
			ContactCityStateZip,
			ApplicationId,
			RTRIM(REPLACE(
			  REPLACE(
				 (SELECT WA2.AllkidsPerson FROM #WithAddress AS WA2 WHERE WA1.ApplicationId = WA2.ApplicationId FOR XML PATH(''))
			  , '<AllkidsPerson>', '')
			, '</AllkidsPerson>', '')) AS AllkidsPeople,
			'|' + RTRIM(REPLACE(
			  REPLACE(
				 (SELECT WA2.MedicaidRenewedPerson FROM #WithAddress AS WA2 WHERE WA1.ApplicationId = WA2.ApplicationId FOR XML PATH(''))
			  , '<MedicaidRenewedPerson>', '')
			, '</MedicaidRenewedPerson>', '')) + '|' AS MedicaidRenewedPeople,
			'|' + RTRIM(REPLACE(
			  REPLACE(
				 ISNULL((SELECT II.MedicaidPersonIncome FROM [dbo].[#IncomeInfo] AS II WHERE WA1.ApplicationId = II.ApplicationId FOR XML PATH('')),
					ContactFullName + '    $0.00    ')
			  , '<MedicaidPersonIncome>', '')
			, '</MedicaidPersonIncome>', '')) + '|' AS MedicaidPeopleIncome,
			PregnancyProvider,
			PregnancyTelephone,
			ContactID as PersonId
    FROM	[dbo].[#WithAddress] AS WA1

	DROP TABLE IF EXISTS [dbo].[#WithAddress],[dbo].[#IncomeInfo];

END
GO