﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="addAnotherChild" xml:space="preserve">
    <value>Add another child</value>
  </data>
  <data name="applicantSSNStatement" xml:space="preserve">
    <value>We need a Social Security Number(SSN) if you want health coverage and have an SSN or can get one.  We use SSNs to check income and other information to see who is eligible for help paying for health coverage.  If [NAME] needs help getting an SSN, visit socialsecurity.gov, or call 1-************, TTY users should call 1-************.</value>
  </data>
  <data name="applyingForHealthCoverage" xml:space="preserve">
    <value>Applying for Health coverage?</value>
  </data>
  <data name="DrasticChange" xml:space="preserve">
    <value>You are not allowed to make changes on this page. If changes are needed then remove the household member and add him again with updated information. This can be completed from Household Summary page which will be displayed after you have reviewed the information for each household member.</value>
  </data>
  <data name="enterSSCardName" xml:space="preserve">
    <value>Enter the exact name as shown on [NAME]'s Social Security card</value>
  </data>
  <data name="header" xml:space="preserve">
    <value>Personal Information</value>
  </data>
  <data name="householdMemberNumberHeader" xml:space="preserve">
    <value>Household member [HHNBR] </value>
  </data>
  <data name="howIsPersonRelated" xml:space="preserve">
    <value>How is [NAME] related to [FILER]?</value>
  </data>
  <data name="minNbrOfHouseHoldRequired" xml:space="preserve">
    <value>There must be at least one household member applying to continue</value>
  </data>
  <data name="moreThanOneHousehold" xml:space="preserve">
    <value>Please include yourself in the number of household along with other applicants.</value>
  </data>
  <data name="nameSameOnSSCard" xml:space="preserve">
    <value>Is [NAME] the same name that appears on [HIS/HER] Social Security card?</value>
  </data>
  <data name="noDuplicateSSN" xml:space="preserve">
    <value>Duplicate social security numbers are not allowed in an application. Please correct it.</value>
  </data>
  <data name="nonApplicantSSNStatement" xml:space="preserve">
    <value>Providing your Social Security Number(SSN) can be helpful even if you don't want health coverage because it speeds up the application process.  We use SSNs to check income and other information to see who is eligible for help paying for health coverage.  If [NAME] needs help getting an SSN, visit socialsecurity.gov, or call 1-************, TTY users should call 1-************.</value>
  </data>
  <data name="numberOfHHMembers" xml:space="preserve">
    <value>How many people in your family and household want health coverage? Include yourself even if you are not applying.</value>
  </data>
  <data name="onlineRenewalInfo" xml:space="preserve">
    <value>You will be allowed to make changes to your household composition before submission, if required.</value>
  </data>
  <data name="otherRelationship" xml:space="preserve">
    <value>Other Relationship</value>
  </data>
  <data name="part1IsRelationshipOf" xml:space="preserve">
    <value>[NAME] is the [RELATION] </value>
    <comment>part 1, before relationship dropdown</comment>
  </data>
  <data name="part2IsRelationshipOf" xml:space="preserve">
    <value> of [NAME]</value>
    <comment>part 2, after relationship dropdown</comment>
  </data>
  <data name="personIsTheRelationOf" xml:space="preserve">
    <value>[NAME] is the [RELATION] of [FILER].</value>
  </data>
  <data name="ssnNote" xml:space="preserve">
    <value>if you are not applying for Health coverage, you can enter *********** as SSN or leave it empty.</value>
  </data>
  <data name="selectRelationship" xml:space="preserve">
    <value>Select relationship</value>
  </data>
</root>