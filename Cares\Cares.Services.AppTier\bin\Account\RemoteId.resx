﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="help" xml:space="preserve">
    <value>We need to verify your identify to keep someone else 
   from applying for health coverage in your name.</value>
  </data>
  <data name="Information1" xml:space="preserve">
    <value>Based on your information, below are some questions only you will be able to answer.</value>
  </data>
  <data name="Information2" xml:space="preserve">
    <value>Answer these questions below so that we can verify your Identity</value>
  </data>
  <data name="PageTitleRemoteId" xml:space="preserve">
    <value>Identity Verification</value>
  </data>
  <data name="warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="ErrorMessage" xml:space="preserve">
    <value>There is an error processing your request at this time. Please try again in few minutes.</value>
  </data>
  <data name="InfoMessage" xml:space="preserve">
    <value>Once your identity is verified you will be able to submit an application online.</value>
  </data>
  <data name="InfoMessage1" xml:space="preserve">
    <value>Your identity could not be verified. Please submit a paper application in order to apply for health coverage.</value>
  </data>
  <data name="InfoMessage3" xml:space="preserve">
    <value>You have exceeded the number of identity proofing attempts allowed for one day. Please try again tomorrow.</value>
  </data>
  <data name="VerificationErrorA" xml:space="preserve">
    <value>We are currently unable to verify your identity based on the information you have provided. Please call Experian at 1-************</value>
  </data>
  <data name="Help1" xml:space="preserve">
    <value>                      
                     Call Center Hours
 
              Mon-Fri - 7:30 am - 9:00 pm 
                 Sat - 9:00 am -7:00 pm 
                Sun - 10:00am - 7:00 pm 
                 Central Standard Time</value>
  </data>
  <data name="PaperApplicationMsg" xml:space="preserve">
    <value>You can also download a paper application by {0} and apply by mail.</value>
  </data>
  <data name="ClickingHere" xml:space="preserve">
    <value>clicking here</value>
  </data>
  <data name="ReferenceNumberLabel" xml:space="preserve">
    <value>Reference Number :</value>
  </data>
  <data name="VerificationErrorB" xml:space="preserve">
    <value> with the Reference Number provided below.</value>
  </data>
  <data name="SsnLabel" xml:space="preserve">
    <value>Social Security Number (SSN)</value>
  </data>
  <data name="SsnNeededMessage" xml:space="preserve">
    <value>Your SSN is needed in order to assist in confirming your identity.</value>
  </data>
  <data name="Footer" xml:space="preserve">
    <value>Identity checks provided by Experian. Please do not contact Medicaid or ALL KIDS help desk.</value>
  </data>
  <data name="PageTitleExperian" xml:space="preserve">
    <value>Identity Verification - Contact Experian</value>
  </data>
  <data name="PageTitlePaper" xml:space="preserve">
    <value>Identity Verification - Submit Paper Application</value>
  </data>
  <data name="PageTitleSSN" xml:space="preserve">
    <value>Identity Verification - SSN Needed</value>
  </data>
  <data name="PageTitleTryAgian" xml:space="preserve">
    <value>Identity Verification - Try Again Later</value>
  </data>
  <data name="Timeout" xml:space="preserve">
    <value>You took too long to answer. Please try again.</value>
  </data>
</root>