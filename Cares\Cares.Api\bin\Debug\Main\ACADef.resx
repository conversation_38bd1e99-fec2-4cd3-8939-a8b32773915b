﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="acaButton" xml:space="preserve">
    <value>Affordable Care Act Definitions</value>
  </data>
  <data name="definition10_1" xml:space="preserve">
    <value>A navigator is an individual or organization that's trained and able to help consumers, small businesses, and their employees as they look for health coverage options through the Marketplace, including completing eligibility and enrollment forms. These individuals and organizations are required to be unbiased. Their services are free to consumers.</value>
  </data>
  <data name="definition11_1" xml:space="preserve">
    <value>The period of time that individuals are eligible to enroll in a Qualified Health Plan can enroll in a plan in the Marketplace.  For 2014, the Open Enrollment Period is October 1, 2013–March 31, 2014. For 2015 and later years, the Open Enrollment Period is October 15 to December 7 of the previous year. Individuals may also qualify for Special Enrollment Periods outside of Open Enrollment if they experience certain events. (See Special Enrollment Period and Qualifying Life Event)</value>
  </data>
  <data name="definition11_2" xml:space="preserve">
    <value>You can submit an application for health coverage outside of the Marketplace, or apply for Medicaid or CHIP, at any time of the year.</value>
  </data>
  <data name="definition12_1" xml:space="preserve">
    <value>The Affordable Care Act provides a new tax credit to help you afford health coverage purchased through the Marketplace. Advance payments of the tax credit can be used right away to lower your monthly premium costs. If you qualify, you may choose how much advance credit payments to apply to your premiums each month, up to a maximum amount. If the amount of advance credit payments you get for the year is less than the tax credit you're due, you’ll get the difference as a refundable credit when you file your federal income tax return. If your advance payments for the year are more than the amount of your credit, you must repay the excess advance payments with your tax return.</value>
  </data>
  <data name="definition13_1" xml:space="preserve">
    <value>The net income you earn from your own trade or business. For example, any net income (profit) you earn from goods you sell or services you provide to others counts as self-employment income.</value>
  </data>
  <data name="definition13_2" xml:space="preserve">
    <value>Self-employment income could also come from a distributive share from a partnership.</value>
  </data>
  <data name="definition1_1" xml:space="preserve">
    <value>ACA is the comprehensive health care reform law enacted in March 2010. The law was enacted in two parts: The Patient Protection and Affordable Care Act was signed into law on March 23, 2010 and was amended by the Health Care and Education Reconciliation Act on March 30, 2010. The name “Affordable Care Act” is used to refer to the final, amended version of the law.</value>
  </data>
  <data name="definition2_1" xml:space="preserve">
    <value>The Affordable Care Act provides a new tax credit to help you afford health coverage purchased through the Marketplace. Advance payments of the tax credit can be used right away to lower your monthly premium costs. If you qualify, you may choose how much advance credit payments to apply to your premiums each month, up to a maximum amount. If the amount of advance credit payments you get for the year is less than the tax credit you're due, you'll get the difference as a refundable credit when you file your federal income tax return. If your advance payments for the year are more than the amount of your credit, you must repay the excess advance payments with your tax return. This is called premium tax credit.</value>
  </data>
  <data name="definition3_1" xml:space="preserve">
    <value>An authorized representative is someone who you choose to act on your behalf with the Marketplace, like a family member or other trusted person. Some authorized representatives may have legal authority to act on your behalf.</value>
  </data>
  <data name="definition4_1" xml:space="preserve">
    <value>An individual (affiliated with a designated organization) who is trained and able to help consumers, small businesses, and their employees as they look for health coverage options through the Marketplace, including helping them complete eligibility and enrollment forms. Their services are free to consumers.</value>
  </data>
  <data name="definition5_1" xml:space="preserve">
    <value>The share of costs covered by your insurance that you pay out of your own pocket. This term generally includes deductibles, coinsurance, and copayments, or similar charges, but it doesn't include premiums, balance billing amounts for non-network providers, or the cost of non-covered services. Cost sharing in Medicaid and CHIP also includes premiums.</value>
  </data>
  <data name="definition6_1" xml:space="preserve">
    <value>A discount that lowers the amount you have to pay out-of-pocket for deductibles, coinsurance, and copayments. You can get this reduction if you get health insurance through the Marketplace, your income is below a certain level, and you choose a health plan from the Silver plan category (see Health Plan Categories). If you're a member of a federally recognized tribe, you may qualify for additional cost-sharing benefits.</value>
  </data>
  <data name="definition7_1" xml:space="preserve">
    <value>A resource where individuals, families, and small businesses can learn about their health coverage options; compare health insurance plans based on costs, benefits, and other important features; choose a plan; and enroll in coverage. The Marketplace also provides information on programs that help people with low to moderate income and resources pay for coverage. This includes ways to save on the monthly premiums and out-of-pocket costs of coverage available through the Marketplace, and information about other programs, including Medicaid and the Children’s Health Insurance Program (CHIP). The Marketplace encourages competition among private health plans, and is accessible through websites, call centers, and in-person assistance.</value>
  </data>
  <data name="definition8_1" xml:space="preserve">
    <value>The type of coverage an individual needs to have to meet the individual responsibility requirement under the Affordable Care Act. This includes individual market policies, job-based coverage, Medicare, Medicaid, CHIP, TRICARE and certain other coverage.</value>
  </data>
  <data name="definition9_1" xml:space="preserve">
    <value>The figure used to determine eligibility for lower costs in the Marketplace and for Medicaid and CHIP. Generally, modified adjusted gross income is your adjusted gross income plus any tax-exempt Social Security, interest, or foreign income you have.</value>
  </data>
  <data name="question1" xml:space="preserve">
    <value>What is the Affordable Care Act?</value>
  </data>
  <data name="question10" xml:space="preserve">
    <value>What is a Navigator?</value>
  </data>
  <data name="question11" xml:space="preserve">
    <value>What is meant by the Open Enrollment Period?</value>
  </data>
  <data name="question12" xml:space="preserve">
    <value>What is a Premium Tax Credit?</value>
  </data>
  <data name="question13" xml:space="preserve">
    <value>What is Self-Employment Income?</value>
  </data>
  <data name="question2" xml:space="preserve">
    <value>What is a Advanced Premium Tax Credit?</value>
  </data>
  <data name="question3" xml:space="preserve">
    <value>What is an Authorized Representative?</value>
  </data>
  <data name="question4" xml:space="preserve">
    <value>What is a Certified Applicant Counselor?</value>
  </data>
  <data name="question5" xml:space="preserve">
    <value>What is Cost Sharing?</value>
  </data>
  <data name="question6" xml:space="preserve">
    <value>What is Cost Sharing Reduction?</value>
  </data>
  <data name="question7" xml:space="preserve">
    <value>What is the Health Insurance Marketplace?</value>
  </data>
  <data name="question8" xml:space="preserve">
    <value>What is meant by Minimum Essential Coverage?</value>
  </data>
  <data name="question9" xml:space="preserve">
    <value>What is Modified Adjusted Gross Income(MAGI)?</value>
  </data>
</root>