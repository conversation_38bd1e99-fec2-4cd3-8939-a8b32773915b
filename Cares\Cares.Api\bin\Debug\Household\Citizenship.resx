﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="alienNumber" xml:space="preserve">
    <value>Alien Number</value>
  </data>
  <data name="cardReceiptNumber" xml:space="preserve">
    <value>Card/Receipt Number</value>
  </data>
  <data name="citizenOrNational" xml:space="preserve">
    <value>Is [NAME], (DOB:[DOB]) a U.S. citizen or U.S. national?</value>
  </data>
  <data name="citizenshipCertificate" xml:space="preserve">
    <value>Certificate of Citizenship</value>
  </data>
  <data name="citizenshipCertificateNumber" xml:space="preserve">
    <value>Citizenship Certificate Number</value>
  </data>
  <data name="countryOfIssuance" xml:space="preserve">
    <value>Country of issuance</value>
  </data>
  <data name="dialogImmigrationStatus1" xml:space="preserve">
    <value>Permanent Resident Card('Green Card', I-551)</value>
  </data>
  <data name="dialogImmigrationStatus10" xml:space="preserve">
    <value>Certificate of Eligibility for Nonimmigrant(F-1) Student Status(I-20)</value>
  </data>
  <data name="dialogImmigrationStatus11" xml:space="preserve">
    <value>Certificate of Eligibility for Exchange Visitor(J-1) Status(DS2019)</value>
  </data>
  <data name="dialogImmigrationStatus12" xml:space="preserve">
    <value>Notice of Action(I-797)</value>
  </data>
  <data name="dialogImmigrationStatus2" xml:space="preserve">
    <value>Temporary I-551 Stamp(on passport or I-94, I-94A)</value>
  </data>
  <data name="dialogImmigrationStatus3" xml:space="preserve">
    <value>Machine Readable Immigrant Visa(with temporary I-551 language)</value>
  </data>
  <data name="dialogImmigrationStatus4" xml:space="preserve">
    <value>Employment Authorization Card(EAD, I-766)</value>
  </data>
  <data name="dialogImmigrationStatus5" xml:space="preserve">
    <value>Arrival/Department Record(I-94, I-94A)</value>
  </data>
  <data name="dialogImmigrationStatus6" xml:space="preserve">
    <value>Arrival/Department Record in foreign passport(I-94)</value>
  </data>
  <data name="dialogImmigrationStatus7" xml:space="preserve">
    <value>Foreign passport</value>
  </data>
  <data name="dialogImmigrationStatus8" xml:space="preserve">
    <value>Reentry permit(I-327)</value>
  </data>
  <data name="dialogImmigrationStatus9" xml:space="preserve">
    <value>Refugee Travel Document(I-571)</value>
  </data>
  <data name="dialogStatement1" xml:space="preserve">
    <value>Please review the list below of eligible statuses.</value>
  </data>
  <data name="dialogStatement2" xml:space="preserve">
    <value>If one of them pertains to this person, select YES to eligible immigration status.</value>
  </data>
  <data name="dialogStatement3" xml:space="preserve">
    <value>If there is no relevant status, this person might still be eligible for services if he/she has an emergency or is pregnant.</value>
  </data>
  <data name="dlgEligibleImmigrationHeader" xml:space="preserve">
    <value>Eligible Immigration Message</value>
  </data>
  <data name="documentDesc" xml:space="preserve">
    <value>Document description</value>
  </data>
  <data name="documentExpiration" xml:space="preserve">
    <value>Document expiration date</value>
  </data>
  <data name="documentType" xml:space="preserve">
    <value>Document Type</value>
  </data>
  <data name="doesNameHaveEligibleImmigrationStatus" xml:space="preserve">
    <value>Does [NAME], (DOB:[DOB]) have eligible immigration status?</value>
  </data>
  <data name="eligImmigrationDocTypeSupportingInfo" xml:space="preserve">
    <value>Please provide supporting information</value>
  </data>
  <data name="enterDocumentName" xml:space="preserve">
    <value>Enter the same name as shown on [NAME]'s, (DOB:[DOB]) document</value>
  </data>
  <data name="haveAnyTheseDocs" xml:space="preserve">
    <value>Does [NAME], (DOB:[DOB]) have any of these documents?</value>
  </data>
  <data name="header" xml:space="preserve">
    <value>Citizenship Information</value>
  </data>
  <data name="i94Number" xml:space="preserve">
    <value>I-94 number</value>
  </data>
  <data name="immigrationStatus" xml:space="preserve">
    <value>Does [NAME], (DOB:[DOB]) have eligible immigration status?</value>
  </data>
  <data name="inUSSince1996" xml:space="preserve">
    <value>Has [NAME], (DOB:[DOB]) lived in the U.S. since before August 1996?</value>
  </data>
  <data name="military" xml:space="preserve">
    <value>Is [NAME], (DOB:[DOB]) an honorably discharged veteran or active-duty member of the military?</value>
  </data>
  <data name="naturalizationCertificate" xml:space="preserve">
    <value>Naturalization Certificate</value>
  </data>
  <data name="naturalizationCertificateNumber" xml:space="preserve">
    <value>Naturalization Certificate Number</value>
  </data>
  <data name="naturalizedOrDerived" xml:space="preserve">
    <value>Is [NAME], (DOB:[DOB]) a naturalized or derived citizen?</value>
  </data>
  <data name="OtherDocumentSelectRequired" xml:space="preserve">
    <value>Please select at least one option from list of Other Document Types.</value>
  </data>
  <data name="passport" xml:space="preserve">
    <value>Passport  Number</value>
  </data>
  <data name="passportExpiration" xml:space="preserve">
    <value>Passport Expiration Date</value>
  </data>
  <data name="sameNameOnDocument" xml:space="preserve">
    <value>Is [NAME], (DOB:[DOB]) the same name that appears on [HIS/HER] document?</value>
  </data>
  <data name="selectDocumentType" xml:space="preserve">
    <value>Please select appropriate document type.</value>
  </data>
  <data name="sevisID" xml:space="preserve">
    <value>SEVIS ID Number</value>
  </data>
  <data name="visaNumber" xml:space="preserve">
    <value>Visa Number</value>
  </data>
  <data name="cardRecieptInvalid" xml:space="preserve">
    <value>&lt;ul&gt;
&lt;li&gt;Invalid Card/Receipt Number.&lt;/li&gt;
&lt;li&gt;Card/Receipt number must be  13 characters long  with the first 3 letters followed by 10 digits.&lt;/li&gt;&lt;li&gt;Card number can not be all same digits Eg{111,999}. &lt;/li&gt;
&lt;/ul&gt;</value>
  </data>
  <data name="docDescription" xml:space="preserve">
    <value>Document description</value>
  </data>
  <data name="i94" xml:space="preserve">
    <value>I-94 should follow one of the following format: &lt;br&gt;
    *  11 Numbers&lt;br&gt;
    *  [ 9 digit numeric][1 Alphabet][1 digit numeric]</value>
  </data>
</root>