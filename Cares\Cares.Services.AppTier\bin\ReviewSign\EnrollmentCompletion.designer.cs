﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.ReviewSign {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class EnrollmentCompletion {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EnrollmentCompletion() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.ReviewSign.EnrollmentCompletion", typeof(EnrollmentCompletion).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enrollment Completion.
        /// </summary>
        public static string enrollmentCompletionHeader {
            get {
                return ResourceManager.GetString("enrollmentCompletionHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You may be wondering what to do now..
        /// </summary>
        public static string enrollmentCompletionStatement {
            get {
                return ResourceManager.GetString("enrollmentCompletionStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What to do for everyone that&apos;s enrolled in CHIP..
        /// </summary>
        public static string whatToDoForChip {
            get {
                return ResourceManager.GetString("whatToDoForChip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What to do for everyone that&apos;s enrolled in Medicaid..
        /// </summary>
        public static string whatToDoForMedicaid {
            get {
                return ResourceManager.GetString("whatToDoForMedicaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What to do for everyone that&apos;s eligible for tax credits..
        /// </summary>
        public static string whatToDoForTaxCredits {
            get {
                return ResourceManager.GetString("whatToDoForTaxCredits", resourceCulture);
            }
        }
    }
}
