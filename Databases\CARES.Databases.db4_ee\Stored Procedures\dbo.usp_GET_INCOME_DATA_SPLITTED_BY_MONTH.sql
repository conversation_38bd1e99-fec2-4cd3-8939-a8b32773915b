CREATE PROCEDURE [dbo].[usp_GET_APPLICATION_INCOME_DATA_SPLITTED_BY_MONTH]
(
	@appId BIGINT = 0
)
AS
-- ==============================================================================================
-- Author:		<PERSON>
-- Create date:	09/10/2025
-- Description:	Gets the income data splitted by month based on AppID
-- Modified by:
--				<PERSON> : 9/15/25 : Update filter to get only Applicant Income.
--				<PERSON> : 9/16/25 : Update logic to show all records by type and month.
--				<PERSON> : 9/16/25 : Update logic iterate by App Income Id.
--				<PERSON> : 9/16/25 : Removing no needed logic.
-- ==============================================================================================
BEGIN
	SET NOCOUNT ON;

	DROP TABLE IF EXISTS #incomeData, #incomeIndividual, #incomeFiltered, #finalDataSplitted;

	CREATE TABLE #finalDataSplitted
	(
		RowN INT IDENTITY(1, 1),
		APPLICATION_NON_MAGI_INCOME_ID int,
		APPLICATION_NON_MAGI_INCOME_DETAIL_ID int,
		INCOME_TYPE_ID INT,
		INCOME_MONTH DATE,
		INCOME_DES NVARCHAR(100),
		VETERAN_STATUS NVARCHAR(50),
		VA_INDICATOR_ID INT,
		DETAIL_GROSS decimal(8,2),
		DETAIL_NET decimal(8,2)
	)

	-- Get all INCOME
	SELECT
		inc.APPLICATION_ID,
		inc.APPLICATION_NON_MAGI_INCOME_ID,
		inc.INCOME_TYPE_ID,
		ref.INCOME_DESC,
		det.VETERAN_STATUS,
		inc.VA_INDICATOR_ID,
		det.APPLICATION_NON_MAGI_INCOME_DETAIL_ID,
		det.INCOME_MONTH,
		det.DATE_STOP,
		det.GROSS_INCOME_AMOUNT as DetailGrossIncome,
		det.MONTHLY_COUNTABLE_NET_INCOME_AMOUNT as DetailNetIncome
	INTO #incomeData
	FROM application_non_magi_income_detail AS det
		INNER JOIN application_non_magi_income AS inc on inc.application_non_magi_income_id = det.application_non_magi_income_id
		INNER JOIN ref_income_type as ref on ref.INCOME_TYPE_ID = inc.income_type_id
	WHERE
		inc.APPLICATION_ID = @appId
		AND inc.INCOME_RELATIONSHIP_TYPE_ID = 1				-- 1 = Applicant
	ORDER BY 1 DESC

	--***************** GET INCOME HEADERS  *********************
	SELECT
		ROW_NUMBER () OVER(ORDER BY v.APPLICATION_NON_MAGI_INCOME_ID) AS RowN,
		v.APPLICATION_NON_MAGI_INCOME_ID
	INTO #incomeIndividual
	FROM #incomeData AS v
	GROUP BY APPLICATION_NON_MAGI_INCOME_ID


	DECLARE @recordType INT = 1;
	DECLARE @detailId INT;
	DECLARE @headerId INT;

	DECLARE @TotalHeader INT = 0;

	-- ************** Getting all existing detail records count
	SELECT @TotalHeader = count(*) FROM #incomeIndividual;


	WHILE @recordType <= @TotalHeader
	BEGIN

		DECLARE @year AS INT;
		DECLARE @priorGross AS DECIMAL = 0;

		SELECT @headerId = t.APPLICATION_NON_MAGI_INCOME_ID
		FROM #incomeIndividual as t
		WHERE t.RowN = @recordType;

		-- **************** SETTING  MONTHS LOOP *****************

		DECLARE @iterationMonth DATE;
		DECLARE @endMonth DATE = GETDATE();

		--TEMP VARIABLES FOR PREVIOUS RECORD IF NO CURRENT RECORD FOUND
		DECLARE @APPLICATION_NON_MAGI_INCOME_ID BIGINT;
		DECLARE @APPLICATION_NON_MAGI_INCOME_DETAIL_ID BIGINT;
		DECLARE @INCOME_TYPE_ID INT;
		DECLARE @INCOME_MONTH DATE;
		DECLARE @DATE_STOP DATE;
		DECLARE @INCOME_DESC VARCHAR(50);
		DECLARE @VETERAN_STATUS AS VARCHAR(50);
		DECLARE @VA_INDICATOR_ID INT;
		DECLARE @DetailGrossIncome DECIMAL(8,2);
		DECLARE @DetailNetIncome DECIMAL(8,2);

		-- Clearing out temp table for reuse.
		DROP TABLE IF EXISTS #incomeFiltered;

		--************** FILTERING INCOME PER TYPE
		SELECT
			ROW_NUMBER () OVER(ORDER BY D.INCOME_MONTH) AS RowN,
			APPLICATION_ID,
			APPLICATION_NON_MAGI_INCOME_ID,
			INCOME_TYPE_ID,
			INCOME_DESC,
			VETERAN_STATUS,
			VA_INDICATOR_ID,
			APPLICATION_NON_MAGI_INCOME_DETAIL_ID,
			INCOME_MONTH,
			DATE_STOP,
			DetailGrossIncome,
			DetailNetIncome
		INTO #incomeFiltered
		FROM #incomeData AS D
		WHERE D.APPLICATION_NON_MAGI_INCOME_ID = @headerId


		SELECT TOP 1
			@iterationMonth = d.INCOME_MONTH
		FROM #incomeFiltered AS d;

		--*************** LOOP STARTS  *******************
		WHILE @iterationMonth <= @endMonth
		BEGIN

			DECLARE @hasRecord AS INT = 0;

			SELECT @hasRecord = count(*)
			FROM #incomeFiltered AS f
			WHERE f.INCOME_MONTH = @iterationMonth;

			--************** IF EXIST A RECORD FOR THE MONTH TAKE IT. ELSE, CONTINUE INSERTING UNTIL DATE_STOP THEN INSERT ZERO GROSS
			IF @hasRecord > 0
			BEGIN
				SELECT
					@APPLICATION_NON_MAGI_INCOME_ID = APPLICATION_NON_MAGI_INCOME_ID,
					@APPLICATION_NON_MAGI_INCOME_DETAIL_ID = APPLICATION_NON_MAGI_INCOME_DETAIL_ID,
					@INCOME_TYPE_ID = INCOME_TYPE_ID,
					@INCOME_MONTH = INCOME_MONTH,
					@DATE_STOP = ISNULL(DATE_STOP, GETDATE()),
					@INCOME_DESC = INCOME_DESC,
					@VETERAN_STATUS = VETERAN_STATUS ,
					@VA_INDICATOR_ID = VA_INDICATOR_ID,
					@DetailGrossIncome = DetailGrossIncome,
					@DetailNetIncome = DetailNetIncome
				FROM #incomeFiltered AS f
				WHERE f.INCOME_MONTH = @iterationMonth;

			END
			ELSE
			BEGIN
				IF @iterationMonth > @DATE_STOP
					-- Clearing Gross to insert Zero.
					SELECT @DetailGrossIncome = 0, @DetailNetIncome = 0;
			END

			--**************** INSERTING DATA INTO TEMP TABLE TO BE RETRIEVED *****************
			INSERT INTO #finalDataSplitted (APPLICATION_NON_MAGI_INCOME_ID, APPLICATION_NON_MAGI_INCOME_DETAIL_ID,	INCOME_TYPE_ID,
												INCOME_MONTH, INCOME_DES, VETERAN_STATUS, VA_INDICATOR_ID, DETAIL_GROSS, DETAIL_NET)
			VALUES (@APPLICATION_NON_MAGI_INCOME_ID, @APPLICATION_NON_MAGI_INCOME_DETAIL_ID, @INCOME_TYPE_ID,
												@iterationMonth, @INCOME_DESC, @VETERAN_STATUS, @VA_INDICATOR_ID, @DetailGrossIncome, @DetailNetIncome);


			SET @iterationMonth = DATEADD(MONTH, 1, @iterationMonth);
		END

		SET @recordType = @recordType + 1;
	END

	--RESULT
	SELECT * FROM #finalDataSplitted;

	DROP TABLE IF EXISTS #finalDataSplitted;

	SET NOCOUNT OFF;
END