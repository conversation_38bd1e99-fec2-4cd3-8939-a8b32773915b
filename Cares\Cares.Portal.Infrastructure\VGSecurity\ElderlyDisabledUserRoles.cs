using System.Collections.Generic;
using static Cares.Portal.Infrastructure.VGSecurity.CaresSecurity;

namespace Cares.Portal.Infrastructure.VGSecurity
{
    public class ElderlyDisabledUserRoles
    {
        public bool DataEntry { get; set; }

        public bool Supervisor { get; set; }

        public bool Specialist { get; set; }

        public bool Senior { get; set; }

        public bool DataAdmin { get; set; }

        public bool Manager { get; set; }
    }

    /// <summary>
    /// Stores each E&D roles read-only access to the E&D screens.
    /// </summary>
    public static class ElderlyDisabledViewAccessByRole
    {
        /// <summary>
        /// The E&D read-only access while the application is in a terminal status. True means read-only.
        /// </summary>
        public static Dictionary<string, ElderlyDisabledUserRoles> ElderlyDisabledTerminalStatus = new Dictionary<string, ElderlyDisabledUserRoles>()
        {
            ["Application"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledRepresentative"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledSpouse"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledVeteranInformation"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledHouseholdMembers"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledNonMagiIncome"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledQitAndAllocation"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledProperty"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledResource"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLifeInsurance"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledPersonalProperty"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledMedicalInsurance"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ModifyEnrollmentAction"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLetters"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["Notes"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledEnrollmentHistory"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["WorkerReminders"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLiability"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLiabilityTests"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
        };

        /// <summary>
        /// The E&D read-only access while the application is in a non-terminal status. True means read-only.
        /// </summary>
        public static Dictionary<string, ElderlyDisabledUserRoles> ElderlyDisabledNonTerminalStatus = new Dictionary<string, ElderlyDisabledUserRoles>()
        {
            ["Application"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledRepresentative"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledSpouse"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledVeteranInformation"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledHouseholdMembers"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledNonMagiIncome"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledQitAndAllocation"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledProperty"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledResource"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLifeInsurance"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledPersonalProperty"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledMedicalInsurance"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLiability"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLiabilityTests"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledEligibilityEnrollment"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledEnrollComplete"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledMSPEnrollComplete"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["PendingApplicationAction"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledLetters"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["ElderlyDisabledEnrollmentHistory"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["Notes"] = new ElderlyDisabledUserRoles() { DataEntry = false, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
            ["WorkerReminders"] = new ElderlyDisabledUserRoles() { DataEntry = true, Supervisor = false, Specialist = false, Senior = false, Manager = false, DataAdmin = false },
        };

        /// <summary>
        /// Returns bool based on the userRoles
        /// </summary>
        /// <param name="edViewRoles"></param>
        /// <param name="role"></param>
        /// <returns></returns>
        public static bool GetUserAccessOnView(ElderlyDisabledUserRoles edViewRoles, int role)
        {
            // If for some reason this is null, we'll just return true and allow the user to continue
            if (edViewRoles == null)
            {
                return true;
            }

            switch ((UserRoles)role)
            {
                case UserRoles.EandD_Data_Admin: return edViewRoles.DataAdmin;
                case UserRoles.EandD_Supervisor: return edViewRoles.Supervisor;
                case UserRoles.EandD_Specialist: return edViewRoles.Specialist;
                case UserRoles.EandD_Data_Entry: return edViewRoles.DataEntry;
                case UserRoles.EandD_Senior: return edViewRoles.Senior;
                case UserRoles.EandD_Manager: return edViewRoles.Manager;
                case UserRoles.RMA_ADMIN: return false;
                case UserRoles.RMA_ELIGIBILITY: return false;
                case UserRoles.MCD_ADMIN: return false;

                default: return true;
            }
        }
    }
}