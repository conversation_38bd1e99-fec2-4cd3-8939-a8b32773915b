﻿body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td {
    margin: 0;
    padding: 0;
    font-family: 'Open Sans', sans-serif;
}

html, body {
}

body {
    font: 16px/19px 'Lato', Arial, sans-serif;
    color: #333;
    overflow: scroll;
    font-family: 'Open Sans', sans-serif;
}

.nav-tabs > .active > a,
.nav-tabs > .active > a:hover,
.nav-tabs > .active > a:focus {
    background-color: #F1F1F1;
    border: 1px solid #9B9696;
    border-bottom-color: transparent;
}

#loginMasterBody {
    background-image: url(../Content/images/LoginPic.jpg);
    background-repeat: no-repeat;
    background-position: center top;
    -moz-background-size: cover;
    -o-background-size: cover;
    -webkit-background-size: cover;
    background-size: cover;
}

.main_head {
    background-image: url(../Content/images/CHIP_Banner.jpg) !important;
    background-repeat: no-repeat;
    background-position: center top;
    -moz-background-size: 100% auto !important;
    -o-background-size: 100% auto !important;
    -webkit-background-size: 100% auto !important;
    background-size: 100% auto !important;
}


td {
    padding: 5px !important;
    margin-right: 10px;
    padding-top: 3px !important;
    padding-bottom: 3px !important;
    font-family: 'Open Sans', sans-serif;
}

button {
    font-family: 'Open Sans', sans-serif;
}

option {
    background-color: #f2eeee;
    font-family: 'Open Sans', sans-serif;
}

select {
    font-family: 'Open Sans', sans-serif;
}

.font10 {
    font-size: 10px !important;
}

.font14 {
    font-size: 14px !important;
}

.body-wrapper {
    position: relative;
    height: 1000px;
}

.background-gradient {
    position: absolute;
    top: 106px;
    width: 100%;
    height: 520px;
    background: #d8f3ff;
    background: -moz-linear-gradient(top,#d8f3ff 0,white 100%);
    background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#d8f3ff),color-stop(100%,white));
    background: -webkit-linear-gradient(top,#d8f3ff 0,white 100%);
    background: -o-linear-gradient(top,#d8f3ff 0,white 100%);
    background: -ms-linear-gradient(top,#d8f3ff 0,white 100%);
    background: linear-gradient(to bottom,#d8f3ff 0,#fff 100%);
}

.top-nav-bar {
    height: 35px;
    padding-top: 85px;
    padding-left: 105px;
}

    .top-nav-bar.stuck {
        position: fixed;
        top: 0;
        -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, .3);
        box-shadow: 0 2px 4px rgba(0, 0, 0, .3);
        z-index: 1000;
        width: 100%;
    }

    .top-nav-bar .top-nav-wrapper {
        width: 980px;
        margin: 0 auto;
        position: relative;
        top: -45%;
        left: -2%;
    }

    .top-nav-bar ul {
        height: 35px;
        border-left: 1px solid #004e87;
        border: 1px;
    }

        .top-nav-bar ul li.selected {
            color: white;
            background-color: #006EB9;
            line-height: 35px;
            padding: 0 20px;
            border-bottom: 1px solid #006EB9;
            border-left: 1px solid #005e9e;
            border-right: 1px solid #005e9e;
        }

        .top-nav-bar ul li {
            display: inline-block;
            height: 35px;
            border-left: 1px solid #006EB9;
            font-size: 18px;
            -webkit-box-shadow: 1px 1px 1px #6b6b6b;
            box-shadow: 1px 1px 1px #6b6b6b;
            line-height: 35px;
            background-color: #70BB35;
            font: 16px Arial,Helvetica,sans-serif;
            padding: 0;
            margin: 0;
            float: left;
        }

            .top-nav-bar ul li:first-child {
                border-top-left-radius: 10px !important;
                border-left-width: 0px;
            }

            .top-nav-bar ul li a {
                filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#b5dd6f',endColorstr='#64b738',GradientType=0);
                display: block;
                height: 35px;
                padding-left: 20px;
                padding-right: 20px;
                color: #FFF;
                text-decoration: none;
                -webkit-text-shadow: 0 1px 1px #5EA638;
                text-shadow: 0 1px 1px #5EA638;
                line-height: 35px;
                padding-top: 6px;
            }

.main_head {
    width: 100%;
    overflow: hidden;
    height: 110px;
    background: #006EB9;
    background: -moz-linear-gradient(left,#006eb9 35%,#004e87 60%) repeat scroll 0 0 transparent;
    background: -webkit-gradient(linear,left top,right top,color-stop(35%,#006eb9),color-stop(60%,#004e87)) repeat scroll 0 0 transparent;
    background: -webkit-linear-gradient(left,#006eb9 35%,#004e87 60%) repeat scroll 0 0 transparent;
    background: -o-linear-gradient(left,#006eb9 35%,#004e87 60%) repeat scroll 0 0 transparent;
    background: -ms-linear-gradient(left,#006eb9 35%,#004e87 60%) repeat scroll 0 0 transparent;
    background: linear-gradient(to right,#006eb9 35%,#004e87 60%) repeat scroll 0 0 transparent;
}

    .main_head div.head_wrap {
        width: 980px;
        padding-top: 25px;
        margin: 0 auto;
        color: #fff;
        height: 102px;
        text-align: right;
        position: relative;
    }

a {
    color: #004E87;
    outline: 0 none;
    text-decoration: none;
    font-family: 'Open Sans', sans-serif;
}

.main_nav {
    height: 0;
    position: relative;
    top: -24px;
    z-index: 4;
}

    .main_nav > div {
        width: 980px;
        margin: 0 auto;
        height: 48px;
        line-height: 48px;
        -webkit-box-shadow: 0 3px 8px rgba(0,0,0,0.4);
        box-shadow: 0 3px 8px rgba(0,0,0,0.4);
        z-index: 5;
        background: #FFF;
        background: -moz-linear-gradient(top,white 0,#eaeaec 100%);
        background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,white),color-stop(100%,#eaeaec));
        background: -webkit-linear-gradient(top,white 0,#eaeaec 100%);
        background: -o-linear-gradient(top,white 0,#eaeaec 100%);
        background: -ms-linear-gradient(top,white 0,#eaeaec 100%);
        background: linear-gradient(to bottom,#fff 0,#eaeaec 100%);
        border-radius: 6px 6px 6px 6px;
    }

        .main_nav > div > ul {
            border-right: 1px solid white;
            border-radius: 6px 0 0 6px;
            display: inline-block;
        }

            .main_nav > div > ul > li.selected {
                background: #78d6f5;
                background: -moz-linear-gradient(top,#78d6f5 0,#109fce 100%);
                background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#78d6f5),color-stop(100%,#109fce));
                background: -webkit-linear-gradient(top,#78d6f5 0,#109fce 100%);
                background: -o-linear-gradient(top,#78d6f5 0,#109fce 100%);
                background: -ms-linear-gradient(top,#78d6f5 0,#109fce 100%);
                background: linear-gradient(to bottom,#78d6f5 0,#109fce 100%);
                height: 48px;
                border-right: 1px solid #2aa0d0;
                border-left: 1px solid #2aa0d0;
            }

            .main_nav > div > ul > li.first-child {
                border-left: none 0 white;
                border-radius: 6px 0 0 6px;
            }

            .main_nav > div > ul > li {
                float: left;
                border-left: 1px solid #fff;
                border-right: 1px solid #ccc;
                position: static;
            }

                .main_nav > div > ul > li.selected > a {
                    color: white;
                    -webkit-text-shadow: 0 1px 1px rgba(0,0,0,0.8);
                    text-shadow: 0 1px 1px rgba(0,0,0,0.8);
                    height: 48px;
                    padding: 0 23px;
                }

                .main_nav > div > ul > li > a {
                    -webkit-text-shadow: 0 1px 1px #fff;
                    text-shadow: 0 1px 1px #fff;
                    height: 48px;
                    display: inline-block;
                    padding: 0 23px;
                }

.main_content {
    padding-bottom: 20px;
    font: 12px/1.2em "HelveticaNeue-Light","Helvetica Neue Light","Helvetica Neue",Helvetica,Arial,"Lucida Grande",sans-serif;
    position: relative;
    z-index: 3;
    width: 85%;
    margin: 0 auto 20px;
    margin-right: 10px;
}

.content {
    width: 81.1%;
    float: right;
    visibility: visible;
    position: static;
    vertical-align: top;
    clear: right;
    overflow: hidden;
    position: relative;
    z-index: 3;
}

.fullScreen {
    padding: 15px;
    visibility: visible;
    position: static;
    vertical-align: top;
    clear: right;
    overflow: hidden;
    position: relative;
    z-index: 3;
}

.forms {
    margin: 10px;
}

.home_footer {
    clear: both;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 20px 0 0;
    width: 100%;
    z-index: 1;
    position: relative;
    padding: 0 0 30px;
}

    .home_footer .main_foot {
        background: #fff;
        border-top: 1px solid #ccc;
    }

.small_foot {
    width: 100%;
    border-top: 3px solid #004e87;
    text-align: center;
}

::selection {
    background: #b3d4fc;
    -webkit-text-shadow: none;
    text-shadow: none;
    font-family: 'Open Sans', sans-serif;
}

textarea, input[type="text"],
input[type="password"], input[type="datetime"],
input[type="datetime-local"], input[type="date"],
input[type="month"], input[type="time"],
input[type="week"], input[type="number"],
input[type="email"], input[type="url"],
input[type="tel"], input[type="color"],
.uneditable-input {
    display: inline-block;
    font-size: 15px;
    color: #555555;
    vertical-align: middle;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    margin-bottom: 0px;
}

textarea {
    height: 50px !important;
}

.cares-radio-EandD {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: inline-block;
    position: relative;
    background-color: #ffffff;
    color: #666;
    top: 3px;
    height: 20px;
    width: 20px;
    border: 1px solid #787777;
    border-radius: 50px;
    margin-right: 7px;
    outline: none;
}

    .cares-radio-EandD:checked::after {
        content: ' ';
        width: 12px;
        height: 12px;
        border-radius: 50px;
        position: absolute;
        top: 0.2em;
        left: 0.2em;
        background: #26a839;
        -webkit-box-shadow: inset 0px 0px 10px rgba(45, 143, 30, 0.30);
        box-shadow: inset 0px 0px 10px rgba(45, 143, 30, 0.30);
        -webkit-text-shadow: 0px;
        text-shadow: 0px;
    }

    .cares-radio-EandD:checked::-ms-check {
        background-color: #fafafa;
        color: #26a839;
        border: 1px solid #5d930f;
        -webkit-box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1), inset 0px 0px 10px rgba(0,0,0,0.1);
        box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1), inset 0px 0px 10px rgba(0,0,0,0.1);
    }
.cares-radio {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: inline-block;
    position: relative;
    background-color: #f1f1f1;
    color: #666;
    top: 3px;
    height: 20px;
    width: 20px;
    border: 1px solid #787777;
    border-radius: 50px;
    margin-right: 7px;
    outline: none;
}

    .cares-radio:checked::after {
        content: ' ';
        width: 12px;
        height: 12px;
        border-radius: 50px;
        position: absolute;
        top: 0.2em;
        left: 0.2em;
        background: #26a839;
        -webkit-box-shadow: inset 0px 0px 10px rgba(45, 143, 30, 0.30);
        box-shadow: inset 0px 0px 10px rgba(45, 143, 30, 0.30);
        -webkit-text-shadow: 0px;
        text-shadow: 0px;
    }

    .cares-radio:checked::-ms-check {
        background-color: #fafafa;
        color: #26a839;
        border: 1px solid #5d930f;
        -webkit-box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1), inset 0px 0px 10px rgba(0,0,0,0.1);
        box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1), inset 0px 0px 10px rgba(0,0,0,0.1);
    }

input[type="checkbox"] {
    vertical-align: top;
}

.pushup {
    position: relative;
    top: -3px;
}

.leftMargin {
    margin-left: 10px;
}

.leftMargin20 {
    margin-left: 20px;
}

fieldset {
    border-radius: 5px;
    background: #F4FBFD;
    margin: 5px 5px 5px 0;
    padding: 5px 10px 5px 10px;
    -webkit-box-shadow: 0 0 5px rgba(0,0,0,.3);
    box-shadow: 0 0 5px rgba(0,0,0,.3);
    position: relative;
    border: 3px groove rgb(172, 175, 177);
}

.ssn-fieldset {
    background: #F4FBFD !important;
    margin: 2px 2px 2px 0 !important;
    padding: 2px !important;
    position: relative !important;
    border: 1px groove rgb(249, 252, 255) !important;
}


.custom-fieldset {
    background: #F4FBFD !important;
    margin: 5px 5px 5px 0 !important;
    padding: 5px 10px 5px 10px !important;
    position: relative !important;
    border: 1px groove rgb(249, 252, 255) !important;
}

#address {
    background: #F4FBFD;
    border: 0px;
}

legend {
    width: auto;
    padding: 0 5px 1px 5px;
    background: #2F5574;
    color: white;
    font-size: 14px;
    line-height: 18px;
    border-radius: 2px;
    margin-bottom: 0px;
}

.section {
    position: relative;
    margin: -5px 0;
    padding: 5px 5px 1px;
    background-color: #fff;
    border: 1px solid #ddd;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    font-family: 'Open Sans', sans-serif;
}

#NameSection.section:after {
    content: "Name";
    position: absolute;
    top: -1px;
    left: -1px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: bold;
    background-color: #469dc8;
    border: 1px solid #ddd;
    color: #f5f5f5;
    -webkit-border-radius: 4px 0 4px 0;
    -moz-border-radius: 4px 0 4px 0;
    border-radius: 4px 0 4px 0;
}

#AddressSection.section:after {
    content: "Address";
    position: absolute;
    top: -1px;
    left: -1px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: bold;
    background-color: #469dc8;
    border: 1px solid #ddd;
    color: #f5f5f5;
    -webkit-border-radius: 4px 0 4px 0;
    -moz-border-radius: 4px 0 4px 0;
    border-radius: 4px 0 4px 0;
}

.table {
    margin-bottom: 0px;
}

.centerButtons {
    position: relative;
    vertical-align: central;
}

.hideShowButton {
    position: relative;
    float: right;
    top: 3px;
    left: 3px;
    padding: 1px 5px;
    border-radius: 0px;
    font-size: 10.5px;
}

.AlignTop {
    vertical-align: top;
}

hr {
    border-top: 1px solid #A6A6A6;
    margin: 10px;
}

h5 {
    font-size: 15px;
    font-weight: 600;
    font-family: 'Open Sans', sans-serif;
}

.EmptyWidth {
    width: 39px;
}

.bottomBorder {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: lightgray;
    margin-bottom: 12px;
}

.Width100 {
    width: 100%;
}

.Width80 {
    width: 80%;
}

.Width50 {
    width: 50%;
}

.Width25 {
    width: 25%;
}

.Width35 {
    width: 35%;
}

.Width70 {
    width: 70%;
}

.Width20 {
    width: 20%;
}

.width150Px {
    width: 150px;
}

.width75Px {
    width: 75px;
}

.width100Px {
    width: 100px;
}

.width125Px {
    width: 125px;
}

.width160Px {
    width: 160px;
}

.width250Px {
    width: 250px;
}

.width260Px {
    width: 260px;
}



.width200Important
 {
    width: 200px !important;
}
.width75PercentImp {
    width:75% !important;
}

.PaddingLeft50 {
    padding-left: 50px;
}

.viewCss {
    background-color: #EDEDED;
    padding-left: 15px;
    padding-right: 5px;
}

div#spinner {
    display: none;
    width: 100px;
    height: 100px;
    position: fixed;
    top: 50%;
    left: 50%;
    background: url(spinner.gif) no-repeat center #fff;
    text-align: center;
    padding: 10px;
    font: normal 16px Tahoma, Geneva, sans-serif;
    border: 1px solid #666;
    margin-left: -50px;
    margin-top: -50px;
    z-index: 2;
    overflow: auto;
}

.divBox {
    border-radius: 10px;
    border: 2px groove;
    padding: 10px 10px 10px 20px;
}

.divmorepaddingBox {
    border-radius: 10px;
    border: 2px groove;
    padding: 10px 10px 50px 20px;
}

.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button {
    font-family: 'Open Sans', sans-serif;
}

.clsDialog {
    position: relative;
    z-index: 3;
    font-weight: 500 !important;
}

.verticalHr {
    width: .25em;
    height: 17em;
    background: #A6A6A6;
}

.loginErrorHeader {
    font-size: 16px !important;
    height: 22px !important;
    line-height: 22px !important;
}

.errorDiv {
    width: auto;
    height: auto;
    color: white;
    padding: 15px;
    -moz-border-radius: 5px; /* for Firefox */
    -webkit-border-radius: 5px; /* for Webkit-Browsers */
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
}

#ErrorHeader {
    text-align: center;
    font-size: 20px;
    background-color: #D13232;
    border-bottom-color: rgb(253, 247, 247);
    border-bottom-width: 2px;
    border-bottom-style: solid;
    border-radius: 5px 5px 0 0;
    height: 30px;
    line-height: 30px;
}

#InfoHeader {
    text-align: center;
    font-size: 20px;
    background-color: #3B5D2D;
    border-bottom-color: rgb(253, 247, 247);
    border-bottom-width: 2px;
    border-bottom-style: solid;
    border-radius: 5px 5px 0 0;
    height: 30px;
    line-height: 30px;
}

.frameDiv {
    padding: 0px;
    border: thin solid #000000;
    border-radius: 5px;
    background-color: rgba(255, 232, 226, 0.61);
}

#PersonUpdateSaveSucceeded {
    display: flex;
    width: 100%;
    justify-content: center;
}

    #PersonUpdateSaveSucceeded div.personUpdatedSaveSuccess {
        background-color: #415828;
        margin-left: 20%;
        margin-right: 20%;
        padding-top: 20px;
        padding-bottom: 20px;
        text-align: center;
        font-size: 1.75rem;
        color: white;
        font-weight: bold;
        border-radius: 10px;
        line-height: 30px;
        vertical-align: middle;
    }

#InfoHeader ~ ul li {
    color: #2D6810 !important;
}

    #InfoHeader ~ ul li a {
        color: #2D6810 !important;
    }

.errorDiv ul li a {
    color: #F80909;
}

.errorDiv ul li {
    color: #F80909;
}

.marginTop20 {
    margin-top: -20px;
}

.marginTop10 {
    margin-top: 10px;
}

.marginTop20Right5px {
    margin-top: -20px;
    margin-right: 5px;
}

#divLogin {
    position: relative;
    top: 571px;
    left: 28%;
    margin-top: 10px;
}

.table-condensed th, .table-condensed td {
    padding: 0px;
}

.fieldset {
    margin-top: 5px;
    margin-bottom: 5px;
}

.padding2 {
    padding: 2px;
}
.padding5 {
    padding: 5px;
}

.disabledLink {
    pointer-events: none;
    cursor: default;
    opacity: 0.4
}

.center-align {
    text-align: center !important;
}

.middle-align {
    vertical-align: middle !important;
    text-align: center !important;
}

.left-align {
    text-align: left !important;
}

.right-align {
    text-align: right !important;
}

.table-cell-border-right {
    border-right: 1px solid rgb(177, 175, 175) !important;
}

.title-width {
    background-color: #3498DB;
    color: #ffffff;
    height: 35px;
    margin: 10px 0 10px 0;
    width: 97%;
}

.condensed-header {
    background-color: rgba(157, 231, 241, 0.47);
    height: 20px !important;
    line-height: 20px !important;
    font-size: 13px;
    font-weight: bold;
}

.td-condensed {
    height: 14px !important;
    line-height: 14px !important;
}

.td0TopBorder {
    border-top: 0px !important;
}

.Heigth50 {
    height: 250px !important;
}

.iframe {
    width: 70%;
    height: 700px;
    margin-left: 3px;
}

.span12 {
    margin-left: 3px !important;
}

.span4 {
    margin-left: 3px !important;
}

.span3 {
    margin-left: 3px !important;
}

.inner-header-title {
    background-color: #3498DB;
    color: #ffffff;
    width: 20%;
    margin: 10px 0 10px 15px;
}

.full-width-div {
    display: inline-block;
    width: 100%
}

    .full-width-div .inner-div {
        float: left;
        width: 49.5%;
    }

        .full-width-div .inner-div table {
            width: 100%;
            margin-left: 0;
        }

            .full-width-div .inner-div table td {
                width: 50%;
            }

.header-width {
    background-color: #3498DB;
    color: #ffffff;
    width: 230px;
    margin: 10px 0 10px 15px;
}

.table-font .td-label-style label {
    font-weight: normal;
    font-size: 14px;
}

.popover {
    max-width: 1450px !important;
    z-index: 3000;
    border-radius: 20px;
    border-color: #88CCF1;
    background: rgb(250, 255, 189);
    background: -moz-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(246,246,246,1) 0%, rgba(237,237,237,1) 100%);
    background: -webkit-gradient(left top, right top, color-stop(0%, rgba(255,255,255,1)), color-stop(0%, rgba(246,246,246,1)), color-stop(100%, rgba(237,237,237,1)));
    background: -webkit-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(246,246,246,1) 0%, rgba(237,237,237,1) 100%);
    background: -o-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(246,246,246,1) 0%, rgba(237,237,237,1) 100%);
    background: -ms-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(246,246,246,1) 0%, rgba(237,237,237,1) 100%);
    background: linear-gradient(to right, rgba(255,255,255,1) 0%, rgba(246,246,246,1) 0%, rgba(237,237,237,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ededed', GradientType=1 );
}

.appDetailMaster {
    margin-left: 162px;
    top: 57px !important;
    margin-right: 15px;
    position: fixed;
    color: #333333;
    width: 89.5%;
    height: 30px;
    z-index: 300;
    border-radius: 3px;
    margin-top: 2px;
    background: #AECE97;
    border: 1px solid black;
}

    .appDetailMaster td {
        border-right: 2px groove white;
        padding-right: 2px;
    }

    .appDetailMaster input[type='text'] {
        border-radius: 10px;
    }

.viewTopMargin {
    margin-left: 162px;
    margin-top: 0px;
    margin-right: 15px;
}

.viewTopMarginNoLeft {
    margin-left: 20px;
    margin-top: 85px;
    margin-right: 15px;
}

.divTable {
    overflow-y: auto;
    max-height: 160px;
}

.wrap {
    width: auto;
}

    .wrap table {
        width: 100%;
        table-layout: fixed;
    }

.inner_table {
    max-height: 150px;
    overflow-y: auto;
}

.inner_table_big {
    max-height: 1500px;
    overflow-y: auto;
}

.btnBatchs {
    text-align: center;
    margin-top: -10px;
    background-color: rgb(209, 213, 216);
}

.noAppDetailMaster {
    margin-top: -41px;
}

.selRow {
    background-color: rgb(246, 243, 168);
}

.width20Px {
    width: 20px !important;
}

.width50Px {
    width: 50px !important;
}

.width75Px {
    width: 80px !important;
}

.width90Px {
    width: 90px !important;
}

.width100Px {
    width: 100px !important;
}

.width125Px {
    width: 133px !important;
}

.width150Px {
    width: 150px !important;
}

.width175Px {
    width: 175px !important;
}

.width200Px {
    width: 200px !important;
}

.width5Px {
    width: 5px !important;
}

.width60Px {
    width: 60px !important;
}

.width25Px {
    width: 25px !important;
}

.width110Px {
    width: 110px !important;
}

.width52Px {
    width: 52px !important;
}

.width450Px {
    width: 450px !important;
}

.height100Px {
    height: 100px !important;
}

.height20Px {
    height: 20px !important;
}

#tblSearchCriteria tr td label,
#accountingPaymentSearchResultslist tr td,
#userAccountSearchResultList tr td {
    font-size: 13px;
}

#tblPayerInformation tr td label,
#tblPayerInformation tr td input {
    font-size: 14px;
    height: 14px !important;
}

#tblPayerInformation {
    zoom: 0.9;
}

.pandiingLtrHdr {
    margin-right: 345px !important;
    color: white;
}

.Width1Px {
    width: 1px;
}

#personalInfoBtnDiv {
    background: rgba(157, 231, 241, 0.47);
    padding: 5px;
}

#btnSavePersonalInfo {
    position: relative;
    left: 40%;
}

#pcReqBtnDiv {
    background: rgba(157, 231, 241, 0.47);
    padding: 5px;
}

#btnSavePCRequest {
    position: relative;
    left: 40%;
}

.marginTopLeft {
    margin-top: -30px;
    margin-left: -165px;
}

/* General styles for the modal */
/*
Styles for the html/body for special modal where we want 3d effects
Note that we need a container wrapping all content on the page for the
perspective effects (not including the modals and the overlay).
*/
.md-perspective,
.md-perspective body {
    height: 100%;
    overflow: hidden;
}

    .md-perspective body {
        background: #222;
        -webkit-perspective: 600px;
        -moz-perspective: 600px;
        -ms-perspective: 600px;
        perspective: 600px;
    }

.container {
    background: #e74c3c;
    min-height: 100%;
}

.md-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 50%;
    max-width: 630px;
    min-width: 320px;
    height: auto;
    z-index: 2000;
    visibility: hidden;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    -o-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}

.md-show {
    visibility: visible;
}

.md-overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    visibility: hidden;
    top: 0;
    left: 0;
    z-index: 1040;
    opacity: 0.8;
    background-color: rgba(0, 0, 0, 0.56);
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}

.md-show ~ .md-overlay {
    opacity: 0.8;
    visibility: visible;
}
/* Content styles */
.md-content {
    color: #000000;
    background: #ffffff;
    position: relative;
    border-radius: 3px;
    margin: 0 auto;
}

    .md-content h3 {
        margin: 0;
        padding: 0.4em;
        text-align: center;
        font-size: 2.4em;
        font-weight: 300;
        opacity: 0.8;
        background: #000000;
        color: white;
        border-radius: 3px 3px 0 0;
    }

    .md-content > div {
        padding: 15px 40px 30px;
        margin: 0;
        font-weight: 300;
        font-size: 1.15em;
    }

        .md-content > div p {
            margin: 0;
            padding: 10px 0;
        }

        .md-content > div ul {
            margin: 0;
            padding: 0 0 30px 20px;
        }

            .md-content > div ul li {
                padding: 5px 0;
            }

    .md-content button {
        display: block;
        margin: 0 auto;
        font-size: 0.8em;
        font-family: 'Open Sans', sans-serif;
    }
/* Individual modal styles with animations/transitions */
/* Effect 1: Fade in and scale up */
.md-effect-1 .md-content {
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
    opacity: 0;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}

.md-show.md-effect-1 .md-content {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 1;
}

caption {
    background: none repeat scroll 0 0 #ddd;
    text-align: left;
    padding: 5px 5px;
    overflow: hidden;
}

.caption-01 {
    position: relative;
}

.tdEligibility {
    background-color: lightgreen !important;
    color: blue !important;
    font-weight: 700;
}

.tdStateAid {
    background-color: rgba(0, 0, 0, .3) !important;
    color: blue !important;
    font-weight: 700;
}

.tdDisabled {
    background-color: #469dc8 !important;
    color: #222 !important;
    font-weight: 700;
}

.trBGColor {
    background-color: rgb(175, 218, 247);
}

.black_overlay {
    display: none;
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 1001;
    -moz-opacity: 0.8;
    opacity: .80;
    filter: alpha(opacity=80);
    color: green !important;
    font-weight: 700 !important;
}

.white_content {
    display: none;
    position: absolute;
    top: 25%;
    left: 25%;
    width: 50%;
    height: 50%;
    padding: 16px;
    background-color: white;
    z-index: 1002;
    overflow: auto;
}

.dropdown-menu > li.column-menu ul {
    display: inline-block;
    list-style: none outside none;
    margin: 10px;
    width: 100%;
}

.dropdown-menu > li.column-menu li {
    display: inline;
    float: left;
    width: 100%;
}

.dropdown-menu > li.column-menu:first-child {
    margin-left: 0;
}

.dropdown-menu > li.column-menu li:not(:first-child) {
    margin-left: 10px;
}

.dropdown-menu > li.column-menu li:first-child {
    font-weight: bold;
    font-size: 1.5em;
    color: #340f5a;
}

.tv {
    font-weight: 500;
    font-size: 30PX;
    text-align: center !important;
}

.tv-th {
    text-align: center !important;
}

.img-lock {
    width: 25px;
    height: 25px;
    margin-bottom: 0px;
}

    .img-lock:hover {
        margin-bottom: 5px;
    }

.app-summary-button {
    height: 0px;
    margin-top: -18px;
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left: 9px !important;
    border-width: 0px !important;
}

.td-borderStyle {
    border-bottom: 1px solid rgb(211, 211, 211) !important;
}

/* Matches the width of the Create New button */
#divCreateNew {
    padding: 0;
    -moz-min-width: 86.5px;
    -ms-min-width: 86.5px;
    -o-min-width: 86.5px;
    -webkit-min-width: 86.5px;
    min-width: 86.5px;
}

/* Matches the width of the Update/Renewal button */
#divRenewApp {
    padding: 0;
    -moz-min-width: 179.2px;
    -ms-min-width: 179.2px;
    -o-min-width: 179.2px;
    -webkit-min-width: 179.2px;
    min-width: 179.2px;
}

    #divCreateNew > div, #divRenewApp > div {
        border-top: 1px solid #808080;
    }

        #divCreateNew > div:last-child, #divRenewApp > div:last-child {
            border-bottom: 1px solid #808080;
        }

        #divCreateNew > div:hover, #divRenewApp > div:hover {
            background-color: #52A552;
            color: white;
            cursor: pointer;
        }

.disabledButtonLink {
    pointer-events: none;
    -ms-opacity: 0.5;
    opacity: 0.5;
}

#hdnSM {
    border-radius: 0;
    line-height: 15px;
}

.alert-danger, .alert-error {
    color: #77302C;
    background-color: #f2dede;
    border-color: #eed3d7;
}

.btn-gradient {
    background-image: linear-gradient(to bottom, rgba(255,255,255,0.5), rgba(255,255,255,0.2) 49%,rgba(0,0,0,0.15) 51%, rgba(0,0,0,0.05));
    background-repeat: repeat-x;
}

.btn-style {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
    box-shadow: 0px 10px 14px -3px #3e7327;
}

.table tbody tr.info > td {
    background-color: white;
}

.bottom10 {
    margin-bottom: 10px;
}

.left15 {
    margin-left: 15px;
}

.header-style {
    background-color: #3498DB;
    color: #ffffff;
    height: 35px;
    margin: 10px 0 0 0;
}

.top15 {
    margin-top: 15px;
}

.paddingLeft0 {
    padding-left: 0px !important;
}

.panel-title-new {
    width: 100% !important;
}

    .panel-title-new .panel-title {
        font-size: 20px;
        padding-left: 15px;
        padding-top: 5px;
    }

.input-bold {
    font-weight: bold;
    font-size: 17px;
}


.fieldset-table .td-label-style label {
    font-size: 17px;
    margin-top: 3px;
    margin-bottom: 3px;
}

.race-table tr input[type="checkbox"] {
    margin-bottom: 15px;
}

.caption-new {
    color: #323839;
}
.errorTextColor {
    color :white
}

.btn-primary-new {
    color: #fff;
    background-color: #0000EE;
    border-color: #0000EE;
}

.header-height {
    height: 38px !important;
    line-height: 14px !important;
}

.width16 {
    width: 16%;
}

.otherIncome span {
    font-size: 12px;
    font-weight: bold;
    width: 40%;
    display: inline-block;
}

.otherIncome input {
    margin-bottom: 3px;
    margin-top: 3px;
    width: 50%;
}

.verified-amount span {
    font-size: 12px;
    font-weight: bold;
    width: 31%;
    display: inline-block;
}

.verified-amount input {
    margin-bottom: 3px;
    margin-top: 3px;
    width: 62%;
}

.MSPIntakeAddress  {
}

.maxWidth1000 {
    max-width: 1000px;
}

.label-background-color {
    background-color: whitesmoke;
}

    .label-background-color label {
        font-weight: bold;
    }
a { color: #0000EE; }

.noWrapCell {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 1px;
}

.no-dropdown-arrow::after {
    display: none;
}