﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.ReviewSign {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ApplicationSummary {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ApplicationSummary() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.ReviewSign.ApplicationSummary", typeof(ApplicationSummary).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string address {
            get {
                return ResourceManager.GetString("address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Information.
        /// </summary>
        public static string addressInformation {
            get {
                return ResourceManager.GetString("addressInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Type.
        /// </summary>
        public static string addressType {
            get {
                return ResourceManager.GetString("addressType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Citizen.
        /// </summary>
        public static string citizen {
            get {
                return ResourceManager.GetString("citizen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DOB.
        /// </summary>
        public static string dob {
            get {
                return ResourceManager.GetString("dob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Summary.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review the Household Summary grid above.  This is your final chance to modify incorrect household data, add additional members, or remove members.  Once you Save &amp; Continue, you will no longer be able to edit the Personal Information screens..
        /// </summary>
        public static string hhSummaryWarning {
            get {
                return ResourceManager.GetString("hhSummaryWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Household Information.
        /// </summary>
        public static string householdInformation {
            get {
                return ResourceManager.GetString("householdInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Household Summary.
        /// </summary>
        public static string householdSummary {
            get {
                return ResourceManager.GetString("householdSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Information.
        /// </summary>
        public static string incomeInformation {
            get {
                return ResourceManager.GetString("incomeInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Type.
        /// </summary>
        public static string incomeType {
            get {
                return ResourceManager.GetString("incomeType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Married.
        /// </summary>
        public static string married {
            get {
                return ResourceManager.GetString("married", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pregnant.
        /// </summary>
        public static string pregnant {
            get {
                return ResourceManager.GetString("pregnant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSN.
        /// </summary>
        public static string ssn {
            get {
                return ResourceManager.GetString("ssn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Status.
        /// </summary>
        public static string status {
            get {
                return ResourceManager.GetString("status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valid SSN.
        /// </summary>
        public static string validSSN {
            get {
                return ResourceManager.GetString("validSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check that the date of birth is correctly entered and that the name and Social Security number are entered the same as on the person&apos;s Social Security card..
        /// </summary>
        public static string validSSNNote {
            get {
                return ResourceManager.GetString("validSSNNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work Load.
        /// </summary>
        public static string workLoad {
            get {
                return ResourceManager.GetString("workLoad", resourceCulture);
            }
        }
    }
}
