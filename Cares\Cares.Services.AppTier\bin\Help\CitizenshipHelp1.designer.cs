﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Help {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class CitizenshipHelp {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CitizenshipHelp() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Help.CitizenshipHelp", typeof(CitizenshipHelp).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to • Arrival/Departure Record (I-94, I-94A):* I-94 Arrival/Departure Records are issued to people when they enter the U.S. The bottom portion of the I-94 should be stapled to the passport. Enter the I-94 number, which is usually found at the top, left-hand side of the form. The I-94 paper form will no longer be provided upon arrival to the U.S., except in limited circumstances. If a person doesn’t have a paper version of the I-94, they can get a copy at cbp.gov/I94..
        /// </summary>
        public static string arrival_I94 {
            get {
                return ResourceManager.GetString("arrival_I94", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;b&gt;When you enter your document type, you may need to enter one or more of these fields:&lt;hr&gt;&lt;/b&gt;
        ///•&lt;b&gt; Alien number:&lt;/b&gt; The alien number (also called the alien registration number) can be found on the immigration document. It starts with an “A” and ends with 8-9 numbers.&lt;br&gt;
        ///•&lt;b&gt; I-94 number:&lt;/b&gt; The I-94 number (also called the admission number) is printed on the I-94 or I-94A. This is an 11-digit number and is usually found at the top, left-hand side of the document.&lt;br&gt;
        ///•&lt;b&gt; Passport or document numbe [rest of string was truncated]&quot;;.
        /// </summary>
        public static string docFieldsHelp {
            get {
                return ResourceManager.GetString("docFieldsHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to •&lt;b&gt;Certificate of Eligibility for Exchange Visitor (J-1) Status (DS2019):&lt;/b&gt; Certificates of Eligibility for Exchange Visitor Status (DS-2019s) are the documents that support applications for exchange visitor visa statuses (J-1s or J-2s). Enter the SEVIS ID number, which is located at the top, right-hand side of the document..
        /// </summary>
        public static string eligiExhagne_J1Status {
            get {
                return ResourceManager.GetString("eligiExhagne_J1Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to •&lt;b&gt; Certificate of Eligibility for Nonimmigrant (F-1) Student Status (I-20):&lt;/b&gt; I-20 Certificates of Eligibility for Non-immigrant Student Status are the documents that support applications for student visa statuses (F-1s or F-2s). Enter the SEVIS ID number, which is located at the top, right-hand side of the document..
        /// </summary>
        public static string eligiNonImmi_F1_StudentSatatus {
            get {
                return ResourceManager.GetString("eligiNonImmi_F1_StudentSatatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to • &lt;b&gt;Employment Authorization Card (EAD, I-766):&lt;/b&gt;* Employment Authorization Cards (or I-766s) are issued to people who are authorized to work temporarily in the U.S. Enter the Alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. Also enter the care expiration date, as listed on the card..
        /// </summary>
        public static string employmentAuthoCard {
            get {
                return ResourceManager.GetString("employmentAuthoCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to •&lt;b&gt; Foreign passport:&lt;/b&gt; Passports from foreign countries are used when non-immigrants enter the U.S. Enter the passport number and passport expiration date..
        /// </summary>
        public static string foreignPassport {
            get {
                return ResourceManager.GetString("foreignPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to • &lt;b&gt;Arrival/Departure Record in foreign passport (I-94):&lt;/b&gt;* I-94 Arrival/Departure Records are issued to non-immigrants when they enter the U.S. The bottom portion of the I-94 should be stapled to the foreign passport. Enter the I-94 number, which is usually found at the top, left-hand side of the form. Also enter the passport number and expiration date..
        /// </summary>
        public static string foreignPassport_I94 {
            get {
                return ResourceManager.GetString("foreignPassport_I94", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to •&lt;b&gt; Permanent Resident Card (“Green Card,” I-551):&lt;/b&gt;* I-551 Permanent Resident Cards (or “Green Cards”) are issued to eligible immigrants who enter the U. S. to permanently live. To verify your eligible immigration status, enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. Also enter the card number, which is listed on the card. On the I-551, this number is listed under the heading “A#” or “USCIS#.”.
        /// </summary>
        public static string greenCard {
            get {
                return ResourceManager.GetString("greenCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string greenCardImgHelp {
            get {
                return ResourceManager.GetString("greenCardImgHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to • &lt;b&gt;Machine Readable Immigrant Visa (with temporary I-551 language):&lt;b&gt;* Machine-readable immigrant visas (MRIVs) with temporary I-551 language are documents indicating permanent resident status. Enter the Alien number (also known as alien registration number), which may start with an “A” and end with 8-9 numbers. Some MRIVs may not have an “A” before the number. Also enter the passport number..
        /// </summary>
        public static string machineReadableImmiVisa {
            get {
                return ResourceManager.GetString("machineReadableImmiVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to • &lt;b&gt;Notice of Action (I-797):&lt;/b&gt;* Notices of Action (I-797s) are communication from U.S. Citizenship and Immigration Service about immigration benefits. I-797s can be used for different purposes, like an approval notice, receipt notice, or a replacement for an I-94. Sometimes these notices have other documents attached to them, like I-360s (petitions for Amerasian, widow(er), or special immigrant statuses)..
        /// </summary>
        public static string noticeOfAction_I797 {
            get {
                return ResourceManager.GetString("noticeOfAction_I797", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to •&lt;b&gt; Reentry Permit (I-327):&lt;/b&gt; Re-entry permits (or I-327s), when valid, allow permanent residents to leave and re-enter the U.S. These permits are located in multi-purpose booklets called “U.S. Travel Documents.” Enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. This number is located at the top, right-hand side of the document..
        /// </summary>
        public static string reentryPermit {
            get {
                return ResourceManager.GetString("reentryPermit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to •&lt;b&gt; Refugee Travel Document (I-571):&lt;/b&gt; Refugee Travel Documents (or I-571s) may be issued to refugees and asylees for travel purposes. These permits should be located in multi-purpose booklets called “U.S. Travel Documents.” Enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers. This number is located at the top, right-hand side of the document..
        /// </summary>
        public static string refugee {
            get {
                return ResourceManager.GetString("refugee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to •&lt;b&gt; Temporary I-551 Stamp (on passport or I-94, I-94A):&lt;/b&gt;* Temporary I-551 stamps can be used to attest to permanent resident status. A temporary I-551 stamp will have a handwritten or stamped issue date and a “valid until” date. This stamp can be found on the front of an I-94 form or in the foreign passport. Enter the alien number (also called the alien registration number), which starts with an “A” and ends with 8-9 numbers..
        /// </summary>
        public static string temporary551Stamp {
            get {
                return ResourceManager.GetString("temporary551Stamp", resourceCulture);
            }
        }
    }
}
