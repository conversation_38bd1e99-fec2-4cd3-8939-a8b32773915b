﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AlienI94Number" xml:space="preserve">
    <value>Alien or I-94 number</value>
  </data>
  <data name="AlienNumber" xml:space="preserve">
    <value>Alien Number</value>
  </data>
  <data name="AnswerRequired" xml:space="preserve">
    <value>Answer is required</value>
  </data>
  <data name="ApplicationProcessError" xml:space="preserve">
    <value>There was a problem with the application process.</value>
  </data>
  <data name="AuthRepName" xml:space="preserve">
    <value>Name of authorized representative:</value>
  </data>
  <data name="AuthRepOrgId" xml:space="preserve">
    <value>Organization Id (if applicable)</value>
  </data>
  <data name="AuthRepOrgName" xml:space="preserve">
    <value>Organization name (if applicable)</value>
  </data>
  <data name="AuthRepPhone" xml:space="preserve">
    <value>Phone number:</value>
  </data>
  <data name="Award" xml:space="preserve">
    <value>You have been awarded coverage.</value>
  </data>
  <data name="BestContactPhone" xml:space="preserve">
    <value>Best contact phone number</value>
  </data>
  <data name="ByClickingHereAuthRep" xml:space="preserve">
    <value>By clicking here, you allow this person to sign your application, get official information about this application, and act for you on all future matters with the Alabama Medicaid Agency.</value>
  </data>
  <data name="CardNumberOrPassport" xml:space="preserve">
    <value>Card number or passport number</value>
  </data>
  <data name="CertificateNumber" xml:space="preserve">
    <value>Certificate Number</value>
  </data>
  <data name="ESign" xml:space="preserve">
    <value>By clicking here, you are swearing that everything you wrote on this form is true as far as you know.  We will keep your information secure and private.</value>
  </data>
  <data name="HaveInsurance" xml:space="preserve">
    <value>Do you have any insurance coverage?</value>
  </data>
  <data name="HaveMedicaidCard" xml:space="preserve">
    <value>Do you have an Alabama Medicaid Card?</value>
  </data>
  <data name="HonorableDischargeOrActiveDuty" xml:space="preserve">
    <value>Are you, or your spouse or parent, an honorably discharged veteran or an active-duty member of the US military?</value>
  </data>
  <data name="ImmigrationDocExpDate" xml:space="preserve">
    <value>Document expiration date</value>
  </data>
  <data name="ImmigrationDocType" xml:space="preserve">
    <value>Immigration document type</value>
  </data>
  <data name="ImmigrationOther" xml:space="preserve">
    <value>Other (Category code or country of issuance)</value>
  </data>
  <data name="ImmigrationStatusType" xml:space="preserve">
    <value>Status type (optional)</value>
  </data>
  <data name="IsCitizen" xml:space="preserve">
    <value>US Citizen, national or legal immigrant?</value>
  </data>
  <data name="IsHomeless" xml:space="preserve">
    <value>If homeless, check the box &amp; tell us where we can reach you.</value>
  </data>
  <data name="LivedInUsSince1996" xml:space="preserve">
    <value>Have you lived in the US since 1996?</value>
  </data>
  <data name="MedicaidNbr" xml:space="preserve">
    <value>Medicaid Nbr</value>
  </data>
  <data name="NameAsItAppears" xml:space="preserve">
    <value>Name as it appears on your immigration document</value>
  </data>
  <data name="NaturalizedOrDerived" xml:space="preserve">
    <value>Are you a naturalized or derived citizen?</value>
  </data>
  <data name="NotCitizenHasImmStatus" xml:space="preserve">
    <value>If you are not a US Citizen or National, do you have eligible immigration status?</value>
  </data>
  <data name="OtherPhone" xml:space="preserve">
    <value>(Other) phone number</value>
  </data>
  <data name="ReadingLanguage" xml:space="preserve">
    <value>What language do you read best?</value>
  </data>
  <data name="ReceiveInfoByEmail" xml:space="preserve">
    <value>Would you like to receive information by email?</value>
  </data>
  <data name="SevisId" xml:space="preserve">
    <value>SEVIS ID</value>
  </data>
  <data name="SignatureText" xml:space="preserve">
    <value>By clicking here, you are swearing that everything you wrote on this form is true as far as you know.
We will keep your information secure and private.</value>
  </data>
  <data name="SignatureTitle" xml:space="preserve">
    <value>SIGNATURE:</value>
  </data>
  <data name="SpokenLanguage" xml:space="preserve">
    <value>What language do you speak best?</value>
  </data>
  <data name="UnknownError" xml:space="preserve">
    <value>TODO: Some form of "An unknown error occurred" message here.</value>
  </data>
</root>