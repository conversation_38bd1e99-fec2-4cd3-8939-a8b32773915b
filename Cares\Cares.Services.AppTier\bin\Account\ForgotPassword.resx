﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CheckEmail" xml:space="preserve">
    <value>Please check your email and follow the provided instructions to reset your password. </value>
  </data>
  <data name="confirmPassword" xml:space="preserve">
    <value>Confirm New Password</value>
  </data>
  <data name="Instruction" xml:space="preserve">
    <value>Please enter your username and click Next.</value>
  </data>
  <data name="newPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="SideMenuHeader" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="resetSuccessfull" xml:space="preserve">
    <value>Your password reset was successfull! </value>
  </data>
  <data name="PasswordAnswerDoesNotMatch" xml:space="preserve">
    <value>Security Answer does not match. Please try again with correct answer.</value>
  </data>
  <data name="Failure" xml:space="preserve">
    <value>Password Reset Failed.  Please try again later.</value>
  </data>
  <data name="ForgotPasswordUsernameStepEmailhtml" xml:space="preserve">
    <value>&lt;!DOCTYPE html&gt;
&lt;html xmlns="http://www.w3.org/1999/xhtml"&gt;
&lt;head&gt;
    &lt;title&gt;&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;p&gt;Dear [FullName],&lt;/p&gt;

    &lt;p&gt;A password reset request has been received for your login.&lt;/p&gt;

    &lt;p&gt;To reset your password, please follow this link below :  &lt;br/&gt;&lt;br/&gt;[url]&lt;/p&gt;   
&lt;p&gt; &lt;b&gt; Note: - The above password reset link would expire in 24 hours&lt;/b&gt;&lt;/p&gt;
    &lt;p&gt;If you are not [FullName] or didn't request a password reset on the &lt;a href="[SiteHome]"&gt;Alabamacares.Alabama.gov &lt;/a&gt; website, please call us at 1-888-373-5437.&lt;/p&gt;
    
    &lt;p&gt;Regards,&lt;/p&gt;
    &lt;p&gt;AlabamaCares&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</value>
  </data>
  <data name="PasswordResetSuccessfullhtml" xml:space="preserve">
    <value>&lt;!DOCTYPE html&gt;
&lt;html xmlns="http://www.w3.org/1999/xhtml"&gt;
&lt;head&gt;
    &lt;title&gt;&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;p&gt;Dear [FullName],&lt;/p&gt;

    &lt;p&gt;Your password reset was successfull.&lt;/p&gt;

    &lt;p&gt;Please follow this link to Login to &lt;a href="[SiteHome]"&gt;Alabamacares.Alabama.gov &lt;/a&gt; :- &lt;br/&gt;&lt;br/&gt;"[url]"&lt;/p&gt;   

    &lt;p&gt;If you are not [FullName] or didn't request a password reset on the &lt;a href="[SiteHome]"&gt;Alabamacares.Alabama.gov &lt;/a&gt; website, please call us at 1-888-373-5437.&lt;/p&gt;

    &lt;p&gt;Regards,&lt;/p&gt;
    &lt;p&gt;AlabamaCares&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>User not found.</value>
  </data>
  <data name="Thanks" xml:space="preserve">
    <value>Thanks,</value>
  </data>
  <data name="resetLinkExpired" xml:space="preserve">
    <value>Password reset link expired !!!  Please click on the link below and go through the password reset process again.</value>
  </data>
</root>