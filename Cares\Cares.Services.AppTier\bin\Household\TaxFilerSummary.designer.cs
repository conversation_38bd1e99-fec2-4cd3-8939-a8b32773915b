﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class TaxFilerSummary {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TaxFilerSummary() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.TaxFilerSummary", typeof(TaxFilerSummary).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Dependents.
        /// </summary>
        public static string dependents {
            get {
                return ResourceManager.GetString("dependents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filing Jointly.
        /// </summary>
        public static string filingJointly {
            get {
                return ResourceManager.GetString("filingJointly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filing Taxes.
        /// </summary>
        public static string filingTaxes {
            get {
                return ResourceManager.GetString("filingTaxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Person Name.
        /// </summary>
        public static string personName {
            get {
                return ResourceManager.GetString("personName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spouse Name.
        /// </summary>
        public static string spouseName {
            get {
                return ResourceManager.GetString("spouseName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Filer Summary.
        /// </summary>
        public static string taxFilerSummaryHeader {
            get {
                return ResourceManager.GetString("taxFilerSummaryHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review the Tax Filer Summary above. This is your final chance to modify incorrect tax filer data.  Once you Save &amp; Continue, you will no longer be able to edit the tax filer screens..
        /// </summary>
        public static string warning {
            get {
                return ResourceManager.GetString("warning", resourceCulture);
            }
        }
    }
}
