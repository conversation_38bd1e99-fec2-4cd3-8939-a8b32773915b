{"Tabs": [{"Id": "<PERSON><PERSON>", "Class": "nav-item border-bottom", "ShowInTerminalStatus": true, "Anchors": [{"Id": "aApplicant", "Text": "Applicant", "Controller": "RMA", "Action": "Applicant", "Class": "nav-link"}]}, {"Id": "<PERSON><PERSON><PERSON><PERSON>", "Class": "nav-item border-bottom", "ShowInTerminalStatus": false, "Anchors": [{"Id": "aSpouse", "Text": "Spouse", "Controller": "RMA", "Action": "RefugeeSpouse", "Class": "nav-link"}]}, {"Id": "medicalInsuranceli", "Class": "nav-item border-bottom", "ShowInTerminalStatus": true, "Anchors": [{"Id": "aMedicalInsurance", "Text": "Medical Insurance", "Controller": "RMA", "Action": "RefugeeMedicalInsurance", "Class": "nav-link"}]}, {"Id": "incomeli", "Class": "nav-item border-bottom", "ShowInTerminalStatus": false, "Anchors": [{"Id": "aIncome", "Text": "Income", "Controller": "RMA", "Action": "RefugeeIncome", "Class": "nav-link"}]}, {"Id": "<PERSON><PERSON>", "Class": "nav-item border-bottom", "ShowInTerminalStatus": false, "Anchors": [{"Id": "aEligibility", "Text": "Elig/Enrollment", "Controller": "RMA", "Action": "RefugeeEligibilityEnrollment", "Class": "nav-link"}]}, {"Id": "enroll<PERSON><PERSON><PERSON><PERSON>", "Class": "nav-item border-bottom", "ShowInTerminalStatus": true, "Anchors": [{"Id": "aEnrollHistory", "Text": "Enroll History", "Controller": "EnrollmentHistory", "Action": "RefugeeIndex", "Class": "nav-link"}]}, {"Id": "lettersLi", "Class": "nav-item border-bottom", "ShowInTerminalStatus": true, "Anchors": [{"Id": "aLetters", "Text": "Letters", "Controller": "ElderlyDisabledLetters", "Action": "Index", "Class": "nav-link"}]}, {"Id": "notesli", "Class": "row m-0 align-items-center notes-item nav-item border-bottom", "ShowInTerminalStatus": true, "Anchors": [{"Id": "aNotesLabel", "Text": "Notes", "Controller": "Notes", "Action": "LookupNotesNew", "Class": "col no-hover notes-link nav-link"}, {"Id": "aNotesIcon", "Text": "Add Notes", "Controller": "Notes", "Action": "LookupNotesNew", "Class": "col no-hover notes-link nav-link", "IsIcon": true, "IconClass": "material-icons add-notes"}]}]}