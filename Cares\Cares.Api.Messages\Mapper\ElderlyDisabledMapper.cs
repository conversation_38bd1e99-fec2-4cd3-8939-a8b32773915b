﻿using AutoMapper;
using Cares.Api.Infrastructure.Enums;
using Cares.Api.Messages.Applications;
using Cares.Api.Messages.ApplicationSnapshot;
using Cares.Api.Messages.COLA;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Api.Messages.Landing;
using Cares.Api.Messages.Person;
using Cares.Api.Messages.Representatives;
using Cares.Portal.Worker.Models.Models.Application.ViewModel;
using Cares.Portal.Worker.Models.Models.Cola;
using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel;
using Cares.Portal.Worker.Models.Models.Enrollment.ViewModel;
using Cares.Portal.Worker.Models.Models.Landing.ViewModel;
using Cares.Portal.Worker.Models.Models.Person.ViewModel;
using System;
using AddressViewModel = Cares.Portal.Worker.Models.Models.Person.ViewModel.AddressViewModel;

namespace Cares.Api.Messages.Mapper
{
    public static class ElderlyDisabledMapper
    {
        /// <summary>
        /// Gets the shared mapper.
        /// </summary>
        /// <returns></returns>
        public static IMapper GetSharedMapper()
        {
            //  TODO: Add code as needed
            var config = new MapperConfiguration(new AutoMapper.Configuration.MapperConfigurationExpression());

            return config.CreateMapper();
        }

        /// <summary>
        /// A mapper specifically for Elderly & Disabled
        /// </summary>
        /// <returns></returns>
        public static IMapper GetElderlyDisabledMapper()
        {
            var config = new MapperConfiguration(cfg =>
            {
                CommonMapper.AddCommonMappers(cfg);
                addApplicationMappers(cfg);
                addPersonMappers(cfg);
                addElderlyDisabledMappers(cfg);
                addExpediteMappers(cfg);
                addFinancialInsMappers(cfg);
                addColaMapper(cfg);
            });
            return config.CreateMapper();
        }

        /// <summary>
        /// Maps to elderly disable representatives view model.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="repInfo">The representative information.</param>
        /// <returns></returns>
        public static ElderlyDisabledRepresentativesViewModel MapToElderlyDisableRepresentativesViewModel(long applicationId, RepresentativeInfo repInfo)
        {
            var mapper = GetElderlyDisabledMapper();

            // Create the view model
            ElderlyDisabledRepresentativesViewModel viewModel = new ElderlyDisabledRepresentativesViewModel()
            {
                ApplicationId = applicationId
            };

            // Fill this in with the first household member
            if (repInfo.SponsorData.HouseholdMembers.Count > 0)
            {
                viewModel.ContactPersonId = repInfo.SponsorData.HouseholdMembers[0].PersonId;

                // Loop through all the sponsors
                foreach (var sponsorData in repInfo.SponsorData.HouseholdMembers[0].SponsorDetail)
                {
                    var sponsorVM = mapper.Map<ElderlyDisabledSponsorViewModel>(sponsorData);

                    // Map the first address in the DTO list into our single Address
                    if (sponsorData.Addresses != null && sponsorData.Addresses.Count > 0)
                    {
                        sponsorVM.Address = mapper.Map<AddressViewModel>(sponsorData.Addresses[0]);
                    }

                    viewModel.Sponsors.Add(sponsorVM);
                }
            }

            viewModel.ApplicationStatusId = repInfo.ApplicationStatusId;
            viewModel.SubProgramCatogory = repInfo.SubProgramCatogory;

            return viewModel;
        }

        private static void addColaMapper(IMapperConfigurationExpression cfg)
        {
            cfg.CreateMap<ColaFactSheetCategoriesDto, ColaFactSheetReadonlyViewModel>();
            cfg.CreateMap<ColaFactSheetReadonlyViewModel, ColaFactSheetCategoriesDto>();

            cfg.CreateMap<ColaFactSheetCategoryDto, ColaFactSheetCategoryViewModel>()
                .ForPath(dest => dest.CategoryEnum, opt => opt.Ignore());
            cfg.CreateMap<ColaFactSheetCategoryViewModel, ColaFactSheetCategoryDto>();

            cfg.CreateMap<ColaFactSheetSubCategoryDto, ColaFactSheetSubCategoryViewModel>()
                .ForPath(dest => dest.SubCatEnum, opt => opt.Ignore());
            cfg.CreateMap<ColaFactSheetSubCategoryViewModel, ColaFactSheetSubCategoryDto>();

            cfg.CreateMap<ColaFactSheetYearValueDto, ColaFactSheetYearValueViewModel>();
            cfg.CreateMap<ColaFactSheetYearValueViewModel, ColaFactSheetYearValueDto>();

        }

        /// <summary>
        /// Dto To view model mapping for application objects
        /// </summary>
        /// <param name="cfg"></param>
        private static void addApplicationMappers(IMapperConfigurationExpression cfg)
        {
            // Dto to ViewModel
            cfg.CreateMap<ElderlyDisabledApplicationDetailDto, ApplicationDetailViewModel>();
            cfg.CreateMap<ApplicationDto, ApplicationViewModel>()
                // In case of no worker number, user may enter 00, which is a valid value. Since worker number is designed as a byte field in DB, doing additional translation here.
                .ForMember(dest => dest.WorkerNumber, opt => opt.MapFrom(src => src.WorkerNumber.HasValue ? src.WorkerNumber.Value.ToString("0#") : "00"))
                .ForMember(dest => dest.DataEnteredBy, opt => opt.MapFrom(src => src.CreatedBy));
            cfg.CreateMap<ApplicationResidencyInformation, ApplicationResidencyInfoViewModel>();
            cfg.CreateMap<ApplicationLivingArrangement, ApplicationLivingArrangementViewModel>();
            cfg.CreateMap<ElderlyDisabledFacilityDto, ElderlyDisabledFacilityViewModel>();
            cfg.CreateMap<ElderlyDisabledFacilityListDto, ElderlyDisabledFacilityListViewModel>();
            cfg.CreateMap<ElderlyDisabledWorkerNumberListDto, ElderlyDisabledWorkerNumberListViewModel>();
            cfg.CreateMap<ApplicationSnapshotDto, ApplicationSnapshotHtmlViewModel>();
            cfg.CreateMap<PersonalNeedsAllowanceDto, PersonalNeedsAllowanceViewModel>();

            // ViewModel to Dto
            cfg.CreateMap<ApplicationViewModel, ApplicationDto>()
                .ForMember(dest => dest.WorkerNumber, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.WorkerNumber) ? (byte?)0 : Convert.ToByte(src.WorkerNumber)))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.DataEnteredBy));
            cfg.CreateMap<ApplicationDetailViewModel, ElderlyDisabledApplicationDetailDto>();
            cfg.CreateMap<ApplicationResidencyInfoViewModel, ApplicationResidencyInformation>();
            cfg.CreateMap<ApplicationLivingArrangementViewModel, ApplicationLivingArrangement>();
            cfg.CreateMap<ElderlyDisabledFacilityViewModel, ElderlyDisabledFacilityDto>();
            cfg.CreateMap<ElderlyDisabledFacilityListViewModel, ElderlyDisabledFacilityListDto>();
            cfg.CreateMap<ElderlyDisabledWorkerNumberListViewModel, ElderlyDisabledWorkerNumberListDto>();
            cfg.CreateMap<ApplicationSnapshotHtmlViewModel, ApplicationSnapshotDto>();
            cfg.CreateMap<PersonalNeedsAllowanceViewModel, PersonalNeedsAllowanceDto>();
        }

        /// <summary>
        /// Dto to view model mappings for Person related objects
        /// </summary>
        /// <param name="cfg"></param>
        private static void addPersonMappers(IMapperConfigurationExpression cfg)
        {
            // Dto to ViewModel
            cfg.CreateMap<ElderlyDisabledRace, ElderlyDisabledRaceViewModel>();
            cfg.CreateMap<PersonAddresses, PersonAddressesViewModel>();
            cfg.CreateMap<PersonAddress, PersonAddressViewModel>();
            cfg.CreateMap<DependentPersonAddress, DependentPersonAddressViewModel>();
            cfg.CreateMap<Address, AddressViewModel>();
            cfg.CreateMap<PhoneAndPersonPhone, PhoneAndPersonPhoneViewModel>();
            cfg.CreateMap<PersonContactPreference, ElderlyDisabledContactPreferenceViewModel>();
            cfg.CreateMap<LandingPersonAlert, LandingPersonAlertViewModel>();

            // ViewModel to Dto
            cfg.CreateMap<ElderlyDisabledRaceViewModel, ElderlyDisabledRace>();
            cfg.CreateMap<PersonAddressesViewModel, PersonAddresses>();
            cfg.CreateMap<PersonAddressViewModel, PersonAddress>();
            cfg.CreateMap<DependentPersonAddressViewModel, DependentPersonAddress>();
            cfg.CreateMap<AddressViewModel, Address>();
            cfg.CreateMap<PhoneAndPersonPhoneViewModel, PhoneAndPersonPhone>();
            cfg.CreateMap<ElderlyDisabledContactPreferenceViewModel, PersonContactPreference>();
            cfg.CreateMap<LandingPersonAlertViewModel, LandingPersonAlert>();
        }

        /// <summary>
        /// Elderly & Disabled specific mappings
        /// </summary>
        /// <param name="cfg"></param>
        private static void addElderlyDisabledMappers(IMapperConfigurationExpression cfg)
        {
            #region DTO to View Model
            // Dto to ViewModel
            cfg.CreateMap<ApplicationElderlyDisabledDetail, ApplicationElderlyDisabledDetailViewModel>()
                .ForPath(dest => dest.HospitalAdmissionDate, opt => opt.MapFrom(src => src.HospitalAdmissionDate == DateTime.MinValue ? (DateTime?)null : src.HospitalAdmissionDate))
                .ForPath(dest => dest.NursingHomeAdmissionDate, opt => opt.MapFrom(src => src.NursingHomeAdmissionDate == DateTime.MinValue ? (DateTime?)null : src.NursingHomeAdmissionDate))
                .ForPath(dest => dest.MaritalStatusDate, opt => opt.MapFrom(src => src.MaritalStatusDate == DateTime.MinValue ? (DateTime?)null : src.MaritalStatusDate));
            cfg.CreateMap<ElderlyDisabledPerson, ElderlyDisabledPersonViewModel>()
                .ForMember(d => d.ConfirmSsn, opt => opt.MapFrom(s => s.Ssn));
            cfg.CreateMap<ElderlyDisabledPersonDetail, ElderlyDisabledPersonDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledApplicationDto, ElderlyDisabledApplicationViewModel>()
                .ForPath(dest => dest.PersonPhones.Phones, opt => opt.MapFrom(src => src.PersonPhones));
            cfg.CreateMap<Models.Shared.Address, AddressViewModel>()
                .ForMember(dest => dest.AddressLine2, opt => opt.MapFrom(src => src.AptSuiteLot))
                .ForMember(dest => dest.StateId, opt => opt.MapFrom(src => (short?)(string.IsNullOrEmpty(src.State) ? 0 : Convert.ToInt16(src.State))))
                .ForMember(dest => dest.CountyId, opt => opt.MapFrom(src => (short?)(string.IsNullOrEmpty(src.County) ? 0 : Convert.ToInt16(src.County))));
            cfg.CreateMap<Models.Application.Representative.SponsorDetail, ElderlyDisabledSponsorViewModel>()
                .ForPath(dest => dest.FirstName, opt => opt.MapFrom(src => src.Name.FirstName))
                .ForPath(dest => dest.MiddleName, opt => opt.MapFrom(src => src.Name.MiddleName))
                .ForPath(dest => dest.LastName, opt => opt.MapFrom(src => src.Name.LastName))
                .ForPath(dest => dest.SuffixId, opt => opt.MapFrom(src => src.Name.SuffixId))
                .ForPath(dest => dest.PhoneList.Phones, opt => opt.MapFrom(src => src.PhoneList));
            cfg.CreateMap<Models.Shared.Phone, PhoneAndPersonPhoneViewModel>()
                .ForPath(dest => dest.PhoneId, opt => opt.MapFrom(src => src.PhoneID))
                .ForPath(dest => dest.PhoneNo, opt => opt.MapFrom(src => src.PhoneNumber))
                .ForPath(dest => dest.Ext, opt => opt.MapFrom(src => src.PhoneExtension))
                .ForPath(dest => dest.PersonId, opt => opt.Ignore())
                .ForPath(dest => dest.PhoneTypeId, opt => opt.MapFrom(src => src.PhoneTypeID))
                .ForPath(dest => dest.IsDeleted, opt => opt.MapFrom(src => src.isDeleted));
            cfg.CreateMap<ElderlyDisabledSpouseDto, ElderlyDisabledSpouseViewModel>();
            cfg.CreateMap<ElderlyDisabledFormerSpouseDto, ElderlyDisabledFormerSpouseViewModel>()
                .ForPath(dest => dest.ConfirmSsn, opt => opt.MapFrom(src => src.Ssn))
                .ForPath(dest => dest.MarriageBeginDate, opt => opt.MapFrom(src => src.MarriageBeginDate == DateTime.MinValue ? (DateTime?)null : src.MarriageBeginDate))
                .ForPath(dest => dest.MarriageEndDate, opt => opt.MapFrom(src => src.MarriageEndDate == DateTime.MinValue ? (DateTime?)null : src.MarriageEndDate))
                .ForMember(dest => dest.MarriageEndedReasonId, opt => opt.MapFrom<MarriageEndReasonDtoResolver>());
            cfg.CreateMap<ElderlyDisabledHouseholdMembersDto, ElderlyDisabledHouseholdMembersViewModel>();
            cfg.CreateMap<ElderlyDisabledHouseholdMemberDto, ElderlyDisabledHouseholdMemberViewModel>();
            cfg.CreateMap<ElderlyDisabledNonMagiIncomeDetailDto, ElderlyDisabledNonMagiIncomeDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledFamilyAllocationDetail, ElderlyDisabledFamilyAllocationViewModel>();
            cfg.CreateMap<ElderlyDisabledSpouseAllocationDetail, ElderlyDisabledSpouseAllocationViewModel>();
            cfg.CreateMap<ElderlyDisabledQitAndAllocationDto, ElderlyDisabledQitAndAllocationViewModel>();
            cfg.CreateMap<SpousalAllocationAmountDto, SpousalAllocationAmount>();
            cfg.CreateMap<SpousalAllocationDetailsDto, SpousalAllocationDetails>();
            cfg.CreateMap<ElderlyDisabledNonMagiIncomesDto, ElderlyDisabledNonMagiIncomeViewModel>()
                .ForMember(dest => dest.SpousalImpoverishmentIndicator, opt => opt.MapFrom<SpousalImpoverishmentIndicatorDtoResolver>());
            cfg.CreateMap<NonMagiIncome, NonMagiIncomeViewModel>()
                .ForPath(dest => dest.OtherIncomeDescription, opt => opt.MapFrom(src => src.OtherIncome));
            cfg.CreateMap<ElderlyDisabledPropertyDto, ElderlyDisabledPropertyViewModel>();
            cfg.CreateMap<ElderlyDisabledPropertyParcelDto, ElderlyDisabledPropertyParcelViewModel>()
                .ForPath(d => d.Address.AddressLine1, opt => opt.MapFrom(s => s.ParcelAddressLine1))
                .ForPath(d => d.Address.AddressLine2, opt => opt.MapFrom(s => s.ParcelAddressLine2))
                .ForPath(d => d.Address.City, opt => opt.MapFrom(s => s.ParcelCity))
                .ForPath(d => d.Address.StateId, opt => opt.MapFrom(s => s.ParcelStateId))
                .ForPath(d => d.Address.ZipCode, opt => opt.MapFrom(s => s.ParcelZipCode))
                .ForPath(d => d.Address.CountyId, opt => opt.MapFrom(s => s.ParcelInStateCountyId))
                .ForPath(d => d.Address.OutOfStateCounty, opt => opt.MapFrom(s => s.ParcelOutOfStateCountyName));
            cfg.CreateMap<ElderlyDisabledPropertyParcelDetailDto, ElderlyDisabledPropertyParcelDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledPropertyPreviousDto, ElderlyDisabledPropertyPreviousViewModel>()
                .ForPath(d => d.Address.AddressLine1, opt => opt.MapFrom(s => s.PreviousPropertyAddressLine1))
                .ForPath(d => d.Address.AddressLine2, opt => opt.MapFrom(s => s.PreviousPropertyAddressLine2))
                .ForPath(d => d.Address.City, opt => opt.MapFrom(s => s.PreviousPropertyCity))
                .ForPath(d => d.Address.StateId, opt => opt.MapFrom(s => s.PreviousPropertyStateId))
                .ForPath(d => d.Address.ZipCode, opt => opt.MapFrom(s => s.PreviousPropertyZipCode))
                .ForPath(d => d.Address.CountyId, opt => opt.MapFrom(s => s.PreviousPropertyInStateCountyId))
                .ForPath(d => d.Address.OutOfStateCounty, opt => opt.MapFrom(s => s.PreviousPropertyOutOfStateCountyName));
            cfg.CreateMap<ElderlyDisabledPropertyPreviousDetailDto, ElderlyDisabledPropertyPreviousDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledPropertyMobileHomeDto, ElderlyDisabledPropertyMobileHomeViewModel>()
                .ForPath(d => d.Address.AddressLine1, opt => opt.MapFrom(s => s.MobileHomeLandAddressLine1))
                .ForPath(d => d.Address.AddressLine2, opt => opt.MapFrom(s => s.MobileHomeLandAddressLine2))
                .ForPath(d => d.Address.City, opt => opt.MapFrom(s => s.MobileHomeLandCity))
                .ForPath(d => d.Address.StateId, opt => opt.MapFrom(s => s.MobileHomeStateId))
                .ForPath(d => d.Address.ZipCode, opt => opt.MapFrom(s => s.MobileHomeLandZipCode))
                .ForPath(d => d.Address.CountyId, opt => opt.MapFrom(s => s.MobileHomeInStateCountyId))
                .ForPath(d => d.Address.OutOfStateCounty, opt => opt.MapFrom(s => s.MobileHomeOutOfStateCountyName));
            cfg.CreateMap<ElderlyDisabledPropertyMobileHomeDetailDto, ElderlyDisabledPropertyMobileHomeDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledResourceDto, ElderlyDisabledResourceViewModel>()
                .ForPath(d => d.ResourceTransfers, opt => opt.MapFrom(s => s.ResourceTransferDetails));
            cfg.CreateMap<ElderlyDisabledResourceBankDetailDto, ElderlyDisabledResourceBankDetailViewModel>()
                .ForPath(d => d.BankAccountTypeId, opt => opt.MapFrom(s => s.BankAccountTypeId == 0 ? (byte?)null : s.BankAccountTypeId))
                .ForPath(d => d.BankId, opt => opt.MapFrom(s => s.BankId))
                .ForPath(d => d.BankAddress.AddressLine1, opt => opt.MapFrom(s => s.Address.AddressLine1))
                .ForPath(d => d.BankAddress.AddressLine2, opt => opt.MapFrom(s => s.Address.AddressLine2))
                .ForPath(d => d.BankAddress.City, opt => opt.MapFrom(s => s.Address.City))
                .ForPath(d => d.BankAddress.StateId, opt => opt.MapFrom(s => s.Address.StateId))
                .ForPath(d => d.BankAddress.StateName, opt => opt.MapFrom(s => s.Address.StateName))
                .ForPath(d => d.BankAddress.ZipCode, opt => opt.MapFrom(s => s.Address.ZipCode))
                .ForPath(d => d.BankAddress.CountyId, opt => opt.MapFrom(s => s.Address.CountyId))
                .ForPath(d => d.BankAddress.OutOfStateCounty, opt => opt.MapFrom(s => s.Address.OutOfStateCounty));
            cfg.CreateMap<ElderlyDisabledResourceBankSubDetailDto, ElderlyDisabledResourceBankMonthDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledResourceDetailDto, ElderlyDisabledResourceDetailViewModel>()
                .ForPath(d => d.ApplicantResourceAmount, opt => opt.MapFrom(s => s.ApplicantAmount))
                .ForPath(d => d.SpouseResourceAmount, opt => opt.MapFrom(s => s.SpouseAmount));
            cfg.CreateMap<ElderlyDisabledResourceMonthDetailDto, ElderlyDisabledResourceMonthDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledResourceTransferDto, ElderlyDisabledResourceTransferViewModel>();
            cfg.CreateMap<ElderlyDisabledResourceTransferMonthDetailDto, ElderlyDisabledResourceTransferMonthDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledLifeInsuranceDetailDto, ElderlyDisabledLifeInsuranceDetailViewModel>()
                .ForPath(dest => dest.Address.AddressLine1, opt => opt.MapFrom(src => src.StreetName))
                .ForPath(dest => dest.Address.AddressLine2, opt => opt.MapFrom(src => src.AptOrSuiteNumber))
                .ForPath(dest => dest.Address.City, opt => opt.MapFrom(src => src.City))
                .ForPath(dest => dest.Address.StateId, opt => opt.MapFrom(src => src.StateId))
                .ForPath(dest => dest.Address.ZipCode, opt => opt.MapFrom(src => src.Zipcode))
                .ForPath(dest => dest.Address.CountyId, opt => opt.MapFrom(src => src.CountyId))
                .ForPath(dest => dest.Address.OutOfStateCounty, opt => opt.MapFrom(src => src.OutOfStateCountyName));
            cfg.CreateMap<ElderlyDisabledLifeInsuranceAdditionalDetailDto, ElderlyDisabledLifeInsuranceAdditionalDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledOtherBurialFundsDto, ElderlyDisabledOtherBurialFundsViewModel>()
                .ForPath(dest => dest.Address.AddressLine1, opt => opt.MapFrom(src => src.StreetName))
                .ForPath(dest => dest.Address.AddressLine2, opt => opt.MapFrom(src => src.AptOrSuiteNumber))
                .ForPath(dest => dest.Address.City, opt => opt.MapFrom(src => src.City))
                .ForPath(dest => dest.Address.StateId, opt => opt.MapFrom(src => src.StateId))
                .ForPath(dest => dest.Address.ZipCode, opt => opt.MapFrom(src => src.Zipcode))
                .ForPath(dest => dest.Address.CountyId, opt => opt.MapFrom(src => src.CountyId))
                .ForPath(dest => dest.Address.OutOfStateCounty, opt => opt.MapFrom(src => src.OutOfStateCountyName));
            cfg.CreateMap<ElderlyDisabledOtherBurialFundsDetailsDto, ElderlyDisabledOtherBurialFundsDetailsViewModel>();
            cfg.CreateMap<ElderlyDisabledPrepaidBurialSpaceDetailDto, ElderlyDisabledPrepaidBurialSpaceDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledAdditionalBurialFundsDto, ElderlyDisabledAdditionalBurialFundsViewModel>();
            cfg.CreateMap<ElderlyDisabledAdditionalBurialFundsBudgetDto, ElderlyDisabledAdditionalBurialFundsBudgetViewModel>();
            cfg.CreateMap<ElderlyDisabledLifeInsuranceDto, ElderlyDisabledLifeInsuranceViewModel>();
            cfg.CreateMap<MedicalLongTermCareInsuranceDetailDto, MedicalLongTermCareInsuranceDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledMedicalInsuranceDetailDto, ElderlyDisabledMedicalInsuranceDetailViewModel>()
                .ForPath(dest => dest.IsPremiumPayerSelfOther, opt => opt.MapFrom(src => src.PremiumPayerRelationshipTypeId.HasValue ? (bool?)(src.PremiumPayerRelationshipTypeId.Value == (byte)enumRelationship.Self) : null))
                .ForPath(dest => dest.Address.AddressLine1, opt => opt.MapFrom(src => src.StreetName))
                .ForPath(dest => dest.Address.AddressLine2, opt => opt.MapFrom(src => src.AptOrSuiteNumber))
                .ForPath(dest => dest.Address.City, opt => opt.MapFrom(src => src.City))
                .ForPath(dest => dest.Address.StateId, opt => opt.MapFrom(src => src.StateId))
                .ForPath(dest => dest.Address.ZipCode, opt => opt.MapFrom(src => src.Zipcode))
                .ForPath(dest => dest.Address.CountyId, opt => opt.MapFrom(src => src.CountyId));
            cfg.CreateMap<ElderlyDisabledMedicalInsuranceMonthDetailDto, ElderlyDisabledMedicalInsuranceMonthDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledTBQMedicarePartDInfoDto, ElderlyDisabledTBQMedicarePartDInfoViewModel>();
            cfg.CreateMap<ElderlyDisabledMedicalInsuranceDto, ElderlyDisabledMedicalInsuranceViewModel>();
            cfg.CreateMap<ElderlyDisabledMedicalInsurancePartDBudgetDto, ElderlyDisabledMedicalInsurancePartDBudgetViewModel>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyDto, ElderlyDisabledPersonalPropertyViewModel>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyAutoDto, ElderlyDisabledPersonalPropertyAutoViewModel>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyAutoDetailDto, ElderlyDisabledPersonalPropertyAutoDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyMachineDto, ElderlyDisabledPersonalPropertyMachineViewModel>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyMachineDetailDto, ElderlyDisabledPersonalPropertyMachineMonthDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyCollectibleDto, ElderlyDisabledPersonalPropertyCollectibleViewModel>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyCollectibleDetailDto, ElderlyDisabledPersonalPropertyCollectibleMonthDetailViewModel>();
            cfg.CreateMap<ElderlyDisabledVeteranDto, ElderlyDisabledVeteranViewModel>()
                .ForPath(dest => dest.ContactPersonId, opt => opt.MapFrom(src => src.ContactPersonId));
            cfg.CreateMap<ElderlyDisabledPerson, ElderlyDisabledVeteranPersonViewModel>()
                .ForMember(d => d.ConfirmSsn, opt => opt.MapFrom(s => s.Ssn));
            cfg.CreateMap<ElderlyDisabledApplicationInfoBarDto, ElderlyDisabledApplicationInfoBarViewModel>();
            cfg.CreateMap<PersonInfoDto, PersonInfoViewModel>();
            cfg.CreateMap<RepresentativeInfoDto, RepresentativeInfoViewModel>();
            cfg.CreateMap<ElderlyDisabledLiabilityDetailDto, ElderlyDisabledLiabilityDetailViewModel>()
                .ForPath(dest => dest.HasTransferPenalty, opt => opt.MapFrom(src => src.HasTransferPenalty ?? false));
            cfg.CreateMap<ElderlyDisabledLiabilityDto, ElderlyDisabledLiabilityViewModel>();
            cfg.CreateMap<ElderlyDisabledLiabilityTestDto, ElderlyDisabledLiabilityTestViewModel>();
            cfg.CreateMap<ElderlyDisabledLiabilityDetailChangeCodeDto, ElderlyDisabledLiabilityDetailChangeCodeViewModel>();
            cfg.CreateMap<ElderlyDisabledEligibilityDeterminationsDto, ElderlyDisabledEligibilityEnrollmentViewModel>();
            cfg.CreateMap<ElderlyDisabledEligibilityDeterminationDto, ElderlyDisabledEligEnrollAwardDenyViewModel>()
                .ForPath(d => d.AppEligibilityDeterminationId, opt => opt.MapFrom(s => s.AppEligibilityElderlyDisabledDeterminationId))
                .ForPath(d => d.StateAidCategoryId, opt => opt.MapFrom(s => s.ProgramSubCategoryId))
                .ForPath(d => d.AwardOption, opt => opt.MapFrom(s => (enumElderlyDisabledAwardOption?)s.AwardOption));
            cfg.CreateMap<ElderlyDisabledEligibilityDeterminationDto, EligEnrollAwardDenyViewModel>()
                .ForPath(d => d.AppEligibilityDeterminationId, opt => opt.MapFrom(s => s.AppEligibilityElderlyDisabledDeterminationId))
                .ForPath(d => d.StateAidCategoryId, opt => opt.MapFrom(s => s.ProgramSubCategoryId));
            cfg.CreateMap<ExparteApplicationInfoDto, ExparteApplicationInfoViewModel>();
            cfg.CreateMap<ExparteApplicationInfoViewModel, ExparteApplicationInfoDto>();

            #endregion

            #region View Model to DTO
            // ViewModel to Dto
            cfg.CreateMap<ApplicationElderlyDisabledDetailViewModel, ApplicationElderlyDisabledDetail>()
                .ForPath(dest => dest.HospitalAdmissionDate, opt => opt.MapFrom(src => src.HospitalAdmissionDate ?? DateTime.MinValue))
                .ForPath(dest => dest.NursingHomeAdmissionDate, opt => opt.MapFrom(src => src.NursingHomeAdmissionDate ?? DateTime.MinValue))
                .ForPath(dest => dest.MaritalStatusDate, opt => opt.MapFrom(src => src.MaritalStatusDate ?? DateTime.MinValue));
            cfg.CreateMap<ElderlyDisabledPersonViewModel, ElderlyDisabledPerson>();
            cfg.CreateMap<ElderlyDisabledPersonDetailViewModel, ElderlyDisabledPersonDetail>();
            cfg.CreateMap<ElderlyDisabledApplicationViewModel, ElderlyDisabledApplicationDto>()
                .ForPath(dest => dest.PersonPhones, opt => opt.MapFrom(src => src.PersonPhones.Phones));
            cfg.CreateMap<AddressViewModel, Models.Shared.Address>()
                .ForMember(dest => dest.AptSuiteLot, opt => opt.MapFrom(src => src.AddressLine2))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.StateId.HasValue ? src.StateId.Value.ToString() : string.Empty))
                .ForMember(dest => dest.County, opt => opt.MapFrom(src => src.CountyId.HasValue ? src.CountyId.Value.ToString() : string.Empty));
            cfg.CreateMap<ElderlyDisabledSponsorViewModel, Models.Application.Representative.SponsorDetail>()
                .ForPath(dest => dest.Name.FirstName, opt => opt.MapFrom(src => src.FirstName))
                .ForPath(dest => dest.Name.MiddleName, opt => opt.MapFrom(src => src.MiddleName))
                .ForPath(dest => dest.Name.LastName, opt => opt.MapFrom(src => src.LastName))
                .ForPath(dest => dest.Name.SuffixId, opt => opt.MapFrom(src => src.SuffixId))
                .ForPath(dest => dest.PhoneList, opt => opt.MapFrom(src => src.PhoneList.Phones));
            cfg.CreateMap<PhoneAndPersonPhoneViewModel, Models.Shared.Phone>()
                .ForPath(dest => dest.PhoneID, opt => opt.MapFrom(src => src.PhoneId))
                .ForPath(dest => dest.PhoneNumber, opt => opt.MapFrom(src => src.PhoneNo))
                .ForPath(dest => dest.PhoneExtension, opt => opt.MapFrom(src => src.Ext))
                .ForPath(dest => dest.PhoneTypeID, opt => opt.MapFrom(src => src.PhoneTypeId))
                .ForPath(dest => dest.isDeleted, opt => opt.MapFrom(src => src.IsDeleted))
                .ForPath(dest => dest.hideHeader, opt => opt.Ignore())
                .ForPath(dest => dest.PhoneTypeText, opt => opt.Ignore())
                .ForPath(dest => dest.HideDelete, opt => opt.Ignore());
            cfg.CreateMap<ElderlyDisabledQitAndAllocationViewModel, ElderlyDisabledQitAndAllocationDto>();
            cfg.CreateMap<SpousalAllocationDetails, SpousalAllocationDetailsDto>();
            cfg.CreateMap<SpousalAllocationAmount, SpousalAllocationAmountDto>();
            cfg.CreateMap<ElderlyDisabledSpouseAllocationViewModel, ElderlyDisabledSpouseAllocationDetail>();
            cfg.CreateMap<ElderlyDisabledFamilyAllocationViewModel, ElderlyDisabledFamilyAllocationDetail>();
            cfg.CreateMap<ElderlyDisabledSpouseViewModel, ElderlyDisabledSpouseDto>();
            cfg.CreateMap<ElderlyDisabledFormerSpouseViewModel, ElderlyDisabledFormerSpouseDto>()
                .ForPath(dest => dest.MarriageBeginDate, opt => opt.MapFrom(src => src.MarriageBeginDate ?? DateTime.MinValue))
                .ForPath(dest => dest.MarriageEndDate, opt => opt.MapFrom(src => src.MarriageEndDate ?? DateTime.MinValue))
                .ForPath(dest => dest.MarriageEndedDeath, opt => opt.MapFrom(src => src.MarriageEndedReasonId == (byte)enumMarriageEndedReason.Death))
                .ForPath(dest => dest.MarriageEndedDivorce, opt => opt.MapFrom(src => src.MarriageEndedReasonId == (byte)enumMarriageEndedReason.Divorce))
                .ForPath(dest => dest.MarriageEndedOther, opt => opt.MapFrom(src => src.MarriageEndedReasonId == (byte)enumMarriageEndedReason.Other));
            cfg.CreateMap<ElderlyDisabledHouseholdMembersViewModel, ElderlyDisabledHouseholdMembersDto>();
            cfg.CreateMap<ElderlyDisabledHouseholdMemberViewModel, ElderlyDisabledHouseholdMemberDto>();
            cfg.CreateMap<ElderlyDisabledNonMagiIncomeDetailViewModel, ElderlyDisabledNonMagiIncomeDetailDto>();
            cfg.CreateMap<ElderlyDisabledNonMagiIncomeViewModel, ElderlyDisabledNonMagiIncomesDto>()
                .ForPath(dest => dest.SpousalImpoverishmentIndicator, opt => opt.MapFrom(src => src.SpousalImpoverishmentIndicator.HasValue ? src.SpousalImpoverishmentIndicator.Value.ToString() : string.Empty));
            cfg.CreateMap<NonMagiIncomeViewModel, NonMagiIncome>()
                .ForPath(dest => dest.OtherIncome, opt => opt.MapFrom(src => src.OtherIncomeDescription));
            cfg.CreateMap<ElderlyDisabledPropertyViewModel, ElderlyDisabledPropertyDto>();
            cfg.CreateMap<ElderlyDisabledPropertyParcelViewModel, ElderlyDisabledPropertyParcelDto>()
                .ForPath(d => d.ParcelAddressLine1, opt => opt.MapFrom(s => s.Address.AddressLine1))
                .ForPath(d => d.ParcelAddressLine2, opt => opt.MapFrom(s => s.Address.AddressLine2))
                .ForPath(d => d.ParcelCity, opt => opt.MapFrom(s => s.Address.City))
                .ForPath(d => d.ParcelStateId, opt => opt.MapFrom(s => s.Address.StateId))
                .ForPath(d => d.ParcelZipCode, opt => opt.MapFrom(s => s.Address.ZipCode))
                .ForPath(d => d.ParcelInStateCountyId, opt => opt.MapFrom(s => s.Address.CountyId))
                .ForPath(d => d.ParcelOutOfStateCountyName, opt => opt.MapFrom(s => s.Address.OutOfStateCounty));
            cfg.CreateMap<ElderlyDisabledPropertyParcelDetailViewModel, ElderlyDisabledPropertyParcelDetailDto>();
            cfg.CreateMap<ElderlyDisabledPropertyPreviousViewModel, ElderlyDisabledPropertyPreviousDto>()
                .ForPath(d => d.PreviousPropertyAddressLine1, opt => opt.MapFrom(s => s.Address.AddressLine1))
                .ForPath(d => d.PreviousPropertyAddressLine2, opt => opt.MapFrom(s => s.Address.AddressLine2))
                .ForPath(d => d.PreviousPropertyCity, opt => opt.MapFrom(s => s.Address.City))
                .ForPath(d => d.PreviousPropertyZipCode, opt => opt.MapFrom(s => s.Address.ZipCode))
                .ForPath(d => d.PreviousPropertyStateId, opt => opt.MapFrom(s => s.Address.StateId))
                .ForPath(d => d.PreviousPropertyInStateCountyId, opt => opt.MapFrom(s => s.Address.CountyId))
                .ForPath(d => d.PreviousPropertyOutOfStateCountyName, opt => opt.MapFrom(s => s.Address.OutOfStateCounty));
            cfg.CreateMap<ElderlyDisabledPropertyPreviousDetailViewModel, ElderlyDisabledPropertyPreviousDetailDto>();
            cfg.CreateMap<ElderlyDisabledPropertyMobileHomeViewModel, ElderlyDisabledPropertyMobileHomeDto>()
                .ForPath(d => d.MobileHomeLandAddressLine1, opt => opt.MapFrom(s => s.Address.AddressLine1))
                .ForPath(d => d.MobileHomeLandAddressLine2, opt => opt.MapFrom(s => s.Address.AddressLine2))
                .ForPath(d => d.MobileHomeLandCity, opt => opt.MapFrom(s => s.Address.City))
                .ForPath(d => d.MobileHomeStateId, opt => opt.MapFrom(s => s.Address.StateId))
                .ForPath(d => d.MobileHomeLandZipCode, opt => opt.MapFrom(s => s.Address.ZipCode))
                .ForPath(d => d.MobileHomeInStateCountyId, opt => opt.MapFrom(s => s.Address.CountyId))
                .ForPath(d => d.MobileHomeOutOfStateCountyName, opt => opt.MapFrom(s => s.Address.OutOfStateCounty));
            cfg.CreateMap<ElderlyDisabledPropertyMobileHomeDetailViewModel, ElderlyDisabledPropertyMobileHomeDetailDto>();
            cfg.CreateMap<ElderlyDisabledResourceViewModel, ElderlyDisabledResourceDto>()
                .ForPath(dest => dest.ResourceTransferDetails, opt => opt.MapFrom(src => src.ResourceTransfers));
            cfg.CreateMap<ElderlyDisabledResourceBankDetailViewModel, ElderlyDisabledResourceBankDetailDto>()
                .ForPath(dest => dest.BankAccountTypeId, opt => opt.MapFrom(src => src.BankAccountTypeId ?? 0))
                .ForPath(dest => dest.BankId, opt => opt.MapFrom(src => src.BankId))
                .ForPath(dest => dest.Address.AddressLine1, opt => opt.MapFrom(s => s.BankAddress.AddressLine1))
                .ForPath(dest => dest.Address.AddressLine2, opt => opt.MapFrom(s => s.BankAddress.AddressLine2))
                .ForPath(dest => dest.Address.City, opt => opt.MapFrom(s => s.BankAddress.City))
                .ForPath(dest => dest.Address.StateId, opt => opt.MapFrom(s => s.BankAddress.StateId))
                .ForPath(dest => dest.Address.StateName, opt => opt.MapFrom(s => s.BankAddress.StateName))
                .ForPath(dest => dest.Address.ZipCode, opt => opt.MapFrom(s => s.BankAddress.ZipCode))
                .ForPath(dest => dest.Address.CountyId, opt => opt.MapFrom(s => s.BankAddress.CountyId))
                .ForPath(dest => dest.Address.OutOfStateCounty, opt => opt.MapFrom(s => s.BankAddress.OutOfStateCounty));
            cfg.CreateMap<ElderlyDisabledResourceBankMonthDetailViewModel, ElderlyDisabledResourceBankSubDetailDto>();
            cfg.CreateMap<ElderlyDisabledResourceDetailViewModel, ElderlyDisabledResourceDetailDto>()
                .ForPath(dest => dest.ApplicantAmount, opt => opt.MapFrom(src => src.ApplicantResourceAmount))
                .ForPath(dest => dest.SpouseAmount, opt => opt.MapFrom(src => src.SpouseResourceAmount));
            cfg.CreateMap<ElderlyDisabledResourceMonthDetailViewModel, ElderlyDisabledResourceMonthDetailDto>();
            cfg.CreateMap<ElderlyDisabledResourceTransferViewModel, ElderlyDisabledResourceTransferDto>();
            cfg.CreateMap<ElderlyDisabledResourceTransferMonthDetailViewModel, ElderlyDisabledResourceTransferMonthDetailDto>();
            cfg.CreateMap<ElderlyDisabledLifeInsuranceDetailViewModel, ElderlyDisabledLifeInsuranceDetailDto>()
                .ForPath(dest => dest.StreetName, opt => opt.MapFrom(src => src.Address.AddressLine1))
                .ForPath(dest => dest.AptOrSuiteNumber, opt => opt.MapFrom(src => src.Address.AddressLine2))
                .ForPath(dest => dest.City, opt => opt.MapFrom(src => src.Address.City))
                .ForPath(dest => dest.StateId, opt => opt.MapFrom(src => src.Address.StateId))
                .ForPath(dest => dest.Zipcode, opt => opt.MapFrom(src => src.Address.ZipCode))
                .ForPath(dest => dest.CountyId, opt => opt.MapFrom(src => src.Address.CountyId))
                .ForPath(dest => dest.OutOfStateCountyName, opt => opt.MapFrom(src => src.Address.OutOfStateCounty));
            cfg.CreateMap<ElderlyDisabledLifeInsuranceAdditionalDetailViewModel, ElderlyDisabledLifeInsuranceAdditionalDetailDto>();
            cfg.CreateMap<ElderlyDisabledOtherBurialFundsViewModel, ElderlyDisabledOtherBurialFundsDto>()
                .ForPath(dest => dest.StreetName, opt => opt.MapFrom(src => src.Address.AddressLine1))
                .ForPath(dest => dest.AptOrSuiteNumber, opt => opt.MapFrom(src => src.Address.AddressLine2))
                .ForPath(dest => dest.City, opt => opt.MapFrom(src => src.Address.City))
                .ForPath(dest => dest.StateId, opt => opt.MapFrom(src => src.Address.StateId))
                .ForPath(dest => dest.Zipcode, opt => opt.MapFrom(src => src.Address.ZipCode))
                .ForPath(dest => dest.CountyId, opt => opt.MapFrom(src => src.Address.CountyId))
                .ForPath(dest => dest.OutOfStateCountyName, opt => opt.MapFrom(src => src.Address.OutOfStateCounty));
            cfg.CreateMap<ElderlyDisabledOtherBurialFundsDetailsViewModel, ElderlyDisabledOtherBurialFundsDetailsDto>();
            cfg.CreateMap<ElderlyDisabledPrepaidBurialSpaceDetailViewModel, ElderlyDisabledPrepaidBurialSpaceDetailDto>();
            cfg.CreateMap<ElderlyDisabledAdditionalBurialFundsViewModel, ElderlyDisabledAdditionalBurialFundsDto>();
            cfg.CreateMap<ElderlyDisabledAdditionalBurialFundsBudgetViewModel, ElderlyDisabledAdditionalBurialFundsBudgetDto>();
            cfg.CreateMap<ElderlyDisabledLifeInsuranceViewModel, ElderlyDisabledLifeInsuranceDto>();
            cfg.CreateMap<MedicalLongTermCareInsuranceDetailViewModel, MedicalLongTermCareInsuranceDetailDto>();
            cfg.CreateMap<ElderlyDisabledMedicalInsuranceDetailViewModel, ElderlyDisabledMedicalInsuranceDetailDto>()
                .ForPath(dest => dest.PremiumPayerRelationshipTypeId, opt => opt.MapFrom(src => src.IsPremiumPayerSelfOther.HasValue ? (byte?)(src.IsPremiumPayerSelfOther.Value ? (byte)enumRelationship.Self : (byte)enumRelationship.Other) : null))
                .ForPath(dest => dest.StreetName, opt => opt.MapFrom(src => src.Address.AddressLine1))
                .ForPath(dest => dest.AptOrSuiteNumber, opt => opt.MapFrom(src => src.Address.AddressLine2))
                .ForPath(dest => dest.City, opt => opt.MapFrom(src => src.Address.City))
                .ForPath(dest => dest.StateId, opt => opt.MapFrom(src => src.Address.StateId))
                .ForPath(dest => dest.Zipcode, opt => opt.MapFrom(src => src.Address.ZipCode))
                .ForPath(dest => dest.CountyId, opt => opt.MapFrom(src => src.Address.CountyId));
            cfg.CreateMap<ElderlyDisabledMedicalInsuranceMonthDetailViewModel, ElderlyDisabledMedicalInsuranceMonthDetailDto>();
            cfg.CreateMap<ElderlyDisabledTBQMedicarePartDInfoViewModel, ElderlyDisabledTBQMedicarePartDInfoDto>();
            cfg.CreateMap<ElderlyDisabledMedicalInsuranceViewModel, ElderlyDisabledMedicalInsuranceDto>();
            cfg.CreateMap<ElderlyDisabledMedicalInsurancePartDBudgetViewModel, ElderlyDisabledMedicalInsurancePartDBudgetDto>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyViewModel, ElderlyDisabledPersonalPropertyDto>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyAutoViewModel, ElderlyDisabledPersonalPropertyAutoDto>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyAutoDetailViewModel, ElderlyDisabledPersonalPropertyAutoDetailDto>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyMachineViewModel, ElderlyDisabledPersonalPropertyMachineDto>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyMachineMonthDetailViewModel,ElderlyDisabledPersonalPropertyMachineDetailDto > ();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyCollectibleViewModel, ElderlyDisabledPersonalPropertyCollectibleDto>();
            cfg.CreateMap<ElderlyDisabledPersonalPropertyCollectibleMonthDetailViewModel, ElderlyDisabledPersonalPropertyCollectibleDetailDto>();
            cfg.CreateMap<ElderlyDisabledVeteranViewModel, ElderlyDisabledVeteranDto>()
                .ForPath(dest => dest.ContactPersonId, opt => opt.MapFrom(src => src.ContactPersonId));
            cfg.CreateMap<ElderlyDisabledVeteranPersonViewModel, ElderlyDisabledPerson>();
            cfg.CreateMap<PersonInfoViewModel, PersonInfoDto>();
            cfg.CreateMap<RepresentativeInfoViewModel, RepresentativeInfoDto>();
            cfg.CreateMap<ElderlyDisabledLiabilityDetailViewModel, ElderlyDisabledLiabilityDetailDto>();
            cfg.CreateMap<ElderlyDisabledLiabilityViewModel, ElderlyDisabledLiabilityDto>();
            cfg.CreateMap<ElderlyDisabledLiabilityTestViewModel, ElderlyDisabledLiabilityTestDto>();
            cfg.CreateMap<ElderlyDisabledLiabilityDetailChangeCodeViewModel, ElderlyDisabledLiabilityDetailChangeCodeDto>();
            cfg.CreateMap<ElderlyDisabledEligibilityEnrollmentViewModel, ElderlyDisabledEligibilityDeterminationsDto>();
            cfg.CreateMap<EligEnrollAwardDenyViewModel, ElderlyDisabledEligibilityDeterminationDto>()
                .ForPath(d => d.AppEligibilityElderlyDisabledDeterminationId, opt => opt.MapFrom(s => s.AppEligibilityDeterminationId))
                .ForPath(d => d.ProgramSubCategoryId, opt => opt.MapFrom(s => s.StateAidCategoryId))
                .ForPath(d => d.StartDate, opt => opt.MapFrom(s => s.StartDate))
                .ForPath(d => d.CancelDate, opt => opt.MapFrom(s => s.CancelDate))
                .ForPath(d => d.DenialReasons, opt => opt.MapFrom(s => s.DenialReasons));
            cfg.CreateMap<ElderlyDisabledEligEnrollAwardDenyViewModel, ElderlyDisabledEligibilityDeterminationDto>()
                .ForPath(d => d.AppEligibilityElderlyDisabledDeterminationId, opt => opt.MapFrom(s => s.AppEligibilityDeterminationId))
                .ForPath(d => d.IsInterimMSP, opt => opt.MapFrom(s => s.IsMsp))
                .ForPath(d => d.ProgramSubCategoryId, opt => opt.MapFrom(s => s.StateAidCategoryId))
                .ForPath(d => d.AwardOption, opt => opt.MapFrom(s => (byte?)s.AwardOption));
            #endregion
        }

        /// <summary>
        /// Mappers specific to Expedite
        /// </summary>
        /// <param name="cfg"></param>
        private static void addExpediteMappers(IMapperConfigurationExpression cfg)
        {
            // Dto to ViewModel
            cfg.CreateMap<ExpediteFacilityProvidersDto, ExpediteFacilityProvidersViewModel>();
            cfg.CreateMap<ExpediteFacilityProviderDto, ExpediteFacilityProviderViewModel>();
            cfg.CreateMap<ExpediteFacilityDto, ExpediteFacilityViewModel>();
            cfg.CreateMap<ProviderDto, ProviderViewModel>();
            cfg.CreateMap<ReconcileFacilityProviderDto, ReconcileFacilityProviderViewModel>();

            // ViewModel to Dto
            cfg.CreateMap<ExpediteFacilityProvidersViewModel, ExpediteFacilityProvidersDto>();
            cfg.CreateMap<ExpediteFacilityProviderViewModel, ExpediteFacilityProviderDto>();
            cfg.CreateMap<ExpediteFacilityViewModel, ExpediteFacilityDto>();
            cfg.CreateMap<ProviderViewModel, ProviderDto>();
            cfg.CreateMap<ReconcileFacilityProviderViewModel, ReconcileFacilityProviderDto>();
        }

        /// <summary>
        /// Mappers specific to Financial Institution
        /// </summary>
        /// <param name="cfg"></param>
        private static void addFinancialInsMappers(IMapperConfigurationExpression cfg)
        {
            // Dto to ViewModel
            cfg.CreateMap<FinancialInstitutionsDto, FinancialInstitutionsViewModel>();
            cfg.CreateMap<FinancialInstitutionDto, FinancialInstitutionViewModel>();

            //ViewModel to Dto
            cfg.CreateMap<FinancialInstitutionsViewModel, FinancialInstitutionsDto>();
            cfg.CreateMap<FinancialInstitutionViewModel, FinancialInstitutionDto>();
        }

        /// <summary>
        /// Resolves the marriage end reason id for the DTO from the view model's data.
        /// </summary>
        /// <seealso cref="AutoMapper.IValueResolver&lt;Cares.Api.Messages.ElderlyDisabled.ElderlyDisabledFormerSpouseDto, Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.ElderlyDisabledFormerSpouseViewModel, System.Int32?&gt;" />
        public class MarriageEndReasonDtoResolver : IValueResolver<ElderlyDisabledFormerSpouseDto, ElderlyDisabledFormerSpouseViewModel, byte?>
        {
            public byte? Resolve(ElderlyDisabledFormerSpouseDto source, ElderlyDisabledFormerSpouseViewModel destination, byte? destMember, ResolutionContext context)
            {
                if (source.MarriageEndedDeath)
                {
                    return (byte?)enumMarriageEndedReason.Death;
                }
                if (source.MarriageEndedDivorce)
                {
                    return (byte?)enumMarriageEndedReason.Divorce;
                }
                if (source.MarriageEndedOther)
                {
                    return (byte?)enumMarriageEndedReason.Other;
                }

                return null;
            }
        }

        /// <summary>
        /// Resolves the spousal impoverishment indicator by converting the 'string' to 'char' from the DTO to the VM.
        /// </summary>
        public class SpousalImpoverishmentIndicatorDtoResolver : IValueResolver<ElderlyDisabledNonMagiIncomesDto, ElderlyDisabledNonMagiIncomeViewModel, char?>
        {
            public char? Resolve(ElderlyDisabledNonMagiIncomesDto source, ElderlyDisabledNonMagiIncomeViewModel destination, char? destMember, ResolutionContext context)
            {
                if (!string.IsNullOrEmpty(source.SpousalImpoverishmentIndicator) && source.SpousalImpoverishmentIndicator.Length == 1)
                {
                    char SpousalImpoverishmentIndicator;
                    char.TryParse(source.SpousalImpoverishmentIndicator, out SpousalImpoverishmentIndicator);
                    return SpousalImpoverishmentIndicator;
                }
                return null;
            }
        }
    }
}
