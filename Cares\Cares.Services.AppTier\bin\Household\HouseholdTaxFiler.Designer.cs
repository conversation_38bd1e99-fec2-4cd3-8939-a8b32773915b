﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class HouseholdTaxFiler {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal HouseholdTaxFiler() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.HouseholdTaxFiler", typeof(HouseholdTaxFiler).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add another dependent.
        /// </summary>
        public static string addAnotherDependent {
            get {
                return ResourceManager.GetString("addAnotherDependent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD SPOUSE DETAILS.
        /// </summary>
        public static string addSpouseDetails {
            get {
                return ResourceManager.GetString("addSpouseDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add another tax dependent.
        /// </summary>
        public static string addTaxDependent {
            get {
                return ResourceManager.GetString("addTaxDependent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will&amp;nbsp;[TAXFILER]&amp;nbsp;claim any dependents on&amp;nbsp;[HIS/HER/THEIR]&amp;nbsp;federal income tax return for&amp;nbsp;[COVERAGEYEAR]?.
        /// </summary>
        public static string claimDependents {
            get {
                return ResourceManager.GetString("claimDependents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will&amp;nbsp;[TAXFILER]&amp;nbsp;claim different dependents on the&amp;nbsp;[COVERAGEYEARPLUS]&amp;nbsp;tax return?.
        /// </summary>
        public static string claimDifferentDependentsNextYear {
            get {
                return ResourceManager.GetString("claimDifferentDependentsNextYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) be claimed as a dependent on someone else&apos;s federal income tax return for&amp;nbsp;[COVERAGEYEAR]?.
        /// </summary>
        public static string claimedAsDependentOnAnotherReturn {
            get {
                return ResourceManager.GetString("claimedAsDependentOnAnotherReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will&amp;nbsp;[TAXFILER]&amp;nbsp;claim any other dependents on his/her/their federal income tax return for&amp;nbsp;[COVERAGEYEAR]?.
        /// </summary>
        public static string claimOtherDependents {
            get {
                return ResourceManager.GetString("claimOtherDependents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To find out more about dependents,&amp;nbsp;[LINK].
        /// </summary>
        public static string dependentHelpLink {
            get {
                return ResourceManager.GetString("dependentHelpLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selection of a dependent is required..
        /// </summary>
        public static string dependentListSelectionRequired {
            get {
                return ResourceManager.GetString("dependentListSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A dependent is someone who gets most of his or her financial support from someone else. Children, other family members, or other people who live with the tax filer can be dependents..
        /// </summary>
        public static string dependentNote {
            get {
                return ResourceManager.GetString("dependentNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you and/or your family want health coverage for next year, tell us about any expected differences between who you claim as dependents now, and why you’ll claim on the tax return for next Year..
        /// </summary>
        public static string diffDependentNote {
            get {
                return ResourceManager.GetString("diffDependentNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coverage Eligibility Tip.
        /// </summary>
        public static string dlgNonApplicantParentHeader {
            get {
                return ResourceManager.GetString("dlgNonApplicantParentHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Filer Information.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How is&amp;nbsp;[NAME]&amp;nbsp;related to&amp;nbsp;[TAXFILER]?.
        /// </summary>
        public static string howIsPersonRelated {
            get {
                return ResourceManager.GetString("howIsPersonRelated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) married?.
        /// </summary>
        public static string isMarried {
            get {
                return ResourceManager.GetString("isMarried", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is&amp;nbsp;[TAXFILER]&amp;nbsp;married?.
        /// </summary>
        public static string isTaxFilerMarried {
            get {
                return ResourceManager.GetString("isTaxFilerMarried", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with brothers or sisters?.
        /// </summary>
        public static string liveWithBrotherSister {
            get {
                return ResourceManager.GetString("liveWithBrotherSister", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with the parent or stepparent(s) that claim&amp;nbsp;[NAME]&amp;nbsp;on the tax return?.
        /// </summary>
        public static string liveWithClaimingParent {
            get {
                return ResourceManager.GetString("liveWithClaimingParent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with a parent and/or stepparent?.
        /// </summary>
        public static string liveWithParent {
            get {
                return ResourceManager.GetString("liveWithParent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[NAME] , (DOB:&amp;nbsp;[DOB]) live with a parent or stepparent other than&amp;nbsp;[TAXFILER]?.
        /// </summary>
        public static string liveWithParentNotOnApp {
            get {
                return ResourceManager.GetString("liveWithParentNotOnApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with a son, daughter, stepson, or stepdaughter?.
        /// </summary>
        public static string liveWithSonDaughter {
            get {
                return ResourceManager.GetString("liveWithSonDaughter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[NAME]&amp;nbsp;live with&amp;nbsp;[HIS/HER]&amp;nbsp;spouse?.
        /// </summary>
        public static string liveWithSpouse {
            get {
                return ResourceManager.GetString("liveWithSpouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME]&amp;nbsp;may be eligible for Medicaid or ALL Kids through the parent they live with.  That parent can also file an application.  To do so, the parent can create an account or fill out a paper application to mail in.  You can also continue with this application now to see if&amp;nbsp;[NAMES]&amp;nbsp;can get tax credit to pay for health coverage for&amp;nbsp;[NAME]&amp;nbsp;instead..
        /// </summary>
        public static string mayBeEligibleForMediChip {
            get {
                return ResourceManager.GetString("mayBeEligibleForMediChip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must provide information about&amp;nbsp;[NAMES]&apos;s, (DOB:&amp;nbsp;[DOB]) income and about who else is on the tax return to get a tax credit to help pay for health coverage for&amp;nbsp;[NAME].  However, you can continue with this application without telling us more about&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) to see if you can get covered by&amp;nbsp;[AMACHIP]..
        /// </summary>
        public static string mustProvideInfo {
            get {
                return ResourceManager.GetString("mustProvideInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any brother or sister that lives with&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]).
        /// </summary>
        public static string nameOfBroSis {
            get {
                return ResourceManager.GetString("nameOfBroSis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of dependents.
        /// </summary>
        public static string nameOfDependent {
            get {
                return ResourceManager.GetString("nameOfDependent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of parent or stepparent that claims&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]).
        /// </summary>
        public static string nameOfParent {
            get {
                return ResourceManager.GetString("nameOfParent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any son, daughter, stepson, or stepdaughter that lives with&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]).
        /// </summary>
        public static string nameOfSonDaughter {
            get {
                return ResourceManager.GetString("nameOfSonDaughter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of spouse.
        /// </summary>
        public static string nameOfSpouse {
            get {
                return ResourceManager.GetString("nameOfSpouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You don&apos;t have to file taxes to apply for coverage..
        /// </summary>
        public static string noNeedFileTaxesforCoverage {
            get {
                return ResourceManager.GetString("noNeedFileTaxesforCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME]&apos;s, (DOB:&amp;nbsp;[DOB]) Spouse.
        /// </summary>
        public static string personsSpouse {
            get {
                return ResourceManager.GetString("personsSpouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) plan to file a federal income tax return for&amp;nbsp;[COVERAGEYEAR]?.
        /// </summary>
        public static string planToFileTaxes {
            get {
                return ResourceManager.GetString("planToFileTaxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does&amp;nbsp;[TAXFILER]&amp;nbsp;plan to file a joint federal income tax return with a spouse for&amp;nbsp;[COVERAGEYEAR]?.
        /// </summary>
        public static string planToFileTaxesWithSpouse {
            get {
                return ResourceManager.GetString("planToFileTaxesWithSpouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select&amp;nbsp;[NAME]&apos;s, (DOB:&amp;nbsp;[DOB]) parents and stepparent(s) that live with&amp;nbsp;[HIM/HER].
        /// </summary>
        public static string selectParentThatLiveHere {
            get {
                return ResourceManager.GetString("selectParentThatLiveHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Someone else who isn&apos;t applying for health insurance.
        /// </summary>
        public static string someoneElseNotApplying {
            get {
                return ResourceManager.GetString("someoneElseNotApplying", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selection of someone with whom you are filing jointly is required..
        /// </summary>
        public static string spousePersonListSelectionRequired {
            get {
                return ResourceManager.GetString("spousePersonListSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once a change is made to this screen, all tax filer data will be removed and you must enter it again.  If updating this information is not needed, you may press the Cancel button now..
        /// </summary>
        public static string taxFilerUpdateWarning {
            get {
                return ResourceManager.GetString("taxFilerUpdateWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who is the tax filer that will claim&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) on their income tax return?.
        /// </summary>
        public static string taxFilerWhoWillClaimPerson {
            get {
                return ResourceManager.GetString("taxFilerWhoWillClaimPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to provide the claiming tax filer&apos;s information, so they can apply for a tax credit?.
        /// </summary>
        public static string wantToProvideInfo {
            get {
                return ResourceManager.GetString("wantToProvideInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who is a brother or sister living with&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB])?.
        /// </summary>
        public static string whoIsBrotherSister {
            get {
                return ResourceManager.GetString("whoIsBrotherSister", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who is&amp;nbsp;[NAME]&apos;s, (DOB:&amp;nbsp;[DOB]) spouse?.
        /// </summary>
        public static string whoIsSpouse {
            get {
                return ResourceManager.GetString("whoIsSpouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who will&amp;nbsp;[TAXFILER]&amp;nbsp;claim on&amp;nbsp;[HIS/HER/THEIR]&amp;nbsp;tax return for&amp;nbsp;[COVERAGEYEARPLUS]?.
        /// </summary>
        public static string whoWillPersonClaim {
            get {
                return ResourceManager.GetString("whoWillPersonClaim", resourceCulture);
            }
        }
    }
}
