﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Main {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Dashboard {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Dashboard() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Main.Dashboard", typeof(Dashboard).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ALL Kids Premium Due.
        /// </summary>
        public static string amountDueInfo {
            get {
                return ResourceManager.GetString("amountDueInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application &amp; Enrollment Status.
        /// </summary>
        public static string appEnrollStatus {
            get {
                return ResourceManager.GetString("appEnrollStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application ID.
        /// </summary>
        public static string appID {
            get {
                return ResourceManager.GetString("appID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        public static string approved {
            get {
                return ResourceManager.GetString("approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Status.
        /// </summary>
        public static string appStatus {
            get {
                return ResourceManager.GetString("appStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Date.
        /// </summary>
        public static string cancelDate {
            get {
                return ResourceManager.GetString("cancelDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contract Effective Date.
        /// </summary>
        public static string ced {
            get {
                return ResourceManager.GetString("ced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create New Application.
        /// </summary>
        public static string createNewApplication {
            get {
                return ResourceManager.GetString("createNewApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Denied.
        /// </summary>
        public static string denied {
            get {
                return ResourceManager.GetString("denied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date .
        /// </summary>
        public static string dueDate {
            get {
                return ResourceManager.GetString("dueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make changes to My Account.
        /// </summary>
        public static string editMyAccount {
            get {
                return ResourceManager.GetString("editMyAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enrollment Status.
        /// </summary>
        public static string enrollStatus {
            get {
                return ResourceManager.GetString("enrollStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finish Application.
        /// </summary>
        public static string finishApplication {
            get {
                return ResourceManager.GetString("finishApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboard.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Messages and Announcements.
        /// </summary>
        public static string messages {
            get {
                return ResourceManager.GetString("messages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have no messages or announcements.
        /// </summary>
        public static string noMessages {
            get {
                return ResourceManager.GetString("noMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Denied due to non-payment of premium: .
        /// </summary>
        public static string nonPaymentDenialMessage {
            get {
                return ResourceManager.GetString("nonPaymentDenialMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay Online.
        /// </summary>
        public static string payOnline {
            get {
                return ResourceManager.GetString("payOnline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All payments made will be reflected the next day.
        /// </summary>
        public static string premiumDueNotice {
            get {
                return ResourceManager.GetString("premiumDueNotice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Program.
        /// </summary>
        public static string program {
            get {
                return ResourceManager.GetString("program", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Renewal.
        /// </summary>
        public static string renewal {
            get {
                return ResourceManager.GetString("renewal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Renew Application.
        /// </summary>
        public static string renewApplicaiton {
            get {
                return ResourceManager.GetString("renewApplicaiton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medicaid will be mailing you a paper voter registration application in the near future. But, if you have a valid Alabama driver’s license or State ID, you can also register to vote online right now by clicking.
        /// </summary>
        public static string sosMessage1 {
            get {
                return ResourceManager.GetString("sosMessage1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You should be registered to vote at your address, so you should submit a registration application to update your address if you are not sure if your voter registration is current..
        /// </summary>
        public static string sosMessage2 {
            get {
                return ResourceManager.GetString("sosMessage2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you have a question about voter registration, you can call the Alabama Secretary of State toll-free at 1-800-274-8683.
        /// </summary>
        public static string sosMessage3 {
            get {
                return ResourceManager.GetString("sosMessage3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Source.
        /// </summary>
        public static string source {
            get {
                return ResourceManager.GetString("source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date.
        /// </summary>
        public static string startDate {
            get {
                return ResourceManager.GetString("startDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string status {
            get {
                return ResourceManager.GetString("status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status/Action.
        /// </summary>
        public static string statusAction {
            get {
                return ResourceManager.GetString("statusAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit Date.
        /// </summary>
        public static string submitDate {
            get {
                return ResourceManager.GetString("submitDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Amount Due.
        /// </summary>
        public static string totalAmtDue {
            get {
                return ResourceManager.GetString("totalAmtDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Detail.
        /// </summary>
        public static string viewDetail {
            get {
                return ResourceManager.GetString("viewDetail", resourceCulture);
            }
        }
    }
}
