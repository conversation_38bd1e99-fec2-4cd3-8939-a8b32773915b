﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Account {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class RemoteId {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal RemoteId() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Account.RemoteId", typeof(RemoteId).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to clicking here.
        /// </summary>
        public static string ClickingHere {
            get {
                return ResourceManager.GetString("ClickingHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is an error processing your request at this time. Please try again in few minutes..
        /// </summary>
        public static string ErrorMessage {
            get {
                return ResourceManager.GetString("ErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity checks provided by Experian. Please do not contact Medicaid or ALL KIDS help desk..
        /// </summary>
        public static string Footer {
            get {
                return ResourceManager.GetString("Footer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We need to verify your identify to keep someone else 
        ///   from applying for health coverage in your name..
        /// </summary>
        public static string help {
            get {
                return ResourceManager.GetString("help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to                       
        ///                     Call Center Hours
        /// 
        ///              Mon-Fri - 7:30 am - 9:00 pm 
        ///                 Sat - 9:00 am -7:00 pm 
        ///                Sun - 10:00am - 7:00 pm 
        ///                 Central Standard Time.
        /// </summary>
        public static string Help1 {
            get {
                return ResourceManager.GetString("Help1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once your identity is verified you will be able to submit an application online..
        /// </summary>
        public static string InfoMessage {
            get {
                return ResourceManager.GetString("InfoMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your identity could not be verified. Please submit a paper application in order to apply for health coverage..
        /// </summary>
        public static string InfoMessage1 {
            get {
                return ResourceManager.GetString("InfoMessage1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have exceeded the number of identity proofing attempts allowed for one day. Please try again tomorrow..
        /// </summary>
        public static string InfoMessage3 {
            get {
                return ResourceManager.GetString("InfoMessage3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Based on your information, below are some questions only you will be able to answer..
        /// </summary>
        public static string Information1 {
            get {
                return ResourceManager.GetString("Information1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer these questions below so that we can verify your Identity.
        /// </summary>
        public static string Information2 {
            get {
                return ResourceManager.GetString("Information2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity Verification - Contact Experian.
        /// </summary>
        public static string PageTitleExperian {
            get {
                return ResourceManager.GetString("PageTitleExperian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity Verification - Submit Paper Application.
        /// </summary>
        public static string PageTitlePaper {
            get {
                return ResourceManager.GetString("PageTitlePaper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity Verification.
        /// </summary>
        public static string PageTitleRemoteId {
            get {
                return ResourceManager.GetString("PageTitleRemoteId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity Verification - SSN Needed.
        /// </summary>
        public static string PageTitleSSN {
            get {
                return ResourceManager.GetString("PageTitleSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity Verification - Try Again Later.
        /// </summary>
        public static string PageTitleTryAgian {
            get {
                return ResourceManager.GetString("PageTitleTryAgian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can also download a paper application by {0} and apply by mail..
        /// </summary>
        public static string PaperApplicationMsg {
            get {
                return ResourceManager.GetString("PaperApplicationMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference Number :.
        /// </summary>
        public static string ReferenceNumberLabel {
            get {
                return ResourceManager.GetString("ReferenceNumberLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security Number (SSN).
        /// </summary>
        public static string SsnLabel {
            get {
                return ResourceManager.GetString("SsnLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your SSN is needed in order to assist in confirming your identity..
        /// </summary>
        public static string SsnNeededMessage {
            get {
                return ResourceManager.GetString("SsnNeededMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You took too long to answer. Please try again..
        /// </summary>
        public static string Timeout {
            get {
                return ResourceManager.GetString("Timeout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are currently unable to verify your identity based on the information you have provided. Please call Experian at 1-866-578-5409.
        /// </summary>
        public static string VerificationErrorA {
            get {
                return ResourceManager.GetString("VerificationErrorA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  with the Reference Number provided below..
        /// </summary>
        public static string VerificationErrorB {
            get {
                return ResourceManager.GetString("VerificationErrorB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string warning {
            get {
                return ResourceManager.GetString("warning", resourceCulture);
            }
        }
    }
}
