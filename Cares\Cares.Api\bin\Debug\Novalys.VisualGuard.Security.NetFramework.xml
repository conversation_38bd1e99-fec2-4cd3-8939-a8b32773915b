<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Novalys.VisualGuard.Security.NetFramework</name>
    </assembly>
    <members>
        <member name="T:Novalys.VisualGuard.Security.NetFramework.PlatformPlugIns.VGNetFrameworkIdentityModules">
            <summary>
            This class represents platform plugIn for list of identity modules for net framework specifically.
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.PlatformPlugIns.VGNetFrameworkIdentityModules.GetModules">
            <summary>
            Gets IdentityModules for net framework.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGNetFrameworkExecutionEngine">
            <summary>
            The Visual Guard execution engine executes all security actions for the application according to the permissions of the user.
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGNetFrameworkExecutionEngine.#ctor(Novalys.VisualGuard.Security.VGIPrincipal,Novalys.VisualGuard.Security.VGSecurityErrorEventHandler,System.Boolean,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGNetFrameworkExecutionEngine"/>
            </summary>
            <param name="principal">the principal for which you want to execute security actions</param>
            <param name="securityErrorHandler">the handler used when an error occurs.</param>
            <param name="useInterception">Indicates if the automatic interception is used.</param>
            <param name="checkForAspNet">Indicates that the execution engine should check if the component to secure is an ASP.Net Page, UserControl or MasterPage.</param>
        </member>
        <member name="T:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGICreationInterceptionSink">
            <summary>
            Sink for intercepting the creation of objects 
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGICreationInterceptionSink.CreateInstance(System.Runtime.Remoting.Activation.IConstructionCallMessage)">
            <summary>
            Create the instance of an intercepted class by using the specified <see cref="T:System.Runtime.Remoting.Activation.IConstructionCallMessage"/>
            </summary>
            <param name="ctorCall">an <see cref="T:System.Runtime.Remoting.Activation.IConstructionCallMessage"/> specifying the information about the object to instantiate.</param>
            <returns></returns>
        </member>
        <member name="T:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGIInterceptionSink">
            <summary>
            Base interface for interception sinks
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGIInterceptionSink.Next">
            <summary>
            Gets or sets the next interception sink
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGNetFrameworkInterceptionManager.Intercept(System.Type)">
            <summary>
            Activates object creation interception for the given type.
            Construction messages will be processed by calling the provided delegate.
            </summary>
            <param name="type">the type to intercept</param>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.VGNetFrameworkInterceptionManager.CreateInstance(System.Runtime.Remoting.Activation.IConstructionCallMessage)">
            <summary>
            Create an instance of an intercepted class
            </summary>
            <param name="ctorCall">the message containing constructor parameters</param>
            <returns>a IConstructionReturnMessage object</returns>
        </member>
        <member name="T:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorChannel">
            <summary>
            Summary description for InterceptorChannel.
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorChannel.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorChannel.ChannelName">
            <summary>
            
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorChannel.ChannelPriority">
            <summary>
            
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorChannel.CreateMessageSink(System.String,System.Object,System.String@)">
            <summary>
            
            </summary>
            <param name="url"></param>
            <param name="remoteChannelData"></param>
            <param name="objectURI"></param>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorChannel.Parse(System.String,System.String@)">
            <summary>
            
            </summary>
            <param name="url"></param>
            <param name="objectURI"></param>
            <returns></returns>
        </member>
        <member name="T:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink">
            <summary>
            
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.NextChannelSink">
            <summary>
            
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.NextSink">
            <summary>
            
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.Properties">
            <summary>
            
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.SyncProcessMessage(System.Runtime.Remoting.Messaging.IMessage)">
            <summary>
            
            </summary>
            <param name="msg"></param>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.AsyncProcessMessage(System.Runtime.Remoting.Messaging.IMessage,System.Runtime.Remoting.Messaging.IMessageSink)">
            <summary>
            
            </summary>
            <param name="msg"></param>
            <param name="replySink"></param>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.AsyncProcessRequest(System.Runtime.Remoting.Channels.IClientChannelSinkStack,System.Runtime.Remoting.Messaging.IMessage,System.Runtime.Remoting.Channels.ITransportHeaders,System.IO.Stream)">
            <summary>
            
            </summary>
            <param name="sinkStack"></param>
            <param name="msg"></param>
            <param name="headers"></param>
            <param name="stream"></param>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.AsyncProcessResponse(System.Runtime.Remoting.Channels.IClientResponseChannelSinkStack,System.Object,System.Runtime.Remoting.Channels.ITransportHeaders,System.IO.Stream)">
            <summary>
            
            </summary>
            <param name="sinkStack"></param>
            <param name="state"></param>
            <param name="headers"></param>
            <param name="stream"></param>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.GetRequestStream(System.Runtime.Remoting.Messaging.IMessage,System.Runtime.Remoting.Channels.ITransportHeaders)">
            <summary>
            
            </summary>
            <param name="msg"></param>
            <param name="headers"></param>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.NetFramework.ExecutionEngine.InterceptorSink.ProcessMessage(System.Runtime.Remoting.Messaging.IMessage,System.Runtime.Remoting.Channels.ITransportHeaders,System.IO.Stream,System.Runtime.Remoting.Channels.ITransportHeaders@,System.IO.Stream@)">
            <summary>
            
            </summary>
            <param name="msg"></param>
            <param name="requestHeaders"></param>
            <param name="requestStream"></param>
            <param name="responseHeaders"></param>
            <param name="responseStream"></param>
        </member>
        <member name="T:Novalys.VisualGuard.Security.NetFramework.VGNetFrameworkPlatformPlugInsProvider">
            <summary>
            Represents .net Framework Platform PlugIn Provider.
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.VGNetFrameworkPlatformPlugInsProvider.VGAssemblyHelper">
            <summary>
            AssemblyHelper PlugIn
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.VGNetFrameworkPlatformPlugInsProvider.VGLikeOperatorHelper">
            <summary>
            LikeOperator PlugIn
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.VGNetFrameworkPlatformPlugInsProvider.VGAppDomainHelper">
            <summary>
            AppDomainHelper PlugIn
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.VGNetFrameworkPlatformPlugInsProvider.IdentityModules">
            <summary>
            Gets a list of Identity modules for .Net framework.
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.NetFramework.VGNetFrameworkPlatformPlugInsProvider.Type">
            <summary>
            Gets the type of PlatformPlugInProvider
            </summary>
        </member>
    </members>
</doc>
