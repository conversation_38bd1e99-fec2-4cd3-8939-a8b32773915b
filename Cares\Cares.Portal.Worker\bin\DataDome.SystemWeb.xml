<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DataDome.SystemWeb</name>
    </assembly>
    <members>
        <member name="T:DataDome.Web.ConnectorHttpModule">
            <summary>
            Main ASP.NET HTTP Module that will handle incoming requests and delegating them to
            API server.
            </summary>
        </member>
        <member name="M:DataDome.Web.ConnectorHttpModule.OnBeginRequest(System.Object,System.EventArgs)">
            <summary>
            HTTP request processing handler.
            </summary>
            <remarks>
            This event (BeginRequest) is the first in ASP.NET processing pipeline. So it is
            responsible for core shield logic.
            </remarks>
            <param name="sender">
            Event source.
            </param>
            <param name="args">
            Event arguments.
            </param>
        </member>
        <member name="M:DataDome.Web.ConnectorHttpModule.System#Web#IHttpModule#Init(System.Web.HttpApplication)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Web.ConnectorHttpModule.System#Web#IHttpModule#Dispose">
            <inheritdoc />
        </member>
        <member name="T:DataDome.Web.WebConnectorRequest">
            <summary>
            Implementation for <see cref="T:DataDome.Web.IConnectorRequest"/> for ASP.NET HTTP module version,
            based on System.Web classes like <see cref="T:System.Web.HttpContext"/>, <see cref="T:System.Web.HttpRequest"/>,
            etc.
            </summary>
        </member>
        <member name="P:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#Id">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#ClientId">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#Url">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#Local">
            <inheritdoc />
        </member>
        <member name="P:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#Remote">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#GetCookie(System.String)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#SetCookie(System.Web.HttpCookie)">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#Collect(System.Collections.Generic.IDictionary{System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="M:DataDome.Web.WebConnectorRequest.DataDome#Web#IConnectorRequest#Dump">
            <inheritdoc />
        </member>
    </members>
</doc>
