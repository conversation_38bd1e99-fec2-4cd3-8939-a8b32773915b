﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{505845FB-0862-467B-92DC-09F27AEA9002}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Cares.Api.Messages</RootNamespace>
    <AssemblyName>Cares.Api.Messages</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Dev|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Dev\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Test\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging|AnyCPU'">
    <OutputPath>bin\Staging\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=10.0.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.10.1.1\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ElderlyDisabled\ElderlyDisabledAdditionalBurialFundsBudgetDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledApplicationDetailDcsDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledFamilyAllocationDetail.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledLiabilityTestDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledLifeInsuranceAdditionalDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledMedicalInsurancePartDBudgetDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledMedicalInsuranceMonthDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledOtherBurialFundsDetailsDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPrepaidBurialSpaceDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPropertyPreviousDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonalPropertyCollectibleDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonalPropertyMachineDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledQitAndAllocationDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledResourceMonthDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledNonMagiIncomeDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonalPropertyAutoDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPropertyMobileHomeDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPropertyParcelDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledResourceBankSubDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledResourceTransferMonthDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledSpouseAllocationDetail.cs" />
    <Compile Include="ElderlyDisabled\MedicalLongTermCareInsuranceDetailDto.cs" />
    <Compile Include="ElderlyDisabled\SpousalAllocationAmountDto.cs" />
    <Compile Include="ElderlyDisabled\SpousalAllocationDetailsDto.cs" />
    <Compile Include="PresumptiveEligibility\PEPDeterminerDto.cs" />
    <Compile Include="PresumptiveEligibility\PEPDeterminersDto.cs" />
    <Compile Include="PresumptiveEligibility\PEPProvidersDto.cs" />
    <Compile Include="PresumptiveEligibility\PEPSearchDeterminersDto.cs" />
    <Compile Include="PresumptiveEligibility\PEPSnapshotDto.cs" />
    <Compile Include="PresumptiveEligibility\ProviderRepresentativeDto.cs" />
    <Compile Include="PresumptiveEligibility\PEPProviderDto.cs" />
    <Compile Include="Facility\DysFacilitiesDto.cs" />
    <Compile Include="Facility\DysFacilityDto.cs" />
    <Compile Include="ElderlyDisabled\PersonalNeedsAllowanceDto.cs" />
    <Compile Include="JIY\ApplicationDetailsJiyDto.cs" />
    <Compile Include="ApplicationSnapshot\ApplicationSnapshotDto.cs" />
    <Compile Include="Applications\ApplicationElderlyDisabledDetail.cs" />
    <Compile Include="Applications\ApplicationLivingArrangement.cs" />
    <Compile Include="Applications\ApplicationResidencyInformation.cs" />
    <Compile Include="Applications\ApplicationDto.cs" />
    <Compile Include="Applications\ApplicationStatusHistory.cs" />
    <Compile Include="Applications\BaseApplicationDetailDto.cs" />
    <Compile Include="Applications\CopyApplicationRequest.cs" />
    <Compile Include="Applications\CopyApplicationResponse.cs" />
    <Compile Include="Applications\CopyEDAppToMspRequest.cs" />
    <Compile Include="BENDEX\BENDEXResponse.cs" />
    <Compile Include="BENDEX\BENDEXRequest.cs" />
    <Compile Include="COLA\ColaFactSheetCategoriesDto.cs" />
    <Compile Include="COLA\ColaFactSheetCategoryDto.cs" />
    <Compile Include="COLA\ColaFactSheetSubCategoryDto.cs" />
    <Compile Include="COLA\ColaFactSheetYearsDto.cs" />
    <Compile Include="COLA\ColaFactSheetYearValueDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledAdditionalBurialFundsDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledApplicationDetailDto.cs" />
    <Compile Include="Constants.cs" />
    <Compile Include="DHR\ELE\ELEMemberResponse.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledApplicationInfoBarDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledEligibilityDeterminationDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledEligibilityDeterminationsDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledExpediteFacilityDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledExpediteFacilityListDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledLiabilityDetailChangeCode.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledNonMagiIncomesDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledTBQMedicarePartDInfoDto.cs" />
    <Compile Include="ElderlyDisabled\ExpediteFacilityProvidersDto.cs" />
    <Compile Include="ElderlyDisabled\ExpediteFacilityDto.cs" />
    <Compile Include="ElderlyDisabled\ExpediteFacilityProviderDto.cs" />
    <Compile Include="ElderlyDisabled\ExparteApplicationInfoDto.cs" />
    <Compile Include="ElderlyDisabled\FinancialInstitutionDto.cs" />
    <Compile Include="ElderlyDisabled\FinancialInstitutionsDto.cs" />
    <Compile Include="ElderlyDisabled\PersonInfoDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledLiabilityDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledLiabilityDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ProviderDto.cs" />
    <Compile Include="ElderlyDisabled\ReconcileFacilityProviderDto.cs" />
    <Compile Include="ElderlyDisabled\RepresentativeInfoDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledLifeInsuranceDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledMedicalInsuranceDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledMedicalInsuranceDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonalPropertyCollectibleDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonalPropertyMachineDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonalPropertyDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledLifeInsuranceDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledOtherBurialFundsDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonalPropertyAutoDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledResourceDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledResourceBankDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledResourceDetailDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledResourceTransferDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledFacilityDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledFacilityListDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledFormerSpouseDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledApplicationDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledHouseholdMembersDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledHouseholdMemberDto.cs" />
    <Compile Include="Applications\NonMagiIncome.cs" />
    <Compile Include="Applications\NonMagiIncomesDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPerson.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPersonDetail.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPropertyDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPropertyMobileHomeDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPropertyParcelDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledPropertyPreviousDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledSpouseDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledVeteranDto.cs" />
    <Compile Include="ElderlyDisabled\ElderlyDisabledWorkerNumberListDto.cs" />
    <Compile Include="ElderlyDisabled\StagingExpediteImportDetailsDto.cs" />
    <Compile Include="ElderlyDisabled\StagingExpediteImportDto.cs" />
    <Compile Include="Enrollment\CheckAddUnborn.cs" />
    <Compile Include="Enrollment\DenialReasonDTO.cs" />
    <Compile Include="Enrollment\DenialReasonsDTO.cs" />
    <Compile Include="Enrollment\Enrollment.cs" />
    <Compile Include="Enrollment\MECCheck.cs" />
    <Compile Include="Enrollment\PersonEnrollmentsResponse.cs" />
    <Compile Include="Enums\EnrollmentStatus.cs" />
    <Compile Include="ExpressLaneElibility\ELEEnrollmentRequest.cs" />
    <Compile Include="ExpressLaneElibility\ELEEnrollmentResponse.cs" />
    <Compile Include="ExpressLaneElibility\ELEEnrollmentWebMethodMessage.cs" />
    <Compile Include="ExpressLaneElibility\ELEEnrollmentWebMethodResponse.cs" />
    <Compile Include="DHR\ELE\ELEUpdateInputParameters.cs" />
    <Compile Include="DHR\ELE\BaseELEModel.cs" />
    <Compile Include="DHR\ELE\SelectELEMembersResponse.cs" />
    <Compile Include="ExpressLaneElibility\ELEMessage.cs" />
    <Compile Include="ExpressLaneElibility\ELEResponse.cs" />
    <Compile Include="HistoricalData\HistoricalDataDetail.cs" />
    <Compile Include="HistoricalData\HistoricalDataDetailsResponse.cs" />
    <Compile Include="HistoricalData\HistoricalDataSearchRequest.cs" />
    <Compile Include="HistoricalData\HistoricalDataSearchResponse.cs" />
    <Compile Include="HistoricalData\HistoricalDataSearchResult.cs" />
    <Compile Include="Hub\RIDP\FraudWebMethodMessage.cs" />
    <Compile Include="Hub\RIDP\FraudWebMethodResponse.cs" />
    <Compile Include="Hub\RIDP\PersonRIDP.cs" />
    <Compile Include="Hub\RIDP\PrimaryGetMany.cs" />
    <Compile Include="Hub\RIDP\PrimaryWebMethodMessage.cs" />
    <Compile Include="Hub\RIDP\PrimaryWebMethodResponse.cs" />
    <Compile Include="Hub\RIDP\FraudRequest.cs" />
    <Compile Include="Hub\RIDP\FraudResponse.cs" />
    <Compile Include="Hub\RIDP\PrimaryRequest.cs" />
    <Compile Include="Hub\RIDP\PrimaryResponse.cs" />
    <Compile Include="Hub\RIDP\SecondaryRequest.cs" />
    <Compile Include="Hub\RIDP\SecondaryResponse.cs" />
    <Compile Include="Hub\RIDP\SecondaryWebMethodMessage.cs" />
    <Compile Include="Hub\RIDP\SecondaryWebMethodResponse.cs" />
    <Compile Include="Infrastructure\LookupDto.cs" />
    <Compile Include="InRule\EnDStateAidCategoryDeterminationDTO.cs" />
    <Compile Include="JIY\ReleaseDateHistoryJiyDto.cs" />
    <Compile Include="Landing\LandingElderlyDisabled.cs" />
    <Compile Include="Landing\LandingLtc.cs" />
    <Compile Include="Landing\LandingNonUSAddress.cs" />
    <Compile Include="Landing\LandingApplication.cs" />
    <Compile Include="Landing\LandingApplicationDetails.cs" />
    <Compile Include="Landing\LandingApplicationIdResponse.cs" />
    <Compile Include="Landing\LandingAddress.cs" />
    <Compile Include="Landing\LandingApplicationInfo.cs" />
    <Compile Include="Landing\LandingApplicationInfoMember.cs" />
    <Compile Include="Landing\LandingChainAndCrossReference.cs" />
    <Compile Include="Landing\LandingChainAndCrossReferenceFields.cs" />
    <Compile Include="Landing\LandingCommunication.cs" />
    <Compile Include="Landing\LandingDetails.cs" />
    <Compile Include="Landing\LandingEnrollment.cs" />
    <Compile Include="Landing\LandingPersonAlert.cs" />
    <Compile Include="Landing\LandingPersonDetailsResponse.cs" />
    <Compile Include="Landing\LandingPersonEnrollment.cs" />
    <Compile Include="Landing\LandingPhones.cs" />
    <Compile Include="Landing\LandingPlasticCard.cs" />
    <Compile Include="Landing\LandingPostpartumDetails.cs" />
    <Compile Include="Landing\LandingSearchRequest.cs" />
    <Compile Include="Landing\LandingSearchResponse.cs" />
    <Compile Include="Landing\LandingSearchResult.cs" />
    <Compile Include="Letters\AnnualEligibilityReviewLetterParameterDetail.cs" />
    <Compile Include="Letters\ElderlyDisabledAwardLetterDto.cs" />
    <Compile Include="Letters\ElderlyDisabledDenialDescriptionsDto.cs" />
    <Compile Include="Letters\ElderlyDisabledDenialLetterDto.cs" />
    <Compile Include="Letters\ElderlyDisabledDraftandFinalizedDetailDto.cs" />
    <Compile Include="Letters\ElderlyDisabledLettersDto.cs" />
    <Compile Include="Letters\ElderlyDisabledLiaiblityInfoLetterDto.cs" />
    <Compile Include="Letters\ElderlyDisabledTerminationLetterDto.cs" />
    <Compile Include="Letters\LetterParameterDetailsDto.cs" />
    <Compile Include="Letters\LetterRecipientDto.cs" />
    <Compile Include="Mapper\CommonMapper.cs" />
    <Compile Include="Mapper\ElderlyDisabledMapper.cs" />
    <Compile Include="Mapper\WorkerPortalAccountMapper.cs" />
    <Compile Include="Notes\NoteDto.cs" />
    <Compile Include="Notes\NotesDto.cs" />
    <Compile Include="Notes\NotesPersonDto.cs" />
    <Compile Include="OnlineWorkFlow\OnlineWorkFlow.cs" />
    <Compile Include="Person\Address.cs" />
    <Compile Include="Person\DependentPersonAddress.cs" />
    <Compile Include="Person\PersonAddresses.cs" />
    <Compile Include="Person\PersonContactPreference.cs" />
    <Compile Include="Person\PersonAddress.cs" />
    <Compile Include="Person\ElderlyDisabledRace.cs" />
    <Compile Include="Person\PersonDto.cs" />
    <Compile Include="Person\PersonRace.cs" />
    <Compile Include="Person\PhoneAndPersonPhone.cs" />
    <Compile Include="Person\UnbornDto.cs" />
    <Compile Include="Person\UnbornList.cs" />
    <Compile Include="Person\UpdatePersonSsn.cs" />
    <Compile Include="PrimaryKeyId.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Reference\BaseReferenceDto.cs" />
    <Compile Include="Reference\ReferenceResponse.cs" />
    <Compile Include="RefugeeMedicalAssistance\ApplicationRefugeeDetailDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeApplicantDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeApplicantRace.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeApplicationDetailDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeApplicationInformationDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeEligibilityDeterminationDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeIncomeDetailDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeIncomeDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeMedicalInsuranceDetailDto.cs" />
    <Compile Include="RefugeeMedicalAssistance\RefugeeSpouseDto.cs" />
    <Compile Include="RenewalRedeterminationValidation\RRVRequest.cs" />
    <Compile Include="RenewalRedeterminationValidation\RRVResponse.cs" />
    <Compile Include="Representatives\AuthorizedRepresentative.cs" />
    <Compile Include="Representatives\HouseholdMember.cs" />
    <Compile Include="Representatives\RepresentativeInfo.cs" />
    <Compile Include="Representatives\SponsorInfo.cs" />
    <Compile Include="SerializerHelper.cs" />
    <Compile Include="SVES\ClaimNumberDetail.cs" />
    <Compile Include="SVES\SVESAdditionalRequestInfo.cs" />
    <Compile Include="SVES\SVESCrossReferenceAccountNumber.cs" />
    <Compile Include="SVES\SVESMBCHistory.cs" />
    <Compile Include="SVES\SVESRequest.cs" />
    <Compile Include="SVES\SVESResponse.cs" />
    <Compile Include="SVES\SVESStandardResponse.cs" />
    <Compile Include="SVES\SVESTitleIIResponse.cs" />
    <Compile Include="SVES\SVESTitleXVIResponse.cs" />
    <Compile Include="SVES\SVESUnearnedIncome.cs" />
    <Compile Include="TBQ\TBQCoPaymentInfo.cs" />
    <Compile Include="TBQ\TBQCrossReferenceInfo.cs" />
    <Compile Include="TBQ\TBQEntitlementInfo.cs" />
    <Compile Include="TBQ\TBQESRDClinicalDialysisInfo.cs" />
    <Compile Include="TBQ\TBQGroupHealthPlanInfo.cs" />
    <Compile Include="TBQ\TBQMBIInfo.cs" />
    <Compile Include="TBQ\TBQPartBEntitlementInfo.cs" />
    <Compile Include="TBQ\TBQPartBThirdPartyInfo.cs" />
    <Compile Include="TBQ\TBQResponse.cs" />
    <Compile Include="TBQ\TBQThirdPartyInfo.cs" />
    <Compile Include="WebUserAccount\UserAccountDetailDto.cs" />
    <Compile Include="WebUserAccount\WebUserAccountDetail.cs" />
    <Compile Include="WebUserAccount\WorkerPortalAccountDto.cs" />
    <Compile Include="WebUserAccount\WorkerReminderDto.cs" />
    <Compile Include="WebUserAccount\WorkerReminderListDto.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Cares.Api.Infrastructure\Cares.Api.Infrastructure.csproj">
      <Project>{d8f7ab2c-bb31-4fe1-b470-9ae360f5abe5}</Project>
      <Name>Cares.Api.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Citizen.Models\Cares.Portal.Citizen.Models.csproj">
      <Project>{BDC9717F-EA1A-471F-9C30-A41D7F19CEDF}</Project>
      <Name>Cares.Portal.Citizen.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Infrastructure\Cares.Portal.Infrastructure.csproj">
      <Project>{ee96e415-e65c-4069-858a-3897238dcf11}</Project>
      <Name>Cares.Portal.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Worker.Models\Cares.Portal.Worker.Models.csproj">
      <Project>{94792cd6-a08c-4e5e-85a0-171cb5acb89f}</Project>
      <Name>Cares.Portal.Worker.Models</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Hub\FFM\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>