﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Data
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class CaresApplicationDBEntities : DbContext
    {
        public CaresApplicationDBEntities()
            : base("name=CaresApplicationDBEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<APPLICATION_HOUSEHOLD> APPLICATION_HOUSEHOLD { get; set; }
        public virtual DbSet<APPLICATION_INCOME> APPLICATION_INCOME { get; set; }
        public virtual DbSet<APPLICATION_NOTES> APPLICATION_NOTES { get; set; }
        public virtual DbSet<APPLICATION_NOTES_DETAIL> APPLICATION_NOTES_DETAIL { get; set; }
        public virtual DbSet<EMPLOYER> EMPLOYERs { get; set; }
        public virtual DbSet<EMPLOYER_ADDRESS> EMPLOYER_ADDRESS { get; set; }
        public virtual DbSet<EMPLOYER_CONTACT> EMPLOYER_CONTACT { get; set; }
        public virtual DbSet<EMPLOYER_EMAIL> EMPLOYER_EMAIL { get; set; }
        public virtual DbSet<EMPLOYER_PHONE> EMPLOYER_PHONE { get; set; }
        public virtual DbSet<ENROLLEE_UPDATE> ENROLLEE_UPDATE { get; set; }
        public virtual DbSet<FH_SPONSORSHIP_DATA> FH_SPONSORSHIP_DATA { get; set; }
        public virtual DbSet<FH_SSA_COMPOSITE_REQUEST> FH_SSA_COMPOSITE_REQUEST { get; set; }
        public virtual DbSet<FH_SSA_COMPOSITE_RESPONSE> FH_SSA_COMPOSITE_RESPONSE { get; set; }
        public virtual DbSet<FH_SSA_INCARCERATION_DETAIL> FH_SSA_INCARCERATION_DETAIL { get; set; }
        public virtual DbSet<FH_VLP_GETCASEDETAILS_RESPONSE> FH_VLP_GETCASEDETAILS_RESPONSE { get; set; }
        public virtual DbSet<FH_VLP_REQUEST> FH_VLP_REQUEST { get; set; }
        public virtual DbSet<FH_VLP_RESPONSE> FH_VLP_RESPONSE { get; set; }
        public virtual DbSet<FH_VLPREVERIFY_REQUEST> FH_VLPREVERIFY_REQUEST { get; set; }
        public virtual DbSet<FH_VLPSUBMIT_REQUEST> FH_VLPSUBMIT_REQUEST { get; set; }
        public virtual DbSet<LETTER_HISTORY> LETTER_HISTORY { get; set; }
        public virtual DbSet<PHONE> PHONEs { get; set; }
        public virtual DbSet<PLASTIC_CARD> PLASTIC_CARD { get; set; }
        public virtual DbSet<REF_LETTER_CATEGORY> REF_LETTER_CATEGORY { get; set; }
        public virtual DbSet<REF_RIDP_STATUS> REF_RIDP_STATUS { get; set; }
        public virtual DbSet<CONTACT_PREFERENCE> CONTACT_PREFERENCE { get; set; }
        public virtual DbSet<PERSON_ADDRESS> PERSON_ADDRESS { get; set; }
        public virtual DbSet<PERSON_ASSISTANCE> PERSON_ASSISTANCE { get; set; }
        public virtual DbSet<PERSON_DOC> PERSON_DOC { get; set; }
        public virtual DbSet<PERSON_ETHNICITY> PERSON_ETHNICITY { get; set; }
        public virtual DbSet<PERSON_PHONE> PERSON_PHONE { get; set; }
        public virtual DbSet<PERSON_RACE> PERSON_RACE { get; set; }
        public virtual DbSet<APPLICATION_ENROLLMENT> APPLICATION_ENROLLMENT { get; set; }
        public virtual DbSet<APPLICATION_ENROLLMENT_HISTORY> APPLICATION_ENROLLMENT_HISTORY { get; set; }
        public virtual DbSet<T_PERSON_RIDP> T_PERSON_RIDP { get; set; }
        public virtual DbSet<ACCOUNT> ACCOUNTs { get; set; }
        public virtual DbSet<FH_RIDP_FIRST_REQUEST> FH_RIDP_FIRST_REQUEST { get; set; }
        public virtual DbSet<FH_RIDP_FIRST_RESPONSE> FH_RIDP_FIRST_RESPONSE { get; set; }
        public virtual DbSet<FH_RIDP_FIRST_RESPONSE_QUESTION> FH_RIDP_FIRST_RESPONSE_QUESTION { get; set; }
        public virtual DbSet<FH_RIDP_FRAUD_REQUEST> FH_RIDP_FRAUD_REQUEST { get; set; }
        public virtual DbSet<FH_RIDP_FRAUD_RESPONSE> FH_RIDP_FRAUD_RESPONSE { get; set; }
        public virtual DbSet<FH_RIDP_SECOND_REQUEST> FH_RIDP_SECOND_REQUEST { get; set; }
        public virtual DbSet<FH_RIDP_SECOND_RESPONSE> FH_RIDP_SECOND_RESPONSE { get; set; }
        public virtual DbSet<PERSON> People { get; set; }
        public virtual DbSet<PERSON_DETAIL> PERSON_DETAIL { get; set; }
        public virtual DbSet<PERSON_RIDP> PERSON_RIDP { get; set; }
        public virtual DbSet<APPLICATION_STATUS_HISTORY> APPLICATION_STATUS_HISTORY { get; set; }
        public virtual DbSet<APPLICATION_FUTURE_INCOME> APPLICATION_FUTURE_INCOME { get; set; }
        public virtual DbSet<APPLICATION_HEALTH_COVERAGE> APPLICATION_HEALTH_COVERAGE { get; set; }
        public virtual DbSet<APPLICATION_CHIP_SPECIFIC> APPLICATION_CHIP_SPECIFIC { get; set; }
        public virtual DbSet<APPLICATION_EHC> APPLICATION_EHC { get; set; }
        public virtual DbSet<APPLICATION_EHC_EMPLOYEE> APPLICATION_EHC_EMPLOYEE { get; set; }
        public virtual DbSet<APPLICATION_EHC_EMPLOYER> APPLICATION_EHC_EMPLOYER { get; set; }
        public virtual DbSet<APPLICATION_MED_CHIP_SPECIFIC> APPLICATION_MED_CHIP_SPECIFIC { get; set; }
        public virtual DbSet<APPLICATION_MED_CHIP_SPECIFIC_DETAIL> APPLICATION_MED_CHIP_SPECIFIC_DETAIL { get; set; }
        public virtual DbSet<EXPRESS_LANE_ELIGIBILITY> EXPRESS_LANE_ELIGIBILITY { get; set; }
        public virtual DbSet<APPLICATION_SIGN_SUBMIT> APPLICATION_SIGN_SUBMIT { get; set; }
        public virtual DbSet<APPLICATION_SIGN_SUBMIT_INCARCERATED> APPLICATION_SIGN_SUBMIT_INCARCERATED { get; set; }
        public virtual DbSet<REF_LETTER_PARAGRAPH> REF_LETTER_PARAGRAPH { get; set; }
        public virtual DbSet<REF_SUFFIX> REF_SUFFIX { get; set; }
        public virtual DbSet<RENEWAL_REDETERMINATION_VERIFICATION> RENEWAL_REDETERMINATION_VERIFICATION { get; set; }
        public virtual DbSet<APPLICATION_RENEWAL> APPLICATION_RENEWAL { get; set; }
        public virtual DbSet<REF_APPLICATION_STATUS> REF_APPLICATION_STATUS { get; set; }
        public virtual DbSet<sysnl_donotmodify__PERSON> sysnl_donotmodify__PERSON { get; set; }
        public virtual DbSet<CHI_PAY_PERIOD> CHI_PAY_PERIOD { get; set; }
        public virtual DbSet<CURRENT_HOUSEHOLD_INCOME> CURRENT_HOUSEHOLD_INCOME { get; set; }
        public virtual DbSet<sysnl_donotmodify__FTI_DETAIL> sysnl_donotmodify__FTI_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_DEDUCTIONS> APPLICATION_DEDUCTIONS { get; set; }
        public virtual DbSet<FH_MEDICARE_INSURANCE> FH_MEDICARE_INSURANCE { get; set; }
        public virtual DbSet<FH_MEDICARE_RESPONSE> FH_MEDICARE_RESPONSE { get; set; }
        public virtual DbSet<MEDICAID_RECIPIENTS> MEDICAID_RECIPIENTS { get; set; }
        public virtual DbSet<MEDICAID_SUSPENDED> MEDICAID_SUSPENDED { get; set; }
        public virtual DbSet<REF_STATE> REF_STATE { get; set; }
        public virtual DbSet<ACCOUNT_DETAIL> ACCOUNT_DETAIL { get; set; }
        public virtual DbSet<ACCOUNT_PREREQUISITE> ACCOUNT_PREREQUISITE { get; set; }
        public virtual DbSet<ACCOUNT_SECURITY_QUESTION> ACCOUNT_SECURITY_QUESTION { get; set; }
        public virtual DbSet<MEC_CHECK> MEC_CHECK { get; set; }
        public virtual DbSet<REF_COUNTY> REF_COUNTY { get; set; }
        public virtual DbSet<REF_PHONE_TYPE> REF_PHONE_TYPE { get; set; }
        public virtual DbSet<REF_EMPLOYER_COVERAGE_CHANGE> REF_EMPLOYER_COVERAGE_CHANGE { get; set; }
        public virtual DbSet<REF_AIAN_INCOME_SOURCE> REF_AIAN_INCOME_SOURCE { get; set; }
        public virtual DbSet<REF_FED_REC_TRIBES> REF_FED_REC_TRIBES { get; set; }
        public virtual DbSet<REF_HEALTH_COVERAGE_MC_TYPE> REF_HEALTH_COVERAGE_MC_TYPE { get; set; }
        public virtual DbSet<REF_HEALTH_COVERAGE_TYPE> REF_HEALTH_COVERAGE_TYPE { get; set; }
        public virtual DbSet<AC_BALANCE_ADJUSTMENT> AC_BALANCE_ADJUSTMENT { get; set; }
        public virtual DbSet<AC_BALANCE_ADJUSTMENT_DETAIL> AC_BALANCE_ADJUSTMENT_DETAIL { get; set; }
        public virtual DbSet<AC_PAYMENT_BATCH> AC_PAYMENT_BATCH { get; set; }
        public virtual DbSet<AC_PAYMENT_REFUND> AC_PAYMENT_REFUND { get; set; }
        public virtual DbSet<REF_AC_ADJUSTMENT_REASON> REF_AC_ADJUSTMENT_REASON { get; set; }
        public virtual DbSet<REF_AC_ADJUSTMENT_TYPE> REF_AC_ADJUSTMENT_TYPE { get; set; }
        public virtual DbSet<REF_AC_BATCH_STATUS> REF_AC_BATCH_STATUS { get; set; }
        public virtual DbSet<REF_AC_PAYMENT_TYPE> REF_AC_PAYMENT_TYPE { get; set; }
        public virtual DbSet<REF_AC_REFUND_REASON> REF_AC_REFUND_REASON { get; set; }
        public virtual DbSet<REF_CANCEL_REASON> REF_CANCEL_REASON { get; set; }
        public virtual DbSet<LETTER_HISTORY_DETAIL> LETTER_HISTORY_DETAIL { get; set; }
        public virtual DbSet<ADDRESS> ADDRESSes { get; set; }
        public virtual DbSet<REF_RACE> REF_RACE { get; set; }
        public virtual DbSet<REF_DHR_COVERAGE_TYPE> REF_DHR_COVERAGE_TYPE { get; set; }
        public virtual DbSet<APPLICATION_REPRESENTATIVE> APPLICATION_REPRESENTATIVE { get; set; }
        public virtual DbSet<t_PERSON_ADDRESS> t_PERSON_ADDRESS { get; set; }
        public virtual DbSet<REF_GENDER> REF_GENDER { get; set; }
        public virtual DbSet<t_PERSON_DETAIL> t_PERSON_DETAIL { get; set; }
        public virtual DbSet<vw_ADDRESS> vw_ADDRESS { get; set; }
        public virtual DbSet<EMAIL_ADDRESS> EMAIL_ADDRESS { get; set; }
        public virtual DbSet<PERSON_SANITIZED_NAME> PERSON_SANITIZED_NAME { get; set; }
        public virtual DbSet<REF_PROCESS_PARAM> REF_PROCESS_PARAM { get; set; }
        public virtual DbSet<REF_PROCESS_PARAM_VALUE> REF_PROCESS_PARAM_VALUE { get; set; }
        public virtual DbSet<APPLICATION_BANNER> APPLICATION_BANNER { get; set; }
        public virtual DbSet<MEDICARE_INFORMATION> MEDICARE_INFORMATION { get; set; }
        public virtual DbSet<SPONSOR_PHONE> SPONSOR_PHONE { get; set; }
        public virtual DbSet<NON_MAGI_APPLICATION_INCOME> NON_MAGI_APPLICATION_INCOME { get; set; }
        public virtual DbSet<REF_SPONSOR_TYPE> REF_SPONSOR_TYPE { get; set; }
        public virtual DbSet<REF_SPONSOR_LEGAL_AUTHORITY> REF_SPONSOR_LEGAL_AUTHORITY { get; set; }
        public virtual DbSet<REF_SPONSOR_ROLE> REF_SPONSOR_ROLE { get; set; }
        public virtual DbSet<PERSON_SEARCH> PERSON_SEARCH { get; set; }
        public virtual DbSet<REF_SUB_PROGRAM_CATEGORY> REF_SUB_PROGRAM_CATEGORY { get; set; }
        public virtual DbSet<WorkerPortal_Account> WorkerPortal_Account { get; set; }
        public virtual DbSet<APPLICATION_DETAIL> APPLICATION_DETAIL { get; set; }
        public virtual DbSet<APPLICATION> APPLICATIONs { get; set; }
        public virtual DbSet<REF_SUSPENSION_REASON> REF_SUSPENSION_REASON { get; set; }
        public virtual DbSet<REF_LETTER_XREF> REF_LETTER_XREF { get; set; }
        public virtual DbSet<REF_SUSPENSION_REASON_SOURCE> REF_SUSPENSION_REASON_SOURCE { get; set; }
        public virtual DbSet<FH_NON_ESI_MEC_RESPONSE> FH_NON_ESI_MEC_RESPONSE { get; set; }
        public virtual DbSet<APPLICATION_AIAN_INCOME_SOURCE> APPLICATION_AIAN_INCOME_SOURCE { get; set; }
        public virtual DbSet<REF_INCOME_FREQUENCY> REF_INCOME_FREQUENCY { get; set; }
        public virtual DbSet<REF_INCOME_TYPE> REF_INCOME_TYPE { get; set; }
        public virtual DbSet<REF_LETTER_TYPE> REF_LETTER_TYPE { get; set; }
        public virtual DbSet<REF_RELATIONSHIP_TYPE> REF_RELATIONSHIP_TYPE { get; set; }
        public virtual DbSet<APPLICATION_DEPENDENTS> APPLICATION_DEPENDENTS { get; set; }
        public virtual DbSet<APPLICATION_LIVES_WITH> APPLICATION_LIVES_WITH { get; set; }
        public virtual DbSet<APPLICATION_MEDICAID_SPECIFIC> APPLICATION_MEDICAID_SPECIFIC { get; set; }
        public virtual DbSet<SPONSOR> SPONSOR { get; set; }
        public virtual DbSet<APPLICATION_FORMER_SPOUSE> APPLICATION_FORMER_SPOUSE { get; set; }
        public virtual DbSet<APPLICATION_NON_MAGI_NO_SSN_PERSON> APPLICATION_NON_MAGI_NO_SSN_PERSON { get; set; }
        public virtual DbSet<REF_DISTRICT_OFFICE> REF_DISTRICT_OFFICE { get; set; }
        public virtual DbSet<REF_ADDITIONAL_BURIAL_FUNDS_TYPE> REF_ADDITIONAL_BURIAL_FUNDS_TYPE { get; set; }
        public virtual DbSet<REF_APPLICATION_TYPE> REF_APPLICATION_TYPE { get; set; }
        public virtual DbSet<REF_BANK_ACCOUNT_TYPE> REF_BANK_ACCOUNT_TYPE { get; set; }
        public virtual DbSet<REF_INSURANCE_TYPE> REF_INSURANCE_TYPE { get; set; }
        public virtual DbSet<REF_LIVING_ARRANGEMENT> REF_LIVING_ARRANGEMENT { get; set; }
        public virtual DbSet<REF_MANUAL_CITIZENSHIP_VER_DOC> REF_MANUAL_CITIZENSHIP_VER_DOC { get; set; }
        public virtual DbSet<REF_RESOURCE_TYPE> REF_RESOURCE_TYPE { get; set; }
        public virtual DbSet<t_PERSON_PHONE> t_PERSON_PHONE { get; set; }
        public virtual DbSet<APPLICATION_LIFE_INSURANCE> APPLICATION_LIFE_INSURANCE { get; set; }
        public virtual DbSet<APPLICATION_LIVING_ARRANGEMENT> APPLICATION_LIVING_ARRANGEMENT { get; set; }
        public virtual DbSet<APPLICATION_MEDICAL_INSURANCE> APPLICATION_MEDICAL_INSURANCE { get; set; }
        public virtual DbSet<APPLICATION_NON_MAGI_INCOME> APPLICATION_NON_MAGI_INCOME { get; set; }
        public virtual DbSet<APPLICATION_OTHER_BURIAL_FUNDS> APPLICATION_OTHER_BURIAL_FUNDS { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_PROPERTY> APPLICATION_PERSONAL_PROPERTY { get; set; }
        public virtual DbSet<APPLICATION_PROPERTY> APPLICATION_PROPERTY { get; set; }
        public virtual DbSet<APPLICATION_RESIDENCY_INFORMATION> APPLICATION_RESIDENCY_INFORMATION { get; set; }
        public virtual DbSet<APPLICATION_RESOURCE> APPLICATION_RESOURCE { get; set; }
        public virtual DbSet<APPLICATION_RESOURCE_DETAIL> APPLICATION_RESOURCE_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_VETERAN_STATUS> APPLICATION_VETERAN_STATUS { get; set; }
        public virtual DbSet<t_CONTACT_PREFERENCE> t_CONTACT_PREFERENCE { get; set; }
        public virtual DbSet<APPLICATION_ADDITIONAL_BURIAL_FUNDS> APPLICATION_ADDITIONAL_BURIAL_FUNDS { get; set; }
        public virtual DbSet<APPLICATION_LIFE_INSURANCE_DETAIL> APPLICATION_LIFE_INSURANCE_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_PROPERTY_MACHINE> APPLICATION_PERSONAL_PROPERTY_MACHINE { get; set; }
        public virtual DbSet<REF_ELDERLY_DISABLED_PROGRAM> REF_ELDERLY_DISABLED_PROGRAM { get; set; }
        public virtual DbSet<REF_PROGRAM> REF_PROGRAM { get; set; }
        public virtual DbSet<REF_PROGRAM_SUB_CATEGORY> REF_PROGRAM_SUB_CATEGORY { get; set; }
        public virtual DbSet<REF_SVES_UNEARNED_INCOME_FREQUENCY> REF_SVES_UNEARNED_INCOME_FREQUENCY { get; set; }
        public virtual DbSet<REF_SVES_UNEARNED_INCOME_TYPE> REF_SVES_UNEARNED_INCOME_TYPE { get; set; }
        public virtual DbSet<REF_TBQ_BENEFICIARY_PART_B_THIRD_PARTY_PREMIUM_PAYER> REF_TBQ_BENEFICIARY_PART_B_THIRD_PARTY_PREMIUM_PAYER { get; set; }
        public virtual DbSet<REF_TBQ_ENROLLMENT_STATUS> REF_TBQ_ENROLLMENT_STATUS { get; set; }
        public virtual DbSet<REF_TBQ_THIRD_PARTY_BUY_IN_ELIGIBILITY> REF_TBQ_THIRD_PARTY_BUY_IN_ELIGIBILITY { get; set; }
        public virtual DbSet<SVES_REQUEST> SVES_REQUEST { get; set; }
        public virtual DbSet<REF_TBQ_BENEFICIARY_PART_A_ENROLLMENT_REASON> REF_TBQ_BENEFICIARY_PART_A_ENROLLMENT_REASON { get; set; }
        public virtual DbSet<REF_TBQ_BENEFICIARY_PART_B_ENROLLMENT_REASON> REF_TBQ_BENEFICIARY_PART_B_ENROLLMENT_REASON { get; set; }
        public virtual DbSet<REF_TBQ_MBI_EFFECTIVE_REASON> REF_TBQ_MBI_EFFECTIVE_REASON { get; set; }
        public virtual DbSet<REF_TBQ_MBI_END_REASON> REF_TBQ_MBI_END_REASON { get; set; }
        public virtual DbSet<REF_TBQ_PLAN_BENEFITS_PACKAGE_COVERAGE_TYPE> REF_TBQ_PLAN_BENEFITS_PACKAGE_COVERAGE_TYPE { get; set; }
        public virtual DbSet<REF_SVES_XREF_CODE> REF_SVES_XREF_CODE { get; set; }
        public virtual DbSet<REF_STATE_REASON_CODE> REF_STATE_REASON_CODE { get; set; }
        public virtual DbSet<REF_IMMIGRATION_STATUS> REF_IMMIGRATION_STATUS { get; set; }
        public virtual DbSet<REF_RMA_APPLICANT_CATEGORY> REF_RMA_APPLICANT_CATEGORY { get; set; }
        public virtual DbSet<APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL> APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_INCOME_DETAIL> APPLICATION_INCOME_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_MEDICAL_INSURANCE_DETAIL> APPLICATION_MEDICAL_INSURANCE_DETAIL { get; set; }
        public virtual DbSet<REF_TBQ_BENE_ESRD_TERMINATION_REASON> REF_TBQ_BENE_ESRD_TERMINATION_REASON { get; set; }
        public virtual DbSet<REF_TBQ_PART_A_NON_ENTITLEMENT_STATUS> REF_TBQ_PART_A_NON_ENTITLEMENT_STATUS { get; set; }
        public virtual DbSet<REF_TBQ_PART_B_NON_ENTITLEMENT_STATUS> REF_TBQ_PART_B_NON_ENTITLEMENT_STATUS { get; set; }
        public virtual DbSet<REF_ELDERLY_DISABLED_FORM_NAME> REF_ELDERLY_DISABLED_FORM_NAME { get; set; }
        public virtual DbSet<REF_ELDERLY_DISABLED_FORM_TYPE> REF_ELDERLY_DISABLED_FORM_TYPE { get; set; }
        public virtual DbSet<t_PERSON_SUSPENSION> t_PERSON_SUSPENSION { get; set; }
        public virtual DbSet<APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL> APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PROPERTY_MOBILE_HOME> APPLICATION_PROPERTY_MOBILE_HOME { get; set; }
        public virtual DbSet<REF_EXCLUDED_RESOURCE> REF_EXCLUDED_RESOURCE { get; set; }
        public virtual DbSet<APPLICATION_PROPERTY_PARCEL> APPLICATION_PROPERTY_PARCEL { get; set; }
        public virtual DbSet<REF_TAX_ASSESSOR_OFFICE> REF_TAX_ASSESSOR_OFFICE { get; set; }
        public virtual DbSet<APPLICATION_RESOURCE_TRANSFER> APPLICATION_RESOURCE_TRANSFER { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE> APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE { get; set; }
        public virtual DbSet<REF_EXPEDITE_FACILITY_PROVIDER> REF_EXPEDITE_FACILITY_PROVIDER { get; set; }
        public virtual DbSet<REF_NH_PROVIDER> REF_NH_PROVIDER { get; set; }
        public virtual DbSet<REF_NH_PROVIDER_TYPE> REF_NH_PROVIDER_TYPE { get; set; }
        public virtual DbSet<WORKER_REMINDER> WORKER_REMINDER { get; set; }
        public virtual DbSet<REF_WORKER_REMINDER_TYPE> REF_WORKER_REMINDER_TYPE { get; set; }
        public virtual DbSet<PERSON_LIABILITY_CHANGE_CODE> PERSON_LIABILITY_CHANGE_CODE { get; set; }
        public virtual DbSet<vw_WORKER_REMINDER> vw_WORKER_REMINDER { get; set; }
        public virtual DbSet<PERSON_LIABILITY> PERSON_LIABILITY { get; set; }
        public virtual DbSet<REF_IH_ROLE> REF_IH_ROLE { get; set; }
        public virtual DbSet<APPLICATION_ELIGIBILITY_DENIAL> APPLICATION_ELIGIBILITY_DENIAL { get; set; }
        public virtual DbSet<REF_LIABILITY_CHANGE_CODE> REF_LIABILITY_CHANGE_CODE { get; set; }
        public virtual DbSet<REF_BANK> REF_BANK { get; set; }
        public virtual DbSet<APPLICATION_RESOURCE_BANK> APPLICATION_RESOURCE_BANK { get; set; }
        public virtual DbSet<APPLICATION_PREVIOUS_ENROLLMENT> APPLICATION_PREVIOUS_ENROLLMENT { get; set; }
        public virtual DbSet<vw_ELIGIBILITY_ENROLLMENT> vw_ELIGIBILITY_ENROLLMENT { get; set; }
        public virtual DbSet<APPLICATION_ELIGIBILITY> APPLICATION_ELIGIBILITY { get; set; }
        public virtual DbSet<APPLICATION_SNAPSHOT> APPLICATION_SNAPSHOT { get; set; }
        public virtual DbSet<REF_OVERRIDE_REASON> REF_OVERRIDE_REASON { get; set; }
        public virtual DbSet<APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION> APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION { get; set; }
        public virtual DbSet<APPLICATION_ELDERLY_DISABLED_DETAIL> APPLICATION_ELDERLY_DISABLED_DETAIL { get; set; }
        public virtual DbSet<vw_APPLICATION_HISTORY_AMAES> vw_APPLICATION_HISTORY_AMAES { get; set; }
        public virtual DbSet<REF_IH_SITE> REF_IH_SITE { get; set; }
        public virtual DbSet<FH_MONTHLY_INCOME_INFORMATION> FH_MONTHLY_INCOME_INFORMATION { get; set; }
        public virtual DbSet<COLA_FACTSHEET_YEAR> COLA_FACTSHEET_YEAR { get; set; }
        public virtual DbSet<REF_COLA_FACTSHEET_CATEGORY> REF_COLA_FACTSHEET_CATEGORY { get; set; }
        public virtual DbSet<COLA_FACTSHEET_YEAR_VALUE> COLA_FACTSHEET_YEAR_VALUE { get; set; }
        public virtual DbSet<REF_COLA_FACTSHEET_SUB_CATEGORY> REF_COLA_FACTSHEET_SUB_CATEGORY { get; set; }
        public virtual DbSet<vw_APPLICATION_DETAILS_QIT_AND_LIEN> vw_APPLICATION_DETAILS_QIT_AND_LIEN { get; set; }
        public virtual DbSet<APPLICATION_DETAIL_JIY> APPLICATION_DETAIL_JIY { get; set; }
        public virtual DbSet<APPLICATION_RESOURCE_BANK_DETAIL> APPLICATION_RESOURCE_BANK_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PROPERTY_PARCEL_DETAIL> APPLICATION_PROPERTY_PARCEL_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PROPERTY_MOBILE_HOME_DETAIL> APPLICATION_PROPERTY_MOBILE_HOME_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_NEEDS_ALLOWANCE> APPLICATION_PERSONAL_NEEDS_ALLOWANCE { get; set; }
        public virtual DbSet<APPLICATION_NON_MAGI_INCOME_DETAIL> APPLICATION_NON_MAGI_INCOME_DETAIL { get; set; }
        public virtual DbSet<REF_DYSLOCATIONS> REF_DYSLOCATIONS { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_PROPERTY_AUTO_DETAIL> APPLICATION_PERSONAL_PROPERTY_AUTO_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_FAMILY_ALLOCATION_DETAIL> APPLICATION_FAMILY_ALLOCATION_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_SPOUSE_ALLOCATION_DETAIL> APPLICATION_SPOUSE_ALLOCATION_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_PROPERTY_AUTO> APPLICATION_PERSONAL_PROPERTY_AUTO { get; set; }
        public virtual DbSet<APPLICATION_RESOURCE_MONTH_DETAIL> APPLICATION_RESOURCE_MONTH_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PROPERTY_PREVIOUS_DETAIL> APPLICATION_PROPERTY_PREVIOUS_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL> APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_MEDICAL_INSURANCE_MONTH_DETAIL> APPLICATION_MEDICAL_INSURANCE_MONTH_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE_DETAIL> APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PERSONAL_PROPERTY_MACHINE_DETAIL> APPLICATION_PERSONAL_PROPERTY_MACHINE_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_LIFE_INSURANCE_ADDITIONAL_DETAIL> APPLICATION_LIFE_INSURANCE_ADDITIONAL_DETAIL { get; set; }
        public virtual DbSet<APPLICATION_PROPERTY_PREVIOUS> APPLICATION_PROPERTY_PREVIOUS { get; set; }
        public virtual DbSet<APPLICATION_MEDICAL_INSURANCE_LTC_BUDGET> APPLICATION_MEDICAL_INSURANCE_LTC_BUDGET { get; set; }
        public virtual DbSet<APPLICATION_MEDICAL_INSURANCE_PART_D_BUDGET> APPLICATION_MEDICAL_INSURANCE_PART_D_BUDGET { get; set; }
        public virtual DbSet<APPLICATION_DETAIL_PEP> APPLICATION_DETAIL_PEP { get; set; }
        public virtual DbSet<PRESUMPTIVE_DETERMINER> PRESUMPTIVE_DETERMINER { get; set; }
        public virtual DbSet<PROVIDER_REPRESENTATIVE> PROVIDER_REPRESENTATIVE { get; set; }
        public virtual DbSet<PRESUMPTIVE_PROVIDER> PRESUMPTIVE_PROVIDER { get; set; }
        public virtual DbSet<APPLICATION_ADDITIONAL_BURIAL_FUNDS_BUDGET> APPLICATION_ADDITIONAL_BURIAL_FUNDS_BUDGET { get; set; }
        public virtual DbSet<REF_PREPAID_BURIAL_CONTRACT> REF_PREPAID_BURIAL_CONTRACT { get; set; }
        public virtual DbSet<APPLICATION_OTHER_BURIAL_FUNDS_BUDGET> APPLICATION_OTHER_BURIAL_FUNDS_BUDGET { get; set; }
        public virtual DbSet<REF_PROVIDER_DETERMINER_STATUS> REF_PROVIDER_DETERMINER_STATUS { get; set; }
        public virtual DbSet<APPLICATION_PREPAID_BURIAL_SPACE_DETAIL> APPLICATION_PREPAID_BURIAL_SPACE_DETAIL { get; set; }
        public virtual DbSet<PERSON_LIABILITY_TEST> PERSON_LIABILITY_TEST { get; set; }
        public virtual DbSet<REF_LIABILITY_TEST_TYPE> REF_LIABILITY_TEST_TYPE { get; set; }
        public virtual DbSet<REF_FAMILY_NEEDS_ALLOWANCE> REF_FAMILY_NEEDS_ALLOWANCE { get; set; }
    
        public virtual int usp_DELETE_PERSON_RIDP(Nullable<long> rIDP_PERSON_ID, string updatedBy)
        {
            var rIDP_PERSON_IDParameter = rIDP_PERSON_ID.HasValue ?
                new ObjectParameter("RIDP_PERSON_ID", rIDP_PERSON_ID) :
                new ObjectParameter("RIDP_PERSON_ID", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_PERSON_RIDP", rIDP_PERSON_IDParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_RIDP_Result> usp_SELECT_PERSON_RIDP(Nullable<long> rIDP_PERSON_ID, Nullable<long> pERSON_ID, Nullable<long> aCCOUNT_ID)
        {
            var rIDP_PERSON_IDParameter = rIDP_PERSON_ID.HasValue ?
                new ObjectParameter("RIDP_PERSON_ID", rIDP_PERSON_ID) :
                new ObjectParameter("RIDP_PERSON_ID", typeof(long));
    
            var pERSON_IDParameter = pERSON_ID.HasValue ?
                new ObjectParameter("PERSON_ID", pERSON_ID) :
                new ObjectParameter("PERSON_ID", typeof(long));
    
            var aCCOUNT_IDParameter = aCCOUNT_ID.HasValue ?
                new ObjectParameter("ACCOUNT_ID", aCCOUNT_ID) :
                new ObjectParameter("ACCOUNT_ID", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_RIDP_Result>("usp_SELECT_PERSON_RIDP", rIDP_PERSON_IDParameter, pERSON_IDParameter, aCCOUNT_IDParameter);
        }
    
        public virtual int usp_UPSERT_PERSON_RIDP(Nullable<long> personRIDPId, Nullable<long> personId, Nullable<long> accountId, string statusCode, Nullable<System.DateTime> startDate, Nullable<System.DateTime> completeDate, string referenceNumber, Nullable<System.DateTime> referenceNumberExpiration, string responseCode, string responseCodeDescription, string upsertedBy, ObjectParameter personRIDPIdReturn)
        {
            var personRIDPIdParameter = personRIDPId.HasValue ?
                new ObjectParameter("PersonRIDPId", personRIDPId) :
                new ObjectParameter("PersonRIDPId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var accountIdParameter = accountId.HasValue ?
                new ObjectParameter("AccountId", accountId) :
                new ObjectParameter("AccountId", typeof(long));
    
            var statusCodeParameter = statusCode != null ?
                new ObjectParameter("StatusCode", statusCode) :
                new ObjectParameter("StatusCode", typeof(string));
    
            var startDateParameter = startDate.HasValue ?
                new ObjectParameter("StartDate", startDate) :
                new ObjectParameter("StartDate", typeof(System.DateTime));
    
            var completeDateParameter = completeDate.HasValue ?
                new ObjectParameter("CompleteDate", completeDate) :
                new ObjectParameter("CompleteDate", typeof(System.DateTime));
    
            var referenceNumberParameter = referenceNumber != null ?
                new ObjectParameter("ReferenceNumber", referenceNumber) :
                new ObjectParameter("ReferenceNumber", typeof(string));
    
            var referenceNumberExpirationParameter = referenceNumberExpiration.HasValue ?
                new ObjectParameter("ReferenceNumberExpiration", referenceNumberExpiration) :
                new ObjectParameter("ReferenceNumberExpiration", typeof(System.DateTime));
    
            var responseCodeParameter = responseCode != null ?
                new ObjectParameter("ResponseCode", responseCode) :
                new ObjectParameter("ResponseCode", typeof(string));
    
            var responseCodeDescriptionParameter = responseCodeDescription != null ?
                new ObjectParameter("ResponseCodeDescription", responseCodeDescription) :
                new ObjectParameter("ResponseCodeDescription", typeof(string));
    
            var upsertedByParameter = upsertedBy != null ?
                new ObjectParameter("UpsertedBy", upsertedBy) :
                new ObjectParameter("UpsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_PERSON_RIDP", personRIDPIdParameter, personIdParameter, accountIdParameter, statusCodeParameter, startDateParameter, completeDateParameter, referenceNumberParameter, referenceNumberExpirationParameter, responseCodeParameter, responseCodeDescriptionParameter, upsertedByParameter, personRIDPIdReturn);
        }
    
        public virtual int usp_DELETE_FH_RIDP_FIRST_REQUEST(Nullable<long> rIDPId, string updatedBy)
        {
            var rIDPIdParameter = rIDPId.HasValue ?
                new ObjectParameter("RIDPId", rIDPId) :
                new ObjectParameter("RIDPId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_FH_RIDP_FIRST_REQUEST", rIDPIdParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_FH_RIDP_FIRST_RESPONSE(Nullable<long> rIDPId, string updatedBy)
        {
            var rIDPIdParameter = rIDPId.HasValue ?
                new ObjectParameter("RIDPId", rIDPId) :
                new ObjectParameter("RIDPId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_FH_RIDP_FIRST_RESPONSE", rIDPIdParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_FH_RIDP_FRAUD_REQUEST(Nullable<long> fraudRequestId, string updatedBy)
        {
            var fraudRequestIdParameter = fraudRequestId.HasValue ?
                new ObjectParameter("FraudRequestId", fraudRequestId) :
                new ObjectParameter("FraudRequestId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_FH_RIDP_FRAUD_REQUEST", fraudRequestIdParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_FH_RIDP_FRAUD_RESPONSE(Nullable<long> fraudResponseId, string updatedBy)
        {
            var fraudResponseIdParameter = fraudResponseId.HasValue ?
                new ObjectParameter("FraudResponseId", fraudResponseId) :
                new ObjectParameter("FraudResponseId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_FH_RIDP_FRAUD_RESPONSE", fraudResponseIdParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_FH_RIDP_SECOND_REQUEST(Nullable<long> requestId, string updatedBy)
        {
            var requestIdParameter = requestId.HasValue ?
                new ObjectParameter("RequestId", requestId) :
                new ObjectParameter("RequestId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_FH_RIDP_SECOND_REQUEST", requestIdParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_FH_RIDP_SECOND_RESPONSE(Nullable<long> responseId, string updatedBy)
        {
            var responseIdParameter = responseId.HasValue ?
                new ObjectParameter("ResponseId", responseId) :
                new ObjectParameter("ResponseId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_FH_RIDP_SECOND_RESPONSE", responseIdParameter, updatedByParameter);
        }
    
        public virtual int usp_INSERT_FH_RIDP_FIRST_RESPONSE(Nullable<long> requestRIDPId, string sessionId, string questionOne, string questionTwo, string questionThree, string questionFour, string questionFive, string responseCode, string responseDesc, string referenceNo, string finalDecisionCode, string insertedBy, ObjectParameter responseRIDPIdReturn)
        {
            var requestRIDPIdParameter = requestRIDPId.HasValue ?
                new ObjectParameter("RequestRIDPId", requestRIDPId) :
                new ObjectParameter("RequestRIDPId", typeof(long));
    
            var sessionIdParameter = sessionId != null ?
                new ObjectParameter("SessionId", sessionId) :
                new ObjectParameter("SessionId", typeof(string));
    
            var questionOneParameter = questionOne != null ?
                new ObjectParameter("QuestionOne", questionOne) :
                new ObjectParameter("QuestionOne", typeof(string));
    
            var questionTwoParameter = questionTwo != null ?
                new ObjectParameter("QuestionTwo", questionTwo) :
                new ObjectParameter("QuestionTwo", typeof(string));
    
            var questionThreeParameter = questionThree != null ?
                new ObjectParameter("QuestionThree", questionThree) :
                new ObjectParameter("QuestionThree", typeof(string));
    
            var questionFourParameter = questionFour != null ?
                new ObjectParameter("QuestionFour", questionFour) :
                new ObjectParameter("QuestionFour", typeof(string));
    
            var questionFiveParameter = questionFive != null ?
                new ObjectParameter("QuestionFive", questionFive) :
                new ObjectParameter("QuestionFive", typeof(string));
    
            var responseCodeParameter = responseCode != null ?
                new ObjectParameter("ResponseCode", responseCode) :
                new ObjectParameter("ResponseCode", typeof(string));
    
            var responseDescParameter = responseDesc != null ?
                new ObjectParameter("ResponseDesc", responseDesc) :
                new ObjectParameter("ResponseDesc", typeof(string));
    
            var referenceNoParameter = referenceNo != null ?
                new ObjectParameter("ReferenceNo", referenceNo) :
                new ObjectParameter("ReferenceNo", typeof(string));
    
            var finalDecisionCodeParameter = finalDecisionCode != null ?
                new ObjectParameter("FinalDecisionCode", finalDecisionCode) :
                new ObjectParameter("FinalDecisionCode", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_FH_RIDP_FIRST_RESPONSE", requestRIDPIdParameter, sessionIdParameter, questionOneParameter, questionTwoParameter, questionThreeParameter, questionFourParameter, questionFiveParameter, responseCodeParameter, responseDescParameter, referenceNoParameter, finalDecisionCodeParameter, insertedByParameter, responseRIDPIdReturn);
        }
    
        public virtual int usp_INSERT_FH_RIDP_FRAUD_RESPONSE(Nullable<long> fraudRequestId, string responseCode, string responseDesc, string insertedBy, string finalDecisionCode, string personGivenName, string personMiddleName, string personSurName, string streetName, string cityName, string locStateUspsCode, string locPostalCode, string locPostalCodeExtension, string tDSDecisionCode, ObjectParameter responseId)
        {
            var fraudRequestIdParameter = fraudRequestId.HasValue ?
                new ObjectParameter("FraudRequestId", fraudRequestId) :
                new ObjectParameter("FraudRequestId", typeof(long));
    
            var responseCodeParameter = responseCode != null ?
                new ObjectParameter("ResponseCode", responseCode) :
                new ObjectParameter("ResponseCode", typeof(string));
    
            var responseDescParameter = responseDesc != null ?
                new ObjectParameter("ResponseDesc", responseDesc) :
                new ObjectParameter("ResponseDesc", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            var finalDecisionCodeParameter = finalDecisionCode != null ?
                new ObjectParameter("FinalDecisionCode", finalDecisionCode) :
                new ObjectParameter("FinalDecisionCode", typeof(string));
    
            var personGivenNameParameter = personGivenName != null ?
                new ObjectParameter("PersonGivenName", personGivenName) :
                new ObjectParameter("PersonGivenName", typeof(string));
    
            var personMiddleNameParameter = personMiddleName != null ?
                new ObjectParameter("PersonMiddleName", personMiddleName) :
                new ObjectParameter("PersonMiddleName", typeof(string));
    
            var personSurNameParameter = personSurName != null ?
                new ObjectParameter("PersonSurName", personSurName) :
                new ObjectParameter("PersonSurName", typeof(string));
    
            var streetNameParameter = streetName != null ?
                new ObjectParameter("StreetName", streetName) :
                new ObjectParameter("StreetName", typeof(string));
    
            var cityNameParameter = cityName != null ?
                new ObjectParameter("CityName", cityName) :
                new ObjectParameter("CityName", typeof(string));
    
            var locStateUspsCodeParameter = locStateUspsCode != null ?
                new ObjectParameter("LocStateUspsCode", locStateUspsCode) :
                new ObjectParameter("LocStateUspsCode", typeof(string));
    
            var locPostalCodeParameter = locPostalCode != null ?
                new ObjectParameter("LocPostalCode", locPostalCode) :
                new ObjectParameter("LocPostalCode", typeof(string));
    
            var locPostalCodeExtensionParameter = locPostalCodeExtension != null ?
                new ObjectParameter("LocPostalCodeExtension", locPostalCodeExtension) :
                new ObjectParameter("LocPostalCodeExtension", typeof(string));
    
            var tDSDecisionCodeParameter = tDSDecisionCode != null ?
                new ObjectParameter("TDSDecisionCode", tDSDecisionCode) :
                new ObjectParameter("TDSDecisionCode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_FH_RIDP_FRAUD_RESPONSE", fraudRequestIdParameter, responseCodeParameter, responseDescParameter, insertedByParameter, finalDecisionCodeParameter, personGivenNameParameter, personMiddleNameParameter, personSurNameParameter, streetNameParameter, cityNameParameter, locStateUspsCodeParameter, locPostalCodeParameter, locPostalCodeExtensionParameter, tDSDecisionCodeParameter, responseId);
        }
    
        public virtual int usp_INSERT_FH_RIDP_SECOND_RESPONSE(Nullable<long> secondRequestId, string sessionId, string responseCode, string responseDescription, string finalDecisionCode, string insertedBy, string tDSDescription, string referenceNumber, ObjectParameter responseId)
        {
            var secondRequestIdParameter = secondRequestId.HasValue ?
                new ObjectParameter("SecondRequestId", secondRequestId) :
                new ObjectParameter("SecondRequestId", typeof(long));
    
            var sessionIdParameter = sessionId != null ?
                new ObjectParameter("SessionId", sessionId) :
                new ObjectParameter("SessionId", typeof(string));
    
            var responseCodeParameter = responseCode != null ?
                new ObjectParameter("ResponseCode", responseCode) :
                new ObjectParameter("ResponseCode", typeof(string));
    
            var responseDescriptionParameter = responseDescription != null ?
                new ObjectParameter("ResponseDescription", responseDescription) :
                new ObjectParameter("ResponseDescription", typeof(string));
    
            var finalDecisionCodeParameter = finalDecisionCode != null ?
                new ObjectParameter("FinalDecisionCode", finalDecisionCode) :
                new ObjectParameter("FinalDecisionCode", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            var tDSDescriptionParameter = tDSDescription != null ?
                new ObjectParameter("TDSDescription", tDSDescription) :
                new ObjectParameter("TDSDescription", typeof(string));
    
            var referenceNumberParameter = referenceNumber != null ?
                new ObjectParameter("ReferenceNumber", referenceNumber) :
                new ObjectParameter("ReferenceNumber", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_FH_RIDP_SECOND_RESPONSE", secondRequestIdParameter, sessionIdParameter, responseCodeParameter, responseDescriptionParameter, finalDecisionCodeParameter, insertedByParameter, tDSDescriptionParameter, referenceNumberParameter, responseId);
        }
    
        public virtual ObjectResult<usp_SELECT_FH_RIDP_FIRST_REQUEST_Result> usp_SELECT_FH_RIDP_FIRST_REQUEST(Nullable<long> firstRequestRIDPId, Nullable<long> personRIDPId)
        {
            var firstRequestRIDPIdParameter = firstRequestRIDPId.HasValue ?
                new ObjectParameter("FirstRequestRIDPId", firstRequestRIDPId) :
                new ObjectParameter("FirstRequestRIDPId", typeof(long));
    
            var personRIDPIdParameter = personRIDPId.HasValue ?
                new ObjectParameter("PersonRIDPId", personRIDPId) :
                new ObjectParameter("PersonRIDPId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_FH_RIDP_FIRST_REQUEST_Result>("usp_SELECT_FH_RIDP_FIRST_REQUEST", firstRequestRIDPIdParameter, personRIDPIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_FH_RIDP_FIRST_RESPONSE_Result> usp_SELECT_FH_RIDP_FIRST_RESPONSE(Nullable<long> firstResponseRIDPId, Nullable<long> firstRequestRIDPId)
        {
            var firstResponseRIDPIdParameter = firstResponseRIDPId.HasValue ?
                new ObjectParameter("FirstResponseRIDPId", firstResponseRIDPId) :
                new ObjectParameter("FirstResponseRIDPId", typeof(long));
    
            var firstRequestRIDPIdParameter = firstRequestRIDPId.HasValue ?
                new ObjectParameter("FirstRequestRIDPId", firstRequestRIDPId) :
                new ObjectParameter("FirstRequestRIDPId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_FH_RIDP_FIRST_RESPONSE_Result>("usp_SELECT_FH_RIDP_FIRST_RESPONSE", firstResponseRIDPIdParameter, firstRequestRIDPIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_FH_RIDP_FRAUD_REQUEST_Result> usp_SELECT_FH_RIDP_FRAUD_REQUEST(Nullable<long> fraudRequestId, Nullable<long> firstResponseId, Nullable<long> secondResponseId)
        {
            var fraudRequestIdParameter = fraudRequestId.HasValue ?
                new ObjectParameter("FraudRequestId", fraudRequestId) :
                new ObjectParameter("FraudRequestId", typeof(long));
    
            var firstResponseIdParameter = firstResponseId.HasValue ?
                new ObjectParameter("FirstResponseId", firstResponseId) :
                new ObjectParameter("FirstResponseId", typeof(long));
    
            var secondResponseIdParameter = secondResponseId.HasValue ?
                new ObjectParameter("SecondResponseId", secondResponseId) :
                new ObjectParameter("SecondResponseId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_FH_RIDP_FRAUD_REQUEST_Result>("usp_SELECT_FH_RIDP_FRAUD_REQUEST", fraudRequestIdParameter, firstResponseIdParameter, secondResponseIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_FH_RIDP_FRAUD_RESPONSE_Result> usp_SELECT_FH_RIDP_FRAUD_RESPONSE(Nullable<long> fraudResponseId, Nullable<long> fraudRequestId)
        {
            var fraudResponseIdParameter = fraudResponseId.HasValue ?
                new ObjectParameter("FraudResponseId", fraudResponseId) :
                new ObjectParameter("FraudResponseId", typeof(long));
    
            var fraudRequestIdParameter = fraudRequestId.HasValue ?
                new ObjectParameter("FraudRequestId", fraudRequestId) :
                new ObjectParameter("FraudRequestId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_FH_RIDP_FRAUD_RESPONSE_Result>("usp_SELECT_FH_RIDP_FRAUD_RESPONSE", fraudResponseIdParameter, fraudRequestIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_FH_RIDP_SECOND_REQUEST_Result> usp_SELECT_FH_RIDP_SECOND_REQUEST(Nullable<long> secondRequestId, Nullable<long> firstResponseId)
        {
            var secondRequestIdParameter = secondRequestId.HasValue ?
                new ObjectParameter("SecondRequestId", secondRequestId) :
                new ObjectParameter("SecondRequestId", typeof(long));
    
            var firstResponseIdParameter = firstResponseId.HasValue ?
                new ObjectParameter("FirstResponseId", firstResponseId) :
                new ObjectParameter("FirstResponseId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_FH_RIDP_SECOND_REQUEST_Result>("usp_SELECT_FH_RIDP_SECOND_REQUEST", secondRequestIdParameter, firstResponseIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_FH_RIDP_SECOND_RESPONSE_Result> usp_SELECT_FH_RIDP_SECOND_RESPONSE(Nullable<long> secondResponseId, Nullable<long> secondRequestId)
        {
            var secondResponseIdParameter = secondResponseId.HasValue ?
                new ObjectParameter("SecondResponseId", secondResponseId) :
                new ObjectParameter("SecondResponseId", typeof(long));
    
            var secondRequestIdParameter = secondRequestId.HasValue ?
                new ObjectParameter("SecondRequestId", secondRequestId) :
                new ObjectParameter("SecondRequestId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_FH_RIDP_SECOND_RESPONSE_Result>("usp_SELECT_FH_RIDP_SECOND_RESPONSE", secondResponseIdParameter, secondRequestIdParameter);
        }
    
        public virtual int usp_UPSERT_FH_RIDP_FIRST_REQUEST(Nullable<long> rIDPId, Nullable<long> personRIDPId, string firstName, string lastName, string streetName, string cityName, string stateAbrev, string zipCode, Nullable<System.DateTime> dateOfBirth, string sSN, string middleName, string suffix, string zipCodePlusFour, string phoneNumber, string upsertedBy, ObjectParameter rIDPIdReturn)
        {
            var rIDPIdParameter = rIDPId.HasValue ?
                new ObjectParameter("RIDPId", rIDPId) :
                new ObjectParameter("RIDPId", typeof(long));
    
            var personRIDPIdParameter = personRIDPId.HasValue ?
                new ObjectParameter("PersonRIDPId", personRIDPId) :
                new ObjectParameter("PersonRIDPId", typeof(long));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var streetNameParameter = streetName != null ?
                new ObjectParameter("StreetName", streetName) :
                new ObjectParameter("StreetName", typeof(string));
    
            var cityNameParameter = cityName != null ?
                new ObjectParameter("CityName", cityName) :
                new ObjectParameter("CityName", typeof(string));
    
            var stateAbrevParameter = stateAbrev != null ?
                new ObjectParameter("StateAbrev", stateAbrev) :
                new ObjectParameter("StateAbrev", typeof(string));
    
            var zipCodeParameter = zipCode != null ?
                new ObjectParameter("ZipCode", zipCode) :
                new ObjectParameter("ZipCode", typeof(string));
    
            var dateOfBirthParameter = dateOfBirth.HasValue ?
                new ObjectParameter("DateOfBirth", dateOfBirth) :
                new ObjectParameter("DateOfBirth", typeof(System.DateTime));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var middleNameParameter = middleName != null ?
                new ObjectParameter("MiddleName", middleName) :
                new ObjectParameter("MiddleName", typeof(string));
    
            var suffixParameter = suffix != null ?
                new ObjectParameter("Suffix", suffix) :
                new ObjectParameter("Suffix", typeof(string));
    
            var zipCodePlusFourParameter = zipCodePlusFour != null ?
                new ObjectParameter("ZipCodePlusFour", zipCodePlusFour) :
                new ObjectParameter("ZipCodePlusFour", typeof(string));
    
            var phoneNumberParameter = phoneNumber != null ?
                new ObjectParameter("PhoneNumber", phoneNumber) :
                new ObjectParameter("PhoneNumber", typeof(string));
    
            var upsertedByParameter = upsertedBy != null ?
                new ObjectParameter("UpsertedBy", upsertedBy) :
                new ObjectParameter("UpsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_FH_RIDP_FIRST_REQUEST", rIDPIdParameter, personRIDPIdParameter, firstNameParameter, lastNameParameter, streetNameParameter, cityNameParameter, stateAbrevParameter, zipCodeParameter, dateOfBirthParameter, sSNParameter, middleNameParameter, suffixParameter, zipCodePlusFourParameter, phoneNumberParameter, upsertedByParameter, rIDPIdReturn);
        }
    
        public virtual int usp_UPSERT_FH_RIDP_FRAUD_REQUEST(string referenceNumber, Nullable<long> firstResponseId, Nullable<long> secondResponseId, Nullable<long> fraudRequestId, string subscriberNumber, string upsertedBy, ObjectParameter fraudIdReturn)
        {
            var referenceNumberParameter = referenceNumber != null ?
                new ObjectParameter("ReferenceNumber", referenceNumber) :
                new ObjectParameter("ReferenceNumber", typeof(string));
    
            var firstResponseIdParameter = firstResponseId.HasValue ?
                new ObjectParameter("FirstResponseId", firstResponseId) :
                new ObjectParameter("FirstResponseId", typeof(long));
    
            var secondResponseIdParameter = secondResponseId.HasValue ?
                new ObjectParameter("SecondResponseId", secondResponseId) :
                new ObjectParameter("SecondResponseId", typeof(long));
    
            var fraudRequestIdParameter = fraudRequestId.HasValue ?
                new ObjectParameter("FraudRequestId", fraudRequestId) :
                new ObjectParameter("FraudRequestId", typeof(long));
    
            var subscriberNumberParameter = subscriberNumber != null ?
                new ObjectParameter("SubscriberNumber", subscriberNumber) :
                new ObjectParameter("SubscriberNumber", typeof(string));
    
            var upsertedByParameter = upsertedBy != null ?
                new ObjectParameter("UpsertedBy", upsertedBy) :
                new ObjectParameter("UpsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_FH_RIDP_FRAUD_REQUEST", referenceNumberParameter, firstResponseIdParameter, secondResponseIdParameter, fraudRequestIdParameter, subscriberNumberParameter, upsertedByParameter, fraudIdReturn);
        }
    
        public virtual int usp_UPSERT_FH_RIDP_SECOND_REQUEST(string sessionId, string questionOneOptionId, string questionTwoOptionId, string questionThreeOptionId, string questionFourOptionId, string questionFiveOptionId, Nullable<long> secondRequestId, Nullable<long> firstResponseId, string upsertedBy, ObjectParameter rIDPIdReturn)
        {
            var sessionIdParameter = sessionId != null ?
                new ObjectParameter("SessionId", sessionId) :
                new ObjectParameter("SessionId", typeof(string));
    
            var questionOneOptionIdParameter = questionOneOptionId != null ?
                new ObjectParameter("QuestionOneOptionId", questionOneOptionId) :
                new ObjectParameter("QuestionOneOptionId", typeof(string));
    
            var questionTwoOptionIdParameter = questionTwoOptionId != null ?
                new ObjectParameter("QuestionTwoOptionId", questionTwoOptionId) :
                new ObjectParameter("QuestionTwoOptionId", typeof(string));
    
            var questionThreeOptionIdParameter = questionThreeOptionId != null ?
                new ObjectParameter("QuestionThreeOptionId", questionThreeOptionId) :
                new ObjectParameter("QuestionThreeOptionId", typeof(string));
    
            var questionFourOptionIdParameter = questionFourOptionId != null ?
                new ObjectParameter("QuestionFourOptionId", questionFourOptionId) :
                new ObjectParameter("QuestionFourOptionId", typeof(string));
    
            var questionFiveOptionIdParameter = questionFiveOptionId != null ?
                new ObjectParameter("QuestionFiveOptionId", questionFiveOptionId) :
                new ObjectParameter("QuestionFiveOptionId", typeof(string));
    
            var secondRequestIdParameter = secondRequestId.HasValue ?
                new ObjectParameter("SecondRequestId", secondRequestId) :
                new ObjectParameter("SecondRequestId", typeof(long));
    
            var firstResponseIdParameter = firstResponseId.HasValue ?
                new ObjectParameter("FirstResponseId", firstResponseId) :
                new ObjectParameter("FirstResponseId", typeof(long));
    
            var upsertedByParameter = upsertedBy != null ?
                new ObjectParameter("UpsertedBy", upsertedBy) :
                new ObjectParameter("UpsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_FH_RIDP_SECOND_REQUEST", sessionIdParameter, questionOneOptionIdParameter, questionTwoOptionIdParameter, questionThreeOptionIdParameter, questionFourOptionIdParameter, questionFiveOptionIdParameter, secondRequestIdParameter, firstResponseIdParameter, upsertedByParameter, rIDPIdReturn);
        }
    
        public virtual ObjectResult<usp_Select_ELE_Errors_Result> usp_Select_ELE_Errors(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_ELE_Errors_Result>("usp_Select_ELE_Errors", cancelDateParameter);
        }
    
        public virtual ObjectResult<string> usp_Select_ELE_Indicator(Nullable<long> applicationId, Nullable<long> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_Select_ELE_Indicator", applicationIdParameter, personIdParameter);
        }
    
        public virtual int usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_WITH_NEW_APPLICATION_ID(Nullable<long> applicationId, Nullable<long> newApplicationId, Nullable<System.DateTime> cancelDate)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var newApplicationIdParameter = newApplicationId.HasValue ?
                new ObjectParameter("NewApplicationId", newApplicationId) :
                new ObjectParameter("NewApplicationId", typeof(long));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_WITH_NEW_APPLICATION_ID", applicationIdParameter, newApplicationIdParameter, cancelDateParameter);
        }
    
        public virtual ObjectResult<usp_Select_From_Application_Result> usp_Select_From_Application(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_From_Application_Result>("usp_Select_From_Application", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_CHIP_Specific_Result> usp_Select_Application_CHIP_Specific(Nullable<long> applicationId, Nullable<long> appCHIPSpecificId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appCHIPSpecificIdParameter = appCHIPSpecificId.HasValue ?
                new ObjectParameter("AppCHIPSpecificId", appCHIPSpecificId) :
                new ObjectParameter("AppCHIPSpecificId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_CHIP_Specific_Result>("usp_Select_Application_CHIP_Specific", applicationIdParameter, appCHIPSpecificIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Deductions_Result> usp_Select_Application_Deductions(Nullable<long> appIncomeId, Nullable<long> appDeductionId)
        {
            var appIncomeIdParameter = appIncomeId.HasValue ?
                new ObjectParameter("AppIncomeId", appIncomeId) :
                new ObjectParameter("AppIncomeId", typeof(long));
    
            var appDeductionIdParameter = appDeductionId.HasValue ?
                new ObjectParameter("AppDeductionId", appDeductionId) :
                new ObjectParameter("AppDeductionId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Deductions_Result>("usp_Select_Application_Deductions", appIncomeIdParameter, appDeductionIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Dependents_Result> usp_Select_Application_Dependents(Nullable<long> applicationId, Nullable<long> appDependentId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appDependentIdParameter = appDependentId.HasValue ?
                new ObjectParameter("AppDependentId", appDependentId) :
                new ObjectParameter("AppDependentId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Dependents_Result>("usp_Select_Application_Dependents", applicationIdParameter, appDependentIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Detail_Result> usp_Select_Application_Detail(Nullable<long> applicationId, Nullable<long> appDetailId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appDetailIdParameter = appDetailId.HasValue ?
                new ObjectParameter("AppDetailId", appDetailId) :
                new ObjectParameter("AppDetailId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Detail_Result>("usp_Select_Application_Detail", applicationIdParameter, appDetailIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Details_For_Ele_Copy_App_Result> usp_Select_Application_Details_For_Ele_Copy_App(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Details_For_Ele_Copy_App_Result>("usp_Select_Application_Details_For_Ele_Copy_App", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_EHC_Result> usp_Select_Application_EHC(Nullable<long> applicationHcId, Nullable<long> applicationEhcId)
        {
            var applicationHcIdParameter = applicationHcId.HasValue ?
                new ObjectParameter("ApplicationHcId", applicationHcId) :
                new ObjectParameter("ApplicationHcId", typeof(long));
    
            var applicationEhcIdParameter = applicationEhcId.HasValue ?
                new ObjectParameter("ApplicationEhcId", applicationEhcId) :
                new ObjectParameter("ApplicationEhcId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_EHC_Result>("usp_Select_Application_EHC", applicationHcIdParameter, applicationEhcIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_EHC_Employee_Result> usp_Select_Application_EHC_Employee(Nullable<long> applicationId, Nullable<long> appEhcEmployeeId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appEhcEmployeeIdParameter = appEhcEmployeeId.HasValue ?
                new ObjectParameter("AppEhcEmployeeId", appEhcEmployeeId) :
                new ObjectParameter("AppEhcEmployeeId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_EHC_Employee_Result>("usp_Select_Application_EHC_Employee", applicationIdParameter, appEhcEmployeeIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_EHC_Employer_Result> usp_Select_Application_EHC_Employer(Nullable<long> applicationEHCId, Nullable<long> appEHCEmployerId)
        {
            var applicationEHCIdParameter = applicationEHCId.HasValue ?
                new ObjectParameter("ApplicationEHCId", applicationEHCId) :
                new ObjectParameter("ApplicationEHCId", typeof(long));
    
            var appEHCEmployerIdParameter = appEHCEmployerId.HasValue ?
                new ObjectParameter("AppEHCEmployerId", appEHCEmployerId) :
                new ObjectParameter("AppEHCEmployerId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_EHC_Employer_Result>("usp_Select_Application_EHC_Employer", applicationEHCIdParameter, appEHCEmployerIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Eligibility_Result> usp_Select_Application_Eligibility(Nullable<long> applicationId, Nullable<long> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Eligibility_Result>("usp_Select_Application_Eligibility", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Enrollment_Result> usp_Select_Application_Enrollment(Nullable<long> applicationId, Nullable<long> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Enrollment_Result>("usp_Select_Application_Enrollment", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Enrollment_History_Result> usp_Select_Application_Enrollment_History(Nullable<int> enrollHistoryId, Nullable<int> personId, Nullable<int> applicationId, Nullable<System.DateTime> startDate, Nullable<System.DateTime> cancelDate, Nullable<byte> cancelReasonId, Nullable<byte> programSubCategoryId, Nullable<byte> programID)
        {
            var enrollHistoryIdParameter = enrollHistoryId.HasValue ?
                new ObjectParameter("EnrollHistoryId", enrollHistoryId) :
                new ObjectParameter("EnrollHistoryId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var startDateParameter = startDate.HasValue ?
                new ObjectParameter("StartDate", startDate) :
                new ObjectParameter("StartDate", typeof(System.DateTime));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            var cancelReasonIdParameter = cancelReasonId.HasValue ?
                new ObjectParameter("CancelReasonId", cancelReasonId) :
                new ObjectParameter("CancelReasonId", typeof(byte));
    
            var programSubCategoryIdParameter = programSubCategoryId.HasValue ?
                new ObjectParameter("ProgramSubCategoryId", programSubCategoryId) :
                new ObjectParameter("ProgramSubCategoryId", typeof(byte));
    
            var programIDParameter = programID.HasValue ?
                new ObjectParameter("ProgramID", programID) :
                new ObjectParameter("ProgramID", typeof(byte));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Enrollment_History_Result>("usp_Select_Application_Enrollment_History", enrollHistoryIdParameter, personIdParameter, applicationIdParameter, startDateParameter, cancelDateParameter, cancelReasonIdParameter, programSubCategoryIdParameter, programIDParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Future_Income_Result> usp_Select_Application_Future_Income(Nullable<long> appIncomeId, Nullable<long> appFutureIncomeId)
        {
            var appIncomeIdParameter = appIncomeId.HasValue ?
                new ObjectParameter("AppIncomeId", appIncomeId) :
                new ObjectParameter("AppIncomeId", typeof(long));
    
            var appFutureIncomeIdParameter = appFutureIncomeId.HasValue ?
                new ObjectParameter("AppFutureIncomeId", appFutureIncomeId) :
                new ObjectParameter("AppFutureIncomeId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Future_Income_Result>("usp_Select_Application_Future_Income", appIncomeIdParameter, appFutureIncomeIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Health_Coverage_Result> usp_Select_Application_Health_Coverage(Nullable<long> applicationId, Nullable<long> appHcId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appHcIdParameter = appHcId.HasValue ?
                new ObjectParameter("AppHcId", appHcId) :
                new ObjectParameter("AppHcId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Health_Coverage_Result>("usp_Select_Application_Health_Coverage", applicationIdParameter, appHcIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Household_Result> usp_Select_Application_Household(Nullable<long> applicationId, Nullable<long> appHouseholdDetailId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appHouseholdDetailIdParameter = appHouseholdDetailId.HasValue ?
                new ObjectParameter("AppHouseholdDetailId", appHouseholdDetailId) :
                new ObjectParameter("AppHouseholdDetailId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Household_Result>("usp_Select_Application_Household", applicationIdParameter, appHouseholdDetailIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Income_Result> usp_Select_Application_Income(Nullable<long> applicationId, Nullable<long> appIncomeId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appIncomeIdParameter = appIncomeId.HasValue ?
                new ObjectParameter("AppIncomeId", appIncomeId) :
                new ObjectParameter("AppIncomeId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Income_Result>("usp_Select_Application_Income", applicationIdParameter, appIncomeIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Income_Detail_Result> usp_Select_Application_Income_Detail(Nullable<long> applicationIncomeId, Nullable<long> appIncomeDetailId)
        {
            var applicationIncomeIdParameter = applicationIncomeId.HasValue ?
                new ObjectParameter("ApplicationIncomeId", applicationIncomeId) :
                new ObjectParameter("ApplicationIncomeId", typeof(long));
    
            var appIncomeDetailIdParameter = appIncomeDetailId.HasValue ?
                new ObjectParameter("AppIncomeDetailId", appIncomeDetailId) :
                new ObjectParameter("AppIncomeDetailId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Income_Detail_Result>("usp_Select_Application_Income_Detail", applicationIncomeIdParameter, appIncomeDetailIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Lives_With_Result> usp_Select_Application_Lives_With(Nullable<long> applicationId, Nullable<long> appLiveWithId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appLiveWithIdParameter = appLiveWithId.HasValue ?
                new ObjectParameter("AppLiveWithId", appLiveWithId) :
                new ObjectParameter("AppLiveWithId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Lives_With_Result>("usp_Select_Application_Lives_With", applicationIdParameter, appLiveWithIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Medicaid_CHIP_Specific_Result> usp_Select_Application_Medicaid_CHIP_Specific(Nullable<long> applicationId, Nullable<long> appMedCHIPId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appMedCHIPIdParameter = appMedCHIPId.HasValue ?
                new ObjectParameter("AppMedCHIPId", appMedCHIPId) :
                new ObjectParameter("AppMedCHIPId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Medicaid_CHIP_Specific_Result>("usp_Select_Application_Medicaid_CHIP_Specific", applicationIdParameter, appMedCHIPIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Medicaid_CHIP_Specific_Detail_Result> usp_Select_Application_Medicaid_CHIP_Specific_Detail(Nullable<long> applicationMedCHIPId, Nullable<long> appMedCHIPDetailId)
        {
            var applicationMedCHIPIdParameter = applicationMedCHIPId.HasValue ?
                new ObjectParameter("ApplicationMedCHIPId", applicationMedCHIPId) :
                new ObjectParameter("ApplicationMedCHIPId", typeof(long));
    
            var appMedCHIPDetailIdParameter = appMedCHIPDetailId.HasValue ?
                new ObjectParameter("AppMedCHIPDetailId", appMedCHIPDetailId) :
                new ObjectParameter("AppMedCHIPDetailId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Medicaid_CHIP_Specific_Detail_Result>("usp_Select_Application_Medicaid_CHIP_Specific_Detail", applicationMedCHIPIdParameter, appMedCHIPDetailIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Medicaid_Specific_Result> usp_Select_Application_Medicaid_Specific(Nullable<long> applicationId, Nullable<long> appMedSpecificId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appMedSpecificIdParameter = appMedSpecificId.HasValue ?
                new ObjectParameter("AppMedSpecificId", appMedSpecificId) :
                new ObjectParameter("AppMedSpecificId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Medicaid_Specific_Result>("usp_Select_Application_Medicaid_Specific", applicationIdParameter, appMedSpecificIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Representative_Result> usp_Select_Application_Representative(Nullable<long> applicationId, string email, Nullable<long> personId, string userId, Nullable<long> applicationRepresentativeId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var emailParameter = email != null ?
                new ObjectParameter("Email", email) :
                new ObjectParameter("Email", typeof(string));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var userIdParameter = userId != null ?
                new ObjectParameter("UserId", userId) :
                new ObjectParameter("UserId", typeof(string));
    
            var applicationRepresentativeIdParameter = applicationRepresentativeId.HasValue ?
                new ObjectParameter("ApplicationRepresentativeId", applicationRepresentativeId) :
                new ObjectParameter("ApplicationRepresentativeId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Representative_Result>("usp_Select_Application_Representative", applicationIdParameter, emailParameter, personIdParameter, userIdParameter, applicationRepresentativeIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_Status_History_Result> usp_Select_Application_Status_History(Nullable<long> applicationId, Nullable<long> appStatusHistoryId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var appStatusHistoryIdParameter = appStatusHistoryId.HasValue ?
                new ObjectParameter("AppStatusHistoryId", appStatusHistoryId) :
                new ObjectParameter("AppStatusHistoryId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_Status_History_Result>("usp_Select_Application_Status_History", applicationIdParameter, appStatusHistoryIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Employer_Result> usp_Select_Employer(Nullable<long> employerId)
        {
            var employerIdParameter = employerId.HasValue ?
                new ObjectParameter("EmployerId", employerId) :
                new ObjectParameter("EmployerId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Employer_Result>("usp_Select_Employer", employerIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Employer_Address_Result> usp_Select_Employer_Address(Nullable<long> employerId, Nullable<long> empAddressId)
        {
            var employerIdParameter = employerId.HasValue ?
                new ObjectParameter("EmployerId", employerId) :
                new ObjectParameter("EmployerId", typeof(long));
    
            var empAddressIdParameter = empAddressId.HasValue ?
                new ObjectParameter("EmpAddressId", empAddressId) :
                new ObjectParameter("EmpAddressId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Employer_Address_Result>("usp_Select_Employer_Address", employerIdParameter, empAddressIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Employer_Contact_Result> usp_Select_Employer_Contact(Nullable<long> employerId, Nullable<long> empContactId)
        {
            var employerIdParameter = employerId.HasValue ?
                new ObjectParameter("EmployerId", employerId) :
                new ObjectParameter("EmployerId", typeof(long));
    
            var empContactIdParameter = empContactId.HasValue ?
                new ObjectParameter("EmpContactId", empContactId) :
                new ObjectParameter("EmpContactId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Employer_Contact_Result>("usp_Select_Employer_Contact", employerIdParameter, empContactIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Employer_Email_Result> usp_Select_Employer_Email(Nullable<long> employerId, Nullable<long> empEmailId)
        {
            var employerIdParameter = employerId.HasValue ?
                new ObjectParameter("EmployerId", employerId) :
                new ObjectParameter("EmployerId", typeof(long));
    
            var empEmailIdParameter = empEmailId.HasValue ?
                new ObjectParameter("EmpEmailId", empEmailId) :
                new ObjectParameter("EmpEmailId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Employer_Email_Result>("usp_Select_Employer_Email", employerIdParameter, empEmailIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Employer_Phone_Result> usp_Select_Employer_Phone(Nullable<long> employerId, Nullable<long> empPhoneId)
        {
            var employerIdParameter = employerId.HasValue ?
                new ObjectParameter("EmployerId", employerId) :
                new ObjectParameter("EmployerId", typeof(long));
    
            var empPhoneIdParameter = empPhoneId.HasValue ?
                new ObjectParameter("EmpPhoneId", empPhoneId) :
                new ObjectParameter("EmpPhoneId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Employer_Phone_Result>("usp_Select_Employer_Phone", employerIdParameter, empPhoneIdParameter);
        }
    
        public virtual int usp_CLEAR_ELE_ERRORS(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_CLEAR_ELE_ERRORS", cancelDateParameter);
        }
    
        public virtual int usp_DELETE_APPLICATION_DATA(Nullable<long> applicationId, Nullable<long> contactId, ObjectParameter success, ObjectParameter breakpointReached, ObjectParameter eELogErrorId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var contactIdParameter = contactId.HasValue ?
                new ObjectParameter("ContactId", contactId) :
                new ObjectParameter("ContactId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_APPLICATION_DATA", applicationIdParameter, contactIdParameter, success, breakpointReached, eELogErrorId);
        }
    
        public virtual int usp_DELETE_APPLICATION_DATA_BY_APPLICATION_ID(Nullable<int> applicationId, ObjectParameter success, ObjectParameter breakpointReached, ObjectParameter eELogErrorId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_APPLICATION_DATA_BY_APPLICATION_ID", applicationIdParameter, success, breakpointReached, eELogErrorId);
        }
    
        public virtual int usp_DELETE_APPLICATION_DATA_BY_CONTACT_ID(Nullable<int> contactId, ObjectParameter success, ObjectParameter breakpointReached, ObjectParameter eELogErrorId)
        {
            var contactIdParameter = contactId.HasValue ?
                new ObjectParameter("ContactId", contactId) :
                new ObjectParameter("ContactId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_APPLICATION_DATA_BY_CONTACT_ID", contactIdParameter, success, breakpointReached, eELogErrorId);
        }
    
        public virtual ObjectResult<usp_ELE_LETTER_Result> usp_ELE_LETTER(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_ELE_LETTER_Result>("usp_ELE_LETTER", cancelDateParameter);
        }
    
        public virtual int usp_POPULATE_APPLICATION_RENEWAL(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_POPULATE_APPLICATION_RENEWAL", cancelDateParameter);
        }
    
        public virtual int usp_POPULATE_EXPRESS_LANE_ELIGIBILITY(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_POPULATE_EXPRESS_LANE_ELIGIBILITY", cancelDateParameter);
        }
    
        public virtual int usp_POPULATE_RENEWAL_REDETERMINATION_VERIFICATION(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_POPULATE_RENEWAL_REDETERMINATION_VERIFICATION", cancelDateParameter);
        }
    
        public virtual int usp_SELECT_APPLICATION_DETAILS_FOR_ELE_COPY_APP1(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_APPLICATION_DETAILS_FOR_ELE_COPY_APP1", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_ELE_ERRORS1_Result> usp_SELECT_ELE_ERRORS1(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ELE_ERRORS1_Result>("usp_SELECT_ELE_ERRORS1", cancelDateParameter);
        }
    
        public virtual ObjectResult<string> usp_SELECT_ELE_INDICATOR1(Nullable<long> applicationId, Nullable<long> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_SELECT_ELE_INDICATOR1", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_MEDICAID_RENEWAL_Result> usp_SELECT_MEDICAID_RENEWAL(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_MEDICAID_RENEWAL_Result>("usp_SELECT_MEDICAID_RENEWAL", cancelDateParameter);
        }
    
        public virtual ObjectResult<string> usp_SELECT_RRV_EQUIFAX_REQUEST_AS_XML(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_SELECT_RRV_EQUIFAX_REQUEST_AS_XML", cancelDateParameter);
        }
    
        public virtual ObjectResult<string> usp_SELECT_RRV_IRS_REQUEST_AS_XML(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_SELECT_RRV_IRS_REQUEST_AS_XML", cancelDateParameter);
        }
    
        public virtual ObjectResult<string> usp_SELECT_RRV_MEDICARE_REQUEST_AS_XML(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_SELECT_RRV_MEDICARE_REQUEST_AS_XML", cancelDateParameter);
        }
    
        public virtual ObjectResult<string> usp_SELECT_RRV_SSA_REQUEST_AS_XML(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_SELECT_RRV_SSA_REQUEST_AS_XML", cancelDateParameter);
        }
    
        public virtual int usp_UPDATE_APPLICATION_RENEWAL_ELE_STATUS_FROM_ELE(Nullable<long> applicationToCopyId, Nullable<System.DateTime> cancelDate)
        {
            var applicationToCopyIdParameter = applicationToCopyId.HasValue ?
                new ObjectParameter("ApplicationToCopyId", applicationToCopyId) :
                new ObjectParameter("ApplicationToCopyId", typeof(long));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_RENEWAL_ELE_STATUS_FROM_ELE", applicationToCopyIdParameter, cancelDateParameter);
        }
    
        public virtual int usp_UPDATE_APPLICATION_RENEWAL_STATUSES(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_RENEWAL_STATUSES", cancelDateParameter);
        }
    
        public virtual int usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_ELIGIBLE_TO_INELIGIBLE(Nullable<long> applicationId, Nullable<System.DateTime> cancelDate)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_ELIGIBLE_TO_INELIGIBLE", applicationIdParameter, cancelDateParameter);
        }
    
        public virtual ObjectResult<usp_Select_Current_Pregnancy_Enrollments_Result> usp_Select_Current_Pregnancy_Enrollments(Nullable<long> motherId)
        {
            var motherIdParameter = motherId.HasValue ?
                new ObjectParameter("motherId", motherId) :
                new ObjectParameter("motherId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Current_Pregnancy_Enrollments_Result>("usp_Select_Current_Pregnancy_Enrollments", motherIdParameter);
        }
    
        public virtual int usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_INELIGIBILITY(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_INELIGIBILITY", cancelDateParameter);
        }
    
        public virtual int usp_DELETE_INBOUND_ACCOUNT_TRANSFER_APPLICATION_BY_ACTIVITY_ID(string activityId, ObjectParameter resultType, ObjectParameter success, ObjectParameter breakpointReached, ObjectParameter eELogErrorId)
        {
            var activityIdParameter = activityId != null ?
                new ObjectParameter("ActivityId", activityId) :
                new ObjectParameter("ActivityId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_INBOUND_ACCOUNT_TRANSFER_APPLICATION_BY_ACTIVITY_ID", activityIdParameter, resultType, success, breakpointReached, eELogErrorId);
        }
    
        public virtual int usp_DELETE_INBOUND_ACCOUNT_TRANSFER_DATA_BY_ACTIVITY_ID(string activityId, ObjectParameter success, ObjectParameter breakpointReached, ObjectParameter eELogErrorId)
        {
            var activityIdParameter = activityId != null ?
                new ObjectParameter("ActivityId", activityId) :
                new ObjectParameter("ActivityId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_INBOUND_ACCOUNT_TRANSFER_DATA_BY_ACTIVITY_ID", activityIdParameter, success, breakpointReached, eELogErrorId);
        }
    
        public virtual ObjectResult<Nullable<long>> usp_SELECT_RRV_APPLICATION_IDS_WITH_ALL_RESPONSES(Nullable<System.DateTime> canceldate, Nullable<bool> isFinalRun)
        {
            var canceldateParameter = canceldate.HasValue ?
                new ObjectParameter("Canceldate", canceldate) :
                new ObjectParameter("Canceldate", typeof(System.DateTime));
    
            var isFinalRunParameter = isFinalRun.HasValue ?
                new ObjectParameter("IsFinalRun", isFinalRun) :
                new ObjectParameter("IsFinalRun", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("usp_SELECT_RRV_APPLICATION_IDS_WITH_ALL_RESPONSES", canceldateParameter, isFinalRunParameter);
        }
    
        public virtual int usp_UPDATE_APPLICATION_RENEWAL_RRV_STATUS(Nullable<long> applicationId, Nullable<System.DateTime> cancelDate, string rRVStatus)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            var rRVStatusParameter = rRVStatus != null ?
                new ObjectParameter("RRVStatus", rRVStatus) :
                new ObjectParameter("RRVStatus", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_RENEWAL_RRV_STATUS", applicationIdParameter, cancelDateParameter, rRVStatusParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> usp_SsnExistsInAmaes(string ssn)
        {
            var ssnParameter = ssn != null ?
                new ObjectParameter("Ssn", ssn) :
                new ObjectParameter("Ssn", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("usp_SsnExistsInAmaes", ssnParameter);
        }
    
        public virtual int usp_INSERT_FH_MEDICARE_RESPONSE(Nullable<long> applicationId, Nullable<long> personId, string personSSNIdentification, string organizationResponseCode, string organizationResponseCodeText, string insertedBy, ObjectParameter medicareResponseId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var personSSNIdentificationParameter = personSSNIdentification != null ?
                new ObjectParameter("PersonSSNIdentification", personSSNIdentification) :
                new ObjectParameter("PersonSSNIdentification", typeof(string));
    
            var organizationResponseCodeParameter = organizationResponseCode != null ?
                new ObjectParameter("OrganizationResponseCode", organizationResponseCode) :
                new ObjectParameter("OrganizationResponseCode", typeof(string));
    
            var organizationResponseCodeTextParameter = organizationResponseCodeText != null ?
                new ObjectParameter("OrganizationResponseCodeText", organizationResponseCodeText) :
                new ObjectParameter("OrganizationResponseCodeText", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_FH_MEDICARE_RESPONSE", applicationIdParameter, personIdParameter, personSSNIdentificationParameter, organizationResponseCodeParameter, organizationResponseCodeTextParameter, insertedByParameter, medicareResponseId);
        }
    
        public virtual ObjectResult<usp_SELECT_ELE_STAGING_MEMBERS_Result> usp_SELECT_ELE_STAGING_MEMBERS(Nullable<System.DateTime> cancelDate)
        {
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ELE_STAGING_MEMBERS_Result>("usp_SELECT_ELE_STAGING_MEMBERS", cancelDateParameter);
        }
    
        public virtual int usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_WITH_DHR_DATA(Nullable<long> eleId, Nullable<bool> includeMember, string queryStatus, string sSN, string fullName, Nullable<System.DateTime> dateOfBirth, string eLEIndicator, string selfEmploymentIncome, string earnedIncome, string employmentIncome, string unearnedIncome, string processingStatus, string comments)
        {
            var eleIdParameter = eleId.HasValue ?
                new ObjectParameter("EleId", eleId) :
                new ObjectParameter("EleId", typeof(long));
    
            var includeMemberParameter = includeMember.HasValue ?
                new ObjectParameter("IncludeMember", includeMember) :
                new ObjectParameter("IncludeMember", typeof(bool));
    
            var queryStatusParameter = queryStatus != null ?
                new ObjectParameter("QueryStatus", queryStatus) :
                new ObjectParameter("QueryStatus", typeof(string));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var fullNameParameter = fullName != null ?
                new ObjectParameter("FullName", fullName) :
                new ObjectParameter("FullName", typeof(string));
    
            var dateOfBirthParameter = dateOfBirth.HasValue ?
                new ObjectParameter("DateOfBirth", dateOfBirth) :
                new ObjectParameter("DateOfBirth", typeof(System.DateTime));
    
            var eLEIndicatorParameter = eLEIndicator != null ?
                new ObjectParameter("ELEIndicator", eLEIndicator) :
                new ObjectParameter("ELEIndicator", typeof(string));
    
            var selfEmploymentIncomeParameter = selfEmploymentIncome != null ?
                new ObjectParameter("SelfEmploymentIncome", selfEmploymentIncome) :
                new ObjectParameter("SelfEmploymentIncome", typeof(string));
    
            var earnedIncomeParameter = earnedIncome != null ?
                new ObjectParameter("EarnedIncome", earnedIncome) :
                new ObjectParameter("EarnedIncome", typeof(string));
    
            var employmentIncomeParameter = employmentIncome != null ?
                new ObjectParameter("EmploymentIncome", employmentIncome) :
                new ObjectParameter("EmploymentIncome", typeof(string));
    
            var unearnedIncomeParameter = unearnedIncome != null ?
                new ObjectParameter("UnearnedIncome", unearnedIncome) :
                new ObjectParameter("UnearnedIncome", typeof(string));
    
            var processingStatusParameter = processingStatus != null ?
                new ObjectParameter("ProcessingStatus", processingStatus) :
                new ObjectParameter("ProcessingStatus", typeof(string));
    
            var commentsParameter = comments != null ?
                new ObjectParameter("Comments", comments) :
                new ObjectParameter("Comments", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_EXPRESS_LANE_ELIGIBILITY_WITH_DHR_DATA", eleIdParameter, includeMemberParameter, queryStatusParameter, sSNParameter, fullNameParameter, dateOfBirthParameter, eLEIndicatorParameter, selfEmploymentIncomeParameter, earnedIncomeParameter, employmentIncomeParameter, unearnedIncomeParameter, processingStatusParameter, commentsParameter);
        }
    
        public virtual int USP_INSERT_MEDICARE_INSURANCE(Nullable<long> medicareResponseId, Nullable<System.DateTime> insuranceEffectiveDate, Nullable<System.DateTime> insuranceEndDate, string insertedBy, ObjectParameter medicareInsuranceId)
        {
            var medicareResponseIdParameter = medicareResponseId.HasValue ?
                new ObjectParameter("MedicareResponseId", medicareResponseId) :
                new ObjectParameter("MedicareResponseId", typeof(long));
    
            var insuranceEffectiveDateParameter = insuranceEffectiveDate.HasValue ?
                new ObjectParameter("InsuranceEffectiveDate", insuranceEffectiveDate) :
                new ObjectParameter("InsuranceEffectiveDate", typeof(System.DateTime));
    
            var insuranceEndDateParameter = insuranceEndDate.HasValue ?
                new ObjectParameter("InsuranceEndDate", insuranceEndDate) :
                new ObjectParameter("InsuranceEndDate", typeof(System.DateTime));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("USP_INSERT_MEDICARE_INSURANCE", medicareResponseIdParameter, insuranceEffectiveDateParameter, insuranceEndDateParameter, insertedByParameter, medicareInsuranceId);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_APP_DETAIL_BY_APP_ID_Result> usp_APPLICATION_SNAPSHOT_APP_DETAIL_BY_APP_ID(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_APP_DETAIL_BY_APP_ID_Result>("usp_APPLICATION_SNAPSHOT_APP_DETAIL_BY_APP_ID", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_APP_HOUSEHOLD_BY_APP_ID_Result> usp_APPLICATION_SNAPSHOT_APP_HOUSEHOLD_BY_APP_ID(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_APP_HOUSEHOLD_BY_APP_ID_Result>("usp_APPLICATION_SNAPSHOT_APP_HOUSEHOLD_BY_APP_ID", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_PERSON_SNAPSHOT_BY_APP_ID_Result> usp_APPLICATION_SNAPSHOT_PERSON_SNAPSHOT_BY_APP_ID(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_PERSON_SNAPSHOT_BY_APP_ID_Result>("usp_APPLICATION_SNAPSHOT_PERSON_SNAPSHOT_BY_APP_ID", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_ADDITIONAL_CONTACT_INFORMATION_Result> usp_APPLICATION_SNAPSHOT_ADDITIONAL_CONTACT_INFORMATION(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_ADDITIONAL_CONTACT_INFORMATION_Result>("usp_APPLICATION_SNAPSHOT_ADDITIONAL_CONTACT_INFORMATION", applicationIdParameter);
        }
    
        public virtual int usp_GET_APPLICATION_TERMINAL_STATUS_IND(Nullable<long> applicationId, ObjectParameter isTerminalApp)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GET_APPLICATION_TERMINAL_STATUS_IND", applicationIdParameter, isTerminalApp);
        }
    
        public virtual ObjectResult<Nullable<System.DateTime>> usp_SELECT_APP_REVIEW_DATE(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<System.DateTime>>("usp_SELECT_APP_REVIEW_DATE", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_HUB_RESPONSES_Result> usp_SELECT_HUB_RESPONSES(Nullable<long> personId, Nullable<long> applicationId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_HUB_RESPONSES_Result>("usp_SELECT_HUB_RESPONSES", personIdParameter, applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_MEDICARE_HUB_RESPONSE_Result> usp_SELECT_MEDICARE_HUB_RESPONSE(Nullable<int> applicationId, Nullable<int> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_MEDICARE_HUB_RESPONSE_Result>("usp_SELECT_MEDICARE_HUB_RESPONSE", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_INCOME_BY_APP_ID_Result> usp_APPLICATION_SNAPSHOT_INCOME_BY_APP_ID(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_INCOME_BY_APP_ID_Result>("usp_APPLICATION_SNAPSHOT_INCOME_BY_APP_ID", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_TAX_FILER_SUMMARY_Result> usp_SELECT_TAX_FILER_SUMMARY(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("applicationId", applicationId) :
                new ObjectParameter("applicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_TAX_FILER_SUMMARY_Result>("usp_SELECT_TAX_FILER_SUMMARY", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_TAX_FILER_Result> usp_APPLICATION_SNAPSHOT_TAX_FILER(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_TAX_FILER_Result>("usp_APPLICATION_SNAPSHOT_TAX_FILER", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_ELIG_ENROLL_INCOME_INFORMATION_Result> usp_APPLICATION_SNAPSHOT_ELIG_ENROLL_INCOME_INFORMATION(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_ELIG_ENROLL_INCOME_INFORMATION_Result>("usp_APPLICATION_SNAPSHOT_ELIG_ENROLL_INCOME_INFORMATION", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_MEC_CHECK_Result> usp_SELECT_MEC_CHECK(Nullable<long> applicationId, Nullable<long> mecCheckId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var mecCheckIdParameter = mecCheckId.HasValue ?
                new ObjectParameter("MecCheckId", mecCheckId) :
                new ObjectParameter("MecCheckId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_MEC_CHECK_Result>("usp_SELECT_MEC_CHECK", applicationIdParameter, mecCheckIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_TERMINAL_APPLOCK_INFO_GRID_DETAILS_Result> usp_SELECT_TERMINAL_APPLOCK_INFO_GRID_DETAILS(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_TERMINAL_APPLOCK_INFO_GRID_DETAILS_Result>("usp_SELECT_TERMINAL_APPLOCK_INFO_GRID_DETAILS", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Batch_And_Payment_Number_Result> usp_Select_Batch_And_Payment_Number(string bATCHNUMBER, Nullable<System.DateTime> sTARTDATE, string bATCHSTATUSID, string pAYMENTNBR, string iNSTRUMENTNBR, string cHIPCONTACTID, string sSN)
        {
            var bATCHNUMBERParameter = bATCHNUMBER != null ?
                new ObjectParameter("BATCHNUMBER", bATCHNUMBER) :
                new ObjectParameter("BATCHNUMBER", typeof(string));
    
            var sTARTDATEParameter = sTARTDATE.HasValue ?
                new ObjectParameter("STARTDATE", sTARTDATE) :
                new ObjectParameter("STARTDATE", typeof(System.DateTime));
    
            var bATCHSTATUSIDParameter = bATCHSTATUSID != null ?
                new ObjectParameter("BATCHSTATUSID", bATCHSTATUSID) :
                new ObjectParameter("BATCHSTATUSID", typeof(string));
    
            var pAYMENTNBRParameter = pAYMENTNBR != null ?
                new ObjectParameter("PAYMENTNBR", pAYMENTNBR) :
                new ObjectParameter("PAYMENTNBR", typeof(string));
    
            var iNSTRUMENTNBRParameter = iNSTRUMENTNBR != null ?
                new ObjectParameter("INSTRUMENTNBR", iNSTRUMENTNBR) :
                new ObjectParameter("INSTRUMENTNBR", typeof(string));
    
            var cHIPCONTACTIDParameter = cHIPCONTACTID != null ?
                new ObjectParameter("CHIPCONTACTID", cHIPCONTACTID) :
                new ObjectParameter("CHIPCONTACTID", typeof(string));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Batch_And_Payment_Number_Result>("usp_Select_Batch_And_Payment_Number", bATCHNUMBERParameter, sTARTDATEParameter, bATCHSTATUSIDParameter, pAYMENTNBRParameter, iNSTRUMENTNBRParameter, cHIPCONTACTIDParameter, sSNParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_SearchPayment_Result> usp_Accounting_SearchPayment(string applicationId, string contactPersonId, string paymentNbr, string firstName, string lastName)
        {
            var applicationIdParameter = applicationId != null ?
                new ObjectParameter("applicationId", applicationId) :
                new ObjectParameter("applicationId", typeof(string));
    
            var contactPersonIdParameter = contactPersonId != null ?
                new ObjectParameter("contactPersonId", contactPersonId) :
                new ObjectParameter("contactPersonId", typeof(string));
    
            var paymentNbrParameter = paymentNbr != null ?
                new ObjectParameter("paymentNbr", paymentNbr) :
                new ObjectParameter("paymentNbr", typeof(string));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("firstName", firstName) :
                new ObjectParameter("firstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("lastName", lastName) :
                new ObjectParameter("lastName", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_SearchPayment_Result>("usp_Accounting_SearchPayment", applicationIdParameter, contactPersonIdParameter, paymentNbrParameter, firstNameParameter, lastNameParameter);
        }
    
        public virtual ObjectResult<usp_Accouting_LetterPanel_PaymentList_Result> usp_Accouting_LetterPanel_PaymentList(Nullable<int> chipContactPersonID, Nullable<long> paymentNbr, string payment_type_no)
        {
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("PaymentNbr", paymentNbr) :
                new ObjectParameter("PaymentNbr", typeof(long));
    
            var payment_type_noParameter = payment_type_no != null ?
                new ObjectParameter("Payment_type_no", payment_type_no) :
                new ObjectParameter("Payment_type_no", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accouting_LetterPanel_PaymentList_Result>("usp_Accouting_LetterPanel_PaymentList", chipContactPersonIDParameter, paymentNbrParameter, payment_type_noParameter);
        }
    
        public virtual int usp_Accouting_LetterPanel_Search_Appid(Nullable<int> appid)
        {
            var appidParameter = appid.HasValue ?
                new ObjectParameter("Appid", appid) :
                new ObjectParameter("Appid", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Accouting_LetterPanel_Search_Appid", appidParameter);
        }
    
        public virtual int usp_Accouting_LetterPanel_Search_ChipContactID(Nullable<int> chipContactPersonID)
        {
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Accouting_LetterPanel_Search_ChipContactID", chipContactPersonIDParameter);
        }
    
        public virtual ObjectResult<usp_Accouting_LetterPanel_Search_FNLN_Result> usp_Accouting_LetterPanel_Search_FNLN(string firstName, string lastName)
        {
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accouting_LetterPanel_Search_FNLN_Result>("usp_Accouting_LetterPanel_Search_FNLN", firstNameParameter, lastNameParameter);
        }
    
        public virtual int usp_Accouting_LetterPanel_Search_Instrument_nbr(string instrument_nbr)
        {
            var instrument_nbrParameter = instrument_nbr != null ?
                new ObjectParameter("Instrument_nbr", instrument_nbr) :
                new ObjectParameter("Instrument_nbr", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Accouting_LetterPanel_Search_Instrument_nbr", instrument_nbrParameter);
        }
    
        public virtual int usp_Accouting_LetterPanel_Search_PaymentNbr(Nullable<long> paymentNbr)
        {
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("PaymentNbr", paymentNbr) :
                new ObjectParameter("PaymentNbr", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Accouting_LetterPanel_Search_PaymentNbr", paymentNbrParameter);
        }
    
        public virtual ObjectResult<usp_Accouting_LetterPanel_Search_SSN_Result> usp_Accouting_LetterPanel_Search_SSN(string sSN)
        {
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accouting_LetterPanel_Search_SSN_Result>("usp_Accouting_LetterPanel_Search_SSN", sSNParameter);
        }
    
        public virtual ObjectResult<usp_Accouting_LetterPanel_SearchPersonList_Result> usp_Accouting_LetterPanel_SearchPersonList(string ssn, string firstName, string lastName, Nullable<int> appid, Nullable<int> chipContactPersonID, Nullable<long> paymentNbr, string instrument_nbr)
        {
            var ssnParameter = ssn != null ?
                new ObjectParameter("ssn", ssn) :
                new ObjectParameter("ssn", typeof(string));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var appidParameter = appid.HasValue ?
                new ObjectParameter("Appid", appid) :
                new ObjectParameter("Appid", typeof(int));
    
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("PaymentNbr", paymentNbr) :
                new ObjectParameter("PaymentNbr", typeof(long));
    
            var instrument_nbrParameter = instrument_nbr != null ?
                new ObjectParameter("Instrument_nbr", instrument_nbr) :
                new ObjectParameter("Instrument_nbr", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accouting_LetterPanel_SearchPersonList_Result>("usp_Accouting_LetterPanel_SearchPersonList", ssnParameter, firstNameParameter, lastNameParameter, appidParameter, chipContactPersonIDParameter, paymentNbrParameter, instrument_nbrParameter);
        }
    
        public virtual ObjectResult<usp_Accouting_LetterPanel_LetterHistorySearch_Result> usp_Accouting_LetterPanel_LetterHistorySearch(Nullable<int> chipContactPersonID)
        {
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accouting_LetterPanel_LetterHistorySearch_Result>("usp_Accouting_LetterPanel_LetterHistorySearch", chipContactPersonIDParameter);
        }
    
        public virtual int Usp_Insert_NSF_Record(Nullable<long> paymentNbr, string user_id)
        {
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("paymentNbr", paymentNbr) :
                new ObjectParameter("paymentNbr", typeof(long));
    
            var user_idParameter = user_id != null ?
                new ObjectParameter("user_id", user_id) :
                new ObjectParameter("user_id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("Usp_Insert_NSF_Record", paymentNbrParameter, user_idParameter);
        }
    
        public virtual int usp_Revert_NSF_Amount(Nullable<long> paymentNbr, string user_id)
        {
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("paymentNbr", paymentNbr) :
                new ObjectParameter("paymentNbr", typeof(long));
    
            var user_idParameter = user_id != null ?
                new ObjectParameter("user_id", user_id) :
                new ObjectParameter("user_id", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Revert_NSF_Amount", paymentNbrParameter, user_idParameter);
        }
    
        public virtual int usp_Accounting_LetterPanel_SaveLetter(Nullable<int> chipContactPersonID, string filepath, string userid, Nullable<byte> lettertypeid)
        {
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            var filepathParameter = filepath != null ?
                new ObjectParameter("filepath", filepath) :
                new ObjectParameter("filepath", typeof(string));
    
            var useridParameter = userid != null ?
                new ObjectParameter("userid", userid) :
                new ObjectParameter("userid", typeof(string));
    
            var lettertypeidParameter = lettertypeid.HasValue ?
                new ObjectParameter("lettertypeid", lettertypeid) :
                new ObjectParameter("lettertypeid", typeof(byte));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Accounting_LetterPanel_SaveLetter", chipContactPersonIDParameter, filepathParameter, useridParameter, lettertypeidParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_LetterPanel_ContactAddress_Result> usp_Accounting_LetterPanel_ContactAddress(Nullable<long> paymentNbr)
        {
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("PaymentNbr", paymentNbr) :
                new ObjectParameter("PaymentNbr", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_LetterPanel_ContactAddress_Result>("usp_Accounting_LetterPanel_ContactAddress", paymentNbrParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_LetterPanel_LetterHistorySearch_Result> usp_Accounting_LetterPanel_LetterHistorySearch(Nullable<int> chipContactPersonID)
        {
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_LetterPanel_LetterHistorySearch_Result>("usp_Accounting_LetterPanel_LetterHistorySearch", chipContactPersonIDParameter);
        }
    
        public virtual ObjectResult<Nullable<bool>> usp_Accounting_LetterPanel_DeleteLetter(Nullable<long> letter_History_Id)
        {
            var letter_History_IdParameter = letter_History_Id.HasValue ?
                new ObjectParameter("Letter_History_Id", letter_History_Id) :
                new ObjectParameter("Letter_History_Id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<bool>>("usp_Accounting_LetterPanel_DeleteLetter", letter_History_IdParameter);
        }
    
        public virtual int usp_SELECT_OUT_OF_STATE_INDICATOR(Nullable<int> aPPLICATION_ID, ObjectParameter iS_OUT_OF_STATE)
        {
            var aPPLICATION_IDParameter = aPPLICATION_ID.HasValue ?
                new ObjectParameter("APPLICATION_ID", aPPLICATION_ID) :
                new ObjectParameter("APPLICATION_ID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_OUT_OF_STATE_INDICATOR", aPPLICATION_IDParameter, iS_OUT_OF_STATE);
        }
    
        public virtual int usp_UPSERT_DHR_APPLICATION_MED_CHIP_SPECIFIC_DETAIL(Nullable<int> applicationId, Nullable<int> personId, Nullable<byte> healthCoverageMCTypeId, string healthPlanName, string insuranceNumber, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var healthCoverageMCTypeIdParameter = healthCoverageMCTypeId.HasValue ?
                new ObjectParameter("HealthCoverageMCTypeId", healthCoverageMCTypeId) :
                new ObjectParameter("HealthCoverageMCTypeId", typeof(byte));
    
            var healthPlanNameParameter = healthPlanName != null ?
                new ObjectParameter("HealthPlanName", healthPlanName) :
                new ObjectParameter("HealthPlanName", typeof(string));
    
            var insuranceNumberParameter = insuranceNumber != null ?
                new ObjectParameter("InsuranceNumber", insuranceNumber) :
                new ObjectParameter("InsuranceNumber", typeof(string));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_DHR_APPLICATION_MED_CHIP_SPECIFIC_DETAIL", applicationIdParameter, personIdParameter, healthCoverageMCTypeIdParameter, healthPlanNameParameter, insuranceNumberParameter, updatedByParameter);
        }
    
        public virtual int usp_UPSERT_DHR_PERSON_RACE(Nullable<int> personId, Nullable<byte> raceId, string updatedBy)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var raceIdParameter = raceId.HasValue ?
                new ObjectParameter("RaceId", raceId) :
                new ObjectParameter("RaceId", typeof(byte));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_DHR_PERSON_RACE", personIdParameter, raceIdParameter, updatedByParameter);
        }
    
        public virtual int usp_INSERT_APPLICATION_DETAIL(Nullable<long> applicationId, Nullable<long> personId, Nullable<bool> isContact, Nullable<bool> isApplying, Nullable<int> repAppFilerId, Nullable<bool> isNameSameSSC, string createdBy, ObjectParameter appDetailId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var isContactParameter = isContact.HasValue ?
                new ObjectParameter("IsContact", isContact) :
                new ObjectParameter("IsContact", typeof(bool));
    
            var isApplyingParameter = isApplying.HasValue ?
                new ObjectParameter("IsApplying", isApplying) :
                new ObjectParameter("IsApplying", typeof(bool));
    
            var repAppFilerIdParameter = repAppFilerId.HasValue ?
                new ObjectParameter("RepAppFilerId", repAppFilerId) :
                new ObjectParameter("RepAppFilerId", typeof(int));
    
            var isNameSameSSCParameter = isNameSameSSC.HasValue ?
                new ObjectParameter("IsNameSameSSC", isNameSameSSC) :
                new ObjectParameter("IsNameSameSSC", typeof(bool));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_APPLICATION_DETAIL", applicationIdParameter, personIdParameter, isContactParameter, isApplyingParameter, repAppFilerIdParameter, isNameSameSSCParameter, createdByParameter, appDetailId);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_BY_PERSONID_Result> usp_SELECT_PERSON_BY_PERSONID(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_BY_PERSONID_Result>("usp_SELECT_PERSON_BY_PERSONID", personIdParameter);
        }
    
        public virtual int usp_UPDATE_APPLICATION_DETAIL(Nullable<long> applicationId, Nullable<long> personId, Nullable<bool> isContact, Nullable<bool> isApplying, Nullable<int> relAppFilerId, Nullable<bool> isNameSameSSC, Nullable<bool> hasEligImmStatus, Nullable<byte> immDocTypeId, string alienNR, string i94NR, string paasportNR, string docNR, Nullable<int> countryId, Nullable<System.DateTime> passportExpDate, string sevisId, string docDesc, Nullable<System.DateTime> docExpDate, string categoryCode, Nullable<bool> docFritAi, Nullable<bool> certHhsOrr, Nullable<bool> orrElig18, Nullable<bool> cubHaitEntrant, Nullable<bool> docWor, Nullable<bool> resAmerSamoa, Nullable<bool> admnOrdDhs, Nullable<bool> otherOther, string otherOtherDesc, string otherOtherAlienNR, string otherOtheri94, Nullable<bool> otherNone, Nullable<bool> isNameSameDoc, Nullable<bool> livedUSSince1996, Nullable<bool> veteranMilitary, string cardReceiptNR, Nullable<long> chipAppId, Nullable<long> chipPersonId, Nullable<long> atPersonId, Nullable<bool> isCitizenshipVerMan, Nullable<byte> manCitVerDocId, string updatedBy, Nullable<long> legacyPersonId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var isContactParameter = isContact.HasValue ?
                new ObjectParameter("IsContact", isContact) :
                new ObjectParameter("IsContact", typeof(bool));
    
            var isApplyingParameter = isApplying.HasValue ?
                new ObjectParameter("IsApplying", isApplying) :
                new ObjectParameter("IsApplying", typeof(bool));
    
            var relAppFilerIdParameter = relAppFilerId.HasValue ?
                new ObjectParameter("RelAppFilerId", relAppFilerId) :
                new ObjectParameter("RelAppFilerId", typeof(int));
    
            var isNameSameSSCParameter = isNameSameSSC.HasValue ?
                new ObjectParameter("IsNameSameSSC", isNameSameSSC) :
                new ObjectParameter("IsNameSameSSC", typeof(bool));
    
            var hasEligImmStatusParameter = hasEligImmStatus.HasValue ?
                new ObjectParameter("HasEligImmStatus", hasEligImmStatus) :
                new ObjectParameter("HasEligImmStatus", typeof(bool));
    
            var immDocTypeIdParameter = immDocTypeId.HasValue ?
                new ObjectParameter("ImmDocTypeId", immDocTypeId) :
                new ObjectParameter("ImmDocTypeId", typeof(byte));
    
            var alienNRParameter = alienNR != null ?
                new ObjectParameter("AlienNR", alienNR) :
                new ObjectParameter("AlienNR", typeof(string));
    
            var i94NRParameter = i94NR != null ?
                new ObjectParameter("I94NR", i94NR) :
                new ObjectParameter("I94NR", typeof(string));
    
            var paasportNRParameter = paasportNR != null ?
                new ObjectParameter("PaasportNR", paasportNR) :
                new ObjectParameter("PaasportNR", typeof(string));
    
            var docNRParameter = docNR != null ?
                new ObjectParameter("DocNR", docNR) :
                new ObjectParameter("DocNR", typeof(string));
    
            var countryIdParameter = countryId.HasValue ?
                new ObjectParameter("CountryId", countryId) :
                new ObjectParameter("CountryId", typeof(int));
    
            var passportExpDateParameter = passportExpDate.HasValue ?
                new ObjectParameter("PassportExpDate", passportExpDate) :
                new ObjectParameter("PassportExpDate", typeof(System.DateTime));
    
            var sevisIdParameter = sevisId != null ?
                new ObjectParameter("SevisId", sevisId) :
                new ObjectParameter("SevisId", typeof(string));
    
            var docDescParameter = docDesc != null ?
                new ObjectParameter("DocDesc", docDesc) :
                new ObjectParameter("DocDesc", typeof(string));
    
            var docExpDateParameter = docExpDate.HasValue ?
                new ObjectParameter("DocExpDate", docExpDate) :
                new ObjectParameter("DocExpDate", typeof(System.DateTime));
    
            var categoryCodeParameter = categoryCode != null ?
                new ObjectParameter("CategoryCode", categoryCode) :
                new ObjectParameter("CategoryCode", typeof(string));
    
            var docFritAiParameter = docFritAi.HasValue ?
                new ObjectParameter("DocFritAi", docFritAi) :
                new ObjectParameter("DocFritAi", typeof(bool));
    
            var certHhsOrrParameter = certHhsOrr.HasValue ?
                new ObjectParameter("CertHhsOrr", certHhsOrr) :
                new ObjectParameter("CertHhsOrr", typeof(bool));
    
            var orrElig18Parameter = orrElig18.HasValue ?
                new ObjectParameter("OrrElig18", orrElig18) :
                new ObjectParameter("OrrElig18", typeof(bool));
    
            var cubHaitEntrantParameter = cubHaitEntrant.HasValue ?
                new ObjectParameter("CubHaitEntrant", cubHaitEntrant) :
                new ObjectParameter("CubHaitEntrant", typeof(bool));
    
            var docWorParameter = docWor.HasValue ?
                new ObjectParameter("DocWor", docWor) :
                new ObjectParameter("DocWor", typeof(bool));
    
            var resAmerSamoaParameter = resAmerSamoa.HasValue ?
                new ObjectParameter("ResAmerSamoa", resAmerSamoa) :
                new ObjectParameter("ResAmerSamoa", typeof(bool));
    
            var admnOrdDhsParameter = admnOrdDhs.HasValue ?
                new ObjectParameter("AdmnOrdDhs", admnOrdDhs) :
                new ObjectParameter("AdmnOrdDhs", typeof(bool));
    
            var otherOtherParameter = otherOther.HasValue ?
                new ObjectParameter("OtherOther", otherOther) :
                new ObjectParameter("OtherOther", typeof(bool));
    
            var otherOtherDescParameter = otherOtherDesc != null ?
                new ObjectParameter("OtherOtherDesc", otherOtherDesc) :
                new ObjectParameter("OtherOtherDesc", typeof(string));
    
            var otherOtherAlienNRParameter = otherOtherAlienNR != null ?
                new ObjectParameter("OtherOtherAlienNR", otherOtherAlienNR) :
                new ObjectParameter("OtherOtherAlienNR", typeof(string));
    
            var otherOtheri94Parameter = otherOtheri94 != null ?
                new ObjectParameter("OtherOtheri94", otherOtheri94) :
                new ObjectParameter("OtherOtheri94", typeof(string));
    
            var otherNoneParameter = otherNone.HasValue ?
                new ObjectParameter("OtherNone", otherNone) :
                new ObjectParameter("OtherNone", typeof(bool));
    
            var isNameSameDocParameter = isNameSameDoc.HasValue ?
                new ObjectParameter("IsNameSameDoc", isNameSameDoc) :
                new ObjectParameter("IsNameSameDoc", typeof(bool));
    
            var livedUSSince1996Parameter = livedUSSince1996.HasValue ?
                new ObjectParameter("LivedUSSince1996", livedUSSince1996) :
                new ObjectParameter("LivedUSSince1996", typeof(bool));
    
            var veteranMilitaryParameter = veteranMilitary.HasValue ?
                new ObjectParameter("VeteranMilitary", veteranMilitary) :
                new ObjectParameter("VeteranMilitary", typeof(bool));
    
            var cardReceiptNRParameter = cardReceiptNR != null ?
                new ObjectParameter("CardReceiptNR", cardReceiptNR) :
                new ObjectParameter("CardReceiptNR", typeof(string));
    
            var chipAppIdParameter = chipAppId.HasValue ?
                new ObjectParameter("ChipAppId", chipAppId) :
                new ObjectParameter("ChipAppId", typeof(long));
    
            var chipPersonIdParameter = chipPersonId.HasValue ?
                new ObjectParameter("ChipPersonId", chipPersonId) :
                new ObjectParameter("ChipPersonId", typeof(long));
    
            var atPersonIdParameter = atPersonId.HasValue ?
                new ObjectParameter("AtPersonId", atPersonId) :
                new ObjectParameter("AtPersonId", typeof(long));
    
            var isCitizenshipVerManParameter = isCitizenshipVerMan.HasValue ?
                new ObjectParameter("IsCitizenshipVerMan", isCitizenshipVerMan) :
                new ObjectParameter("IsCitizenshipVerMan", typeof(bool));
    
            var manCitVerDocIdParameter = manCitVerDocId.HasValue ?
                new ObjectParameter("ManCitVerDocId", manCitVerDocId) :
                new ObjectParameter("ManCitVerDocId", typeof(byte));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            var legacyPersonIdParameter = legacyPersonId.HasValue ?
                new ObjectParameter("LegacyPersonId", legacyPersonId) :
                new ObjectParameter("LegacyPersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_DETAIL", applicationIdParameter, personIdParameter, isContactParameter, isApplyingParameter, relAppFilerIdParameter, isNameSameSSCParameter, hasEligImmStatusParameter, immDocTypeIdParameter, alienNRParameter, i94NRParameter, paasportNRParameter, docNRParameter, countryIdParameter, passportExpDateParameter, sevisIdParameter, docDescParameter, docExpDateParameter, categoryCodeParameter, docFritAiParameter, certHhsOrrParameter, orrElig18Parameter, cubHaitEntrantParameter, docWorParameter, resAmerSamoaParameter, admnOrdDhsParameter, otherOtherParameter, otherOtherDescParameter, otherOtherAlienNRParameter, otherOtheri94Parameter, otherNoneParameter, isNameSameDocParameter, livedUSSince1996Parameter, veteranMilitaryParameter, cardReceiptNRParameter, chipAppIdParameter, chipPersonIdParameter, atPersonIdParameter, isCitizenshipVerManParameter, manCitVerDocIdParameter, updatedByParameter, legacyPersonIdParameter);
        }
    
        public virtual int usp_UPSERT_PERSON(Nullable<int> personId, string firstName, string lastName, string middleName, string sSN, Nullable<System.DateTime> dOB, Nullable<byte> suffix, Nullable<byte> gender, string maidenName, Nullable<byte> isUSCitizen, string updatedBy)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var middleNameParameter = middleName != null ?
                new ObjectParameter("MiddleName", middleName) :
                new ObjectParameter("MiddleName", typeof(string));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var dOBParameter = dOB.HasValue ?
                new ObjectParameter("DOB", dOB) :
                new ObjectParameter("DOB", typeof(System.DateTime));
    
            var suffixParameter = suffix.HasValue ?
                new ObjectParameter("Suffix", suffix) :
                new ObjectParameter("Suffix", typeof(byte));
    
            var genderParameter = gender.HasValue ?
                new ObjectParameter("Gender", gender) :
                new ObjectParameter("Gender", typeof(byte));
    
            var maidenNameParameter = maidenName != null ?
                new ObjectParameter("MaidenName", maidenName) :
                new ObjectParameter("MaidenName", typeof(string));
    
            var isUSCitizenParameter = isUSCitizen.HasValue ?
                new ObjectParameter("IsUSCitizen", isUSCitizen) :
                new ObjectParameter("IsUSCitizen", typeof(byte));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_PERSON", personIdParameter, firstNameParameter, lastNameParameter, middleNameParameter, sSNParameter, dOBParameter, suffixParameter, genderParameter, maidenNameParameter, isUSCitizenParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_PERSON_RACE(Nullable<int> personId, Nullable<int> raceId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var raceIdParameter = raceId.HasValue ?
                new ObjectParameter("RaceId", raceId) :
                new ObjectParameter("RaceId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_PERSON_RACE", personIdParameter, raceIdParameter);
        }
    
        public virtual int usp_INSERT_DHR_PERSON_RACE(Nullable<int> personId, Nullable<byte> raceId, string otherRace, string createdBy)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var raceIdParameter = raceId.HasValue ?
                new ObjectParameter("RaceId", raceId) :
                new ObjectParameter("RaceId", typeof(byte));
    
            var otherRaceParameter = otherRace != null ?
                new ObjectParameter("OtherRace", otherRace) :
                new ObjectParameter("OtherRace", typeof(string));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_DHR_PERSON_RACE", personIdParameter, raceIdParameter, otherRaceParameter, createdByParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_Letter_PaymentReceipt_Result> usp_Accounting_Letter_PaymentReceipt(Nullable<long> paymentNbr)
        {
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("PaymentNbr", paymentNbr) :
                new ObjectParameter("PaymentNbr", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_Letter_PaymentReceipt_Result>("usp_Accounting_Letter_PaymentReceipt", paymentNbrParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_Letter_RefundVoucher_Result> usp_Accounting_Letter_RefundVoucher(Nullable<long> paymentNbr)
        {
            var paymentNbrParameter = paymentNbr.HasValue ?
                new ObjectParameter("PaymentNbr", paymentNbr) :
                new ObjectParameter("PaymentNbr", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_Letter_RefundVoucher_Result>("usp_Accounting_Letter_RefundVoucher", paymentNbrParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_Letter_PaymentHistory_Result> usp_Accounting_Letter_PaymentHistory(Nullable<int> chipContactPersonID, Nullable<System.DateTime> start_date, Nullable<System.DateTime> cancel_date)
        {
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            var start_dateParameter = start_date.HasValue ?
                new ObjectParameter("Start_date", start_date) :
                new ObjectParameter("Start_date", typeof(System.DateTime));
    
            var cancel_dateParameter = cancel_date.HasValue ?
                new ObjectParameter("cancel_date", cancel_date) :
                new ObjectParameter("cancel_date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_Letter_PaymentHistory_Result>("usp_Accounting_Letter_PaymentHistory", chipContactPersonIDParameter, start_dateParameter, cancel_dateParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_RACE_Result> usp_SELECT_PERSON_RACE(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_RACE_Result>("usp_SELECT_PERSON_RACE", personIdParameter);
        }
    
        public virtual int usp_UPDATE_APPLICATION_APPLYING_COUNT(Nullable<int> applicationId, Nullable<byte> applyingCount)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var applyingCountParameter = applyingCount.HasValue ?
                new ObjectParameter("ApplyingCount", applyingCount) :
                new ObjectParameter("ApplyingCount", typeof(byte));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_APPLYING_COUNT", applicationIdParameter, applyingCountParameter);
        }
    
        public virtual int usp_INSERT_APPLICATION_STATUS_HISTORY(Nullable<int> applicationId, Nullable<byte> applicationStatusId, string createdBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var applicationStatusIdParameter = applicationStatusId.HasValue ?
                new ObjectParameter("ApplicationStatusId", applicationStatusId) :
                new ObjectParameter("ApplicationStatusId", typeof(byte));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_APPLICATION_STATUS_HISTORY", applicationIdParameter, applicationStatusIdParameter, createdByParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_Letter_InvoiceStatement_Result> usp_Accounting_Letter_InvoiceStatement(Nullable<int> chipContactPersonID)
        {
            var chipContactPersonIDParameter = chipContactPersonID.HasValue ?
                new ObjectParameter("ChipContactPersonID", chipContactPersonID) :
                new ObjectParameter("ChipContactPersonID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_Letter_InvoiceStatement_Result>("usp_Accounting_Letter_InvoiceStatement", chipContactPersonIDParameter);
        }
    
        public virtual int usp_LANDING_CREATE_NEW_APPLICATION(Nullable<long> contactPersonId, Nullable<byte> subProgramCategoryId, string userId, ObjectParameter newAppId)
        {
            var contactPersonIdParameter = contactPersonId.HasValue ?
                new ObjectParameter("contactPersonId", contactPersonId) :
                new ObjectParameter("contactPersonId", typeof(long));
    
            var subProgramCategoryIdParameter = subProgramCategoryId.HasValue ?
                new ObjectParameter("subProgramCategoryId", subProgramCategoryId) :
                new ObjectParameter("subProgramCategoryId", typeof(byte));
    
            var userIdParameter = userId != null ?
                new ObjectParameter("userId", userId) :
                new ObjectParameter("userId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_LANDING_CREATE_NEW_APPLICATION", contactPersonIdParameter, subProgramCategoryIdParameter, userIdParameter, newAppId);
        }
    
        public virtual int usp_LANDING_GET_STARTING_APPLICATION_ID(Nullable<long> contactPersonId, Nullable<byte> subProgramCategoryId, string userId, ObjectParameter renewAppId, ObjectParameter openAppId, ObjectParameter newAppId)
        {
            var contactPersonIdParameter = contactPersonId.HasValue ?
                new ObjectParameter("contactPersonId", contactPersonId) :
                new ObjectParameter("contactPersonId", typeof(long));
    
            var subProgramCategoryIdParameter = subProgramCategoryId.HasValue ?
                new ObjectParameter("subProgramCategoryId", subProgramCategoryId) :
                new ObjectParameter("subProgramCategoryId", typeof(byte));
    
            var userIdParameter = userId != null ?
                new ObjectParameter("userId", userId) :
                new ObjectParameter("userId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_LANDING_GET_STARTING_APPLICATION_ID", contactPersonIdParameter, subProgramCategoryIdParameter, userIdParameter, renewAppId, openAppId, newAppId);
        }
    
        public virtual ObjectResult<usp_SELECT_APPLOCK_POPUP_ELIGIBILTY_DETAILS_Result> usp_SELECT_APPLOCK_POPUP_ELIGIBILTY_DETAILS(Nullable<long> applicationId, Nullable<long> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_APPLOCK_POPUP_ELIGIBILTY_DETAILS_Result>("usp_SELECT_APPLOCK_POPUP_ELIGIBILTY_DETAILS", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_APPLOCK_POPUP_HOUSEHOLD_DETAILS_Result> usp_SELECT_APPLOCK_POPUP_HOUSEHOLD_DETAILS(Nullable<int> applicationId, Nullable<int> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_APPLOCK_POPUP_HOUSEHOLD_DETAILS_Result>("usp_SELECT_APPLOCK_POPUP_HOUSEHOLD_DETAILS", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_ADDRESS_SUMMARY_BY_PERSON_ID_Result> usp_SELECT_ADDRESS_SUMMARY_BY_PERSON_ID(Nullable<long> pERSON_ID)
        {
            var pERSON_IDParameter = pERSON_ID.HasValue ?
                new ObjectParameter("PERSON_ID", pERSON_ID) :
                new ObjectParameter("PERSON_ID", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ADDRESS_SUMMARY_BY_PERSON_ID_Result>("usp_SELECT_ADDRESS_SUMMARY_BY_PERSON_ID", pERSON_IDParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_RECENT_APPLICATIONS_Result> usp_SELECT_PERSON_RECENT_APPLICATIONS(Nullable<int> personId, Nullable<int> numberOfYears)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var numberOfYearsParameter = numberOfYears.HasValue ?
                new ObjectParameter("NumberOfYears", numberOfYears) :
                new ObjectParameter("NumberOfYears", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_RECENT_APPLICATIONS_Result>("usp_SELECT_PERSON_RECENT_APPLICATIONS", personIdParameter, numberOfYearsParameter);
        }
    
        public virtual ObjectResult<usp_LANDING_PAYEE_ID_SEARCH_Result> usp_LANDING_PAYEE_ID_SEARCH(Nullable<long> payeeId, Nullable<long> personId)
        {
            var payeeIdParameter = payeeId.HasValue ?
                new ObjectParameter("PayeeId", payeeId) :
                new ObjectParameter("PayeeId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_LANDING_PAYEE_ID_SEARCH_Result>("usp_LANDING_PAYEE_ID_SEARCH", payeeIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_LATEST_APP_INFO_Result> usp_SELECT_PERSON_LATEST_APP_INFO(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_LATEST_APP_INFO_Result>("usp_SELECT_PERSON_LATEST_APP_INFO", personIdParameter);
        }
    
        public virtual ObjectResult<usp_LANDING_SELECT_PLASTIC_CARD_INFO_BY_PERSON_ID_Result> usp_LANDING_SELECT_PLASTIC_CARD_INFO_BY_PERSON_ID(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_LANDING_SELECT_PLASTIC_CARD_INFO_BY_PERSON_ID_Result>("usp_LANDING_SELECT_PLASTIC_CARD_INFO_BY_PERSON_ID", personIdParameter);
        }
    
        public virtual int usp_UPDATE_MEDICARE_INFORMATION(Nullable<int> applicationId, Nullable<int> personId, Nullable<bool> attestedHasMedicare, string medicareCardFirstName, string medicareCardLastName, string medicareNumber, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var attestedHasMedicareParameter = attestedHasMedicare.HasValue ?
                new ObjectParameter("AttestedHasMedicare", attestedHasMedicare) :
                new ObjectParameter("AttestedHasMedicare", typeof(bool));
    
            var medicareCardFirstNameParameter = medicareCardFirstName != null ?
                new ObjectParameter("MedicareCardFirstName", medicareCardFirstName) :
                new ObjectParameter("MedicareCardFirstName", typeof(string));
    
            var medicareCardLastNameParameter = medicareCardLastName != null ?
                new ObjectParameter("MedicareCardLastName", medicareCardLastName) :
                new ObjectParameter("MedicareCardLastName", typeof(string));
    
            var medicareNumberParameter = medicareNumber != null ?
                new ObjectParameter("MedicareNumber", medicareNumber) :
                new ObjectParameter("MedicareNumber", typeof(string));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_MEDICARE_INFORMATION", applicationIdParameter, personIdParameter, attestedHasMedicareParameter, medicareCardFirstNameParameter, medicareCardLastNameParameter, medicareNumberParameter, updatedByParameter);
        }
    
        public virtual int usp_UPDATE_PERSON(Nullable<long> personId, Nullable<byte> genderId, Nullable<bool> isUsCitizen, string firstName, string lastName, string middleName, Nullable<byte> suffixId, Nullable<bool> isSSNVerified, Nullable<bool> isCitizenshipVerified, Nullable<long> chipPersonId, Nullable<byte> chipGenderId, Nullable<long> legacyPersonId, string sSN, string updatedBy)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var genderIdParameter = genderId.HasValue ?
                new ObjectParameter("GenderId", genderId) :
                new ObjectParameter("GenderId", typeof(byte));
    
            var isUsCitizenParameter = isUsCitizen.HasValue ?
                new ObjectParameter("IsUsCitizen", isUsCitizen) :
                new ObjectParameter("IsUsCitizen", typeof(bool));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var middleNameParameter = middleName != null ?
                new ObjectParameter("MiddleName", middleName) :
                new ObjectParameter("MiddleName", typeof(string));
    
            var suffixIdParameter = suffixId.HasValue ?
                new ObjectParameter("SuffixId", suffixId) :
                new ObjectParameter("SuffixId", typeof(byte));
    
            var isSSNVerifiedParameter = isSSNVerified.HasValue ?
                new ObjectParameter("IsSSNVerified", isSSNVerified) :
                new ObjectParameter("IsSSNVerified", typeof(bool));
    
            var isCitizenshipVerifiedParameter = isCitizenshipVerified.HasValue ?
                new ObjectParameter("IsCitizenshipVerified", isCitizenshipVerified) :
                new ObjectParameter("IsCitizenshipVerified", typeof(bool));
    
            var chipPersonIdParameter = chipPersonId.HasValue ?
                new ObjectParameter("ChipPersonId", chipPersonId) :
                new ObjectParameter("ChipPersonId", typeof(long));
    
            var chipGenderIdParameter = chipGenderId.HasValue ?
                new ObjectParameter("ChipGenderId", chipGenderId) :
                new ObjectParameter("ChipGenderId", typeof(byte));
    
            var legacyPersonIdParameter = legacyPersonId.HasValue ?
                new ObjectParameter("LegacyPersonId", legacyPersonId) :
                new ObjectParameter("LegacyPersonId", typeof(long));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_PERSON", personIdParameter, genderIdParameter, isUsCitizenParameter, firstNameParameter, lastNameParameter, middleNameParameter, suffixIdParameter, isSSNVerifiedParameter, isCitizenshipVerifiedParameter, chipPersonIdParameter, chipGenderIdParameter, legacyPersonIdParameter, sSNParameter, updatedByParameter);
        }
    
        public virtual int usp_INSERT_MEDICARE_INFORMATION(Nullable<int> applicationId, Nullable<int> personId, Nullable<bool> attestedHasMedicare, string medicareCardFirstName, string medicareCardLastName, string medicareNumber, string insertedBy, ObjectParameter medicareInformationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var attestedHasMedicareParameter = attestedHasMedicare.HasValue ?
                new ObjectParameter("AttestedHasMedicare", attestedHasMedicare) :
                new ObjectParameter("AttestedHasMedicare", typeof(bool));
    
            var medicareCardFirstNameParameter = medicareCardFirstName != null ?
                new ObjectParameter("MedicareCardFirstName", medicareCardFirstName) :
                new ObjectParameter("MedicareCardFirstName", typeof(string));
    
            var medicareCardLastNameParameter = medicareCardLastName != null ?
                new ObjectParameter("MedicareCardLastName", medicareCardLastName) :
                new ObjectParameter("MedicareCardLastName", typeof(string));
    
            var medicareNumberParameter = medicareNumber != null ?
                new ObjectParameter("MedicareNumber", medicareNumber) :
                new ObjectParameter("MedicareNumber", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_MEDICARE_INFORMATION", applicationIdParameter, personIdParameter, attestedHasMedicareParameter, medicareCardFirstNameParameter, medicareCardLastNameParameter, medicareNumberParameter, insertedByParameter, medicareInformationId);
        }
    
        public virtual int usp_INSERT_VETERAN_STATUS(Nullable<int> applicationId, Nullable<int> personId, Nullable<bool> isVeteranOrDependant, string veteranClaimNumber, string veteranFirstName, string veteranMiddleName, string veteranLastName, Nullable<byte> suffixId, string ssn, string relationship, Nullable<bool> hasAppliedSurvivorsImprovement, string appliedInCounty, string insertedBy, ObjectParameter applicationVeteranStatusId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var isVeteranOrDependantParameter = isVeteranOrDependant.HasValue ?
                new ObjectParameter("IsVeteranOrDependant", isVeteranOrDependant) :
                new ObjectParameter("IsVeteranOrDependant", typeof(bool));
    
            var veteranClaimNumberParameter = veteranClaimNumber != null ?
                new ObjectParameter("VeteranClaimNumber", veteranClaimNumber) :
                new ObjectParameter("VeteranClaimNumber", typeof(string));
    
            var veteranFirstNameParameter = veteranFirstName != null ?
                new ObjectParameter("VeteranFirstName", veteranFirstName) :
                new ObjectParameter("VeteranFirstName", typeof(string));
    
            var veteranMiddleNameParameter = veteranMiddleName != null ?
                new ObjectParameter("VeteranMiddleName", veteranMiddleName) :
                new ObjectParameter("VeteranMiddleName", typeof(string));
    
            var veteranLastNameParameter = veteranLastName != null ?
                new ObjectParameter("VeteranLastName", veteranLastName) :
                new ObjectParameter("VeteranLastName", typeof(string));
    
            var suffixIdParameter = suffixId.HasValue ?
                new ObjectParameter("SuffixId", suffixId) :
                new ObjectParameter("SuffixId", typeof(byte));
    
            var ssnParameter = ssn != null ?
                new ObjectParameter("Ssn", ssn) :
                new ObjectParameter("Ssn", typeof(string));
    
            var relationshipParameter = relationship != null ?
                new ObjectParameter("Relationship", relationship) :
                new ObjectParameter("Relationship", typeof(string));
    
            var hasAppliedSurvivorsImprovementParameter = hasAppliedSurvivorsImprovement.HasValue ?
                new ObjectParameter("HasAppliedSurvivorsImprovement", hasAppliedSurvivorsImprovement) :
                new ObjectParameter("HasAppliedSurvivorsImprovement", typeof(bool));
    
            var appliedInCountyParameter = appliedInCounty != null ?
                new ObjectParameter("AppliedInCounty", appliedInCounty) :
                new ObjectParameter("AppliedInCounty", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_VETERAN_STATUS", applicationIdParameter, personIdParameter, isVeteranOrDependantParameter, veteranClaimNumberParameter, veteranFirstNameParameter, veteranMiddleNameParameter, veteranLastNameParameter, suffixIdParameter, ssnParameter, relationshipParameter, hasAppliedSurvivorsImprovementParameter, appliedInCountyParameter, insertedByParameter, applicationVeteranStatusId);
        }
    
        public virtual ObjectResult<usp_SELECT_MEDICARE_INFORMATION_Result> usp_SELECT_MEDICARE_INFORMATION(Nullable<int> applicationId, Nullable<int> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_MEDICARE_INFORMATION_Result>("usp_SELECT_MEDICARE_INFORMATION", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_RESIDENCY_INFORMATION_Result> usp_SELECT_RESIDENCY_INFORMATION(Nullable<int> applicationId, Nullable<int> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_RESIDENCY_INFORMATION_Result>("usp_SELECT_RESIDENCY_INFORMATION", applicationIdParameter, personIdParameter);
        }
    
        public virtual int usp_INSERT_RESIDENCY_INFORMATION(Nullable<int> applicationId, Nullable<int> personId, Nullable<byte> manualVerificationDocumentId, Nullable<bool> hasAppliedOrReceivedSSI, Nullable<bool> isTerminatedSSI, Nullable<System.DateTime> sSITerminationDate, string insertedBy, ObjectParameter applicationDetailId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var manualVerificationDocumentIdParameter = manualVerificationDocumentId.HasValue ?
                new ObjectParameter("ManualVerificationDocumentId", manualVerificationDocumentId) :
                new ObjectParameter("ManualVerificationDocumentId", typeof(byte));
    
            var hasAppliedOrReceivedSSIParameter = hasAppliedOrReceivedSSI.HasValue ?
                new ObjectParameter("HasAppliedOrReceivedSSI", hasAppliedOrReceivedSSI) :
                new ObjectParameter("HasAppliedOrReceivedSSI", typeof(bool));
    
            var isTerminatedSSIParameter = isTerminatedSSI.HasValue ?
                new ObjectParameter("IsTerminatedSSI", isTerminatedSSI) :
                new ObjectParameter("IsTerminatedSSI", typeof(bool));
    
            var sSITerminationDateParameter = sSITerminationDate.HasValue ?
                new ObjectParameter("SSITerminationDate", sSITerminationDate) :
                new ObjectParameter("SSITerminationDate", typeof(System.DateTime));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_RESIDENCY_INFORMATION", applicationIdParameter, personIdParameter, manualVerificationDocumentIdParameter, hasAppliedOrReceivedSSIParameter, isTerminatedSSIParameter, sSITerminationDateParameter, insertedByParameter, applicationDetailId);
        }
    
        public virtual int usp_UPDATE_RESIDENCY_INFORMATION(Nullable<int> applicationId, Nullable<int> personId, Nullable<byte> manualVerDocId, Nullable<bool> hasAppliedOrReceivedSSI, Nullable<bool> isTerminatedSSI, Nullable<System.DateTime> sSITerminationDate, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var manualVerDocIdParameter = manualVerDocId.HasValue ?
                new ObjectParameter("ManualVerDocId", manualVerDocId) :
                new ObjectParameter("ManualVerDocId", typeof(byte));
    
            var hasAppliedOrReceivedSSIParameter = hasAppliedOrReceivedSSI.HasValue ?
                new ObjectParameter("HasAppliedOrReceivedSSI", hasAppliedOrReceivedSSI) :
                new ObjectParameter("HasAppliedOrReceivedSSI", typeof(bool));
    
            var isTerminatedSSIParameter = isTerminatedSSI.HasValue ?
                new ObjectParameter("IsTerminatedSSI", isTerminatedSSI) :
                new ObjectParameter("IsTerminatedSSI", typeof(bool));
    
            var sSITerminationDateParameter = sSITerminationDate.HasValue ?
                new ObjectParameter("SSITerminationDate", sSITerminationDate) :
                new ObjectParameter("SSITerminationDate", typeof(System.DateTime));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_RESIDENCY_INFORMATION", applicationIdParameter, personIdParameter, manualVerDocIdParameter, hasAppliedOrReceivedSSIParameter, isTerminatedSSIParameter, sSITerminationDateParameter, updatedByParameter);
        }
    
        public virtual int usp_UPDATE_VETERAN_STATUS(Nullable<int> applicationId, Nullable<int> personId, Nullable<bool> isVeteranOrDependant, string veteranClaimNumber, string veteranFirstName, string veteranMiddleName, string veteranLastName, Nullable<byte> suffixId, string ssn, string relationship, Nullable<bool> hasAppliedSurvivorsImprovement, string appliedInCounty, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var isVeteranOrDependantParameter = isVeteranOrDependant.HasValue ?
                new ObjectParameter("IsVeteranOrDependant", isVeteranOrDependant) :
                new ObjectParameter("IsVeteranOrDependant", typeof(bool));
    
            var veteranClaimNumberParameter = veteranClaimNumber != null ?
                new ObjectParameter("VeteranClaimNumber", veteranClaimNumber) :
                new ObjectParameter("VeteranClaimNumber", typeof(string));
    
            var veteranFirstNameParameter = veteranFirstName != null ?
                new ObjectParameter("VeteranFirstName", veteranFirstName) :
                new ObjectParameter("VeteranFirstName", typeof(string));
    
            var veteranMiddleNameParameter = veteranMiddleName != null ?
                new ObjectParameter("VeteranMiddleName", veteranMiddleName) :
                new ObjectParameter("VeteranMiddleName", typeof(string));
    
            var veteranLastNameParameter = veteranLastName != null ?
                new ObjectParameter("VeteranLastName", veteranLastName) :
                new ObjectParameter("VeteranLastName", typeof(string));
    
            var suffixIdParameter = suffixId.HasValue ?
                new ObjectParameter("SuffixId", suffixId) :
                new ObjectParameter("SuffixId", typeof(byte));
    
            var ssnParameter = ssn != null ?
                new ObjectParameter("Ssn", ssn) :
                new ObjectParameter("Ssn", typeof(string));
    
            var relationshipParameter = relationship != null ?
                new ObjectParameter("Relationship", relationship) :
                new ObjectParameter("Relationship", typeof(string));
    
            var hasAppliedSurvivorsImprovementParameter = hasAppliedSurvivorsImprovement.HasValue ?
                new ObjectParameter("HasAppliedSurvivorsImprovement", hasAppliedSurvivorsImprovement) :
                new ObjectParameter("HasAppliedSurvivorsImprovement", typeof(bool));
    
            var appliedInCountyParameter = appliedInCounty != null ?
                new ObjectParameter("AppliedInCounty", appliedInCounty) :
                new ObjectParameter("AppliedInCounty", typeof(string));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_VETERAN_STATUS", applicationIdParameter, personIdParameter, isVeteranOrDependantParameter, veteranClaimNumberParameter, veteranFirstNameParameter, veteranMiddleNameParameter, veteranLastNameParameter, suffixIdParameter, ssnParameter, relationshipParameter, hasAppliedSurvivorsImprovementParameter, appliedInCountyParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_NON_MAGI_APPLICATION_INCOME(Nullable<int> nonMagiApplicationIncomeId)
        {
            var nonMagiApplicationIncomeIdParameter = nonMagiApplicationIncomeId.HasValue ?
                new ObjectParameter("NonMagiApplicationIncomeId", nonMagiApplicationIncomeId) :
                new ObjectParameter("NonMagiApplicationIncomeId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_NON_MAGI_APPLICATION_INCOME", nonMagiApplicationIncomeIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_HOUSEHOLD_MEMBERS_Result> usp_SELECT_HOUSEHOLD_MEMBERS(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_HOUSEHOLD_MEMBERS_Result>("usp_SELECT_HOUSEHOLD_MEMBERS", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_SPONSOR_PHONE_INFO_Result> usp_SELECT_SPONSOR_PHONE_INFO(Nullable<int> sponsorId)
        {
            var sponsorIdParameter = sponsorId.HasValue ?
                new ObjectParameter("SponsorId", sponsorId) :
                new ObjectParameter("SponsorId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_SPONSOR_PHONE_INFO_Result>("usp_SELECT_SPONSOR_PHONE_INFO", sponsorIdParameter);
        }
    
        public virtual int usp_SAVE_SPONSOR_PHONE(Nullable<int> sponsorId, Nullable<int> phoneId, string phoneNumber, string phoneExtension, Nullable<int> phoneTypeId, string user)
        {
            var sponsorIdParameter = sponsorId.HasValue ?
                new ObjectParameter("SponsorId", sponsorId) :
                new ObjectParameter("SponsorId", typeof(int));
    
            var phoneIdParameter = phoneId.HasValue ?
                new ObjectParameter("PhoneId", phoneId) :
                new ObjectParameter("PhoneId", typeof(int));
    
            var phoneNumberParameter = phoneNumber != null ?
                new ObjectParameter("PhoneNumber", phoneNumber) :
                new ObjectParameter("PhoneNumber", typeof(string));
    
            var phoneExtensionParameter = phoneExtension != null ?
                new ObjectParameter("PhoneExtension", phoneExtension) :
                new ObjectParameter("PhoneExtension", typeof(string));
    
            var phoneTypeIdParameter = phoneTypeId.HasValue ?
                new ObjectParameter("PhoneTypeId", phoneTypeId) :
                new ObjectParameter("PhoneTypeId", typeof(int));
    
            var userParameter = user != null ?
                new ObjectParameter("User", user) :
                new ObjectParameter("User", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SAVE_SPONSOR_PHONE", sponsorIdParameter, phoneIdParameter, phoneNumberParameter, phoneExtensionParameter, phoneTypeIdParameter, userParameter);
        }
    
        public virtual int usp_DELETE_SPONSOR_PHONE(Nullable<int> phoneId)
        {
            var phoneIdParameter = phoneId.HasValue ?
                new ObjectParameter("PhoneId", phoneId) :
                new ObjectParameter("PhoneId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_SPONSOR_PHONE", phoneIdParameter);
        }
    
        public virtual int usp_DELETE_PHONE_BY_SPONSOR_ID(Nullable<int> sponsorId)
        {
            var sponsorIdParameter = sponsorId.HasValue ?
                new ObjectParameter("SponsorId", sponsorId) :
                new ObjectParameter("SponsorId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_PHONE_BY_SPONSOR_ID", sponsorIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_SPONSOR_Result> usp_SELECT_PERSON_SPONSOR(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_SPONSOR_Result>("usp_SELECT_PERSON_SPONSOR", personIdParameter);
        }
    
        public virtual int usp_GET_EQUIFAX_INCOME(Nullable<long> applicationId, Nullable<long> personId, ObjectParameter equifaxIncome)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GET_EQUIFAX_INCOME", applicationIdParameter, personIdParameter, equifaxIncome);
        }
    
        public virtual ObjectResult<usp_SELECT_SSA_INCOME_Result> usp_SELECT_SSA_INCOME(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_SSA_INCOME_Result>("usp_SELECT_SSA_INCOME", applicationIdParameter);
        }
    
        public virtual int usp_INSERT_NON_MAGI_APPLICATION_INCOME(Nullable<int> applicationId, Nullable<byte> incomeTypeId, string otherIncome, Nullable<byte> applicationTypeId, string claimNumber, Nullable<decimal> applicantAttestedIncome, Nullable<decimal> spouseAttestedIncome, Nullable<decimal> childAttestedIncome, Nullable<decimal> applicantVerifiedIncome, Nullable<decimal> spouseVerifiedIncome, Nullable<decimal> childVerifiedIncome, Nullable<byte> incomeFrequencyId, string insertedBy, ObjectParameter appIncomeId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var incomeTypeIdParameter = incomeTypeId.HasValue ?
                new ObjectParameter("IncomeTypeId", incomeTypeId) :
                new ObjectParameter("IncomeTypeId", typeof(byte));
    
            var otherIncomeParameter = otherIncome != null ?
                new ObjectParameter("OtherIncome", otherIncome) :
                new ObjectParameter("OtherIncome", typeof(string));
    
            var applicationTypeIdParameter = applicationTypeId.HasValue ?
                new ObjectParameter("ApplicationTypeId", applicationTypeId) :
                new ObjectParameter("ApplicationTypeId", typeof(byte));
    
            var claimNumberParameter = claimNumber != null ?
                new ObjectParameter("ClaimNumber", claimNumber) :
                new ObjectParameter("ClaimNumber", typeof(string));
    
            var applicantAttestedIncomeParameter = applicantAttestedIncome.HasValue ?
                new ObjectParameter("ApplicantAttestedIncome", applicantAttestedIncome) :
                new ObjectParameter("ApplicantAttestedIncome", typeof(decimal));
    
            var spouseAttestedIncomeParameter = spouseAttestedIncome.HasValue ?
                new ObjectParameter("SpouseAttestedIncome", spouseAttestedIncome) :
                new ObjectParameter("SpouseAttestedIncome", typeof(decimal));
    
            var childAttestedIncomeParameter = childAttestedIncome.HasValue ?
                new ObjectParameter("ChildAttestedIncome", childAttestedIncome) :
                new ObjectParameter("ChildAttestedIncome", typeof(decimal));
    
            var applicantVerifiedIncomeParameter = applicantVerifiedIncome.HasValue ?
                new ObjectParameter("ApplicantVerifiedIncome", applicantVerifiedIncome) :
                new ObjectParameter("ApplicantVerifiedIncome", typeof(decimal));
    
            var spouseVerifiedIncomeParameter = spouseVerifiedIncome.HasValue ?
                new ObjectParameter("SpouseVerifiedIncome", spouseVerifiedIncome) :
                new ObjectParameter("SpouseVerifiedIncome", typeof(decimal));
    
            var childVerifiedIncomeParameter = childVerifiedIncome.HasValue ?
                new ObjectParameter("ChildVerifiedIncome", childVerifiedIncome) :
                new ObjectParameter("ChildVerifiedIncome", typeof(decimal));
    
            var incomeFrequencyIdParameter = incomeFrequencyId.HasValue ?
                new ObjectParameter("IncomeFrequencyId", incomeFrequencyId) :
                new ObjectParameter("IncomeFrequencyId", typeof(byte));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_NON_MAGI_APPLICATION_INCOME", applicationIdParameter, incomeTypeIdParameter, otherIncomeParameter, applicationTypeIdParameter, claimNumberParameter, applicantAttestedIncomeParameter, spouseAttestedIncomeParameter, childAttestedIncomeParameter, applicantVerifiedIncomeParameter, spouseVerifiedIncomeParameter, childVerifiedIncomeParameter, incomeFrequencyIdParameter, insertedByParameter, appIncomeId);
        }
    
        public virtual ObjectResult<usp_SELECT_GROSS_AMOUNT_OF_NON_MAGI_APPLICATION_INCOME_Result> usp_SELECT_GROSS_AMOUNT_OF_NON_MAGI_APPLICATION_INCOME(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_GROSS_AMOUNT_OF_NON_MAGI_APPLICATION_INCOME_Result>("usp_SELECT_GROSS_AMOUNT_OF_NON_MAGI_APPLICATION_INCOME", applicationIdParameter);
        }
    
        public virtual int usp_DELETE_ASSOCIATED_FAMILY(Nullable<int> mspNoSsnFamilyMemberId, Nullable<int> applicationDetailId)
        {
            var mspNoSsnFamilyMemberIdParameter = mspNoSsnFamilyMemberId.HasValue ?
                new ObjectParameter("MspNoSsnFamilyMemberId", mspNoSsnFamilyMemberId) :
                new ObjectParameter("MspNoSsnFamilyMemberId", typeof(int));
    
            var applicationDetailIdParameter = applicationDetailId.HasValue ?
                new ObjectParameter("ApplicationDetailId", applicationDetailId) :
                new ObjectParameter("ApplicationDetailId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_ASSOCIATED_FAMILY", mspNoSsnFamilyMemberIdParameter, applicationDetailIdParameter);
        }
    
        public virtual int usp_DELETE_SPONSOR_INSERT_NOTES(Nullable<int> sponsorId, Nullable<int> applicationId, string user)
        {
            var sponsorIdParameter = sponsorId.HasValue ?
                new ObjectParameter("SponsorId", sponsorId) :
                new ObjectParameter("SponsorId", typeof(int));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var userParameter = user != null ?
                new ObjectParameter("User", user) :
                new ObjectParameter("User", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_SPONSOR_INSERT_NOTES", sponsorIdParameter, applicationIdParameter, userParameter);
        }
    
        public virtual int usp_SAVE_SPONSOR_INFORMATION(Nullable<int> sponsorId, Nullable<int> personId, Nullable<int> applicationId, string firstName, string middleName, string lastName, Nullable<byte> suffixId, string email, string fax, string addressLine1, string addressLine2, string city, Nullable<short> stateId, string zipcode, Nullable<byte> countyId, Nullable<byte> sponsorTypeId, Nullable<byte> relationshipTypeId, Nullable<byte> sponsorRoleId, Nullable<byte> sponsorLegalAuthId, string user, ObjectParameter sponsorIdReturned)
        {
            var sponsorIdParameter = sponsorId.HasValue ?
                new ObjectParameter("SponsorId", sponsorId) :
                new ObjectParameter("SponsorId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var middleNameParameter = middleName != null ?
                new ObjectParameter("MiddleName", middleName) :
                new ObjectParameter("MiddleName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var suffixIdParameter = suffixId.HasValue ?
                new ObjectParameter("SuffixId", suffixId) :
                new ObjectParameter("SuffixId", typeof(byte));
    
            var emailParameter = email != null ?
                new ObjectParameter("Email", email) :
                new ObjectParameter("Email", typeof(string));
    
            var faxParameter = fax != null ?
                new ObjectParameter("Fax", fax) :
                new ObjectParameter("Fax", typeof(string));
    
            var addressLine1Parameter = addressLine1 != null ?
                new ObjectParameter("AddressLine1", addressLine1) :
                new ObjectParameter("AddressLine1", typeof(string));
    
            var addressLine2Parameter = addressLine2 != null ?
                new ObjectParameter("AddressLine2", addressLine2) :
                new ObjectParameter("AddressLine2", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("City", city) :
                new ObjectParameter("City", typeof(string));
    
            var stateIdParameter = stateId.HasValue ?
                new ObjectParameter("StateId", stateId) :
                new ObjectParameter("StateId", typeof(short));
    
            var zipcodeParameter = zipcode != null ?
                new ObjectParameter("Zipcode", zipcode) :
                new ObjectParameter("Zipcode", typeof(string));
    
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(byte));
    
            var sponsorTypeIdParameter = sponsorTypeId.HasValue ?
                new ObjectParameter("SponsorTypeId", sponsorTypeId) :
                new ObjectParameter("SponsorTypeId", typeof(byte));
    
            var relationshipTypeIdParameter = relationshipTypeId.HasValue ?
                new ObjectParameter("RelationshipTypeId", relationshipTypeId) :
                new ObjectParameter("RelationshipTypeId", typeof(byte));
    
            var sponsorRoleIdParameter = sponsorRoleId.HasValue ?
                new ObjectParameter("SponsorRoleId", sponsorRoleId) :
                new ObjectParameter("SponsorRoleId", typeof(byte));
    
            var sponsorLegalAuthIdParameter = sponsorLegalAuthId.HasValue ?
                new ObjectParameter("SponsorLegalAuthId", sponsorLegalAuthId) :
                new ObjectParameter("SponsorLegalAuthId", typeof(byte));
    
            var userParameter = user != null ?
                new ObjectParameter("User", user) :
                new ObjectParameter("User", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SAVE_SPONSOR_INFORMATION", sponsorIdParameter, personIdParameter, applicationIdParameter, firstNameParameter, middleNameParameter, lastNameParameter, suffixIdParameter, emailParameter, faxParameter, addressLine1Parameter, addressLine2Parameter, cityParameter, stateIdParameter, zipcodeParameter, countyIdParameter, sponsorTypeIdParameter, relationshipTypeIdParameter, sponsorRoleIdParameter, sponsorLegalAuthIdParameter, userParameter, sponsorIdReturned);
        }
    
        public virtual ObjectResult<usp_SELECT_DEEMED_INCOME_Result> usp_SELECT_DEEMED_INCOME(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_DEEMED_INCOME_Result>("usp_SELECT_DEEMED_INCOME", applicationIdParameter);
        }
    
        public virtual int usp_INSERT_APPLICATION_SIGN_SUBMIT(Nullable<int> applicationId, Nullable<bool> medicaidRights, Nullable<bool> coOperateWithAgency, Nullable<bool> isIncarcerated, Nullable<bool> allowRenewal, Nullable<byte> renewalPeriod, Nullable<bool> notifyInformationChanges, Nullable<bool> provideTrueInformation, string householdEsignature, string createdBy, ObjectParameter appSignAndSubmitId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var medicaidRightsParameter = medicaidRights.HasValue ?
                new ObjectParameter("MedicaidRights", medicaidRights) :
                new ObjectParameter("MedicaidRights", typeof(bool));
    
            var coOperateWithAgencyParameter = coOperateWithAgency.HasValue ?
                new ObjectParameter("CoOperateWithAgency", coOperateWithAgency) :
                new ObjectParameter("CoOperateWithAgency", typeof(bool));
    
            var isIncarceratedParameter = isIncarcerated.HasValue ?
                new ObjectParameter("IsIncarcerated", isIncarcerated) :
                new ObjectParameter("IsIncarcerated", typeof(bool));
    
            var allowRenewalParameter = allowRenewal.HasValue ?
                new ObjectParameter("AllowRenewal", allowRenewal) :
                new ObjectParameter("AllowRenewal", typeof(bool));
    
            var renewalPeriodParameter = renewalPeriod.HasValue ?
                new ObjectParameter("RenewalPeriod", renewalPeriod) :
                new ObjectParameter("RenewalPeriod", typeof(byte));
    
            var notifyInformationChangesParameter = notifyInformationChanges.HasValue ?
                new ObjectParameter("NotifyInformationChanges", notifyInformationChanges) :
                new ObjectParameter("NotifyInformationChanges", typeof(bool));
    
            var provideTrueInformationParameter = provideTrueInformation.HasValue ?
                new ObjectParameter("ProvideTrueInformation", provideTrueInformation) :
                new ObjectParameter("ProvideTrueInformation", typeof(bool));
    
            var householdEsignatureParameter = householdEsignature != null ?
                new ObjectParameter("HouseholdEsignature", householdEsignature) :
                new ObjectParameter("HouseholdEsignature", typeof(string));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_APPLICATION_SIGN_SUBMIT", applicationIdParameter, medicaidRightsParameter, coOperateWithAgencyParameter, isIncarceratedParameter, allowRenewalParameter, renewalPeriodParameter, notifyInformationChangesParameter, provideTrueInformationParameter, householdEsignatureParameter, createdByParameter, appSignAndSubmitId);
        }
    
        public virtual int usp_UPDATE_APPLICATION_SIGN_SUBMIT(Nullable<int> applicationId, Nullable<bool> medicaidRights, Nullable<bool> coOperateWithAgency, Nullable<bool> isIncarcerated, Nullable<bool> allowRenewal, Nullable<byte> renewalPeriod, Nullable<bool> notifyInformationChanges, Nullable<bool> provideTrueInformation, string householdEsignature, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var medicaidRightsParameter = medicaidRights.HasValue ?
                new ObjectParameter("MedicaidRights", medicaidRights) :
                new ObjectParameter("MedicaidRights", typeof(bool));
    
            var coOperateWithAgencyParameter = coOperateWithAgency.HasValue ?
                new ObjectParameter("CoOperateWithAgency", coOperateWithAgency) :
                new ObjectParameter("CoOperateWithAgency", typeof(bool));
    
            var isIncarceratedParameter = isIncarcerated.HasValue ?
                new ObjectParameter("IsIncarcerated", isIncarcerated) :
                new ObjectParameter("IsIncarcerated", typeof(bool));
    
            var allowRenewalParameter = allowRenewal.HasValue ?
                new ObjectParameter("AllowRenewal", allowRenewal) :
                new ObjectParameter("AllowRenewal", typeof(bool));
    
            var renewalPeriodParameter = renewalPeriod.HasValue ?
                new ObjectParameter("RenewalPeriod", renewalPeriod) :
                new ObjectParameter("RenewalPeriod", typeof(byte));
    
            var notifyInformationChangesParameter = notifyInformationChanges.HasValue ?
                new ObjectParameter("NotifyInformationChanges", notifyInformationChanges) :
                new ObjectParameter("NotifyInformationChanges", typeof(bool));
    
            var provideTrueInformationParameter = provideTrueInformation.HasValue ?
                new ObjectParameter("ProvideTrueInformation", provideTrueInformation) :
                new ObjectParameter("ProvideTrueInformation", typeof(bool));
    
            var householdEsignatureParameter = householdEsignature != null ?
                new ObjectParameter("HouseholdEsignature", householdEsignature) :
                new ObjectParameter("HouseholdEsignature", typeof(string));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_SIGN_SUBMIT", applicationIdParameter, medicaidRightsParameter, coOperateWithAgencyParameter, isIncarceratedParameter, allowRenewalParameter, renewalPeriodParameter, notifyInformationChangesParameter, provideTrueInformationParameter, householdEsignatureParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_APPLICATION_SIGN_SUBMIT_Result> usp_SELECT_APPLICATION_SIGN_SUBMIT(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_APPLICATION_SIGN_SUBMIT_Result>("usp_SELECT_APPLICATION_SIGN_SUBMIT", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_EQUIFAX_INCOME_Result> usp_SELECT_EQUIFAX_INCOME(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_EQUIFAX_INCOME_Result>("usp_SELECT_EQUIFAX_INCOME", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_CHI_EMPLOYER_NAME_ADDRESS_Result> usp_SELECT_CHI_EMPLOYER_NAME_ADDRESS(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_CHI_EMPLOYER_NAME_ADDRESS_Result>("usp_SELECT_CHI_EMPLOYER_NAME_ADDRESS", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_LANDING_NAME_BASED_SEARCH_Result> usp_LANDING_NAME_BASED_SEARCH(string namesList, string suffix, Nullable<System.DateTime> dOB)
        {
            var namesListParameter = namesList != null ?
                new ObjectParameter("NamesList", namesList) :
                new ObjectParameter("NamesList", typeof(string));
    
            var suffixParameter = suffix != null ?
                new ObjectParameter("Suffix", suffix) :
                new ObjectParameter("Suffix", typeof(string));
    
            var dOBParameter = dOB.HasValue ?
                new ObjectParameter("DOB", dOB) :
                new ObjectParameter("DOB", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_LANDING_NAME_BASED_SEARCH_Result>("usp_LANDING_NAME_BASED_SEARCH", namesListParameter, suffixParameter, dOBParameter);
        }
    
        public virtual int usp_UPSERT_ASSOCIATED_FAMILY(Nullable<int> applicationId, string firstName, string lastName, Nullable<int> age, string emailAddress, Nullable<byte> relationship, string sSN, string phoneNumber, Nullable<System.DateTime> dOB, string medicareNumber, string addressLine1, string addressLine2, string city, Nullable<int> stateId, string zipcode, Nullable<int> countyId, Nullable<bool> isSameAddress, string upsertedBy, ObjectParameter personId, ObjectParameter mspNoSsnFamilyMemberId, ObjectParameter appDetailId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var ageParameter = age.HasValue ?
                new ObjectParameter("Age", age) :
                new ObjectParameter("Age", typeof(int));
    
            var emailAddressParameter = emailAddress != null ?
                new ObjectParameter("EmailAddress", emailAddress) :
                new ObjectParameter("EmailAddress", typeof(string));
    
            var relationshipParameter = relationship.HasValue ?
                new ObjectParameter("Relationship", relationship) :
                new ObjectParameter("Relationship", typeof(byte));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var phoneNumberParameter = phoneNumber != null ?
                new ObjectParameter("PhoneNumber", phoneNumber) :
                new ObjectParameter("PhoneNumber", typeof(string));
    
            var dOBParameter = dOB.HasValue ?
                new ObjectParameter("DOB", dOB) :
                new ObjectParameter("DOB", typeof(System.DateTime));
    
            var medicareNumberParameter = medicareNumber != null ?
                new ObjectParameter("MedicareNumber", medicareNumber) :
                new ObjectParameter("MedicareNumber", typeof(string));
    
            var addressLine1Parameter = addressLine1 != null ?
                new ObjectParameter("AddressLine1", addressLine1) :
                new ObjectParameter("AddressLine1", typeof(string));
    
            var addressLine2Parameter = addressLine2 != null ?
                new ObjectParameter("AddressLine2", addressLine2) :
                new ObjectParameter("AddressLine2", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("City", city) :
                new ObjectParameter("City", typeof(string));
    
            var stateIdParameter = stateId.HasValue ?
                new ObjectParameter("StateId", stateId) :
                new ObjectParameter("StateId", typeof(int));
    
            var zipcodeParameter = zipcode != null ?
                new ObjectParameter("Zipcode", zipcode) :
                new ObjectParameter("Zipcode", typeof(string));
    
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(int));
    
            var isSameAddressParameter = isSameAddress.HasValue ?
                new ObjectParameter("IsSameAddress", isSameAddress) :
                new ObjectParameter("IsSameAddress", typeof(bool));
    
            var upsertedByParameter = upsertedBy != null ?
                new ObjectParameter("UpsertedBy", upsertedBy) :
                new ObjectParameter("UpsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_ASSOCIATED_FAMILY", applicationIdParameter, firstNameParameter, lastNameParameter, ageParameter, emailAddressParameter, relationshipParameter, sSNParameter, phoneNumberParameter, dOBParameter, medicareNumberParameter, addressLine1Parameter, addressLine2Parameter, cityParameter, stateIdParameter, zipcodeParameter, countyIdParameter, isSameAddressParameter, upsertedByParameter, personId, mspNoSsnFamilyMemberId, appDetailId);
        }
    
        public virtual ObjectResult<usp_APPLICATION_SNAPSHOT_APP_INFORMATION_BY_APP_ID_Result> usp_APPLICATION_SNAPSHOT_APP_INFORMATION_BY_APP_ID(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_APPLICATION_SNAPSHOT_APP_INFORMATION_BY_APP_ID_Result>("usp_APPLICATION_SNAPSHOT_APP_INFORMATION_BY_APP_ID", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_NONDECEASED_UNBORNS_BY_MOTHER_ID_Result> usp_SELECT_NONDECEASED_UNBORNS_BY_MOTHER_ID(Nullable<long> motherPersonId)
        {
            var motherPersonIdParameter = motherPersonId.HasValue ?
                new ObjectParameter("MotherPersonId", motherPersonId) :
                new ObjectParameter("MotherPersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_NONDECEASED_UNBORNS_BY_MOTHER_ID_Result>("usp_SELECT_NONDECEASED_UNBORNS_BY_MOTHER_ID", motherPersonIdParameter);
        }
    
        public virtual ObjectResult<usp_LANDING_ADDRESS_Result> usp_LANDING_ADDRESS(string personIds)
        {
            var personIdsParameter = personIds != null ?
                new ObjectParameter("PersonIds", personIds) :
                new ObjectParameter("PersonIds", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_LANDING_ADDRESS_Result>("usp_LANDING_ADDRESS", personIdsParameter);
        }
    
        public virtual int usp_POST_REVIEW_RECEIVED(Nullable<int> applicationId, Nullable<int> personId, string userName, Nullable<System.DateTime> cancelDate)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_POST_REVIEW_RECEIVED", applicationIdParameter, personIdParameter, userNameParameter, cancelDateParameter);
        }
    
        public virtual int usp_UPDATE_APPLICATION_STATUS(Nullable<long> applicationId, Nullable<byte> statusId, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var statusIdParameter = statusId.HasValue ?
                new ObjectParameter("statusId", statusId) :
                new ObjectParameter("statusId", typeof(byte));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("updatedBy", updatedBy) :
                new ObjectParameter("updatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_STATUS", applicationIdParameter, statusIdParameter, updatedByParameter);
        }
    
        public virtual int USP_INSERT_AUTH_REP_ASSISTOR_NOTES(Nullable<int> applicationId, Nullable<int> personId, Nullable<bool> oldHasAuthorizedPerson, Nullable<bool> newHasAuthorizedPerson, string oldAuthorizedName, string newAuthorizedName, string oldAuthorizedAddressLine, string newAuthorizedAddressLine, Nullable<int> oldAuthorizedStateId, Nullable<int> newAuthorizedStateId, string oldAuthorizedZipCode, string newAuthorizedZipCode, Nullable<bool> oldHasAssistor, Nullable<bool> newHasAssistor, string oldAssistorName, string newAssistorName, string oldOrganizationName, string newOrganizationName, string oldOrganizationId, string newOrganizationId, Nullable<bool> oldIsPEDeterminer, Nullable<bool> newIsPEDeterminer, string createdBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var oldHasAuthorizedPersonParameter = oldHasAuthorizedPerson.HasValue ?
                new ObjectParameter("OldHasAuthorizedPerson", oldHasAuthorizedPerson) :
                new ObjectParameter("OldHasAuthorizedPerson", typeof(bool));
    
            var newHasAuthorizedPersonParameter = newHasAuthorizedPerson.HasValue ?
                new ObjectParameter("NewHasAuthorizedPerson", newHasAuthorizedPerson) :
                new ObjectParameter("NewHasAuthorizedPerson", typeof(bool));
    
            var oldAuthorizedNameParameter = oldAuthorizedName != null ?
                new ObjectParameter("OldAuthorizedName", oldAuthorizedName) :
                new ObjectParameter("OldAuthorizedName", typeof(string));
    
            var newAuthorizedNameParameter = newAuthorizedName != null ?
                new ObjectParameter("NewAuthorizedName", newAuthorizedName) :
                new ObjectParameter("NewAuthorizedName", typeof(string));
    
            var oldAuthorizedAddressLineParameter = oldAuthorizedAddressLine != null ?
                new ObjectParameter("OldAuthorizedAddressLine", oldAuthorizedAddressLine) :
                new ObjectParameter("OldAuthorizedAddressLine", typeof(string));
    
            var newAuthorizedAddressLineParameter = newAuthorizedAddressLine != null ?
                new ObjectParameter("NewAuthorizedAddressLine", newAuthorizedAddressLine) :
                new ObjectParameter("NewAuthorizedAddressLine", typeof(string));
    
            var oldAuthorizedStateIdParameter = oldAuthorizedStateId.HasValue ?
                new ObjectParameter("OldAuthorizedStateId", oldAuthorizedStateId) :
                new ObjectParameter("OldAuthorizedStateId", typeof(int));
    
            var newAuthorizedStateIdParameter = newAuthorizedStateId.HasValue ?
                new ObjectParameter("NewAuthorizedStateId", newAuthorizedStateId) :
                new ObjectParameter("NewAuthorizedStateId", typeof(int));
    
            var oldAuthorizedZipCodeParameter = oldAuthorizedZipCode != null ?
                new ObjectParameter("OldAuthorizedZipCode", oldAuthorizedZipCode) :
                new ObjectParameter("OldAuthorizedZipCode", typeof(string));
    
            var newAuthorizedZipCodeParameter = newAuthorizedZipCode != null ?
                new ObjectParameter("NewAuthorizedZipCode", newAuthorizedZipCode) :
                new ObjectParameter("NewAuthorizedZipCode", typeof(string));
    
            var oldHasAssistorParameter = oldHasAssistor.HasValue ?
                new ObjectParameter("OldHasAssistor", oldHasAssistor) :
                new ObjectParameter("OldHasAssistor", typeof(bool));
    
            var newHasAssistorParameter = newHasAssistor.HasValue ?
                new ObjectParameter("NewHasAssistor", newHasAssistor) :
                new ObjectParameter("NewHasAssistor", typeof(bool));
    
            var oldAssistorNameParameter = oldAssistorName != null ?
                new ObjectParameter("OldAssistorName", oldAssistorName) :
                new ObjectParameter("OldAssistorName", typeof(string));
    
            var newAssistorNameParameter = newAssistorName != null ?
                new ObjectParameter("NewAssistorName", newAssistorName) :
                new ObjectParameter("NewAssistorName", typeof(string));
    
            var oldOrganizationNameParameter = oldOrganizationName != null ?
                new ObjectParameter("OldOrganizationName", oldOrganizationName) :
                new ObjectParameter("OldOrganizationName", typeof(string));
    
            var newOrganizationNameParameter = newOrganizationName != null ?
                new ObjectParameter("NewOrganizationName", newOrganizationName) :
                new ObjectParameter("NewOrganizationName", typeof(string));
    
            var oldOrganizationIdParameter = oldOrganizationId != null ?
                new ObjectParameter("OldOrganizationId", oldOrganizationId) :
                new ObjectParameter("OldOrganizationId", typeof(string));
    
            var newOrganizationIdParameter = newOrganizationId != null ?
                new ObjectParameter("NewOrganizationId", newOrganizationId) :
                new ObjectParameter("NewOrganizationId", typeof(string));
    
            var oldIsPEDeterminerParameter = oldIsPEDeterminer.HasValue ?
                new ObjectParameter("OldIsPEDeterminer", oldIsPEDeterminer) :
                new ObjectParameter("OldIsPEDeterminer", typeof(bool));
    
            var newIsPEDeterminerParameter = newIsPEDeterminer.HasValue ?
                new ObjectParameter("NewIsPEDeterminer", newIsPEDeterminer) :
                new ObjectParameter("NewIsPEDeterminer", typeof(bool));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("USP_INSERT_AUTH_REP_ASSISTOR_NOTES", applicationIdParameter, personIdParameter, oldHasAuthorizedPersonParameter, newHasAuthorizedPersonParameter, oldAuthorizedNameParameter, newAuthorizedNameParameter, oldAuthorizedAddressLineParameter, newAuthorizedAddressLineParameter, oldAuthorizedStateIdParameter, newAuthorizedStateIdParameter, oldAuthorizedZipCodeParameter, newAuthorizedZipCodeParameter, oldHasAssistorParameter, newHasAssistorParameter, oldAssistorNameParameter, newAssistorNameParameter, oldOrganizationNameParameter, newOrganizationNameParameter, oldOrganizationIdParameter, newOrganizationIdParameter, oldIsPEDeterminerParameter, newIsPEDeterminerParameter, createdByParameter);
        }
    
        public virtual int usp_SELECT_PERSON_DETAIL_BY_PERSONID(Nullable<long> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_PERSON_DETAIL_BY_PERSONID", personIdParameter);
        }
    
        public virtual int usp_DELETE_NON_MAGI_NO_SSN_PERSON(Nullable<int> applicationNonMagiNoSsnPersonId, Nullable<long> applicationDetailId)
        {
            var applicationNonMagiNoSsnPersonIdParameter = applicationNonMagiNoSsnPersonId.HasValue ?
                new ObjectParameter("ApplicationNonMagiNoSsnPersonId", applicationNonMagiNoSsnPersonId) :
                new ObjectParameter("ApplicationNonMagiNoSsnPersonId", typeof(int));
    
            var applicationDetailIdParameter = applicationDetailId.HasValue ?
                new ObjectParameter("ApplicationDetailId", applicationDetailId) :
                new ObjectParameter("ApplicationDetailId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_NON_MAGI_NO_SSN_PERSON", applicationNonMagiNoSsnPersonIdParameter, applicationDetailIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_APPLICATION_NON_MAGI_NO_SSN_PERSON_Result> usp_SELECT_APPLICATION_NON_MAGI_NO_SSN_PERSON(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_APPLICATION_NON_MAGI_NO_SSN_PERSON_Result>("usp_SELECT_APPLICATION_NON_MAGI_NO_SSN_PERSON", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_NON_MAGI_FAMILY_MEMBERS_Result> usp_SELECT_NON_MAGI_FAMILY_MEMBERS(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_NON_MAGI_FAMILY_MEMBERS_Result>("usp_SELECT_NON_MAGI_FAMILY_MEMBERS", applicationIdParameter);
        }
    
        public virtual int usp_UPSERT_NON_MAGI_NO_SSN_PERSON(Nullable<long> applicationId, string firstName, string middleName, string lastName, Nullable<byte> suffixId, Nullable<int> age, string emailAddress, Nullable<byte> relationshipTypeId, string sSN, string phoneNumber, Nullable<System.DateTime> dOB, string medicareNumber, string addressLine1, string addressLine2, string city, Nullable<int> stateId, string zipcode, Nullable<int> countyId, Nullable<bool> isSameAddress, string upsertedBy, ObjectParameter personId, ObjectParameter applicationNonMagiNoSsnPersonId, ObjectParameter appDetailId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var middleNameParameter = middleName != null ?
                new ObjectParameter("MiddleName", middleName) :
                new ObjectParameter("MiddleName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var suffixIdParameter = suffixId.HasValue ?
                new ObjectParameter("SuffixId", suffixId) :
                new ObjectParameter("SuffixId", typeof(byte));
    
            var ageParameter = age.HasValue ?
                new ObjectParameter("Age", age) :
                new ObjectParameter("Age", typeof(int));
    
            var emailAddressParameter = emailAddress != null ?
                new ObjectParameter("EmailAddress", emailAddress) :
                new ObjectParameter("EmailAddress", typeof(string));
    
            var relationshipTypeIdParameter = relationshipTypeId.HasValue ?
                new ObjectParameter("RelationshipTypeId", relationshipTypeId) :
                new ObjectParameter("RelationshipTypeId", typeof(byte));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var phoneNumberParameter = phoneNumber != null ?
                new ObjectParameter("PhoneNumber", phoneNumber) :
                new ObjectParameter("PhoneNumber", typeof(string));
    
            var dOBParameter = dOB.HasValue ?
                new ObjectParameter("DOB", dOB) :
                new ObjectParameter("DOB", typeof(System.DateTime));
    
            var medicareNumberParameter = medicareNumber != null ?
                new ObjectParameter("MedicareNumber", medicareNumber) :
                new ObjectParameter("MedicareNumber", typeof(string));
    
            var addressLine1Parameter = addressLine1 != null ?
                new ObjectParameter("AddressLine1", addressLine1) :
                new ObjectParameter("AddressLine1", typeof(string));
    
            var addressLine2Parameter = addressLine2 != null ?
                new ObjectParameter("AddressLine2", addressLine2) :
                new ObjectParameter("AddressLine2", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("City", city) :
                new ObjectParameter("City", typeof(string));
    
            var stateIdParameter = stateId.HasValue ?
                new ObjectParameter("StateId", stateId) :
                new ObjectParameter("StateId", typeof(int));
    
            var zipcodeParameter = zipcode != null ?
                new ObjectParameter("Zipcode", zipcode) :
                new ObjectParameter("Zipcode", typeof(string));
    
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(int));
    
            var isSameAddressParameter = isSameAddress.HasValue ?
                new ObjectParameter("IsSameAddress", isSameAddress) :
                new ObjectParameter("IsSameAddress", typeof(bool));
    
            var upsertedByParameter = upsertedBy != null ?
                new ObjectParameter("UpsertedBy", upsertedBy) :
                new ObjectParameter("UpsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_NON_MAGI_NO_SSN_PERSON", applicationIdParameter, firstNameParameter, middleNameParameter, lastNameParameter, suffixIdParameter, ageParameter, emailAddressParameter, relationshipTypeIdParameter, sSNParameter, phoneNumberParameter, dOBParameter, medicareNumberParameter, addressLine1Parameter, addressLine2Parameter, cityParameter, stateIdParameter, zipcodeParameter, countyIdParameter, isSameAddressParameter, upsertedByParameter, personId, applicationNonMagiNoSsnPersonId, appDetailId);
        }
    
        public virtual ObjectResult<usp_SELECT_VETERAN_DETAILS_Result> usp_SELECT_VETERAN_DETAILS(Nullable<int> applicationId, Nullable<int> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_VETERAN_DETAILS_Result>("usp_SELECT_VETERAN_DETAILS", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Application_AIAN_Income_Detail_Result> usp_Select_Application_AIAN_Income_Detail(Nullable<long> applicationIncomeDetailId, Nullable<long> appAIANIncomeSourceId)
        {
            var applicationIncomeDetailIdParameter = applicationIncomeDetailId.HasValue ?
                new ObjectParameter("ApplicationIncomeDetailId", applicationIncomeDetailId) :
                new ObjectParameter("ApplicationIncomeDetailId", typeof(long));
    
            var appAIANIncomeSourceIdParameter = appAIANIncomeSourceId.HasValue ?
                new ObjectParameter("AppAIANIncomeSourceId", appAIANIncomeSourceId) :
                new ObjectParameter("AppAIANIncomeSourceId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Application_AIAN_Income_Detail_Result>("usp_Select_Application_AIAN_Income_Detail", applicationIncomeDetailIdParameter, appAIANIncomeSourceIdParameter);
        }
    
        public virtual ObjectResult<string> usp_SELECT_LATEST_SUB_PROGRAM_CATEGORY_BY_ADDRESS(Nullable<int> pERSON_ID)
        {
            var pERSON_IDParameter = pERSON_ID.HasValue ?
                new ObjectParameter("PERSON_ID", pERSON_ID) :
                new ObjectParameter("PERSON_ID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_SELECT_LATEST_SUB_PROGRAM_CATEGORY_BY_ADDRESS", pERSON_IDParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_NON_US_ADDRESS_Result> usp_SELECT_NON_US_ADDRESS(Nullable<int> pERSON_ID)
        {
            var pERSON_IDParameter = pERSON_ID.HasValue ?
                new ObjectParameter("PERSON_ID", pERSON_ID) :
                new ObjectParameter("PERSON_ID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_NON_US_ADDRESS_Result>("usp_SELECT_NON_US_ADDRESS", pERSON_IDParameter);
        }
    
        public virtual int usp_INSERT_SVES_REQUEST_INFO_BY_PERSON_ID(Nullable<int> personId, string categoryOfAssistance, string requestReason, string titleIIRequest, string titleXVIRequest, string userName)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var categoryOfAssistanceParameter = categoryOfAssistance != null ?
                new ObjectParameter("CategoryOfAssistance", categoryOfAssistance) :
                new ObjectParameter("CategoryOfAssistance", typeof(string));
    
            var requestReasonParameter = requestReason != null ?
                new ObjectParameter("RequestReason", requestReason) :
                new ObjectParameter("RequestReason", typeof(string));
    
            var titleIIRequestParameter = titleIIRequest != null ?
                new ObjectParameter("TitleIIRequest", titleIIRequest) :
                new ObjectParameter("TitleIIRequest", typeof(string));
    
            var titleXVIRequestParameter = titleXVIRequest != null ?
                new ObjectParameter("TitleXVIRequest", titleXVIRequest) :
                new ObjectParameter("TitleXVIRequest", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_SVES_REQUEST_INFO_BY_PERSON_ID", personIdParameter, categoryOfAssistanceParameter, requestReasonParameter, titleIIRequestParameter, titleXVIRequestParameter, userNameParameter);
        }
    
        public virtual int usp_LIS_CREATE_APPLICATION(string sSN, ObjectParameter applicationId, ObjectParameter personId)
        {
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_LIS_CREATE_APPLICATION", sSNParameter, applicationId, personId);
        }
    
        public virtual ObjectResult<usp_SELECT_LIS_RECIPIENTS_Result> usp_SELECT_LIS_RECIPIENTS()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_LIS_RECIPIENTS_Result>("usp_SELECT_LIS_RECIPIENTS");
        }
    
        public virtual ObjectResult<usp_SELECT_NON_MAGI_APPLICATION_INCOME_Result> usp_SELECT_NON_MAGI_APPLICATION_INCOME(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_NON_MAGI_APPLICATION_INCOME_Result>("usp_SELECT_NON_MAGI_APPLICATION_INCOME", applicationIdParameter);
        }
    
        public virtual int usp_UPDATE_APPLICATION_ID_FOR_LIS_RECIPIENT(Nullable<int> lISRecipientId, Nullable<int> applicationId)
        {
            var lISRecipientIdParameter = lISRecipientId.HasValue ?
                new ObjectParameter("LISRecipientId", lISRecipientId) :
                new ObjectParameter("LISRecipientId", typeof(int));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_APPLICATION_ID_FOR_LIS_RECIPIENT", lISRecipientIdParameter, applicationIdParameter);
        }
    
        public virtual int usp_UPDATE_MESSAGE_FOR_LIS_RECIPIENT(Nullable<int> lISRecipientId, string message)
        {
            var lISRecipientIdParameter = lISRecipientId.HasValue ?
                new ObjectParameter("LISRecipientId", lISRecipientId) :
                new ObjectParameter("LISRecipientId", typeof(int));
    
            var messageParameter = message != null ?
                new ObjectParameter("Message", message) :
                new ObjectParameter("Message", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_MESSAGE_FOR_LIS_RECIPIENT", lISRecipientIdParameter, messageParameter);
        }
    
        public virtual int usp_UPDATE_PROCESSING_STATUS_FOR_LIS_RECIPIENT(Nullable<int> lISRecipientId, string status)
        {
            var lISRecipientIdParameter = lISRecipientId.HasValue ?
                new ObjectParameter("LISRecipientId", lISRecipientId) :
                new ObjectParameter("LISRecipientId", typeof(int));
    
            var statusParameter = status != null ?
                new ObjectParameter("Status", status) :
                new ObjectParameter("Status", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_PROCESSING_STATUS_FOR_LIS_RECIPIENT", lISRecipientIdParameter, statusParameter);
        }
    
        public virtual int usp_UPDATE_WORKER_NUMBER(Nullable<long> applicationId, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("updatedBy", updatedBy) :
                new ObjectParameter("updatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_WORKER_NUMBER", applicationIdParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<usp_Accounting_Letter_Type_Result> usp_Accounting_Letter_Type(Nullable<int> letter_Type)
        {
            var letter_TypeParameter = letter_Type.HasValue ?
                new ObjectParameter("Letter_Type", letter_Type) :
                new ObjectParameter("Letter_Type", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Accounting_Letter_Type_Result>("usp_Accounting_Letter_Type", letter_TypeParameter);
        }
    
        public virtual int usp_UPDATE_NON_MAGI_APPLICATION_INCOME(Nullable<int> nonMagiAppIncomeId, Nullable<byte> incomeTypeId, string otherIncome, Nullable<byte> applicationTypeId, string claimNumber, Nullable<decimal> applicantAttestedIncome, Nullable<decimal> spouseAttestedIncome, Nullable<decimal> childAttestedIncome, Nullable<decimal> applicantVerifiedIncome, Nullable<decimal> spouseVerifiedIncome, Nullable<decimal> childVerifiedIncome, Nullable<byte> incomeFrequencyId, string updatedBy)
        {
            var nonMagiAppIncomeIdParameter = nonMagiAppIncomeId.HasValue ?
                new ObjectParameter("NonMagiAppIncomeId", nonMagiAppIncomeId) :
                new ObjectParameter("NonMagiAppIncomeId", typeof(int));
    
            var incomeTypeIdParameter = incomeTypeId.HasValue ?
                new ObjectParameter("IncomeTypeId", incomeTypeId) :
                new ObjectParameter("IncomeTypeId", typeof(byte));
    
            var otherIncomeParameter = otherIncome != null ?
                new ObjectParameter("OtherIncome", otherIncome) :
                new ObjectParameter("OtherIncome", typeof(string));
    
            var applicationTypeIdParameter = applicationTypeId.HasValue ?
                new ObjectParameter("ApplicationTypeId", applicationTypeId) :
                new ObjectParameter("ApplicationTypeId", typeof(byte));
    
            var claimNumberParameter = claimNumber != null ?
                new ObjectParameter("ClaimNumber", claimNumber) :
                new ObjectParameter("ClaimNumber", typeof(string));
    
            var applicantAttestedIncomeParameter = applicantAttestedIncome.HasValue ?
                new ObjectParameter("ApplicantAttestedIncome", applicantAttestedIncome) :
                new ObjectParameter("ApplicantAttestedIncome", typeof(decimal));
    
            var spouseAttestedIncomeParameter = spouseAttestedIncome.HasValue ?
                new ObjectParameter("SpouseAttestedIncome", spouseAttestedIncome) :
                new ObjectParameter("SpouseAttestedIncome", typeof(decimal));
    
            var childAttestedIncomeParameter = childAttestedIncome.HasValue ?
                new ObjectParameter("ChildAttestedIncome", childAttestedIncome) :
                new ObjectParameter("ChildAttestedIncome", typeof(decimal));
    
            var applicantVerifiedIncomeParameter = applicantVerifiedIncome.HasValue ?
                new ObjectParameter("ApplicantVerifiedIncome", applicantVerifiedIncome) :
                new ObjectParameter("ApplicantVerifiedIncome", typeof(decimal));
    
            var spouseVerifiedIncomeParameter = spouseVerifiedIncome.HasValue ?
                new ObjectParameter("SpouseVerifiedIncome", spouseVerifiedIncome) :
                new ObjectParameter("SpouseVerifiedIncome", typeof(decimal));
    
            var childVerifiedIncomeParameter = childVerifiedIncome.HasValue ?
                new ObjectParameter("ChildVerifiedIncome", childVerifiedIncome) :
                new ObjectParameter("ChildVerifiedIncome", typeof(decimal));
    
            var incomeFrequencyIdParameter = incomeFrequencyId.HasValue ?
                new ObjectParameter("IncomeFrequencyId", incomeFrequencyId) :
                new ObjectParameter("IncomeFrequencyId", typeof(byte));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_NON_MAGI_APPLICATION_INCOME", nonMagiAppIncomeIdParameter, incomeTypeIdParameter, otherIncomeParameter, applicationTypeIdParameter, claimNumberParameter, applicantAttestedIncomeParameter, spouseAttestedIncomeParameter, childAttestedIncomeParameter, applicantVerifiedIncomeParameter, spouseVerifiedIncomeParameter, childVerifiedIncomeParameter, incomeFrequencyIdParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<usp_LANDING_SELECT_LTC_DETAILS_Result> usp_LANDING_SELECT_LTC_DETAILS(Nullable<int> pERSON_ID)
        {
            var pERSON_IDParameter = pERSON_ID.HasValue ?
                new ObjectParameter("PERSON_ID", pERSON_ID) :
                new ObjectParameter("PERSON_ID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_LANDING_SELECT_LTC_DETAILS_Result>("usp_LANDING_SELECT_LTC_DETAILS", pERSON_IDParameter);
        }
    
        public virtual int usp_DELETE_ELDERLY_DISABLED_FORMER_SPOUSES(Nullable<long> applicationId, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_ELDERLY_DISABLED_FORMER_SPOUSES", applicationIdParameter, updatedByParameter);
        }
    
        public virtual int usp_DELETE_ELDERLY_DISABLED_SPOUSE(Nullable<long> applicationId, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_ELDERLY_DISABLED_SPOUSE", applicationIdParameter, updatedByParameter);
        }
    
        public virtual int usp_SELECT_APPLICATION_PROPERTY_INFORMATION(Nullable<long> applicationId, ObjectParameter applicantProperty)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_APPLICATION_PROPERTY_INFORMATION", applicationIdParameter, applicantProperty);
        }
    
        public virtual int usp_SELECT_PERSONAL_PROPERTY_INFORMATION(Nullable<long> applicationId, ObjectParameter applicationPersonalPropertyInfo)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_PERSONAL_PROPERTY_INFORMATION", applicationIdParameter, applicationPersonalPropertyInfo);
        }
    
        public virtual int usp_UPSERT_LIFE_INSURANCE_INFORMATION(string applicationInsuranceInfoJson)
        {
            var applicationInsuranceInfoJsonParameter = applicationInsuranceInfoJson != null ?
                new ObjectParameter("ApplicationInsuranceInfoJson", applicationInsuranceInfoJson) :
                new ObjectParameter("ApplicationInsuranceInfoJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_LIFE_INSURANCE_INFORMATION", applicationInsuranceInfoJsonParameter);
        }
    
        public virtual int usp_UPSERT_PERSONAL_PROPERTY_INFORMATION(string applicationPersonalPropertyInfoJson)
        {
            var applicationPersonalPropertyInfoJsonParameter = applicationPersonalPropertyInfoJson != null ?
                new ObjectParameter("ApplicationPersonalPropertyInfoJson", applicationPersonalPropertyInfoJson) :
                new ObjectParameter("ApplicationPersonalPropertyInfoJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_PERSONAL_PROPERTY_INFORMATION", applicationPersonalPropertyInfoJsonParameter);
        }
    
        public virtual int usp_UPSERT_PROPERTY_INFORMATION(string propertyInfoJson)
        {
            var propertyInfoJsonParameter = propertyInfoJson != null ?
                new ObjectParameter("PropertyInfoJson", propertyInfoJson) :
                new ObjectParameter("PropertyInfoJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_PROPERTY_INFORMATION", propertyInfoJsonParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_DETAIL_BY_PERSONID1_Result> usp_SELECT_PERSON_DETAIL_BY_PERSONID1(Nullable<long> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_DETAIL_BY_PERSONID1_Result>("usp_SELECT_PERSON_DETAIL_BY_PERSONID1", personIdParameter);
        }
    
        public virtual int usp_Perform_Patient_Matching_for_Contact_Person(string unknownPersonFN, string unknownPersonLN, Nullable<System.DateTime> unknownPersonDOB, string unknownPersonSsn, string unknownPersonMiddleName, string unknownPersonEmail, string unknownPersonPhone1, string unknownPersonPhone2, string unknownPersonMailAddressLine1, string unknownPersonMailAddressLine2, string unknownPersonMailCity, string unknownPersonMailState, string unknownPersonMailZip, string unknownPersonHomeAddressLine1, string unknownPersonHomeAddressLine2, string unknownPersonHomeCity, string unknownPersonHomeState, string unknownPersonHomeZip, Nullable<byte> usageContext, ObjectParameter existingPersonId)
        {
            var unknownPersonFNParameter = unknownPersonFN != null ?
                new ObjectParameter("unknownPersonFN", unknownPersonFN) :
                new ObjectParameter("unknownPersonFN", typeof(string));
    
            var unknownPersonLNParameter = unknownPersonLN != null ?
                new ObjectParameter("unknownPersonLN", unknownPersonLN) :
                new ObjectParameter("unknownPersonLN", typeof(string));
    
            var unknownPersonDOBParameter = unknownPersonDOB.HasValue ?
                new ObjectParameter("unknownPersonDOB", unknownPersonDOB) :
                new ObjectParameter("unknownPersonDOB", typeof(System.DateTime));
    
            var unknownPersonSsnParameter = unknownPersonSsn != null ?
                new ObjectParameter("unknownPersonSsn", unknownPersonSsn) :
                new ObjectParameter("unknownPersonSsn", typeof(string));
    
            var unknownPersonMiddleNameParameter = unknownPersonMiddleName != null ?
                new ObjectParameter("unknownPersonMiddleName", unknownPersonMiddleName) :
                new ObjectParameter("unknownPersonMiddleName", typeof(string));
    
            var unknownPersonEmailParameter = unknownPersonEmail != null ?
                new ObjectParameter("unknownPersonEmail", unknownPersonEmail) :
                new ObjectParameter("unknownPersonEmail", typeof(string));
    
            var unknownPersonPhone1Parameter = unknownPersonPhone1 != null ?
                new ObjectParameter("unknownPersonPhone1", unknownPersonPhone1) :
                new ObjectParameter("unknownPersonPhone1", typeof(string));
    
            var unknownPersonPhone2Parameter = unknownPersonPhone2 != null ?
                new ObjectParameter("unknownPersonPhone2", unknownPersonPhone2) :
                new ObjectParameter("unknownPersonPhone2", typeof(string));
    
            var unknownPersonMailAddressLine1Parameter = unknownPersonMailAddressLine1 != null ?
                new ObjectParameter("unknownPersonMailAddressLine1", unknownPersonMailAddressLine1) :
                new ObjectParameter("unknownPersonMailAddressLine1", typeof(string));
    
            var unknownPersonMailAddressLine2Parameter = unknownPersonMailAddressLine2 != null ?
                new ObjectParameter("unknownPersonMailAddressLine2", unknownPersonMailAddressLine2) :
                new ObjectParameter("unknownPersonMailAddressLine2", typeof(string));
    
            var unknownPersonMailCityParameter = unknownPersonMailCity != null ?
                new ObjectParameter("unknownPersonMailCity", unknownPersonMailCity) :
                new ObjectParameter("unknownPersonMailCity", typeof(string));
    
            var unknownPersonMailStateParameter = unknownPersonMailState != null ?
                new ObjectParameter("unknownPersonMailState", unknownPersonMailState) :
                new ObjectParameter("unknownPersonMailState", typeof(string));
    
            var unknownPersonMailZipParameter = unknownPersonMailZip != null ?
                new ObjectParameter("unknownPersonMailZip", unknownPersonMailZip) :
                new ObjectParameter("unknownPersonMailZip", typeof(string));
    
            var unknownPersonHomeAddressLine1Parameter = unknownPersonHomeAddressLine1 != null ?
                new ObjectParameter("unknownPersonHomeAddressLine1", unknownPersonHomeAddressLine1) :
                new ObjectParameter("unknownPersonHomeAddressLine1", typeof(string));
    
            var unknownPersonHomeAddressLine2Parameter = unknownPersonHomeAddressLine2 != null ?
                new ObjectParameter("unknownPersonHomeAddressLine2", unknownPersonHomeAddressLine2) :
                new ObjectParameter("unknownPersonHomeAddressLine2", typeof(string));
    
            var unknownPersonHomeCityParameter = unknownPersonHomeCity != null ?
                new ObjectParameter("unknownPersonHomeCity", unknownPersonHomeCity) :
                new ObjectParameter("unknownPersonHomeCity", typeof(string));
    
            var unknownPersonHomeStateParameter = unknownPersonHomeState != null ?
                new ObjectParameter("unknownPersonHomeState", unknownPersonHomeState) :
                new ObjectParameter("unknownPersonHomeState", typeof(string));
    
            var unknownPersonHomeZipParameter = unknownPersonHomeZip != null ?
                new ObjectParameter("unknownPersonHomeZip", unknownPersonHomeZip) :
                new ObjectParameter("unknownPersonHomeZip", typeof(string));
    
            var usageContextParameter = usageContext.HasValue ?
                new ObjectParameter("usageContext", usageContext) :
                new ObjectParameter("usageContext", typeof(byte));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Perform_Patient_Matching_for_Contact_Person", unknownPersonFNParameter, unknownPersonLNParameter, unknownPersonDOBParameter, unknownPersonSsnParameter, unknownPersonMiddleNameParameter, unknownPersonEmailParameter, unknownPersonPhone1Parameter, unknownPersonPhone2Parameter, unknownPersonMailAddressLine1Parameter, unknownPersonMailAddressLine2Parameter, unknownPersonMailCityParameter, unknownPersonMailStateParameter, unknownPersonMailZipParameter, unknownPersonHomeAddressLine1Parameter, unknownPersonHomeAddressLine2Parameter, unknownPersonHomeCityParameter, unknownPersonHomeStateParameter, unknownPersonHomeZipParameter, usageContextParameter, existingPersonId);
        }
    
        public virtual ObjectResult<usp_SELECT_NON_MAGI_PERSON_RACES_Result> usp_SELECT_NON_MAGI_PERSON_RACES(Nullable<long> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_NON_MAGI_PERSON_RACES_Result>("usp_SELECT_NON_MAGI_PERSON_RACES", personIdParameter);
        }
    
        public virtual int usp_UPDATE_NON_MAGI_PERSON_RACES(Nullable<long> personId, Nullable<bool> isWhite, Nullable<bool> isBlack, Nullable<bool> isAmericanIndian, Nullable<bool> isAsian, Nullable<bool> isOther, Nullable<bool> isHispanic, string otherRace, string createdBy)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var isWhiteParameter = isWhite.HasValue ?
                new ObjectParameter("IsWhite", isWhite) :
                new ObjectParameter("IsWhite", typeof(bool));
    
            var isBlackParameter = isBlack.HasValue ?
                new ObjectParameter("IsBlack", isBlack) :
                new ObjectParameter("IsBlack", typeof(bool));
    
            var isAmericanIndianParameter = isAmericanIndian.HasValue ?
                new ObjectParameter("IsAmericanIndian", isAmericanIndian) :
                new ObjectParameter("IsAmericanIndian", typeof(bool));
    
            var isAsianParameter = isAsian.HasValue ?
                new ObjectParameter("IsAsian", isAsian) :
                new ObjectParameter("IsAsian", typeof(bool));
    
            var isOtherParameter = isOther.HasValue ?
                new ObjectParameter("IsOther", isOther) :
                new ObjectParameter("IsOther", typeof(bool));
    
            var isHispanicParameter = isHispanic.HasValue ?
                new ObjectParameter("IsHispanic", isHispanic) :
                new ObjectParameter("IsHispanic", typeof(bool));
    
            var otherRaceParameter = otherRace != null ?
                new ObjectParameter("OtherRace", otherRace) :
                new ObjectParameter("OtherRace", typeof(string));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_NON_MAGI_PERSON_RACES", personIdParameter, isWhiteParameter, isBlackParameter, isAmericanIndianParameter, isAsianParameter, isOtherParameter, isHispanicParameter, otherRaceParameter, createdByParameter);
        }
    
        public virtual int usp_SAVE_ADDRESS_INFORMATION(ObjectParameter aDDRESS_ID, Nullable<int> pERSON_ID, Nullable<byte> aDDRESS_TYPE, string aDDRESS_LINE1, string aDDRESS_LINE2, string cITY, Nullable<short> sTATE_ID, string zIPCODE, Nullable<byte> cOUNTY_ID, Nullable<int> cHIP_ADDRESS_ID, Nullable<short> cHIP_COUNTY_ID, Nullable<short> cHIP_STATE_ID, Nullable<int> nEW_PERSON_ID, Nullable<byte> cHIP_ADDRESS_TYPE, Nullable<int> cHIP_NEW_EMPLOYER_ID, string cREATED_BY, string uPDATED_BY, Nullable<System.DateTime> uPDATED_DATE, Nullable<bool> iS_SAME_ADDERSS, string tag)
        {
            var pERSON_IDParameter = pERSON_ID.HasValue ?
                new ObjectParameter("PERSON_ID", pERSON_ID) :
                new ObjectParameter("PERSON_ID", typeof(int));
    
            var aDDRESS_TYPEParameter = aDDRESS_TYPE.HasValue ?
                new ObjectParameter("ADDRESS_TYPE", aDDRESS_TYPE) :
                new ObjectParameter("ADDRESS_TYPE", typeof(byte));
    
            var aDDRESS_LINE1Parameter = aDDRESS_LINE1 != null ?
                new ObjectParameter("ADDRESS_LINE1", aDDRESS_LINE1) :
                new ObjectParameter("ADDRESS_LINE1", typeof(string));
    
            var aDDRESS_LINE2Parameter = aDDRESS_LINE2 != null ?
                new ObjectParameter("ADDRESS_LINE2", aDDRESS_LINE2) :
                new ObjectParameter("ADDRESS_LINE2", typeof(string));
    
            var cITYParameter = cITY != null ?
                new ObjectParameter("CITY", cITY) :
                new ObjectParameter("CITY", typeof(string));
    
            var sTATE_IDParameter = sTATE_ID.HasValue ?
                new ObjectParameter("STATE_ID", sTATE_ID) :
                new ObjectParameter("STATE_ID", typeof(short));
    
            var zIPCODEParameter = zIPCODE != null ?
                new ObjectParameter("ZIPCODE", zIPCODE) :
                new ObjectParameter("ZIPCODE", typeof(string));
    
            var cOUNTY_IDParameter = cOUNTY_ID.HasValue ?
                new ObjectParameter("COUNTY_ID", cOUNTY_ID) :
                new ObjectParameter("COUNTY_ID", typeof(byte));
    
            var cHIP_ADDRESS_IDParameter = cHIP_ADDRESS_ID.HasValue ?
                new ObjectParameter("CHIP_ADDRESS_ID", cHIP_ADDRESS_ID) :
                new ObjectParameter("CHIP_ADDRESS_ID", typeof(int));
    
            var cHIP_COUNTY_IDParameter = cHIP_COUNTY_ID.HasValue ?
                new ObjectParameter("CHIP_COUNTY_ID", cHIP_COUNTY_ID) :
                new ObjectParameter("CHIP_COUNTY_ID", typeof(short));
    
            var cHIP_STATE_IDParameter = cHIP_STATE_ID.HasValue ?
                new ObjectParameter("CHIP_STATE_ID", cHIP_STATE_ID) :
                new ObjectParameter("CHIP_STATE_ID", typeof(short));
    
            var nEW_PERSON_IDParameter = nEW_PERSON_ID.HasValue ?
                new ObjectParameter("NEW_PERSON_ID", nEW_PERSON_ID) :
                new ObjectParameter("NEW_PERSON_ID", typeof(int));
    
            var cHIP_ADDRESS_TYPEParameter = cHIP_ADDRESS_TYPE.HasValue ?
                new ObjectParameter("CHIP_ADDRESS_TYPE", cHIP_ADDRESS_TYPE) :
                new ObjectParameter("CHIP_ADDRESS_TYPE", typeof(byte));
    
            var cHIP_NEW_EMPLOYER_IDParameter = cHIP_NEW_EMPLOYER_ID.HasValue ?
                new ObjectParameter("CHIP_NEW_EMPLOYER_ID", cHIP_NEW_EMPLOYER_ID) :
                new ObjectParameter("CHIP_NEW_EMPLOYER_ID", typeof(int));
    
            var cREATED_BYParameter = cREATED_BY != null ?
                new ObjectParameter("CREATED_BY", cREATED_BY) :
                new ObjectParameter("CREATED_BY", typeof(string));
    
            var uPDATED_BYParameter = uPDATED_BY != null ?
                new ObjectParameter("UPDATED_BY", uPDATED_BY) :
                new ObjectParameter("UPDATED_BY", typeof(string));
    
            var uPDATED_DATEParameter = uPDATED_DATE.HasValue ?
                new ObjectParameter("UPDATED_DATE", uPDATED_DATE) :
                new ObjectParameter("UPDATED_DATE", typeof(System.DateTime));
    
            var iS_SAME_ADDERSSParameter = iS_SAME_ADDERSS.HasValue ?
                new ObjectParameter("IS_SAME_ADDERSS", iS_SAME_ADDERSS) :
                new ObjectParameter("IS_SAME_ADDERSS", typeof(bool));
    
            var tagParameter = tag != null ?
                new ObjectParameter("tag", tag) :
                new ObjectParameter("tag", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SAVE_ADDRESS_INFORMATION", aDDRESS_ID, pERSON_IDParameter, aDDRESS_TYPEParameter, aDDRESS_LINE1Parameter, aDDRESS_LINE2Parameter, cITYParameter, sTATE_IDParameter, zIPCODEParameter, cOUNTY_IDParameter, cHIP_ADDRESS_IDParameter, cHIP_COUNTY_IDParameter, cHIP_STATE_IDParameter, nEW_PERSON_IDParameter, cHIP_ADDRESS_TYPEParameter, cHIP_NEW_EMPLOYER_IDParameter, cREATED_BYParameter, uPDATED_BYParameter, uPDATED_DATEParameter, iS_SAME_ADDERSSParameter, tagParameter);
        }
    
        public virtual int usp_SELECT_LIFE_INSURANCE_INFORMATION(Nullable<long> applicationId, ObjectParameter applicationInsuranceInfo)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_LIFE_INSURANCE_INFORMATION", applicationIdParameter, applicationInsuranceInfo);
        }
    
        public virtual int usp_SELECT_MEDICARE_BENIFICARY_IDENTIFIER_BY_PERSON_ID(Nullable<long> personId, ObjectParameter mBINumber)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_MEDICARE_BENIFICARY_IDENTIFIER_BY_PERSON_ID", personIdParameter, mBINumber);
        }
    
        public virtual int usp_SELECT_RESOURCE_INFORMATION(Nullable<long> applicationId, ObjectParameter applicantResourceInfo)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_RESOURCE_INFORMATION", applicationIdParameter, applicantResourceInfo);
        }
    
        public virtual int usp_UPSERT_RESOURCE_INFORMATION(string applicantResourceInfoJson)
        {
            var applicantResourceInfoJsonParameter = applicantResourceInfoJson != null ?
                new ObjectParameter("ApplicantResourceInfoJson", applicantResourceInfoJson) :
                new ObjectParameter("ApplicantResourceInfoJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_RESOURCE_INFORMATION", applicantResourceInfoJsonParameter);
        }
    
        public virtual int usp_COPY_ELDERLY_DISABLED_APPLICATION(Nullable<long> appId, string userId, ObjectParameter newAppId)
        {
            var appIdParameter = appId.HasValue ?
                new ObjectParameter("appId", appId) :
                new ObjectParameter("appId", typeof(long));
    
            var userIdParameter = userId != null ?
                new ObjectParameter("userId", userId) :
                new ObjectParameter("userId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_COPY_ELDERLY_DISABLED_APPLICATION", appIdParameter, userIdParameter, newAppId);
        }
    
        public virtual int usp_UPSERT_MEDICAL_INSURANCE_INFORMATION(string applicationMedicalInsuranceInfo)
        {
            var applicationMedicalInsuranceInfoParameter = applicationMedicalInsuranceInfo != null ?
                new ObjectParameter("ApplicationMedicalInsuranceInfo", applicationMedicalInsuranceInfo) :
                new ObjectParameter("ApplicationMedicalInsuranceInfo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_MEDICAL_INSURANCE_INFORMATION", applicationMedicalInsuranceInfoParameter);
        }
    
        public virtual int usp_ELDERLY_DISABLED_ENROLL_COMPLETE_AWARD(Nullable<long> applicationId, Nullable<System.DateTime> startDate, Nullable<System.DateTime> cancelDate, Nullable<byte> stateAidCatId, string createdBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var startDateParameter = startDate.HasValue ?
                new ObjectParameter("StartDate", startDate) :
                new ObjectParameter("StartDate", typeof(System.DateTime));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            var stateAidCatIdParameter = stateAidCatId.HasValue ?
                new ObjectParameter("StateAidCatId", stateAidCatId) :
                new ObjectParameter("StateAidCatId", typeof(byte));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_ELDERLY_DISABLED_ENROLL_COMPLETE_AWARD", applicationIdParameter, startDateParameter, cancelDateParameter, stateAidCatIdParameter, createdByParameter);
        }
    
        public virtual int usp_ELDERLY_DISABLED_ENROLL_COMPLETE_DENY(Nullable<long> applicationId, string denialReasonIds, string createdBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var denialReasonIdsParameter = denialReasonIds != null ?
                new ObjectParameter("DenialReasonIds", denialReasonIds) :
                new ObjectParameter("DenialReasonIds", typeof(string));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_ELDERLY_DISABLED_ENROLL_COMPLETE_DENY", applicationIdParameter, denialReasonIdsParameter, createdByParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_BENDEX_RESPONSE_Result> usp_SELECT_BENDEX_RESPONSE(string sSN)
        {
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_BENDEX_RESPONSE_Result>("usp_SELECT_BENDEX_RESPONSE", sSNParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_SVES_TITLE_XVI_RESPONSE_INFO_Result> usp_SELECT_SVES_TITLE_XVI_RESPONSE_INFO(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_SVES_TITLE_XVI_RESPONSE_INFO_Result>("usp_SELECT_SVES_TITLE_XVI_RESPONSE_INFO", personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_INFO_BY_SSN_OR_MEDICAID_ID_Result> usp_SELECT_PERSON_INFO_BY_SSN_OR_MEDICAID_ID(string sSNorMedId)
        {
            var sSNorMedIdParameter = sSNorMedId != null ?
                new ObjectParameter("SSNorMedId", sSNorMedId) :
                new ObjectParameter("SSNorMedId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_INFO_BY_SSN_OR_MEDICAID_ID_Result>("usp_SELECT_PERSON_INFO_BY_SSN_OR_MEDICAID_ID", sSNorMedIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_TBQ_RESPONSE_Result> usp_SELECT_TBQ_RESPONSE(string sSNorMedId)
        {
            var sSNorMedIdParameter = sSNorMedId != null ?
                new ObjectParameter("SSNorMedId", sSNorMedId) :
                new ObjectParameter("SSNorMedId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_TBQ_RESPONSE_Result>("usp_SELECT_TBQ_RESPONSE", sSNorMedIdParameter);
        }
    
        public virtual int usp_GENERATE_MEDICAID_ID_CHECK_DIGIT(Nullable<long> personId, string updatedBy)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GENERATE_MEDICAID_ID_CHECK_DIGIT", personIdParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_SVES_STANDARD_RESPONSE_INFO_Result> usp_SELECT_SVES_STANDARD_RESPONSE_INFO(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_SVES_STANDARD_RESPONSE_INFO_Result>("usp_SELECT_SVES_STANDARD_RESPONSE_INFO", personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_SVES_TITLE_II_RESPONSE_INFO_Result> usp_SELECT_SVES_TITLE_II_RESPONSE_INFO(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_SVES_TITLE_II_RESPONSE_INFO_Result>("usp_SELECT_SVES_TITLE_II_RESPONSE_INFO", personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_DHR_ELIGIBILITY_DETAIL_Result> usp_SELECT_DHR_ELIGIBILITY_DETAIL(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_DHR_ELIGIBILITY_DETAIL_Result>("usp_SELECT_DHR_ELIGIBILITY_DETAIL", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_STATE_SUPP_CATEGORIES_Result> usp_SELECT_STATE_SUPP_CATEGORIES()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_STATE_SUPP_CATEGORIES_Result>("usp_SELECT_STATE_SUPP_CATEGORIES");
        }
    
        public virtual int usp_UPSERT_DHR_APPLICATION_DETAIL(Nullable<int> applicationId, string parentFirstName, string parentLastName, string workerFirstName, string workerLastName, string countyOffice, string countyOfficePhoneNumber, string countyOfficePhoneNumberExt, string dHRAidCategory, Nullable<short> stateSuppCategoryId, Nullable<System.DateTime> effectiveDate, Nullable<byte> coverageType, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var parentFirstNameParameter = parentFirstName != null ?
                new ObjectParameter("ParentFirstName", parentFirstName) :
                new ObjectParameter("ParentFirstName", typeof(string));
    
            var parentLastNameParameter = parentLastName != null ?
                new ObjectParameter("ParentLastName", parentLastName) :
                new ObjectParameter("ParentLastName", typeof(string));
    
            var workerFirstNameParameter = workerFirstName != null ?
                new ObjectParameter("WorkerFirstName", workerFirstName) :
                new ObjectParameter("WorkerFirstName", typeof(string));
    
            var workerLastNameParameter = workerLastName != null ?
                new ObjectParameter("WorkerLastName", workerLastName) :
                new ObjectParameter("WorkerLastName", typeof(string));
    
            var countyOfficeParameter = countyOffice != null ?
                new ObjectParameter("CountyOffice", countyOffice) :
                new ObjectParameter("CountyOffice", typeof(string));
    
            var countyOfficePhoneNumberParameter = countyOfficePhoneNumber != null ?
                new ObjectParameter("CountyOfficePhoneNumber", countyOfficePhoneNumber) :
                new ObjectParameter("CountyOfficePhoneNumber", typeof(string));
    
            var countyOfficePhoneNumberExtParameter = countyOfficePhoneNumberExt != null ?
                new ObjectParameter("CountyOfficePhoneNumberExt", countyOfficePhoneNumberExt) :
                new ObjectParameter("CountyOfficePhoneNumberExt", typeof(string));
    
            var dHRAidCategoryParameter = dHRAidCategory != null ?
                new ObjectParameter("DHRAidCategory", dHRAidCategory) :
                new ObjectParameter("DHRAidCategory", typeof(string));
    
            var stateSuppCategoryIdParameter = stateSuppCategoryId.HasValue ?
                new ObjectParameter("StateSuppCategoryId", stateSuppCategoryId) :
                new ObjectParameter("StateSuppCategoryId", typeof(short));
    
            var effectiveDateParameter = effectiveDate.HasValue ?
                new ObjectParameter("EffectiveDate", effectiveDate) :
                new ObjectParameter("EffectiveDate", typeof(System.DateTime));
    
            var coverageTypeParameter = coverageType.HasValue ?
                new ObjectParameter("CoverageType", coverageType) :
                new ObjectParameter("CoverageType", typeof(byte));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPSERT_DHR_APPLICATION_DETAIL", applicationIdParameter, parentFirstNameParameter, parentLastNameParameter, workerFirstNameParameter, workerLastNameParameter, countyOfficeParameter, countyOfficePhoneNumberParameter, countyOfficePhoneNumberExtParameter, dHRAidCategoryParameter, stateSuppCategoryIdParameter, effectiveDateParameter, coverageTypeParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_DHR_APPLICATION_INSURANCE_DETAIL_Result> usp_SELECT_DHR_APPLICATION_INSURANCE_DETAIL(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_DHR_APPLICATION_INSURANCE_DETAIL_Result>("usp_SELECT_DHR_APPLICATION_INSURANCE_DETAIL", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL_Result> usp_SELECT_APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL_Result>("usp_SELECT_APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_MEDICAL_INSURANCE_INFORMATION_FOR_SNAPSHOT_Result> usp_SELECT_MEDICAL_INSURANCE_INFORMATION_FOR_SNAPSHOT(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_MEDICAL_INSURANCE_INFORMATION_FOR_SNAPSHOT_Result>("usp_SELECT_MEDICAL_INSURANCE_INFORMATION_FOR_SNAPSHOT", applicationIdParameter);
        }
    
        public virtual int usp_GENERATE_APPLICATION_SNAPSHOT(Nullable<long> applicationId, string createdBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GENERATE_APPLICATION_SNAPSHOT", applicationIdParameter, createdByParameter);
        }
    
        public virtual ObjectResult<usp_GET_RMA_APPLICATION_INCOME_Result> usp_GET_RMA_APPLICATION_INCOME(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_GET_RMA_APPLICATION_INCOME_Result>("usp_GET_RMA_APPLICATION_INCOME", applicationIdParameter);
        }
    
        public virtual int usp_INSERT_ELDERLY_DISABLED_LETTER_DRAFT(Nullable<long> personId, Nullable<long> applicationId, Nullable<int> eDFormTypeId, Nullable<int> eDFormNameId, string letterDraftXml, string createdBy, ObjectParameter eDLetterDraftIdReturn)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var eDFormTypeIdParameter = eDFormTypeId.HasValue ?
                new ObjectParameter("EDFormTypeId", eDFormTypeId) :
                new ObjectParameter("EDFormTypeId", typeof(int));
    
            var eDFormNameIdParameter = eDFormNameId.HasValue ?
                new ObjectParameter("EDFormNameId", eDFormNameId) :
                new ObjectParameter("EDFormNameId", typeof(int));
    
            var letterDraftXmlParameter = letterDraftXml != null ?
                new ObjectParameter("LetterDraftXml", letterDraftXml) :
                new ObjectParameter("LetterDraftXml", typeof(string));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_ELDERLY_DISABLED_LETTER_DRAFT", personIdParameter, applicationIdParameter, eDFormTypeIdParameter, eDFormNameIdParameter, letterDraftXmlParameter, createdByParameter, eDLetterDraftIdReturn);
        }
    
        public virtual ObjectResult<usp_SELECT_ELDERLY_DISABLED_LETTER_DRAFT_Result> usp_SELECT_ELDERLY_DISABLED_LETTER_DRAFT(Nullable<long> eDLetterDraftId)
        {
            var eDLetterDraftIdParameter = eDLetterDraftId.HasValue ?
                new ObjectParameter("EDLetterDraftId", eDLetterDraftId) :
                new ObjectParameter("EDLetterDraftId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ELDERLY_DISABLED_LETTER_DRAFT_Result>("usp_SELECT_ELDERLY_DISABLED_LETTER_DRAFT", eDLetterDraftIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_FINAL_Result> usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_FINAL(Nullable<long> personId, Nullable<long> applicationId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_FINAL_Result>("usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_FINAL", personIdParameter, applicationIdParameter);
        }
    
        public virtual int usp_COPY_INCOME_FOR_RII_RECIPIENT(Nullable<long> oldApplicationId, Nullable<long> newApplicationId, Nullable<decimal> increasePercentage, string user)
        {
            var oldApplicationIdParameter = oldApplicationId.HasValue ?
                new ObjectParameter("OldApplicationId", oldApplicationId) :
                new ObjectParameter("OldApplicationId", typeof(long));
    
            var newApplicationIdParameter = newApplicationId.HasValue ?
                new ObjectParameter("NewApplicationId", newApplicationId) :
                new ObjectParameter("NewApplicationId", typeof(long));
    
            var increasePercentageParameter = increasePercentage.HasValue ?
                new ObjectParameter("IncreasePercentage", increasePercentage) :
                new ObjectParameter("IncreasePercentage", typeof(decimal));
    
            var userParameter = user != null ?
                new ObjectParameter("User", user) :
                new ObjectParameter("User", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_COPY_INCOME_FOR_RII_RECIPIENT", oldApplicationIdParameter, newApplicationIdParameter, increasePercentageParameter, userParameter);
        }
    
        public virtual int usp_UPDATE_MEC_CHECK_FOR_RII_RECIPIENT(Nullable<long> applicationId, Nullable<System.DateTime> date)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var dateParameter = date.HasValue ?
                new ObjectParameter("Date", date) :
                new ObjectParameter("Date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_MEC_CHECK_FOR_RII_RECIPIENT", applicationIdParameter, dateParameter);
        }
    
        public virtual int usp_UPDATE_ELDERLY_DISABLED_LETTER_DRAFT(Nullable<long> eDLetterDraftId, string letterDraftXml, string updatedBy)
        {
            var eDLetterDraftIdParameter = eDLetterDraftId.HasValue ?
                new ObjectParameter("EDLetterDraftId", eDLetterDraftId) :
                new ObjectParameter("EDLetterDraftId", typeof(long));
    
            var letterDraftXmlParameter = letterDraftXml != null ?
                new ObjectParameter("LetterDraftXml", letterDraftXml) :
                new ObjectParameter("LetterDraftXml", typeof(string));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_ELDERLY_DISABLED_LETTER_DRAFT", eDLetterDraftIdParameter, letterDraftXmlParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<Nullable<byte>> usp_GET_COUNTY_ID_BY_ZIPCODE(string zipCode)
        {
            var zipCodeParameter = zipCode != null ?
                new ObjectParameter("ZipCode", zipCode) :
                new ObjectParameter("ZipCode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<byte>>("usp_GET_COUNTY_ID_BY_ZIPCODE", zipCodeParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_DRAFT_Result> usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_DRAFT(Nullable<long> personId, Nullable<long> applicationId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_DRAFT_Result>("usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_DRAFT", personIdParameter, applicationIdParameter);
        }
    
        public virtual int usp_INSERT_ELDERLY_DISABLED_LETTER_FINAL(Nullable<long> personId, Nullable<long> applicationId, Nullable<int> eDFormTypeId, Nullable<int> eDFormNameId, string letterURL, string createdBy, ObjectParameter eDLetterFinalIdReturn)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var eDFormTypeIdParameter = eDFormTypeId.HasValue ?
                new ObjectParameter("EDFormTypeId", eDFormTypeId) :
                new ObjectParameter("EDFormTypeId", typeof(int));
    
            var eDFormNameIdParameter = eDFormNameId.HasValue ?
                new ObjectParameter("EDFormNameId", eDFormNameId) :
                new ObjectParameter("EDFormNameId", typeof(int));
    
            var letterURLParameter = letterURL != null ?
                new ObjectParameter("LetterURL", letterURL) :
                new ObjectParameter("LetterURL", typeof(string));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_ELDERLY_DISABLED_LETTER_FINAL", personIdParameter, applicationIdParameter, eDFormTypeIdParameter, eDFormNameIdParameter, letterURLParameter, createdByParameter, eDLetterFinalIdReturn);
        }
    
        public virtual int usp_DELETE_ELDERLY_DISABLED_LETTER_DRAFT(Nullable<long> eDLetterDraftId, Nullable<long> personId, Nullable<long> applicationId, Nullable<int> edFormTypeId, Nullable<int> edFormNameId)
        {
            var eDLetterDraftIdParameter = eDLetterDraftId.HasValue ?
                new ObjectParameter("EDLetterDraftId", eDLetterDraftId) :
                new ObjectParameter("EDLetterDraftId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var edFormTypeIdParameter = edFormTypeId.HasValue ?
                new ObjectParameter("EdFormTypeId", edFormTypeId) :
                new ObjectParameter("EdFormTypeId", typeof(int));
    
            var edFormNameIdParameter = edFormNameId.HasValue ?
                new ObjectParameter("EdFormNameId", edFormNameId) :
                new ObjectParameter("EdFormNameId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_ELDERLY_DISABLED_LETTER_DRAFT", eDLetterDraftIdParameter, personIdParameter, applicationIdParameter, edFormTypeIdParameter, edFormNameIdParameter);
        }
    
        public virtual int usp_GET_DC_TRACK_CODE(string firstName, string lastName, Nullable<System.DateTime> dOB, string sSN, ObjectParameter dCTrackCode)
        {
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var dOBParameter = dOB.HasValue ?
                new ObjectParameter("DOB", dOB) :
                new ObjectParameter("DOB", typeof(System.DateTime));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GET_DC_TRACK_CODE", firstNameParameter, lastNameParameter, dOBParameter, sSNParameter, dCTrackCode);
        }
    
        public virtual ObjectResult<usp_SELECT_SSI_PERSON_SUSPENSION_Result> usp_SELECT_SSI_PERSON_SUSPENSION(Nullable<long> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("personId", personId) :
                new ObjectParameter("personId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_SSI_PERSON_SUSPENSION_Result>("usp_SELECT_SSI_PERSON_SUSPENSION", personIdParameter);
        }
    
        public virtual int usp_GET_NON_TERMINAL_APPLICATION_BY_CONTACT_PERSON_AND_SUB_PROGRAM_CATEGORY(Nullable<long> personId, Nullable<byte> subProgramCategoryId, ObjectParameter applicationId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var subProgramCategoryIdParameter = subProgramCategoryId.HasValue ?
                new ObjectParameter("SubProgramCategoryId", subProgramCategoryId) :
                new ObjectParameter("SubProgramCategoryId", typeof(byte));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GET_NON_TERMINAL_APPLICATION_BY_CONTACT_PERSON_AND_SUB_PROGRAM_CATEGORY", personIdParameter, subProgramCategoryIdParameter, applicationId);
        }
    
        public virtual int usp_SEARCH_NOTES(Nullable<long> applicationId, string searchText, string noteTypeIds, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, ObjectParameter searchResults)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var searchTextParameter = searchText != null ?
                new ObjectParameter("SearchText", searchText) :
                new ObjectParameter("SearchText", typeof(string));
    
            var noteTypeIdsParameter = noteTypeIds != null ?
                new ObjectParameter("NoteTypeIds", noteTypeIds) :
                new ObjectParameter("NoteTypeIds", typeof(string));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("FromDate", fromDate) :
                new ObjectParameter("FromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("ToDate", toDate) :
                new ObjectParameter("ToDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SEARCH_NOTES", applicationIdParameter, searchTextParameter, noteTypeIdsParameter, fromDateParameter, toDateParameter, searchResults);
        }
    
        public virtual ObjectResult<usp_SELECT_EXPEDITE_FACILITY_PROVIDER_INFO_Result> usp_SELECT_EXPEDITE_FACILITY_PROVIDER_INFO()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_EXPEDITE_FACILITY_PROVIDER_INFO_Result>("usp_SELECT_EXPEDITE_FACILITY_PROVIDER_INFO");
        }
    
        public virtual int usp_INSERT_REF_EXPEDITE_FACILITY_PROVIDER(Nullable<byte> countyId, Nullable<short> stateId, Nullable<byte> districtOfficeId, string facilityName, string addressLine1, string addressLine2, string city, string zipCode, string aNHAHospitalInd, string insertedBy)
        {
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(byte));
    
            var stateIdParameter = stateId.HasValue ?
                new ObjectParameter("StateId", stateId) :
                new ObjectParameter("StateId", typeof(short));
    
            var districtOfficeIdParameter = districtOfficeId.HasValue ?
                new ObjectParameter("DistrictOfficeId", districtOfficeId) :
                new ObjectParameter("DistrictOfficeId", typeof(byte));
    
            var facilityNameParameter = facilityName != null ?
                new ObjectParameter("FacilityName", facilityName) :
                new ObjectParameter("FacilityName", typeof(string));
    
            var addressLine1Parameter = addressLine1 != null ?
                new ObjectParameter("AddressLine1", addressLine1) :
                new ObjectParameter("AddressLine1", typeof(string));
    
            var addressLine2Parameter = addressLine2 != null ?
                new ObjectParameter("AddressLine2", addressLine2) :
                new ObjectParameter("AddressLine2", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("City", city) :
                new ObjectParameter("City", typeof(string));
    
            var zipCodeParameter = zipCode != null ?
                new ObjectParameter("ZipCode", zipCode) :
                new ObjectParameter("ZipCode", typeof(string));
    
            var aNHAHospitalIndParameter = aNHAHospitalInd != null ?
                new ObjectParameter("ANHAHospitalInd", aNHAHospitalInd) :
                new ObjectParameter("ANHAHospitalInd", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_REF_EXPEDITE_FACILITY_PROVIDER", countyIdParameter, stateIdParameter, districtOfficeIdParameter, facilityNameParameter, addressLine1Parameter, addressLine2Parameter, cityParameter, zipCodeParameter, aNHAHospitalIndParameter, insertedByParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_STRING_Result> usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_STRING(string searchText)
        {
            var searchTextParameter = searchText != null ?
                new ObjectParameter("searchText", searchText) :
                new ObjectParameter("searchText", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_STRING_Result>("usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_STRING", searchTextParameter);
        }
    
        public virtual ObjectResult<string> usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_ID(Nullable<int> expediteFacilityId)
        {
            var expediteFacilityIdParameter = expediteFacilityId.HasValue ?
                new ObjectParameter("ExpediteFacilityId", expediteFacilityId) :
                new ObjectParameter("ExpediteFacilityId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_ID", expediteFacilityIdParameter);
        }
    
        public virtual int usp_UPDATE_EXPEDITE_FACILITY_PROVIDER(string expediteFacilityInfoJson)
        {
            var expediteFacilityInfoJsonParameter = expediteFacilityInfoJson != null ?
                new ObjectParameter("ExpediteFacilityInfoJson", expediteFacilityInfoJson) :
                new ObjectParameter("ExpediteFacilityInfoJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_EXPEDITE_FACILITY_PROVIDER", expediteFacilityInfoJsonParameter);
        }
    
        public virtual int usp_DELETE_LIABILITY_SEGMENT(Nullable<long> personLiabilityId, string deletedBy)
        {
            var personLiabilityIdParameter = personLiabilityId.HasValue ?
                new ObjectParameter("PersonLiabilityId", personLiabilityId) :
                new ObjectParameter("PersonLiabilityId", typeof(long));
    
            var deletedByParameter = deletedBy != null ?
                new ObjectParameter("DeletedBy", deletedBy) :
                new ObjectParameter("DeletedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_LIABILITY_SEGMENT", personLiabilityIdParameter, deletedByParameter);
        }
    
        public virtual int usp_DELETE_LIABILITY_SEGMENTS(Nullable<long> personId, Nullable<long> originatingApplicationId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var originatingApplicationIdParameter = originatingApplicationId.HasValue ?
                new ObjectParameter("OriginatingApplicationId", originatingApplicationId) :
                new ObjectParameter("OriginatingApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_DELETE_LIABILITY_SEGMENTS", personIdParameter, originatingApplicationIdParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> usp_UPSERT_WORKER_REMINDER(Nullable<long> workerReminderId, Nullable<byte> districtOfficeId, Nullable<byte> workerNumber, Nullable<long> applicationId, Nullable<long> personId, Nullable<System.DateTime> dueDate, Nullable<short> workerReminderTypeId, string otherReminderTypeDescription, string updatedBy)
        {
            var workerReminderIdParameter = workerReminderId.HasValue ?
                new ObjectParameter("WorkerReminderId", workerReminderId) :
                new ObjectParameter("WorkerReminderId", typeof(long));
    
            var districtOfficeIdParameter = districtOfficeId.HasValue ?
                new ObjectParameter("DistrictOfficeId", districtOfficeId) :
                new ObjectParameter("DistrictOfficeId", typeof(byte));
    
            var workerNumberParameter = workerNumber.HasValue ?
                new ObjectParameter("WorkerNumber", workerNumber) :
                new ObjectParameter("WorkerNumber", typeof(byte));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var dueDateParameter = dueDate.HasValue ?
                new ObjectParameter("DueDate", dueDate) :
                new ObjectParameter("DueDate", typeof(System.DateTime));
    
            var workerReminderTypeIdParameter = workerReminderTypeId.HasValue ?
                new ObjectParameter("WorkerReminderTypeId", workerReminderTypeId) :
                new ObjectParameter("WorkerReminderTypeId", typeof(short));
    
            var otherReminderTypeDescriptionParameter = otherReminderTypeDescription != null ?
                new ObjectParameter("OtherReminderTypeDescription", otherReminderTypeDescription) :
                new ObjectParameter("OtherReminderTypeDescription", typeof(string));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("usp_UPSERT_WORKER_REMINDER", workerReminderIdParameter, districtOfficeIdParameter, workerNumberParameter, applicationIdParameter, personIdParameter, dueDateParameter, workerReminderTypeIdParameter, otherReminderTypeDescriptionParameter, updatedByParameter);
        }
    
        public virtual int usp_SELECT_MEDICAL_INSURANCE_INFORMATION(Nullable<long> applicationId, ObjectParameter applicationMedicalInsuranceInfo)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_MEDICAL_INSURANCE_INFORMATION", applicationIdParameter, applicationMedicalInsuranceInfo);
        }
    
        public virtual int usp_SELECT_TBQ_MEDICARE_PART_D_INFO(Nullable<long> applicationId, ObjectParameter tBQMedicarePartDInfo)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_TBQ_MEDICARE_PART_D_INFO", applicationIdParameter, tBQMedicarePartDInfo);
        }
    
        public virtual ObjectResult<Nullable<long>> usp_COMPLETE_WORKER_REMINDER(Nullable<long> workerReminderId, Nullable<System.DateTime> nextRecurrenceDate, string updatedBy)
        {
            var workerReminderIdParameter = workerReminderId.HasValue ?
                new ObjectParameter("WorkerReminderId", workerReminderId) :
                new ObjectParameter("WorkerReminderId", typeof(long));
    
            var nextRecurrenceDateParameter = nextRecurrenceDate.HasValue ?
                new ObjectParameter("NextRecurrenceDate", nextRecurrenceDate) :
                new ObjectParameter("NextRecurrenceDate", typeof(System.DateTime));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("usp_COMPLETE_WORKER_REMINDER", workerReminderIdParameter, nextRecurrenceDateParameter, updatedByParameter);
        }
    
        public virtual int usp_SELECT_LIABILITY_INFORMATION2(Nullable<long> personId, Nullable<long> personLiabilityId, ObjectParameter liabilityInfo)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var personLiabilityIdParameter = personLiabilityId.HasValue ?
                new ObjectParameter("PersonLiabilityId", personLiabilityId) :
                new ObjectParameter("PersonLiabilityId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_LIABILITY_INFORMATION2", personIdParameter, personLiabilityIdParameter, liabilityInfo);
        }
    
        public virtual ObjectResult<string> usp_UPSERT_LIABILITY_SEGMENT2(string liabilityInfoJson)
        {
            var liabilityInfoJsonParameter = liabilityInfoJson != null ?
                new ObjectParameter("LiabilityInfoJson", liabilityInfoJson) :
                new ObjectParameter("LiabilityInfoJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_UPSERT_LIABILITY_SEGMENT2", liabilityInfoJsonParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> usp_SELECT_WORKER_REMINDER_COUNT(Nullable<byte> districtOfficeCode, Nullable<byte> districtOfficeId, Nullable<byte> workerNumber, Nullable<long> applicationId)
        {
            var districtOfficeCodeParameter = districtOfficeCode.HasValue ?
                new ObjectParameter("DistrictOfficeCode", districtOfficeCode) :
                new ObjectParameter("DistrictOfficeCode", typeof(byte));
    
            var districtOfficeIdParameter = districtOfficeId.HasValue ?
                new ObjectParameter("DistrictOfficeId", districtOfficeId) :
                new ObjectParameter("DistrictOfficeId", typeof(byte));
    
            var workerNumberParameter = workerNumber.HasValue ?
                new ObjectParameter("WorkerNumber", workerNumber) :
                new ObjectParameter("WorkerNumber", typeof(byte));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("usp_SELECT_WORKER_REMINDER_COUNT", districtOfficeCodeParameter, districtOfficeIdParameter, workerNumberParameter, applicationIdParameter);
        }
    
        public virtual int usp_SELECT_LIABILITY_INFORMATION(Nullable<long> personId, Nullable<long> personLiabilityId, ObjectParameter liabilityInfo)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var personLiabilityIdParameter = personLiabilityId.HasValue ?
                new ObjectParameter("PersonLiabilityId", personLiabilityId) :
                new ObjectParameter("PersonLiabilityId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_LIABILITY_INFORMATION", personIdParameter, personLiabilityIdParameter, liabilityInfo);
        }
    
        public virtual ObjectResult<string> usp_UPSERT_LIABILITY_SEGMENT(string liabilityInfoJson)
        {
            var liabilityInfoJsonParameter = liabilityInfoJson != null ?
                new ObjectParameter("LiabilityInfoJson", liabilityInfoJson) :
                new ObjectParameter("LiabilityInfoJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_UPSERT_LIABILITY_SEGMENT", liabilityInfoJsonParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_WORKER_REMINDER_Result> usp_SELECT_WORKER_REMINDER(Nullable<byte> districtOfficeCode, Nullable<byte> workerNumber, Nullable<long> applicationId)
        {
            var districtOfficeCodeParameter = districtOfficeCode.HasValue ?
                new ObjectParameter("DistrictOfficeCode", districtOfficeCode) :
                new ObjectParameter("DistrictOfficeCode", typeof(byte));
    
            var workerNumberParameter = workerNumber.HasValue ?
                new ObjectParameter("WorkerNumber", workerNumber) :
                new ObjectParameter("WorkerNumber", typeof(byte));
    
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_WORKER_REMINDER_Result>("usp_SELECT_WORKER_REMINDER", districtOfficeCodeParameter, workerNumberParameter, applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_GET_WORKERS_BY_DO_CODE_Result> usp_GET_WORKERS_BY_DO_CODE(Nullable<byte> districtOfficeCode)
        {
            var districtOfficeCodeParameter = districtOfficeCode.HasValue ?
                new ObjectParameter("DistrictOfficeCode", districtOfficeCode) :
                new ObjectParameter("DistrictOfficeCode", typeof(byte));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_GET_WORKERS_BY_DO_CODE_Result>("usp_GET_WORKERS_BY_DO_CODE", districtOfficeCodeParameter);
        }
    
        public virtual int usp_INSERT_BANK(Nullable<byte> countyId, Nullable<short> stateId, string bankCode, string bankName, string addressLine1, string addressLine2, string city, string zipcode, string createdBy, ObjectParameter bankId)
        {
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(byte));
    
            var stateIdParameter = stateId.HasValue ?
                new ObjectParameter("StateId", stateId) :
                new ObjectParameter("StateId", typeof(short));
    
            var bankCodeParameter = bankCode != null ?
                new ObjectParameter("BankCode", bankCode) :
                new ObjectParameter("BankCode", typeof(string));
    
            var bankNameParameter = bankName != null ?
                new ObjectParameter("BankName", bankName) :
                new ObjectParameter("BankName", typeof(string));
    
            var addressLine1Parameter = addressLine1 != null ?
                new ObjectParameter("AddressLine1", addressLine1) :
                new ObjectParameter("AddressLine1", typeof(string));
    
            var addressLine2Parameter = addressLine2 != null ?
                new ObjectParameter("AddressLine2", addressLine2) :
                new ObjectParameter("AddressLine2", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("City", city) :
                new ObjectParameter("City", typeof(string));
    
            var zipcodeParameter = zipcode != null ?
                new ObjectParameter("Zipcode", zipcode) :
                new ObjectParameter("Zipcode", typeof(string));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_BANK", countyIdParameter, stateIdParameter, bankCodeParameter, bankNameParameter, addressLine1Parameter, addressLine2Parameter, cityParameter, zipcodeParameter, createdByParameter, bankId);
        }
    
        public virtual int usp_UPDATE_BANK_INFO(Nullable<int> bankId, Nullable<short> stateId, Nullable<byte> countyId, string bankName, string addressLine1, string addressLine2, string city, string zipCode, Nullable<bool> isActive, Nullable<System.DateTime> bankClosedDate, Nullable<System.DateTime> dateLastSent, string updatedBy, Nullable<bool> mergeFlag)
        {
            var bankIdParameter = bankId.HasValue ?
                new ObjectParameter("BankId", bankId) :
                new ObjectParameter("BankId", typeof(int));
    
            var stateIdParameter = stateId.HasValue ?
                new ObjectParameter("StateId", stateId) :
                new ObjectParameter("StateId", typeof(short));
    
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(byte));
    
            var bankNameParameter = bankName != null ?
                new ObjectParameter("BankName", bankName) :
                new ObjectParameter("BankName", typeof(string));
    
            var addressLine1Parameter = addressLine1 != null ?
                new ObjectParameter("AddressLine1", addressLine1) :
                new ObjectParameter("AddressLine1", typeof(string));
    
            var addressLine2Parameter = addressLine2 != null ?
                new ObjectParameter("AddressLine2", addressLine2) :
                new ObjectParameter("AddressLine2", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("City", city) :
                new ObjectParameter("City", typeof(string));
    
            var zipCodeParameter = zipCode != null ?
                new ObjectParameter("ZipCode", zipCode) :
                new ObjectParameter("ZipCode", typeof(string));
    
            var isActiveParameter = isActive.HasValue ?
                new ObjectParameter("IsActive", isActive) :
                new ObjectParameter("IsActive", typeof(bool));
    
            var bankClosedDateParameter = bankClosedDate.HasValue ?
                new ObjectParameter("BankClosedDate", bankClosedDate) :
                new ObjectParameter("BankClosedDate", typeof(System.DateTime));
    
            var dateLastSentParameter = dateLastSent.HasValue ?
                new ObjectParameter("DateLastSent", dateLastSent) :
                new ObjectParameter("DateLastSent", typeof(System.DateTime));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            var mergeFlagParameter = mergeFlag.HasValue ?
                new ObjectParameter("MergeFlag", mergeFlag) :
                new ObjectParameter("MergeFlag", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UPDATE_BANK_INFO", bankIdParameter, stateIdParameter, countyIdParameter, bankNameParameter, addressLine1Parameter, addressLine2Parameter, cityParameter, zipCodeParameter, isActiveParameter, bankClosedDateParameter, dateLastSentParameter, updatedByParameter, mergeFlagParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_BANK_BY_CODE_OR_BY_NAME_Result> usp_SELECT_BANK_BY_CODE_OR_BY_NAME(string searchText)
        {
            var searchTextParameter = searchText != null ?
                new ObjectParameter("searchText", searchText) :
                new ObjectParameter("searchText", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_BANK_BY_CODE_OR_BY_NAME_Result>("usp_SELECT_BANK_BY_CODE_OR_BY_NAME", searchTextParameter);
        }
    
        public virtual ObjectResult<usp_GET_BANK_DATA_Result> usp_GET_BANK_DATA(Nullable<int> bankId)
        {
            var bankIdParameter = bankId.HasValue ?
                new ObjectParameter("BankId", bankId) :
                new ObjectParameter("BankId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_GET_BANK_DATA_Result>("usp_GET_BANK_DATA", bankIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_LATEST_ENROLLMENT_BY_PERSON_ID_Result> usp_SELECT_LATEST_ENROLLMENT_BY_PERSON_ID(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_LATEST_ENROLLMENT_BY_PERSON_ID_Result>("usp_SELECT_LATEST_ENROLLMENT_BY_PERSON_ID", personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_ELDERLY_DISABLED_NON_MAGI_INCOME_Result> usp_SELECT_ELDERLY_DISABLED_NON_MAGI_INCOME(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("applicationId", applicationId) :
                new ObjectParameter("applicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ELDERLY_DISABLED_NON_MAGI_INCOME_Result>("usp_SELECT_ELDERLY_DISABLED_NON_MAGI_INCOME", applicationIdParameter);
        }
    
        public virtual int usp_INSERT_NOTES()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_NOTES");
        }
    
        public virtual ObjectResult<usp_SELECT_MODIFY_PERSON_ENROLLMENT_ENROLLMENTS_Result> usp_SELECT_MODIFY_PERSON_ENROLLMENT_ENROLLMENTS(Nullable<long> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_MODIFY_PERSON_ENROLLMENT_ENROLLMENTS_Result>("usp_SELECT_MODIFY_PERSON_ENROLLMENT_ENROLLMENTS", personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_EXPARTE_APP_BY_PERSON_ID_Result> usp_SELECT_EXPARTE_APP_BY_PERSON_ID(Nullable<long> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_EXPARTE_APP_BY_PERSON_ID_Result>("usp_SELECT_EXPARTE_APP_BY_PERSON_ID", personIdParameter);
        }
    
        public virtual int usp_SET_INSURANCE_SEND_IND_FOR_PERSON_ON_UPDATES(Nullable<int> person_id, string updated_By)
        {
            var person_idParameter = person_id.HasValue ?
                new ObjectParameter("Person_id", person_id) :
                new ObjectParameter("Person_id", typeof(int));
    
            var updated_ByParameter = updated_By != null ?
                new ObjectParameter("Updated_By", updated_By) :
                new ObjectParameter("Updated_By", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SET_INSURANCE_SEND_IND_FOR_PERSON_ON_UPDATES", person_idParameter, updated_ByParameter);
        }
    
        public virtual int usp_SYNC_ELDERLY_DISABLED(Nullable<long> applicationId, Nullable<long> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SYNC_ELDERLY_DISABLED", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_Select_Overlap_Enrollments_Result> usp_Select_Overlap_Enrollments(Nullable<int> pERSON_ID, string startDatesParam, string endDatesParam)
        {
            var pERSON_IDParameter = pERSON_ID.HasValue ?
                new ObjectParameter("PERSON_ID", pERSON_ID) :
                new ObjectParameter("PERSON_ID", typeof(int));
    
            var startDatesParamParameter = startDatesParam != null ?
                new ObjectParameter("StartDatesParam", startDatesParam) :
                new ObjectParameter("StartDatesParam", typeof(string));
    
            var endDatesParamParameter = endDatesParam != null ?
                new ObjectParameter("EndDatesParam", endDatesParam) :
                new ObjectParameter("EndDatesParam", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_Overlap_Enrollments_Result>("usp_Select_Overlap_Enrollments", pERSON_IDParameter, startDatesParamParameter, endDatesParamParameter);
        }
    
        public virtual int usp_Update_Enrollment_Cancel_Date(Nullable<long> applicationId, Nullable<long> personId, Nullable<System.DateTime> cancelDate, Nullable<byte> cancelReasonId, string updatedBy)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            var cancelReasonIdParameter = cancelReasonId.HasValue ?
                new ObjectParameter("CancelReasonId", cancelReasonId) :
                new ObjectParameter("CancelReasonId", typeof(byte));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Update_Enrollment_Cancel_Date", applicationIdParameter, personIdParameter, cancelDateParameter, cancelReasonIdParameter, updatedByParameter);
        }
    
        public virtual ObjectResult<usp_LANDING_SELECT_ELDERLY_DISABLED_DETAILS_BY_PERSON_ID_Result> usp_LANDING_SELECT_ELDERLY_DISABLED_DETAILS_BY_PERSON_ID(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_LANDING_SELECT_ELDERLY_DISABLED_DETAILS_BY_PERSON_ID_Result>("usp_LANDING_SELECT_ELDERLY_DISABLED_DETAILS_BY_PERSON_ID", personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_WORKER_PORTAL_ACCOUNT_Result> usp_SELECT_WORKER_PORTAL_ACCOUNT(string userName, string firstName, string lastName, string siteId, Nullable<bool> accountStatus, Nullable<byte> districtOfficeId, Nullable<byte> countyId, Nullable<long> workerPortalAccountId)
        {
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var siteIdParameter = siteId != null ?
                new ObjectParameter("SiteId", siteId) :
                new ObjectParameter("SiteId", typeof(string));
    
            var accountStatusParameter = accountStatus.HasValue ?
                new ObjectParameter("AccountStatus", accountStatus) :
                new ObjectParameter("AccountStatus", typeof(bool));
    
            var districtOfficeIdParameter = districtOfficeId.HasValue ?
                new ObjectParameter("DistrictOfficeId", districtOfficeId) :
                new ObjectParameter("DistrictOfficeId", typeof(byte));
    
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(byte));
    
            var workerPortalAccountIdParameter = workerPortalAccountId.HasValue ?
                new ObjectParameter("WorkerPortalAccountId", workerPortalAccountId) :
                new ObjectParameter("WorkerPortalAccountId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_WORKER_PORTAL_ACCOUNT_Result>("usp_SELECT_WORKER_PORTAL_ACCOUNT", userNameParameter, firstNameParameter, lastNameParameter, siteIdParameter, accountStatusParameter, districtOfficeIdParameter, countyIdParameter, workerPortalAccountIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_APPLICATION_HISTORY_SEARCH_Result> usp_SELECT_APPLICATION_HISTORY_SEARCH(string name, string sSN, string medicaidNum)
        {
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var medicaidNumParameter = medicaidNum != null ?
                new ObjectParameter("MedicaidNum", medicaidNum) :
                new ObjectParameter("MedicaidNum", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_APPLICATION_HISTORY_SEARCH_Result>("usp_SELECT_APPLICATION_HISTORY_SEARCH", nameParameter, sSNParameter, medicaidNumParameter);
        }
    
        public virtual ObjectResult<usp_LANDING_SELECT_PERSON_DETAILS_BY_PERSON_ID_Result> usp_LANDING_SELECT_PERSON_DETAILS_BY_PERSON_ID(Nullable<int> personId)
        {
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_LANDING_SELECT_PERSON_DETAILS_BY_PERSON_ID_Result>("usp_LANDING_SELECT_PERSON_DETAILS_BY_PERSON_ID", personIdParameter);
        }
    
        public virtual int usp_INSERT_ELDERLY_DISABLED_SPOUSE_INTO_APPLICATION_DETAIL(Nullable<long> applicationId, string createdBy, ObjectParameter appDetailId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var createdByParameter = createdBy != null ?
                new ObjectParameter("CreatedBy", createdBy) :
                new ObjectParameter("CreatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_ELDERLY_DISABLED_SPOUSE_INTO_APPLICATION_DETAIL", applicationIdParameter, createdByParameter, appDetailId);
        }
    
        public virtual int usp_INSERT_SVES_REQUEST_INFO_BY_APP_ID(Nullable<int> applicationId, Nullable<int> personId, string requestReason, string userName)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var requestReasonParameter = requestReason != null ?
                new ObjectParameter("RequestReason", requestReason) :
                new ObjectParameter("RequestReason", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_SVES_REQUEST_INFO_BY_APP_ID", applicationIdParameter, personIdParameter, requestReasonParameter, userNameParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PERSON_WITH_APPLICATION_ID_FROM_PERSON_SSN_Result> usp_SELECT_PERSON_WITH_APPLICATION_ID_FROM_PERSON_SSN(string sSN)
        {
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PERSON_WITH_APPLICATION_ID_FROM_PERSON_SSN_Result>("usp_SELECT_PERSON_WITH_APPLICATION_ID_FROM_PERSON_SSN", sSNParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_ELIGIBILITY_REVIEW_PARAMETERS_Result> usp_SELECT_ELIGIBILITY_REVIEW_PARAMETERS(Nullable<long> applicationId, Nullable<System.DateTime> cancelDate)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var cancelDateParameter = cancelDate.HasValue ?
                new ObjectParameter("CancelDate", cancelDate) :
                new ObjectParameter("CancelDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ELIGIBILITY_REVIEW_PARAMETERS_Result>("usp_SELECT_ELIGIBILITY_REVIEW_PARAMETERS", applicationIdParameter, cancelDateParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_ENROLLMENT_HISTORY_FOR_ED_BY_PERSON_ID_Result> usp_SELECT_ENROLLMENT_HISTORY_FOR_ED_BY_PERSON_ID(Nullable<int> personID)
        {
            var personIDParameter = personID.HasValue ?
                new ObjectParameter("PersonID", personID) :
                new ObjectParameter("PersonID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_ENROLLMENT_HISTORY_FOR_ED_BY_PERSON_ID_Result>("usp_SELECT_ENROLLMENT_HISTORY_FOR_ED_BY_PERSON_ID", personIDParameter);
        }
    
        public virtual ObjectResult<usp_Select_SSI_Application_Representative_With_ApplicationId_Result> usp_Select_SSI_Application_Representative_With_ApplicationId(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Select_SSI_Application_Representative_With_ApplicationId_Result>("usp_Select_SSI_Application_Representative_With_ApplicationId", applicationIdParameter);
        }
    
        public virtual int usp_SAVE_APPLICATION_DETAILS_JIY(Nullable<int> applicationId, Nullable<int> personId, Nullable<bool> has_Medicaid_Card, Nullable<bool> was_In_Foster_Care, Nullable<byte> income_Verification, string income_Verification_Other, Nullable<bool> is_Homeless, Nullable<short> dYSLocation_ID, string facility_Name, string facility_Address_Line1, string facility_Address_Line2, string facility_City, Nullable<byte> facility_State, string facility_Zip, Nullable<byte> facility_County_ID, Nullable<System.DateTime> confinement_Date, Nullable<System.DateTime> release_Date, Nullable<System.DateTime> pre_Release_Date, Nullable<System.DateTime> post_Release_Date, Nullable<System.DateTime> tcm_End_Date, Nullable<byte> committed_County_ID, Nullable<byte> institution_Type, string institution_Type_Other, string facility_Contact_Name, string facility_Contact_Phone, string facility_Other_Phone, string facility_Email, Nullable<byte> appSource, Nullable<byte> appType, Nullable<byte> appStatus, Nullable<System.DateTime> received_Date, Nullable<System.DateTime> entry_Date, Nullable<System.DateTime> determination_Date, string jIY_Worker, string enrolled, Nullable<System.DateTime> complete_Date, string firstName, string middleName, string lastName, Nullable<int> suffixID, Nullable<System.DateTime> dob, string sSN, Nullable<int> gender_ID, Nullable<bool> is_US_Citizen, Nullable<bool> is_Pregnant, Nullable<System.DateTime> expected_Due_Date, Nullable<int> expected_Nbr_Of_Babies, Nullable<decimal> monthly_Income, string mailing_Address_Line1, string mailing_Address_Line2, string mailing_City, Nullable<byte> mailing_State_ID, string mailing_Zip, Nullable<byte> mailing_County_ID, Nullable<bool> is_Home_Address_Same_As_Mailing_Address, string home_Address_Line1, string home_Address_Line2, string home_Address_City, Nullable<byte> home_Address_State_ID, string home_Zip, Nullable<byte> home_County_ID)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            var has_Medicaid_CardParameter = has_Medicaid_Card.HasValue ?
                new ObjectParameter("Has_Medicaid_Card", has_Medicaid_Card) :
                new ObjectParameter("Has_Medicaid_Card", typeof(bool));
    
            var was_In_Foster_CareParameter = was_In_Foster_Care.HasValue ?
                new ObjectParameter("Was_In_Foster_Care", was_In_Foster_Care) :
                new ObjectParameter("Was_In_Foster_Care", typeof(bool));
    
            var income_VerificationParameter = income_Verification.HasValue ?
                new ObjectParameter("Income_Verification", income_Verification) :
                new ObjectParameter("Income_Verification", typeof(byte));
    
            var income_Verification_OtherParameter = income_Verification_Other != null ?
                new ObjectParameter("Income_Verification_Other", income_Verification_Other) :
                new ObjectParameter("Income_Verification_Other", typeof(string));
    
            var is_HomelessParameter = is_Homeless.HasValue ?
                new ObjectParameter("Is_Homeless", is_Homeless) :
                new ObjectParameter("Is_Homeless", typeof(bool));
    
            var dYSLocation_IDParameter = dYSLocation_ID.HasValue ?
                new ObjectParameter("DYSLocation_ID", dYSLocation_ID) :
                new ObjectParameter("DYSLocation_ID", typeof(short));
    
            var facility_NameParameter = facility_Name != null ?
                new ObjectParameter("Facility_Name", facility_Name) :
                new ObjectParameter("Facility_Name", typeof(string));
    
            var facility_Address_Line1Parameter = facility_Address_Line1 != null ?
                new ObjectParameter("Facility_Address_Line1", facility_Address_Line1) :
                new ObjectParameter("Facility_Address_Line1", typeof(string));
    
            var facility_Address_Line2Parameter = facility_Address_Line2 != null ?
                new ObjectParameter("Facility_Address_Line2", facility_Address_Line2) :
                new ObjectParameter("Facility_Address_Line2", typeof(string));
    
            var facility_CityParameter = facility_City != null ?
                new ObjectParameter("Facility_City", facility_City) :
                new ObjectParameter("Facility_City", typeof(string));
    
            var facility_StateParameter = facility_State.HasValue ?
                new ObjectParameter("Facility_State", facility_State) :
                new ObjectParameter("Facility_State", typeof(byte));
    
            var facility_ZipParameter = facility_Zip != null ?
                new ObjectParameter("Facility_Zip", facility_Zip) :
                new ObjectParameter("Facility_Zip", typeof(string));
    
            var facility_County_IDParameter = facility_County_ID.HasValue ?
                new ObjectParameter("Facility_County_ID", facility_County_ID) :
                new ObjectParameter("Facility_County_ID", typeof(byte));
    
            var confinement_DateParameter = confinement_Date.HasValue ?
                new ObjectParameter("Confinement_Date", confinement_Date) :
                new ObjectParameter("Confinement_Date", typeof(System.DateTime));
    
            var release_DateParameter = release_Date.HasValue ?
                new ObjectParameter("Release_Date", release_Date) :
                new ObjectParameter("Release_Date", typeof(System.DateTime));
    
            var pre_Release_DateParameter = pre_Release_Date.HasValue ?
                new ObjectParameter("Pre_Release_Date", pre_Release_Date) :
                new ObjectParameter("Pre_Release_Date", typeof(System.DateTime));
    
            var post_Release_DateParameter = post_Release_Date.HasValue ?
                new ObjectParameter("Post_Release_Date", post_Release_Date) :
                new ObjectParameter("Post_Release_Date", typeof(System.DateTime));
    
            var tcm_End_DateParameter = tcm_End_Date.HasValue ?
                new ObjectParameter("Tcm_End_Date", tcm_End_Date) :
                new ObjectParameter("Tcm_End_Date", typeof(System.DateTime));
    
            var committed_County_IDParameter = committed_County_ID.HasValue ?
                new ObjectParameter("Committed_County_ID", committed_County_ID) :
                new ObjectParameter("Committed_County_ID", typeof(byte));
    
            var institution_TypeParameter = institution_Type.HasValue ?
                new ObjectParameter("Institution_Type", institution_Type) :
                new ObjectParameter("Institution_Type", typeof(byte));
    
            var institution_Type_OtherParameter = institution_Type_Other != null ?
                new ObjectParameter("Institution_Type_Other", institution_Type_Other) :
                new ObjectParameter("Institution_Type_Other", typeof(string));
    
            var facility_Contact_NameParameter = facility_Contact_Name != null ?
                new ObjectParameter("Facility_Contact_Name", facility_Contact_Name) :
                new ObjectParameter("Facility_Contact_Name", typeof(string));
    
            var facility_Contact_PhoneParameter = facility_Contact_Phone != null ?
                new ObjectParameter("Facility_Contact_Phone", facility_Contact_Phone) :
                new ObjectParameter("Facility_Contact_Phone", typeof(string));
    
            var facility_Other_PhoneParameter = facility_Other_Phone != null ?
                new ObjectParameter("Facility_Other_Phone", facility_Other_Phone) :
                new ObjectParameter("Facility_Other_Phone", typeof(string));
    
            var facility_EmailParameter = facility_Email != null ?
                new ObjectParameter("Facility_Email", facility_Email) :
                new ObjectParameter("Facility_Email", typeof(string));
    
            var appSourceParameter = appSource.HasValue ?
                new ObjectParameter("AppSource", appSource) :
                new ObjectParameter("AppSource", typeof(byte));
    
            var appTypeParameter = appType.HasValue ?
                new ObjectParameter("AppType", appType) :
                new ObjectParameter("AppType", typeof(byte));
    
            var appStatusParameter = appStatus.HasValue ?
                new ObjectParameter("AppStatus", appStatus) :
                new ObjectParameter("AppStatus", typeof(byte));
    
            var received_DateParameter = received_Date.HasValue ?
                new ObjectParameter("Received_Date", received_Date) :
                new ObjectParameter("Received_Date", typeof(System.DateTime));
    
            var entry_DateParameter = entry_Date.HasValue ?
                new ObjectParameter("Entry_Date", entry_Date) :
                new ObjectParameter("Entry_Date", typeof(System.DateTime));
    
            var determination_DateParameter = determination_Date.HasValue ?
                new ObjectParameter("Determination_Date", determination_Date) :
                new ObjectParameter("Determination_Date", typeof(System.DateTime));
    
            var jIY_WorkerParameter = jIY_Worker != null ?
                new ObjectParameter("JIY_Worker", jIY_Worker) :
                new ObjectParameter("JIY_Worker", typeof(string));
    
            var enrolledParameter = enrolled != null ?
                new ObjectParameter("Enrolled", enrolled) :
                new ObjectParameter("Enrolled", typeof(string));
    
            var complete_DateParameter = complete_Date.HasValue ?
                new ObjectParameter("Complete_Date", complete_Date) :
                new ObjectParameter("Complete_Date", typeof(System.DateTime));
    
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var middleNameParameter = middleName != null ?
                new ObjectParameter("MiddleName", middleName) :
                new ObjectParameter("MiddleName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var suffixIDParameter = suffixID.HasValue ?
                new ObjectParameter("SuffixID", suffixID) :
                new ObjectParameter("SuffixID", typeof(int));
    
            var dobParameter = dob.HasValue ?
                new ObjectParameter("dob", dob) :
                new ObjectParameter("dob", typeof(System.DateTime));
    
            var sSNParameter = sSN != null ?
                new ObjectParameter("SSN", sSN) :
                new ObjectParameter("SSN", typeof(string));
    
            var gender_IDParameter = gender_ID.HasValue ?
                new ObjectParameter("Gender_ID", gender_ID) :
                new ObjectParameter("Gender_ID", typeof(int));
    
            var is_US_CitizenParameter = is_US_Citizen.HasValue ?
                new ObjectParameter("Is_US_Citizen", is_US_Citizen) :
                new ObjectParameter("Is_US_Citizen", typeof(bool));
    
            var is_PregnantParameter = is_Pregnant.HasValue ?
                new ObjectParameter("Is_Pregnant", is_Pregnant) :
                new ObjectParameter("Is_Pregnant", typeof(bool));
    
            var expected_Due_DateParameter = expected_Due_Date.HasValue ?
                new ObjectParameter("Expected_Due_Date", expected_Due_Date) :
                new ObjectParameter("Expected_Due_Date", typeof(System.DateTime));
    
            var expected_Nbr_Of_BabiesParameter = expected_Nbr_Of_Babies.HasValue ?
                new ObjectParameter("Expected_Nbr_Of_Babies", expected_Nbr_Of_Babies) :
                new ObjectParameter("Expected_Nbr_Of_Babies", typeof(int));
    
            var monthly_IncomeParameter = monthly_Income.HasValue ?
                new ObjectParameter("Monthly_Income", monthly_Income) :
                new ObjectParameter("Monthly_Income", typeof(decimal));
    
            var mailing_Address_Line1Parameter = mailing_Address_Line1 != null ?
                new ObjectParameter("Mailing_Address_Line1", mailing_Address_Line1) :
                new ObjectParameter("Mailing_Address_Line1", typeof(string));
    
            var mailing_Address_Line2Parameter = mailing_Address_Line2 != null ?
                new ObjectParameter("Mailing_Address_Line2", mailing_Address_Line2) :
                new ObjectParameter("Mailing_Address_Line2", typeof(string));
    
            var mailing_CityParameter = mailing_City != null ?
                new ObjectParameter("Mailing_City", mailing_City) :
                new ObjectParameter("Mailing_City", typeof(string));
    
            var mailing_State_IDParameter = mailing_State_ID.HasValue ?
                new ObjectParameter("Mailing_State_ID", mailing_State_ID) :
                new ObjectParameter("Mailing_State_ID", typeof(byte));
    
            var mailing_ZipParameter = mailing_Zip != null ?
                new ObjectParameter("Mailing_Zip", mailing_Zip) :
                new ObjectParameter("Mailing_Zip", typeof(string));
    
            var mailing_County_IDParameter = mailing_County_ID.HasValue ?
                new ObjectParameter("Mailing_County_ID", mailing_County_ID) :
                new ObjectParameter("Mailing_County_ID", typeof(byte));
    
            var is_Home_Address_Same_As_Mailing_AddressParameter = is_Home_Address_Same_As_Mailing_Address.HasValue ?
                new ObjectParameter("Is_Home_Address_Same_As_Mailing_Address", is_Home_Address_Same_As_Mailing_Address) :
                new ObjectParameter("Is_Home_Address_Same_As_Mailing_Address", typeof(bool));
    
            var home_Address_Line1Parameter = home_Address_Line1 != null ?
                new ObjectParameter("Home_Address_Line1", home_Address_Line1) :
                new ObjectParameter("Home_Address_Line1", typeof(string));
    
            var home_Address_Line2Parameter = home_Address_Line2 != null ?
                new ObjectParameter("Home_Address_Line2", home_Address_Line2) :
                new ObjectParameter("Home_Address_Line2", typeof(string));
    
            var home_Address_CityParameter = home_Address_City != null ?
                new ObjectParameter("Home_Address_City", home_Address_City) :
                new ObjectParameter("Home_Address_City", typeof(string));
    
            var home_Address_State_IDParameter = home_Address_State_ID.HasValue ?
                new ObjectParameter("Home_Address_State_ID", home_Address_State_ID) :
                new ObjectParameter("Home_Address_State_ID", typeof(byte));
    
            var home_ZipParameter = home_Zip != null ?
                new ObjectParameter("Home_Zip", home_Zip) :
                new ObjectParameter("Home_Zip", typeof(string));
    
            var home_County_IDParameter = home_County_ID.HasValue ?
                new ObjectParameter("Home_County_ID", home_County_ID) :
                new ObjectParameter("Home_County_ID", typeof(byte));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SAVE_APPLICATION_DETAILS_JIY", applicationIdParameter, personIdParameter, has_Medicaid_CardParameter, was_In_Foster_CareParameter, income_VerificationParameter, income_Verification_OtherParameter, is_HomelessParameter, dYSLocation_IDParameter, facility_NameParameter, facility_Address_Line1Parameter, facility_Address_Line2Parameter, facility_CityParameter, facility_StateParameter, facility_ZipParameter, facility_County_IDParameter, confinement_DateParameter, release_DateParameter, pre_Release_DateParameter, post_Release_DateParameter, tcm_End_DateParameter, committed_County_IDParameter, institution_TypeParameter, institution_Type_OtherParameter, facility_Contact_NameParameter, facility_Contact_PhoneParameter, facility_Other_PhoneParameter, facility_EmailParameter, appSourceParameter, appTypeParameter, appStatusParameter, received_DateParameter, entry_DateParameter, determination_DateParameter, jIY_WorkerParameter, enrolledParameter, complete_DateParameter, firstNameParameter, middleNameParameter, lastNameParameter, suffixIDParameter, dobParameter, sSNParameter, gender_IDParameter, is_US_CitizenParameter, is_PregnantParameter, expected_Due_DateParameter, expected_Nbr_Of_BabiesParameter, monthly_IncomeParameter, mailing_Address_Line1Parameter, mailing_Address_Line2Parameter, mailing_CityParameter, mailing_State_IDParameter, mailing_ZipParameter, mailing_County_IDParameter, is_Home_Address_Same_As_Mailing_AddressParameter, home_Address_Line1Parameter, home_Address_Line2Parameter, home_Address_CityParameter, home_Address_State_IDParameter, home_ZipParameter, home_County_IDParameter);
        }
    
        public virtual int usp_SELECT_APPLICATION_DETAILS_FOR_JIY(Nullable<int> applicationId, Nullable<int> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_APPLICATION_DETAILS_FOR_JIY", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_JIY_RELEASE_DATE_HISTORY_Result> usp_SELECT_JIY_RELEASE_DATE_HISTORY(Nullable<int> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_JIY_RELEASE_DATE_HISTORY_Result>("usp_SELECT_JIY_RELEASE_DATE_HISTORY", applicationIdParameter);
        }
    
        public virtual int usp_GET_ELDERLY_DISABLED_COMMON_LETTER_DATA(Nullable<long> applicationId, Nullable<long> personId, ObjectParameter applicationCountyNumber, ObjectParameter applicationWorkerNumber, ObjectParameter districtOfficeId, ObjectParameter districtOfficeName, ObjectParameter districtOfficePhone, ObjectParameter districtOfficePhone2, ObjectParameter districtOfficeAddress1, ObjectParameter districtOfficeAddress2, ObjectParameter districtOfficeCity, ObjectParameter districtOfficeState, ObjectParameter districtOfficeZipCode, ObjectParameter workerFirstName, ObjectParameter workerMiddleName, ObjectParameter workerLastName, ObjectParameter workerPhoneNumber, ObjectParameter workerPhoneExtension, ObjectParameter workerEmail, ObjectParameter recipientFirstName, ObjectParameter recipientMiddleName, ObjectParameter recipientLastName, ObjectParameter recipientSuffix, ObjectParameter medicaidId, ObjectParameter recipientAddress1, ObjectParameter recipientAddress2, ObjectParameter recipientCity, ObjectParameter recipientState, ObjectParameter recipientZipCode, ObjectParameter isSelfSponsor, ObjectParameter sponsorFirstName, ObjectParameter sponsorMiddleName, ObjectParameter sponsorLastName, ObjectParameter sponsorSuffix, ObjectParameter sponsorAddress1, ObjectParameter sponsorAddress2, ObjectParameter sponsorCity, ObjectParameter sponsorState, ObjectParameter sponsorZipCode, ObjectParameter facilityName, ObjectParameter facilityAddress1, ObjectParameter facilityAddress2, ObjectParameter facilityCity, ObjectParameter facilityState, ObjectParameter facilityZipCode)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GET_ELDERLY_DISABLED_COMMON_LETTER_DATA", applicationIdParameter, personIdParameter, applicationCountyNumber, applicationWorkerNumber, districtOfficeId, districtOfficeName, districtOfficePhone, districtOfficePhone2, districtOfficeAddress1, districtOfficeAddress2, districtOfficeCity, districtOfficeState, districtOfficeZipCode, workerFirstName, workerMiddleName, workerLastName, workerPhoneNumber, workerPhoneExtension, workerEmail, recipientFirstName, recipientMiddleName, recipientLastName, recipientSuffix, medicaidId, recipientAddress1, recipientAddress2, recipientCity, recipientState, recipientZipCode, isSelfSponsor, sponsorFirstName, sponsorMiddleName, sponsorLastName, sponsorSuffix, sponsorAddress1, sponsorAddress2, sponsorCity, sponsorState, sponsorZipCode, facilityName, facilityAddress1, facilityAddress2, facilityCity, facilityState, facilityZipCode);
        }
    
        public virtual ObjectResult<usp_GET_ELDERLY_DISABLED_LIABILITY_LETTER_DATA_Result> usp_GET_ELDERLY_DISABLED_LIABILITY_LETTER_DATA(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_GET_ELDERLY_DISABLED_LIABILITY_LETTER_DATA_Result>("usp_GET_ELDERLY_DISABLED_LIABILITY_LETTER_DATA", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_GET_ELDERLY_DISABLED_TERMINATION_LETTER_DATA_Result> usp_GET_ELDERLY_DISABLED_TERMINATION_LETTER_DATA(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_GET_ELDERLY_DISABLED_TERMINATION_LETTER_DATA_Result>("usp_GET_ELDERLY_DISABLED_TERMINATION_LETTER_DATA", applicationIdParameter);
        }
    
        public virtual ObjectResult<usp_GET_RMA_TERMINATION_LETTER_DATA_Result> usp_GET_RMA_TERMINATION_LETTER_DATA(Nullable<long> applicationId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_GET_RMA_TERMINATION_LETTER_DATA_Result>("usp_GET_RMA_TERMINATION_LETTER_DATA", applicationIdParameter);
        }
    
        public virtual int usp_GET_ELDERLY_DISABLED_DENIAL_LETTER_DATA(Nullable<long> applicationId, ObjectParameter denialLetterData)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GET_ELDERLY_DISABLED_DENIAL_LETTER_DATA", applicationIdParameter, denialLetterData);
        }
    
        public virtual int usp_INSERT_DYS_FACILITY(Nullable<byte> countyId, Nullable<short> stateId, string dYSFacilityName, string addressLine1, string addressLine2, string city, string zipCode, string insertedBy)
        {
            var countyIdParameter = countyId.HasValue ?
                new ObjectParameter("CountyId", countyId) :
                new ObjectParameter("CountyId", typeof(byte));
    
            var stateIdParameter = stateId.HasValue ?
                new ObjectParameter("StateId", stateId) :
                new ObjectParameter("StateId", typeof(short));
    
            var dYSFacilityNameParameter = dYSFacilityName != null ?
                new ObjectParameter("DYSFacilityName", dYSFacilityName) :
                new ObjectParameter("DYSFacilityName", typeof(string));
    
            var addressLine1Parameter = addressLine1 != null ?
                new ObjectParameter("AddressLine1", addressLine1) :
                new ObjectParameter("AddressLine1", typeof(string));
    
            var addressLine2Parameter = addressLine2 != null ?
                new ObjectParameter("AddressLine2", addressLine2) :
                new ObjectParameter("AddressLine2", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("City", city) :
                new ObjectParameter("City", typeof(string));
    
            var zipCodeParameter = zipCode != null ?
                new ObjectParameter("ZipCode", zipCode) :
                new ObjectParameter("ZipCode", typeof(string));
    
            var insertedByParameter = insertedBy != null ?
                new ObjectParameter("InsertedBy", insertedBy) :
                new ObjectParameter("InsertedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_INSERT_DYS_FACILITY", countyIdParameter, stateIdParameter, dYSFacilityNameParameter, addressLine1Parameter, addressLine2Parameter, cityParameter, zipCodeParameter, insertedByParameter);
        }
    
        public virtual int usp_SELECT_APPLICATION_DETAILS_FOR_PEP(Nullable<int> applicationId, Nullable<int> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(int));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SELECT_APPLICATION_DETAILS_FOR_PEP", applicationIdParameter, personIdParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT_Result> usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT(string inputText)
        {
            var inputTextParameter = inputText != null ?
                new ObjectParameter("inputText", inputText) :
                new ObjectParameter("inputText", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT_Result>("usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT", inputTextParameter);
        }
    
        public virtual int usp_SAVE_PEP_PROVIDER(Nullable<int> providerId, string providerName, string providerNpi, string medicaidBillingNo, string providerAddressLine1, string providerAddressLine2, string providerCity, Nullable<short> providerStateId, string providerZip, Nullable<byte> providerCountyId, Nullable<int> representativeId, string representativeName, string representativeEmail, string representativePhone, string facilityOwnership, Nullable<int> providerStatusId, Nullable<System.DateTime> providerStatusDate, Nullable<int> providerEligType)
        {
            var providerIdParameter = providerId.HasValue ?
                new ObjectParameter("ProviderId", providerId) :
                new ObjectParameter("ProviderId", typeof(int));
    
            var providerNameParameter = providerName != null ?
                new ObjectParameter("ProviderName", providerName) :
                new ObjectParameter("ProviderName", typeof(string));
    
            var providerNpiParameter = providerNpi != null ?
                new ObjectParameter("ProviderNpi", providerNpi) :
                new ObjectParameter("ProviderNpi", typeof(string));
    
            var medicaidBillingNoParameter = medicaidBillingNo != null ?
                new ObjectParameter("MedicaidBillingNo", medicaidBillingNo) :
                new ObjectParameter("MedicaidBillingNo", typeof(string));
    
            var providerAddressLine1Parameter = providerAddressLine1 != null ?
                new ObjectParameter("ProviderAddressLine1", providerAddressLine1) :
                new ObjectParameter("ProviderAddressLine1", typeof(string));
    
            var providerAddressLine2Parameter = providerAddressLine2 != null ?
                new ObjectParameter("ProviderAddressLine2", providerAddressLine2) :
                new ObjectParameter("ProviderAddressLine2", typeof(string));
    
            var providerCityParameter = providerCity != null ?
                new ObjectParameter("ProviderCity", providerCity) :
                new ObjectParameter("ProviderCity", typeof(string));
    
            var providerStateIdParameter = providerStateId.HasValue ?
                new ObjectParameter("ProviderStateId", providerStateId) :
                new ObjectParameter("ProviderStateId", typeof(short));
    
            var providerZipParameter = providerZip != null ?
                new ObjectParameter("ProviderZip", providerZip) :
                new ObjectParameter("ProviderZip", typeof(string));
    
            var providerCountyIdParameter = providerCountyId.HasValue ?
                new ObjectParameter("ProviderCountyId", providerCountyId) :
                new ObjectParameter("ProviderCountyId", typeof(byte));
    
            var representativeIdParameter = representativeId.HasValue ?
                new ObjectParameter("RepresentativeId", representativeId) :
                new ObjectParameter("RepresentativeId", typeof(int));
    
            var representativeNameParameter = representativeName != null ?
                new ObjectParameter("RepresentativeName", representativeName) :
                new ObjectParameter("RepresentativeName", typeof(string));
    
            var representativeEmailParameter = representativeEmail != null ?
                new ObjectParameter("RepresentativeEmail", representativeEmail) :
                new ObjectParameter("RepresentativeEmail", typeof(string));
    
            var representativePhoneParameter = representativePhone != null ?
                new ObjectParameter("RepresentativePhone", representativePhone) :
                new ObjectParameter("RepresentativePhone", typeof(string));
    
            var facilityOwnershipParameter = facilityOwnership != null ?
                new ObjectParameter("FacilityOwnership", facilityOwnership) :
                new ObjectParameter("FacilityOwnership", typeof(string));
    
            var providerStatusIdParameter = providerStatusId.HasValue ?
                new ObjectParameter("ProviderStatusId", providerStatusId) :
                new ObjectParameter("ProviderStatusId", typeof(int));
    
            var providerStatusDateParameter = providerStatusDate.HasValue ?
                new ObjectParameter("ProviderStatusDate", providerStatusDate) :
                new ObjectParameter("ProviderStatusDate", typeof(System.DateTime));
    
            var providerEligTypeParameter = providerEligType.HasValue ?
                new ObjectParameter("ProviderEligType", providerEligType) :
                new ObjectParameter("ProviderEligType", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SAVE_PEP_PROVIDER", providerIdParameter, providerNameParameter, providerNpiParameter, medicaidBillingNoParameter, providerAddressLine1Parameter, providerAddressLine2Parameter, providerCityParameter, providerStateIdParameter, providerZipParameter, providerCountyIdParameter, representativeIdParameter, representativeNameParameter, representativeEmailParameter, representativePhoneParameter, facilityOwnershipParameter, providerStatusIdParameter, providerStatusDateParameter, providerEligTypeParameter);
        }
    
        public virtual ObjectResult<usp_SELECT_EMAIL_INFO_PEP_Result> usp_SELECT_EMAIL_INFO_PEP(Nullable<long> applicationId, Nullable<long> personId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(long));
    
            var personIdParameter = personId.HasValue ?
                new ObjectParameter("PersonId", personId) :
                new ObjectParameter("PersonId", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_SELECT_EMAIL_INFO_PEP_Result>("usp_SELECT_EMAIL_INFO_PEP", applicationIdParameter, personIdParameter);
        }
    
        public virtual int usp_SAVE_PEP_DETERMINER(Nullable<int> determinerId, Nullable<int> providerId, string determinerFirstName, string determinerLastName, string determinerPhone, string determinerPhoneExt, string determinerEmail, string determinerFax, Nullable<int> providerStatusId, Nullable<int> determinerStatusId, Nullable<System.DateTime> determinerStatusDate)
        {
            var determinerIdParameter = determinerId.HasValue ?
                new ObjectParameter("DeterminerId", determinerId) :
                new ObjectParameter("DeterminerId", typeof(int));
    
            var providerIdParameter = providerId.HasValue ?
                new ObjectParameter("ProviderId", providerId) :
                new ObjectParameter("ProviderId", typeof(int));
    
            var determinerFirstNameParameter = determinerFirstName != null ?
                new ObjectParameter("DeterminerFirstName", determinerFirstName) :
                new ObjectParameter("DeterminerFirstName", typeof(string));
    
            var determinerLastNameParameter = determinerLastName != null ?
                new ObjectParameter("DeterminerLastName", determinerLastName) :
                new ObjectParameter("DeterminerLastName", typeof(string));
    
            var determinerPhoneParameter = determinerPhone != null ?
                new ObjectParameter("DeterminerPhone", determinerPhone) :
                new ObjectParameter("DeterminerPhone", typeof(string));
    
            var determinerPhoneExtParameter = determinerPhoneExt != null ?
                new ObjectParameter("DeterminerPhoneExt", determinerPhoneExt) :
                new ObjectParameter("DeterminerPhoneExt", typeof(string));
    
            var determinerEmailParameter = determinerEmail != null ?
                new ObjectParameter("DeterminerEmail", determinerEmail) :
                new ObjectParameter("DeterminerEmail", typeof(string));
    
            var determinerFaxParameter = determinerFax != null ?
                new ObjectParameter("DeterminerFax", determinerFax) :
                new ObjectParameter("DeterminerFax", typeof(string));
    
            var providerStatusIdParameter = providerStatusId.HasValue ?
                new ObjectParameter("ProviderStatusId", providerStatusId) :
                new ObjectParameter("ProviderStatusId", typeof(int));
    
            var determinerStatusIdParameter = determinerStatusId.HasValue ?
                new ObjectParameter("DeterminerStatusId", determinerStatusId) :
                new ObjectParameter("DeterminerStatusId", typeof(int));
    
            var determinerStatusDateParameter = determinerStatusDate.HasValue ?
                new ObjectParameter("DeterminerStatusDate", determinerStatusDate) :
                new ObjectParameter("DeterminerStatusDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_SAVE_PEP_DETERMINER", determinerIdParameter, providerIdParameter, determinerFirstNameParameter, determinerLastNameParameter, determinerPhoneParameter, determinerPhoneExtParameter, determinerEmailParameter, determinerFaxParameter, providerStatusIdParameter, determinerStatusIdParameter, determinerStatusDateParameter);
        }
    
        public virtual int usp_CALCULATE_VA_PENSION_NET(Nullable<long> appNonMagiIncomeId, string updatedBy)
        {
            var appNonMagiIncomeIdParameter = appNonMagiIncomeId.HasValue ?
                new ObjectParameter("appNonMagiIncomeId", appNonMagiIncomeId) :
                new ObjectParameter("appNonMagiIncomeId", typeof(long));
    
            var updatedByParameter = updatedBy != null ?
                new ObjectParameter("UpdatedBy", updatedBy) :
                new ObjectParameter("UpdatedBy", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_CALCULATE_VA_PENSION_NET", appNonMagiIncomeIdParameter, updatedByParameter);
        }
    }
}
