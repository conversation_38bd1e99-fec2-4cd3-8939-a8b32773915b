﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="ChangeInCircumstance" xml:space="preserve">
    <value>In the past year</value>
  </data>
  <data name="ChangeJob" xml:space="preserve">
    <value>Changed Job</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Provider Company Name</value>
  </data>
  <data name="Deduction" xml:space="preserve">
    <value>DEDUCTIONS:</value>
  </data>
  <data name="HowOften" xml:space="preserve">
    <value>How Often?</value>
  </data>
  <data name="IncomeScholarship" xml:space="preserve">
    <value>Is any of this income from a scholarship or grant?</value>
  </data>
  <data name="NxtYrIncome" xml:space="preserve">
    <value>Next year total Income</value>
  </data>
  <data name="OtherIncome" xml:space="preserve">
    <value>Other Income</value>
  </data>
  <data name="ProfitLoss" xml:space="preserve">
    <value>Profit/Loss</value>
  </data>
  <data name="StartFewerHours" xml:space="preserve">
    <value>Started Working Fewer Hours</value>
  </data>
  <data name="StopWorking" xml:space="preserve">
    <value>Stopped Working</value>
  </data>
  <data name="TypeofWork" xml:space="preserve">
    <value>Type Of Work</value>
  </data>
  <data name="YearIncome" xml:space="preserve">
    <value>This year total Income</value>
  </data>
  <data name="YearlyIncome" xml:space="preserve">
    <value>YEARLY INCOME:</value>
  </data>
  <data name="HasIncome" xml:space="preserve">
    <value>Does this Person have any Income?</value>
  </data>
  <data name="IncomeSource" xml:space="preserve">
    <value>Income Source</value>
  </data>
  <data name="UnemploymemtBenefitExpire" xml:space="preserve">
    <value>Unemploymemt benefits are set to expire?</value>
  </data>
  <data name="ApplicantGrossAmount" xml:space="preserve">
    <value>Applicant Gross Amount</value>
  </data>
  <data name="ApplicantTotalIncome" xml:space="preserve">
    <value>Applicant Total Income</value>
  </data>
  <data name="ChildGrossAmount" xml:space="preserve">
    <value>Child Gross Amount</value>
  </data>
  <data name="ClaimNumber" xml:space="preserve">
    <value>Claim Number</value>
  </data>
  <data name="Frequency" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="IncomeSummary" xml:space="preserve">
    <value>Income Summary</value>
  </data>
  <data name="IncomeType" xml:space="preserve">
    <value>Income Type</value>
  </data>
  <data name="MinorChildTotalIncome" xml:space="preserve">
    <value>Minor Child Total Income</value>
  </data>
  <data name="SpouseGrossAmount" xml:space="preserve">
    <value>Spouse Gross Amount</value>
  </data>
  <data name="SpouseTotalIncome" xml:space="preserve">
    <value>Spouse Total Income</value>
  </data>
  <data name="IncomeRequired" xml:space="preserve">
    <value>If there is no verified income, Please enter "0".</value>
  </data>
  <data name="DeemNote" xml:space="preserve">
    <value>Note: System will automatically deem the earned and unearned income for claimant, spouse, and child. Please enter the gross verified amount in "Verified" Field.</value>
  </data>
  <data name="VeteranDeemNote" xml:space="preserve">
    <value>Note: Use the Veteran worksheet and enter Countable amount from the worksheet in the "Verified" field.</value>
  </data>
</root>