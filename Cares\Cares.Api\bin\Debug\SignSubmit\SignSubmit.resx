﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="allowToUseDataFor5Years" xml:space="preserve">
    <value>To make it easier to determine my eligibility for help paying for health coverage in future years, I agree to allow the Medicaid and ALL Kids to use income data, including information from tax returns, for the next 5 years(the maximum numbers of years allowed).  The Medicaid and ALL Kids will send me a notice, let me make any changes, and I can opt out at any time.</value>
  </data>
  <data name="cooperateToPursue" xml:space="preserve">
    <value>I know I'll be asked to cooperate with the agency that collects medical support from an absent parent.  If I think that cooperating to collect medical support will harm me or my children, I can tell the agency and I may not have to cooperate.</value>
  </data>
  <data name="esignatureContact" xml:space="preserve">
    <value>[NAME]'s electronic signature</value>
  </data>
  <data name="informUsOfChanges" xml:space="preserve">
    <value>I know that I must tell the program I'll be enrolled in if information I listed on this application changes.  I know I can make changes in "My Account" in this online application or by calling [CHIPPHONE].  I understand that a change in my information could affect my eligibility for member(s) of my household.</value>
  </data>
  <data name="nbrYrsForRenewal" xml:space="preserve">
    <value>Please select number of years for Renewal.</value>
  </data>
  <data name="noOneIncarcerated" xml:space="preserve">
    <value>No one applying for health coverage on this application is incarcerated(detained or jailed).</value>
  </data>
  <data name="pendingDisposition" xml:space="preserve">
    <value>Is this person pending disposition?</value>
  </data>
  <data name="permissionToAutoRenewPeriodOfTime" xml:space="preserve">
    <value>Yes, renew my eligibility automatically for a period of</value>
  </data>
  <data name="providedTrueInformation" xml:space="preserve">
    <value>I'm signing this application under penalty of perjury, which means I've provided true answers to all of the questions to the best of my knowledge.  I know that I may be subject to penalties under federal law if I intentionally provide false or untrue information.</value>
  </data>
  <data name="rightsToPursue" xml:space="preserve">
    <value> I'm giving the Medicaid agency our rights to pursue and get any money from other health insurance, legal settlements, or other third parties.  I'm also giving to the Medicaid agency rights to pursue and get medical support from a spouse or parent.</value>
  </data>
  <data name="selectIncarceratedPerson" xml:space="preserve">
    <value>Please select the person who is incarcerated.</value>
  </data>
  <data name="signAndSubmit" xml:space="preserve">
    <value>Sign &amp; Submit</value>
  </data>
  <data name="signatureRequired" xml:space="preserve">
    <value>Signature is required.</value>
  </data>
  <data name="whoIsIncarcerated" xml:space="preserve">
    <value>Who is incarcerated(detained or jailed)?</value>
  </data>
</root>