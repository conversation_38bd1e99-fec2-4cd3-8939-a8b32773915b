﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Main {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class RIDPFAQ {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal RIDPFAQ() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Main.RIDPFAQ", typeof(RIDPFAQ).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In order to protect your information, we have to take necessary steps to verify your identity, and to avoid any unauthorized person creating an account under your name..
        /// </summary>
        public static string answer1_1 {
            get {
                return ResourceManager.GetString("answer1_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Here&apos;s how it works:.
        /// </summary>
        public static string answer1_2 {
            get {
                return ResourceManager.GetString("answer1_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You’ll be asked three to five questions that can only be answered by you..
        /// </summary>
        public static string answer1_3 {
            get {
                return ResourceManager.GetString("answer1_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You’ll answer from a list of possible choices..
        /// </summary>
        public static string answer1_4 {
            get {
                return ResourceManager.GetString("answer1_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When you answer enough questions correctly, you’ll be able to submit an application online..
        /// </summary>
        public static string answer1_5 {
            get {
                return ResourceManager.GetString("answer1_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to verify your identity one time only..
        /// </summary>
        public static string answer1_6 {
            get {
                return ResourceManager.GetString("answer1_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If your identity can’t be verified online, you’ll be told what to do next..
        /// </summary>
        public static string answer1_7 {
            get {
                return ResourceManager.GetString("answer1_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you do not complete the identity verification process, the process will begin again..
        /// </summary>
        public static string answer1_8 {
            get {
                return ResourceManager.GetString("answer1_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please do not call Medicaid/ALLKids helpdesk..
        /// </summary>
        public static string answer1_9 {
            get {
                return ResourceManager.GetString("answer1_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A reference number is generated when we are unable to verify your identity online..
        /// </summary>
        public static string answer2_1 {
            get {
                return ResourceManager.GetString("answer2_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If a reference number is provided, you will need to contact the Experian call center at .
        /// </summary>
        public static string answer2_2_1 {
            get {
                return ResourceManager.GetString("answer2_2_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1-866-578-5409.
        /// </summary>
        public static string answer2_2_2 {
            get {
                return ResourceManager.GetString("answer2_2_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  and provide them with the reference number..
        /// </summary>
        public static string answer2_2_3 {
            get {
                return ResourceManager.GetString("answer2_2_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A call center agent will ask you a few questions to verify your identity..
        /// </summary>
        public static string answer2_3 {
            get {
                return ResourceManager.GetString("answer2_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once you successfully verify your identity over the phone, you will be allowed to submit an application online on your next login..
        /// </summary>
        public static string answer2_4 {
            get {
                return ResourceManager.GetString("answer2_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Experian call center hours.
        /// </summary>
        public static string answer2_5 {
            get {
                return ResourceManager.GetString("answer2_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monday – Friday: 7:30 AM – 9:00 PM.
        /// </summary>
        public static string answer2_6 {
            get {
                return ResourceManager.GetString("answer2_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday: 9:00 AM – 7:00 PM.
        /// </summary>
        public static string answer2_7 {
            get {
                return ResourceManager.GetString("answer2_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sunday: 10:00 AM – 7:00 PM.
        /// </summary>
        public static string answer2_8 {
            get {
                return ResourceManager.GetString("answer2_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Central Standard Time).
        /// </summary>
        public static string answer2_9 {
            get {
                return ResourceManager.GetString("answer2_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In that case, you will be able to view the latest information we have on file for your household..
        /// </summary>
        public static string answer3_1 {
            get {
                return ResourceManager.GetString("answer3_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You may not be able to change some of the personal information for your household members online..
        /// </summary>
        public static string answer3_2 {
            get {
                return ResourceManager.GetString("answer3_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please submit a paper application if any personal information needs to be updated..
        /// </summary>
        public static string answer3_3 {
            get {
                return ResourceManager.GetString("answer3_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Why do I need to verify my identity?.
        /// </summary>
        public static string question1 {
            get {
                return ResourceManager.GetString("question1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What do I need to do with the reference number which was provided?.
        /// </summary>
        public static string question2 {
            get {
                return ResourceManager.GetString("question2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What if I have been identified as an existing user?.
        /// </summary>
        public static string question3 {
            get {
                return ResourceManager.GetString("question3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity Verification.
        /// </summary>
        public static string ridpButton {
            get {
                return ResourceManager.GetString("ridpButton", resourceCulture);
            }
        }
    }
}
