USE Db4_ee;
GO

SET ANSI_NULLS ON;
GO
SET QUOTED_IDENTIFIER ON;
GO


CREATE PROCEDURE dbo.usp_SAVE_APPLICATION_DETAILS_HPE
    @ApplicationId bigint = 0
  , @PersonId bigint = 0
  , @ProviderId int = 0
  , @DeterminerId bigint = 0
  , @IsSuccess bit = 0 OUTPUT -- 1 = Success; 0 = Failure
AS

/********************************************************************************************
 Stored Procedure : dbo.usp_SAVE_APPLICATION_DETAILS_HPE
 Author           : <PERSON><PERSON><PERSON><PERSON>
 Created On       : 2025-09-22
 Description      : UPSERT HPE application-detail data with validation, audit fields,
                    and protection against updates when the application is in a terminal status.

 Version History
 Ver | Date       | Author                 | PBI#       | Description
 ----|------------|------------------------|------------|---------------------------------------------------------
 01  | 2025-09-22 | Raveendran Krishnasamy | PBI#253366 | Initial implementation (formatted & corrected).
*********************************************************************************************/

BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

   DECLARE 
		@SubProgramId          INT          = 4      -- For HPE
     ,  @IsExists              BIT          = 0
     ,  @IsOkToUpdate          BIT          = 0		 
     ,  @IsExistsProviderId    BIT          = 0
     ,  @IsExistsDeterminerId  BIT          = 0
     ,  @CreatedBy             VARCHAR(50)  = SUSER_SNAME()
     ,  @UpdatedBy             VARCHAR(50)  = SUSER_SNAME()
     ,  @CreatedDate           DATETIME2(7) = SYSDATETIME()
     ,  @UpdatedDate           DATETIME2(7) = SYSDATETIME()
     ,  @HighDate              DATETIME2(7) = CONVERT(DATETIME2(7), '9999-12-31 23:59:59.9999999');



    SET @IsSuccess = 0;

    BEGIN TRY
        BEGIN TRANSACTION;

        -------------------------------------------------------------------------------------
        -- Does an HPE detail record exist for this Application/Person in the HPE subprogram?
        -------------------------------------------------------------------------------------
        SET @IsExists = IIF(
                            EXISTS
                            (
                                SELECT 1
                                FROM dbo.APPLICATION_DETAIL_HPE AS hpe
                                    INNER JOIN dbo.APPLICATION  AS app
                                        ON app.APPLICATION_ID = hpe.APPLICATION_ID
                                    INNER JOIN dbo.PERSON       AS p
                                        ON p.PERSON_ID = hpe.PERSON_ID
                                WHERE hpe.APPLICATION_ID = @ApplicationId
                                      AND hpe.PERSON_ID = @PersonId
                                      AND app.SUB_PROGRAM_CATEGORY_ID = @SubProgramId
                            )
                          , 1
                          , 0);

        -------------------------------------------------------------------------------------
        -- INSERT if missing
        -------------------------------------------------------------------------------------
        IF @IsExists = 0
        BEGIN
            INSERT INTO dbo.APPLICATION_DETAIL_HPE
            (
                APPLICATION_ID
              , PERSON_ID
              , DETERMINER_ID
              , PROVIDER_ID
              , CREATED_BY
              , CREATED_DATE
              , UPDATED_BY
              , UPDATED_DATE
            )
            VALUES
            (
			   @ApplicationId
			 , @PersonId
			 , @DeterminerId
			 , @ProviderId
			 , @CreatedBy
			 , @CreatedDate
			 , @UpdatedBy
			 , @UpdatedDate
			);
        END
        ELSE
        BEGIN
            ---------------------------------------------------------------------------------
            -- UPDATE path: only when NOT in a terminal status
            ---------------------------------------------------------------------------------
            SET @IsOkToUpdate = IIF(
                                    EXISTS
        (
            SELECT 1
            FROM dbo.APPLICATION_DETAIL_HPE               AS hpe
                INNER JOIN dbo.APPLICATION                AS app
                    ON app.APPLICATION_ID = hpe.APPLICATION_ID
                INNER JOIN dbo.PERSON                     AS p
                    ON p.PERSON_ID = hpe.PERSON_ID
                INNER JOIN dbo.APPLICATION_STATUS_HISTORY AS ash
                    ON ash.APPLICATION_ID = app.APPLICATION_ID
                       AND ash.APPLICATION_STATUS_ID = app.APPLICATION_STATUS_ID
                INNER JOIN dbo.REF_APPLICATION_STATUS     AS ras
                    ON ras.APPLICATION_STATUS_ID = app.APPLICATION_STATUS_ID
            WHERE hpe.APPLICATION_ID = @ApplicationId
                  AND hpe.PERSON_ID = @PersonId
                  AND app.SUB_PROGRAM_CATEGORY_ID = @SubProgramId
                  AND ISNULL(ras.TERMINAL_STATUS_IND, 0) <> 1
        )
                                  , 1
                                  , 0);


            IF @IsOkToUpdate = 1
            BEGIN

				-- Ensure that the supplied parameters @DeterminerId and @ProviderId 
				-- refer to valid records that already exist in their respective tables
				-- (e.g., DETERMINER and PROVIDER tables) 

				SET  @IsExistsDeterminerId = (
					SELECT IIF(EXISTS(
									 SELECT 1 
									 FROM dbo.PRESUMPTIVE_DETERMINER AS PD  
									 WHERE PD.DETERMINER_ID = @DeterminerId
									),1,0)
				);

				SET @IsExistsProviderId = (
					SELECT IIF(EXISTS(
									 SELECT 1 
									 FROM dbo.PRESUMPTIVE_PROVIDER AS PP  
									 WHERE PP.PROVIDER_ID = @ProviderId
									),1,0)
				);

				IF @IsOkToUpdate = 1 AND @IsExistsDeterminerId = 1 AND @IsExistsProviderId = 1
                UPDATE hpe
                SET DETERMINER_ID = CASE
                                        WHEN @DeterminerId IS NOT NULL
                                             AND ISNULL(hpe.DETERMINER_ID, 0) <> @DeterminerId THEN
                                            @DeterminerId
                                        WHEN @DeterminerId IS NULL THEN
                                            @DeterminerId -- allows clearing when explicitly passed NULL
                                        ELSE
                                            hpe.DETERMINER_ID
                                    END
                  , PROVIDER_ID = CASE
                                      WHEN @ProviderId IS NOT NULL
                                           AND ISNULL(hpe.PROVIDER_ID, 0) <> @ProviderId THEN
                                          @ProviderId
                                      WHEN @ProviderId IS NULL THEN
                                          @ProviderId -- allows clearing when explicitly passed NULL
                                      ELSE
                                          hpe.PROVIDER_ID
                                  END
                  , UPDATED_BY = @UpdatedBy
                  , UPDATED_DATE = @UpdatedDate
                FROM dbo.APPLICATION_DETAIL_HPE AS hpe
                WHERE hpe.APPLICATION_ID = @ApplicationId
                      AND hpe.PERSON_ID = @PersonId;
            END

        END

        COMMIT TRANSACTION;
        SET @IsSuccess = 1;
    END TRY
    BEGIN CATCH
        IF XACT_STATE() <> 0
            ROLLBACK TRANSACTION;

        -- Surface error details to caller
        SELECT ERROR_NUMBER()    AS ErrorNumber
             , ERROR_SEVERITY()  AS ErrorSeverity
             , ERROR_STATE()     AS ErrorState
             , ERROR_LINE()      AS ErrorLine
             , ERROR_PROCEDURE() AS ErrorProcedure
             , ERROR_MESSAGE()   AS ErrorMessage;

        SET @IsSuccess = 0;
    END CATCH;

    SET NOCOUNT OFF;
    SET XACT_ABORT OFF;
END
GO
