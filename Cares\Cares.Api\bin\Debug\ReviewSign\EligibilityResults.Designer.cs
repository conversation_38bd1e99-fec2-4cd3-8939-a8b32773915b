﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.ReviewSign {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class EligibilityResults {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EligibilityResults() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.ReviewSign.EligibilityResults", typeof(EligibilityResults).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you are approved or denied, a notice of action will be sent to your mailing address.&lt;br/&gt;
        ///If you are approved for ALL Kids, you will receive a card within 5 - 10 business days.&lt;br/&gt;
        ///If you are approved for Medicaid, you will receive a Medicaid ID card and information about Medicaid within 10 days. &lt;b&gt; It may take longer for eligibility to show active in the Medicaid Claims system.&lt;/b&gt;&lt;br/&gt;
        ///If your status is pending, you may be contacted for more information to complete the determination of your eligib [rest of string was truncated]&quot;;.
        /// </summary>
        public static string eligibilityHelpText {
            get {
                return ResourceManager.GetString("eligibilityHelpText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eligibility Results.
        /// </summary>
        public static string eligibilityResults {
            get {
                return ResourceManager.GetString("eligibilityResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eligible for.
        /// </summary>
        public static string eligibleFor {
            get {
                return ResourceManager.GetString("eligibleFor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;a class=&quot;blue-link&quot; href=&quot;http://www.hhs.gov/ocr/office/file&quot; target=&quot;_blank&quot;&gt;www.hhs.gov/ocr/office/file&lt;/a&gt;.
        /// </summary>
        public static string hhsGovUrl {
            get {
                return ResourceManager.GetString("hhsGovUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If I think the Medicaid/Children&apos;s Health Insurance Program (ALL Kids) has made a mistake, I can appeal its decision.  To appeal means to tell someone at the Medicaid/ALL Kids that I think the action is wrong and ask for a fair review of the action.  I know that I can find out how to appeal by contacting the Medicaid/ALL Kids at 1-888-373-KIDS (5437).  I know that I can be represented in the process by someone other than myself.  My eligibility and other information will be explained to me.  Find out more a [rest of string was truncated]&quot;;.
        /// </summary>
        public static string howToAppeal {
            get {
                return ResourceManager.GetString("howToAppeal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More information &amp; appeals.
        /// </summary>
        public static string moreInformationAppeals {
            get {
                return ResourceManager.GetString("moreInformationAppeals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Following federal law, discrimination isn&apos;t permitted on the basis or race, color, national origin, sex, age, sexual orientation, gender identity, or disability.  I can file a complaint of discrimination by visiting [HHSGOVURL]..
        /// </summary>
        public static string noDiscrimination {
            get {
                return ResourceManager.GetString("noDiscrimination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not applying for coverage.
        /// </summary>
        public static string notApplyingForCoverage {
            get {
                return ResourceManager.GetString("notApplyingForCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not eligible for.
        /// </summary>
        public static string notEligibleFor {
            get {
                return ResourceManager.GetString("notEligibleFor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coverage  Type.
        /// </summary>
        public static string program {
            get {
                return ResourceManager.GetString("program", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does anyone in the household want to register to vote?.
        /// </summary>
        public static string registerToVote {
            get {
                return ResourceManager.GetString("registerToVote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register to vote by clicking here..
        /// </summary>
        public static string registerToVoteHere {
            get {
                return ResourceManager.GetString("registerToVoteHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to http://alabamavotes.gov/GetRegForm.aspx?m=voters.
        /// </summary>
        public static string registerToVoteURL {
            get {
                return ResourceManager.GetString("registerToVoteURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do any of these people want to request a determination for Medicaid for [NAME] as conducted by Alabama Medicaid Agency on the basis of disability, blindness, or recurring medical needs or bills?.
        /// </summary>
        public static string requestDisabilityDetermination {
            get {
                return ResourceManager.GetString("requestDisabilityDetermination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It looks like [NAMES] [ISN&apos;T/AREN&apos;T] eligible for Medicaid.  [HE/SHE/THEY] can still continue with Medicaid applications if we send this application to the Alabama Medicaid Agency (AMA).  Do [NAMES] want us to send their information to the AMA so they can check on Medicaid eligibility?.
        /// </summary>
        public static string sendAppToMedicaid {
            get {
                return ResourceManager.GetString("sendAppToMedicaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your application submission.  We will notify you in writing when your eligibility for health care programs has been determined..
        /// </summary>
        public static string tempResultsMsg {
            get {
                return ResourceManager.GetString("tempResultsMsg", resourceCulture);
            }
        }
    }
}
