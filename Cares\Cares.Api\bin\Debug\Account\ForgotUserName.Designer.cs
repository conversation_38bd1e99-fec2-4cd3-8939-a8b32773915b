﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Account {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ForgotUserName {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ForgotUserName() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Account.ForgotUserName", typeof(ForgotUserName).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer security questions.
        /// </summary>
        public static string answerSecQuest {
            get {
                return ResourceManager.GetString("answerSecQuest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To access your information, you will need to answer the security question(s) provided when you originally created your account..
        /// </summary>
        public static string answerSecQuestText {
            get {
                return ResourceManager.GetString("answerSecQuestText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string email {
            get {
                return ResourceManager.GetString("email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Authentication.
        /// </summary>
        public static string emailAuth {
            get {
                return ResourceManager.GetString("emailAuth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To access your information, we will send an email to the address on file for you..
        /// </summary>
        public static string emailAuthText {
            get {
                return ResourceManager.GetString("emailAuthText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None of the usernames are associated with the above mentioned email address.  Please try again with a valid email address..
        /// </summary>
        public static string failureNotificationLabel {
            get {
                return ResourceManager.GetString("failureNotificationLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Alabamacares.Alabama.gov Username..
        /// </summary>
        public static string ForgotUserNameEmailSubject {
            get {
                return ResourceManager.GetString("ForgotUserNameEmailSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter an email address associated with your account and click Next..
        /// </summary>
        public static string Instruction {
            get {
                return ResourceManager.GetString("Instruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Login Date.
        /// </summary>
        public static string LastLoginHeader {
            get {
                return ResourceManager.GetString("LastLoginHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The following are the most recently used usernames associated with [Email]..
        /// </summary>
        public static string ListofEmailsLabel {
            get {
                return ResourceManager.GetString("ListofEmailsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear [FullName].
        /// </summary>
        public static string salutation {
            get {
                return ResourceManager.GetString("salutation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An email has been sent to you with some username(s).  Please check your email and follow the provided instructions..
        /// </summary>
        public static string userFoundCheckEmail {
            get {
                return ResourceManager.GetString("userFoundCheckEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        public static string UserIDHeader {
            get {
                return ResourceManager.GetString("UserIDHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are no username(s) associated with this email address.  Please try again with a valid address..
        /// </summary>
        public static string userNotFound {
            get {
                return ResourceManager.GetString("userNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your username and password are case sensitive.  Please be sure to store your username and password safely..
        /// </summary>
        public static string UserPwdSafetyInstructions {
            get {
                return ResourceManager.GetString("UserPwdSafetyInstructions", resourceCulture);
            }
        }
    }
}
