﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Main {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class MasterPage {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MasterPage() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Main.MasterPage", typeof(MasterPage).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Settings.
        /// </summary>
        public static string acctSettings {
            get {
                return ResourceManager.GetString("acctSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Color Theme.
        /// </summary>
        public static string changeColor {
            get {
                return ResourceManager.GetString("changeColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Text Size.
        /// </summary>
        public static string changeTextSize {
            get {
                return ResourceManager.GetString("changeTextSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string close {
            get {
                return ResourceManager.GetString("close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Black &amp; White.
        /// </summary>
        public static string colorBW {
            get {
                return ResourceManager.GetString("colorBW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        public static string colorDef {
            get {
                return ResourceManager.GetString("colorDef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAQ.
        /// </summary>
        public static string faq {
            get {
                return ResourceManager.GetString("faq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frequently Asked Questions.
        /// </summary>
        public static string faqHeader {
            get {
                return ResourceManager.GetString("faqHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Help Center.
        /// </summary>
        public static string help {
            get {
                return ResourceManager.GetString("help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language Settings.
        /// </summary>
        public static string langSettings {
            get {
                return ResourceManager.GetString("langSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Site Preferences.
        /// </summary>
        public static string sitePreferences {
            get {
                return ResourceManager.GetString("sitePreferences", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your changes have been saved successfully!.
        /// </summary>
        public static string successfulSaveMsg {
            get {
                return ResourceManager.GetString("successfulSaveMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to - Text Size.
        /// </summary>
        public static string textSizeDec {
            get {
                return ResourceManager.GetString("textSizeDec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to + Text Size.
        /// </summary>
        public static string textSizeInc {
            get {
                return ResourceManager.GetString("textSizeInc", resourceCulture);
            }
        }
    }
}
