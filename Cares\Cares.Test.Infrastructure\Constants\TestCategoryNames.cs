﻿namespace Cares.Test
{
    /// <summary>
    /// Placeholder for TestCategory names
    /// </summary>
    public class TestCategoryNames
    {
        // DevTesting tests are more temporary in nature, made include hard-coded AppIds.  Are for the developer
        public const string DevTesting = "DevTesting";
        // All tests labeled with this category will be included with the automated Regression tests:
        public const string Regression = "Regression";
        // All tests labeled with this category will be included with the automated Regression tests:
        public const string RegressionExtendedScenarios = "RegressionExtendedScenarios";
        // All tests labeled with this category will be included with the automated (or manual) Smoke tests:
        public const string Smoke = "Smoke";
        // All tests labeled with this category will be included in automated Load testing:
        public const string Load = "Load";
        // All tests labeled with this category will be included in automated On Demand testing:
        public const string OnDemand = "OnDemand";
        // Placed on Regression tests that have been persistently failing and have not been immediately fixed
        public const string RegressionFailing = "Regression Failing";
        public const string CitizenPortal = "Citizen Portal";
        public const string Performance = "Performance";
        public const string RegressionAddress = "Regression Address";
        public const string RegressionUnborn = "Regression Unborn";
        // Marked tests with this when you want the App to not be deleted by the cleanup script
        public const string LoadAppsForManualTesting = "LoadAppForManualTesting";
        public const string Incarceration = "Incarceration";
        public const string IneligibilityReasonCodes = "Ineligibility Reason Codes";
        public const string Precedence = "Precedence";
        public const string ExparteScenarios = "Exparte Scenarios";
        public const string DateOfdeathRetro = "DateOfdeathRetro";
        public const string DifferentAddress = "DifferentAddress";
        public const string Miscarriage = "Miscarriage";
        public const string POCRRelationships = "POCR Relationships";
        public const string AgingOut = "Aging out";
        public const string LivesWith = "Lives with";
        public const string StateAidCategories = "StateAidCategories";
        public const string RenewalLoad = "Renewal Load";
        public const string Renewal = "Renewal";
        public const string FFMRenewal = "FFMRenewal";
        public const string FFMInbound = "FFMInbound";
        public const string RegressionTODO = "Regression TODO";
        public const string GetApplicationDataUnitTests = "Get Application Data Unit Tests";
        public const string Unit = "Unit";
        public const string Integration = "Integration";
        public const string RemoteIDProofing = "Remote ID Proofing";
        public const string ExpressLaneEligibility = "Express Lane Eligibility";
        public const string RemoteIDProofingLong = "Remote ID Proofing Long";
        public const string LettersDAL = "LettersDAL";
        public const string WPIntegration = "WP Integration";
        public const string EligEnrollTest = "EligEnrollTest";
        public const string WPUnitTest = "WP Unit Test";
        public const string EligEnrollValidator = "EligEnrollValidator";
        public const string HealthCoverageBOValidator = "HealthCoverageBOValidator";
        public const string HealthCoverageTest = "HealthCoverageTest";
        public const string AmaesCheck = "Amaes Check";
        public const string GatewayServices = "Gateway Services";
        public const string ReasonableCompatibility = "Reasonable Compatibility";
        public const string MedicaidIdHistory = "Medicaid Id History";
        public const string MedicareResponse = "Medicare Response";
        public const string HouseholdSize = "Household Size";
        public const string StartDateCancelDate = "StartDateCancelDate";
        public const string Merge = "Merge";
        public const string UserAccount = "UserAccount";
        public const string CopyApp = "CopyApp";
        public const string PersonUpdate = "PersonUpdate";
        public const string AllKidsBalance = "AllKidsBalance";
        public const string AppSnapshot = "AppSnapshot";
        public const string IncomeTests = "Income";
        public const string PatientMatching = "PatientMatching";
        public const string Age = "Age";  // For any test dealing specifically with age (but not AgeOut)
        public const string ChipPregnancy = "ChipPregnancy";
        public const string Sync = "Sync"; // for all sync tests
        public const string OnDemandFailing = "OnDemandFailing"; // for all OnDemand Failing tests
        public const string PastPregnancy = "PastPregnancy";
        // These are tests related to the fact that we've upgraded WP and Cares.Test libraries, but have
        // not upgraded CP yet (for example FluentValidations).  Once complete, this can be removed:
        public const string LibraryUpgradeRequired = "LibraryUpgradeRequired";
        public const string EnrollmentHistory = "Enrollment History ";
        public const string Scenario7 = "Scenario 7-State Aid Categories";
        public const string Scenario9 = "Scenario 9-Precedence Of State Aid Categories";
        public const string Scenario11 = "Scenario 11-Start/Cancel Date and Cancel Reason Logic";
        public const string Scenario14 = "Scenario 14-Ineligibility Reason Codes";
        public const string Scenario15 = "Scenario 15-AMAES CHECK";
        public const string Scenario17 = "Scenario 17-Miscarriage Postpartum Coverage";
        public const string Scenario20 = "Scenario 20-Hub Calls";
        public const string Scenario23 = "Scenario 23-Age Out Functionality";
        public const string Scenario28 = "Scenario 28-BCC Eligibility";
        public const string Scenario29 = "Scenario 29-DYS Eligibility";
        public const string Scenario31 = "Scenario 31-HPE Eligibility";
        public const string Scenario32 = "Scenario 32-Dual Eligibility";
        public const string Scenario33 = "Scenario 33-CHIP Pregnancy";
        public const string Scenario34 = "Scenario 34-DHR Eligibility";
        public const string Scenario35 = "Scenario 35-Landing Page";
        public const string Scenario36 = "Scenario 36-MSP";
        public const string Scenario37 = "Scenario 37-TMA Eligibility";
        public const string DHREligibility = "DHR Eligibility";
        public const string UILogin = "UILogin";
        public const string CPAccount = "CP Account";
        public const string LandingPage = "Landing Page";
        public const string Audit = "Audit";
        public const string AppTier = "AppTier";
        public const string MSP = "MSP";
        public const string Eligibility = "Eligibility";
        public const string ElderlyDisabled = "ElderlyDisabled";
        public const string ElderlyDisabledStateAidCat = "ElderlyDisabledStateAidCat";
        public const string ValidatorTests = "Validator Tests";
        public const string Notes = "Notes";
        public const string Expedite = "Expedite";
        public const string WorkerReminders = "Worker Reminders";
        public const string PostPartum = "PostPartum";
        public const string JIY = "JIY";
        public const string PEP = "PEP";
        public const string EfUpgrade = "EF Upgrade";
    }
}
