CREATE PROCEDURE [dbo].[usp_PROCESS_DOD_STAGING]
		@IsSuccess VARCHAR(5) = 'False' OUTPUT
AS  
BEGIN   
	
-- ====================================================================================
-- Author:	<PERSON>
-- Create date: 03/08/2022
-- Description:	Process the DOD_STAGING table from TBQ data.
-- Modified by: <PERSON>pur on 2022-04-22, Corrected Incorrect join on one of the table.
-- Modified by: <PERSON><PERSON> on 2023-05-19, Add logic for E&D application to truncate current liability segment's end date and delete future liability segment(s).
-- Modified by: <PERSON><PERSON> on 2023-05-25, Add logic to update the liability change code to "The claimant is deceased".
-- Modified by: <PERSON><PERSON> on 2023-06-08, Update the liability note.
-- Modified by: <PERSON><PERSON> on 2023-07-11, Update the liability/transfer penalty note.
-- Modified by: <PERSON><PERSON> on 2023-07-20, Update the verbiage for liability/transfer penalty note.
-- Modified by: <PERSON><PERSON> <PERSON>ruchuri on 2025-09-12, Update the cancel reason for first of next month cancel date records.
-- ========================================================================================

	SET NOCOUNT ON;  

		DECLARE	@CreatedBy CHAR(14) = 'DODAdmin', 
				@CurrentDate DATETIME = GETDATE();

		DECLARE @FirstOfNextMonth DATE = DATEADD(D,1,EOMONTH(@CurrentDate));

		BEGIN TRY
		BEGIN TRANSACTION

		SET TRAN ISOLATION LEVEL READ UNCOMMITTED;

	-- Mark exception if DOD is in future
		UPDATE 	staging.dbo.DOD_STAGING
		SET 	IS_EXCEPTION = 1,
				EXCEPTION_REASON_CODE = 34  --  DOD is in future
		WHERE 	DATE_OF_DEATH > GETDATE()
				AND IS_PROCESSED = 0
				AND IS_EXCEPTION = 0;

		-- Mark exception if DOD already exists in the system.
		UPDATE	DOD
		SET		DOD.IS_EXCEPTION = 1,
				DOD.EXCEPTION_REASON_CODE = 35  -- DOD is already updated in the system
		FROM	staging.dbo.DOD_STAGING AS DOD 
				INNER JOIN db4_ee.dbo.PERSON_DETAIL AS PD ON  DOD.PERSON_ID = PD.PERSON_ID
		WHERE	PD.DATE_OF_DEATH IS NOT NULL
				AND IS_PROCESSED = 0
				AND IS_EXCEPTION = 0;
			
		-- Exclude SSI & DHR People if latest app is in SSI & DHR
		UPDATE  DOD
		SET     DOD.APPLICATION_ID = QRY.APPLICATION_ID,
		        DOD.IS_EXCEPTION = IIF(QRY.SUB_PROGRAM_CATEGORY_ID IN (5,6), 1, DOD.IS_EXCEPTION),
		        DOD.EXCEPTION_REASON_CODE = IIF(QRY.SUB_PROGRAM_CATEGORY_ID IN (5,6), 32, DOD.EXCEPTION_REASON_CODE)  -- Latest App in SSI or DHR
		FROM    staging.dbo.DOD_STAGING AS DOD
		        INNER JOIN 
				(
				SELECT  AD.PERSON_ID,
				        AD.APPLICATION_ID,
						A.SUB_PROGRAM_CATEGORY_ID,
						ROW_NUMBER() OVER (PARTITION BY AD.PERSON_ID ORDER BY AD.APPLICATION_ID DESC) AS AppRowNumber	-- Sort by the highest application id.
				FROM    staging.dbo.DOD_STAGING AS DOD 
				        INNER JOIN  db4_ee.dbo.[APPLICATION_DETAIL] AS AD on DOD.PERSON_ID = AD.PERSON_ID
						INNER JOIN  db4_ee.dbo.[APPLICATION] AS A ON AD.APPLICATION_ID = A.APPLICATION_ID
				WHERE	DOD.IS_EXCEPTION = 0
				        AND DOD.IS_PROCESSED = 0
				)		AS QRY ON DOD.PERSON_ID = QRY.PERSON_ID
		WHERE	QRY.AppRowNumber = 1;

		UPDATE	DOD
		SET		DOD.IS_EXCEPTION = 1,
				DOD.EXCEPTION_REASON_CODE = 36, --Active in SSI or DHR
				DOD.APPLICATION_ID = A.APPLICATION_ID
		FROM	DBO.DOD_STAGING AS DOD
				INNER JOIN DBO.APPLICATION_ENROLLMENT AS AEN ON DOD.PERSON_ID = AEN.PERSON_ID
				INNER JOIN DBO.[APPLICATION] AS A ON AEN.APPLICATION_ID = A.APPLICATION_ID
		WHERE	AEN.[START_DATE] <> AEN.CANCEL_DATE 
	 			AND AEN.CANCEL_DATE >  @FirstOfNextMonth
				AND DOD.IS_EXCEPTION = 0
				AND DOD.IS_PROCESSED = 0
				AND A.SUB_PROGRAM_CATEGORY_ID IN (5,6)  -- DHR & SSI

		-- Get Persons to update DOD
		SELECT 	DOD.PERSON_ID,
				DOD.APPLICATION_ID,
				DOD.DATE_OF_DEATH,
				DOD.DOD_SOURCE
		INTO	dbo.#DODNotes
		FROM	staging.[dbo].[DOD_STAGING] AS DOD 
				INNER JOIN db4_ee.dbo.PERSON_DETAIL AS PD ON DOD.PERSON_ID = PD.PERSON_ID 
		WHERE	DOD.IS_PROCESSED = 0
				AND DOD.IS_EXCEPTION = 0
				AND PD.DATE_OF_DEATH IS NULL;

		-- Update DOD Records
		UPDATE	PD
		SET		DATE_OF_DEATH = DOD.DATE_OF_DEATH
			   ,DEATH_VERIFICATION_DATE = DOD.DATE_OF_DEATH
			   ,DEATH_VERIFICATION_SOURCE = DOD.DOD_SOURCE
			   ,UPDATED_BY = @CreatedBy
			   ,UPDATED_DATE = @CurrentDate
		FROM	dbo.#DODNotes AS DOD 
				INNER JOIN db4_ee.dbo.PERSON_DETAIL AS PD ON DOD.PERSON_ID = PD.PERSON_ID;

		-- Get data to Insert into APPLICATION_ENROLLMENT_HISTORY table
		SELECT   AEN.APPLICATION_ID
		        ,AEN.PERSON_ID
		        ,AEN.[START_DATE]
		        ,AEN.CANCEL_DATE
		        ,AEN.CANCEL_REASON_ID
		        ,AEN.INSURANCE_SEND_DATE
		        ,AE.PROGRAM_ID
		        ,AE.PROGRAM_SUB_CATEGORY_ID
		        ,AE.OVERRIDE_BY
		        ,AE.OVERRIDE_DATE
		        ,AE.OVERRIDE_REASON_ID
		        ,AEN.APPLICATION_ENROLLMENT_ID
				,AEN.WORKER_NUMBER
				,AEN.WORKER_COUNTY_NUMBER
		        ,P.FIRST_NAME
		        ,P.LAST_NAME
		        ,AEN.CREATED_BY
		        ,AEN.CREATED_DATE 
		        ,AEN.UPDATED_BY
		        ,AEN.UPDATED_DATE
				,DOD.DOD_SOURCE
		 INTO   dbo.#UpdateCancelDate
		 FROM   dbo.#DODNotes AS DOD 
		 		INNER JOIN db4_ee.dbo.APPLICATION_ELIGIBILITY AS AE ON DOD.PERSON_ID = AE.PERSON_ID
		        INNER JOIN db4_ee.dbo.APPLICATION_ENROLLMENT AS AEN on AE.APPLICATION_ID = AEN.APPLICATION_ID AND AE.PERSON_ID = AEN.PERSON_ID
		        INNER JOIN db4_ee.dbo.PERSON AS P ON AEN.PERSON_ID = P.PERSON_ID
				INNER JOIN db4_ee.dbo.PERSON_DETAIL AS PD ON P.PERSON_ID = PD.PERSON_ID                 
		 WHERE  AEN.[START_DATE] < AEN.CANCEL_DATE 
	 			AND AEN.CANCEL_DATE >=  @FirstOfNextMonth;
				
              ----INSERT INTO APPLICATION_ENROLLMENT_HISTORY
		INSERT INTO   db4_ee.dbo.APPLICATION_ENROLLMENT_HISTORY (
		              APPLICATION_ID
		              ,PERSON_ID
		              ,[START_DATE]
		              ,CANCEL_DATE
		              ,CANCEL_REASON_ID
		              ,INSURANCE_SEND_DATE
		              ,PROGRAM_ID
		              ,PROGRAM_SUB_CATEGORY_ID
		              ,OVERRIDE_BY
		              ,OVERRIDE_DATE
		              ,OVERRIDE_REASON_ID
					  ,WORKER_NUMBER
					  ,WORKER_COUNTY_NUMBER
		              ,CREATED_BY
		              ,CREATED_DATE
		              ,UPDATED_BY
		              ,UPDATED_DATE 
		         )
		SELECT        APPLICATION_ID
		              ,PERSON_ID
		              ,[START_DATE]
		              ,CANCEL_DATE
		              ,CANCEL_REASON_ID
		              ,INSURANCE_SEND_DATE
		              ,PROGRAM_ID
		              ,PROGRAM_SUB_CATEGORY_ID
		              ,OVERRIDE_BY
		              ,OVERRIDE_DATE
		              ,OVERRIDE_REASON_ID
					  ,WORKER_NUMBER
					  ,WORKER_COUNTY_NUMBER
		              ,CREATED_BY
		              ,CREATED_DATE
		              ,UPDATED_BY
		              ,UPDATED_DATE 
		FROM		dbo.#UpdateCancelDate;

		-- Update Cancel Dates
		UPDATE	 AEN
		SET	 AEN.CANCEL_DATE =
		                        (
									CASE 
									WHEN	PD.DATE_OF_DEATH BETWEEN AEN.[START_DATE] AND AEN.CANCEL_DATE THEN DATEADD(D,1,EOMONTH(PD.DATE_OF_DEATH))
									WHEN	PD.DATE_OF_DEATH < AEN.[START_DATE] THEN AEN.[START_DATE]
									ELSE	AEN.CANCEL_DATE
									END        
								),
		        AEN.CANCEL_REASON_ID = 2,  -- Deceased
		        AEN.INSURANCE_SEND_IND = NULL,
				AEN.UPDATED_BY = @CreatedBy,
		        AEN.UPDATED_DATE = @CurrentDate
		 FROM	dbo.#UpdateCancelDate AS UCD 
		        INNER JOIN db4_ee.dbo.PERSON_DETAIL AS PD ON UCD.PERSON_ID = PD.PERSON_ID
		        INNER JOIN db4_ee.dbo.APPLICATION_ENROLLMENT AS AEN ON UCD.APPLICATION_ID = AEN.APPLICATION_ID AND UCD.PERSON_ID = AEN.PERSON_ID;

	
		-- Cancel Dates Notes Section Begin
		DECLARE  @InsertCancelDateNotes AS dbo.INSERT_NOTES_LIST
		INSERT INTO @InsertCancelDateNotes 
		                 (
		                   APPLICATION_ID
		                  ,PERSON_ID
		                  ,CREATED_BY
		                  ,NOTES_DESC
		                  ,NOTES_REF_ID
                       )
		SELECT	DISTINCT  UCD.APPLICATION_ID,
		                  UCD.PERSON_ID,
		                  @CreatedBy AS CREATED_BY,
		                 'Cancel date updated from '+CAST(UCD.CANCEL_DATE AS VARCHAR(10))+' to '+ CAST(AEN.CANCEL_DATE AS VARCHAR(10))+ ' for '+ FIRST_NAME + '' + LAST_NAME +  ' as verified from the ' + CAST(UCD.DOD_SOURCE AS VARCHAR(10)) +'.' AS NOTES_DESC,
		                  83 --CHANGED CANCEL DATE
		FROM	dbo.#UpdateCancelDate AS UCD
				INNER JOIN db4_ee.dbo.APPLICATION_ENROLLMENT AS AEN ON UCD.APPLICATION_ID = AEN.APPLICATION_ID AND UCD.PERSON_ID = AEN.PERSON_ID
		WHERE	UCD.CANCEL_DATE <> AEN.CANCEL_DATE;
				
		EXEC  [dbo].[usp_INSERT_NOTES] @InsertCancelDateNotes   

		-- Cancel Dates Notes Section End

		-- Update app id's in staging table
		UPDATE  DOD
		SET     DOD.APPLICATION_ID = QRY.APPLICATION_ID
		FROM    staging.[dbo].[DOD_STAGING] AS DOD
		        INNER JOIN 
		(
		SELECT  UCD.PERSON_ID,
		        UCD.APPLICATION_ID,
				ROW_NUMBER() OVER (PARTITION BY UCD.PERSON_ID ORDER BY UCD.APPLICATION_ID DESC) AS AppRowNumber	-- Sort by the highest application id.
		FROM    staging.[dbo].[DOD_STAGING] AS DOD 
		        INNER JOIN  dbo.#UpdateCancelDate AS UCD on dod.PERSON_ID = UCD.PERSON_ID
		) AS QRY ON DOD.PERSON_ID = QRY.PERSON_ID
		WHERE	QRY.AppRowNumber = 1;

		UPDATE 	D
		SET		D.APPLICATION_ID = DOD.APPLICATION_ID
		FROM 	DBO.#DODNotes AS D
				INNER JOIN staging.[dbo].[DOD_STAGING] AS DOD ON D.PERSON_ID = DOD.PERSON_ID;

		-- DOD Notes Section Begin
		DECLARE		   @INSERT_DOD_NOTES AS dbo.INSERT_NOTES_LIST
		INSERT INTO    @INSERT_DOD_NOTES 
	                  (
	                    APPLICATION_ID
	                    ,PERSON_ID
	                    ,CREATED_BY
	                    ,NOTES_DESC
	                    ,NOTES_REF_ID
	                   )
		SELECT DISTINCT APPLICATION_ID,
						PERSON_ID,
						@CreatedBy AS CREATED_BY,
						'The System has updated ' + CAST(db4_ee.[dbo].[fnGetFullName](D.PERSON_ID) AS VARCHAR(100)) + ' DOD to ' + CAST(D.DATE_OF_DEATH AS VARCHAR(10)) + ' as verified from the ' + CAST(D.DOD_SOURCE AS VARCHAR(10)) +'.',
						14
		FROM			dbo.#DODNotes AS D
		WHERE			D.APPLICATION_ID IS NOT NULL;

		EXEC  [dbo].[usp_INSERT_NOTES] @INSERT_DOD_NOTES

		-- Liability notes for DOD. 
		SELECT	A.APPLICATION_ID,
				PL.PERSON_ID,
				PL.PERSON_LIABILITY_ID,
				PL.LIABILITY_START_DATE,
				PL.LIABILITY_END_DATE,
				A.SUB_PROGRAM_CATEGORY_ID,
				DOD.DATE_OF_DEATH
		INTO	#LiabilitiesToTruncate
		FROM	db4_ee.dbo.PERSON_LIABILITY AS PL
				INNER JOIN dbo.#DODNotes AS DOD ON PL.PERSON_ID = DOD.PERSON_ID
				INNER JOIN  db4_ee.dbo.[APPLICATION] AS A On PL.ORIGINATING_APPLICATION_ID = A.APPLICATION_ID
		WHERE	((PL.LIABILITY_START_DATE <= DOD.DATE_OF_DEATH AND (PL.LIABILITY_END_DATE IS NULL OR PL.LIABILITY_END_DATE >= DOD.DATE_OF_DEATH)) 
				OR PL.LIABILITY_START_DATE > DOD.DATE_OF_DEATH) AND A.SUB_PROGRAM_CATEGORY_ID = 8;		-- 8 = E&D.
		
		DECLARE		   @INSERT_LIABILITY_NOTES AS dbo.INSERT_NOTES_LIST
		INSERT INTO    @INSERT_LIABILITY_NOTES 
	                  (
	                    APPLICATION_ID
	                    ,PERSON_ID
	                    ,CREATED_BY
	                    ,NOTES_DESC
	                    ,NOTES_REF_ID
	                   )
		SELECT DISTINCT	APPLICATION_ID,
						PERSON_ID,
						@CreatedBy AS CREATED_BY,
						'Due to the date of death (DOD), the current liability/transfer penalty segment''s end date has been updated and the future liability/transfer penalty segment''s have been deleted.',
						14
		FROM	#LiabilitiesToTruncate
		
		-- Truncate active liability segments.
		UPDATE	PL
		SET		PL.LIABILITY_END_DATE = EOMONTH(LT.DATE_OF_DEATH),
				PL.UPDATED_DATE=@CurrentDate,
				PL.UPDATED_BY=@CreatedBy
		FROM	db4_ee.dbo.PERSON_LIABILITY AS PL
				INNER JOIN #LiabilitiesToTruncate AS LT ON PL.PERSON_LIABILITY_ID = LT.PERSON_LIABILITY_ID
		WHERE	PL.LIABILITY_START_DATE <= LT.DATE_OF_DEATH AND (PL.LIABILITY_END_DATE IS NULL OR PL.LIABILITY_END_DATE >= LT.DATE_OF_DEATH);

		-- Update the liability change code.
		UPDATE	PLCD
		SET		PLCD.LIABILITY_CHANGE_CODE_ID = 22	--	22 = The claimant is deceased.
		FROM	db4_ee.dbo.PERSON_LIABILITY_CHANGE_CODE AS PLCD
				INNER JOIN #LiabilitiesToTruncate AS LT ON PLCD.PERSON_LIABILITY_ID = LT.PERSON_LIABILITY_ID
		WHERE	(LT.LIABILITY_START_DATE <= LT.DATE_OF_DEATH AND (LT.LIABILITY_END_DATE IS NULL OR LT.LIABILITY_END_DATE >= LT.DATE_OF_DEATH));

		DECLARE		@liabilityIdsToDelete TABLE(id BIGINT);
		INSERT INTO	@liabilityIdsToDelete
		SELECT		PL.PERSON_LIABILITY_ID
		FROM		db4_ee.dbo.PERSON_LIABILITY AS PL
					INNER JOIN #LiabilitiesToTruncate AS LT ON PL.PERSON_LIABILITY_ID = LT.PERSON_LIABILITY_ID
		WHERE		PL.LIABILITY_START_DATE > LT.DATE_OF_DEATH; 
		
		-- Delete the change codes.
		DELETE FROM	db4_ee.dbo.PERSON_LIABILITY_CHANGE_CODE
		WHERE		PERSON_LIABILITY_ID IN	(SELECT Id FROM @liabilityIdsToDelete);

		-- Perform the Delete.
		DELETE FROM	db4_ee.dbo.PERSON_LIABILITY
		WHERE		PERSON_LIABILITY_ID IN	(SELECT Id FROM @liabilityIdsToDelete);
		
		EXEC  [dbo].[usp_INSERT_NOTES] @INSERT_LIABILITY_NOTES

		-- Update IS_PROCESSED in staging table
		UPDATE 	DOD
		SET		DOD.IS_PROCESSED = 1,
				DOD.UPDATED_DATE = @CurrentDate
		FROM 	staging.[dbo].[DOD_STAGING] AS DOD 
				INNER JOIN dbo.#DODNotes AS D on DOD.PERSON_ID = D.PERSON_ID;
		
		SELECT  @IsSuccess = 'True';

		COMMIT TRANSACTION;
	
	SET TRAN ISOLATION LEVEL READ COMMITTED;
			
		END TRY
		BEGIN CATCH
		IF (@@TRANCOUNT > 0)
		BEGIN
			ROLLBACK TRANSACTION;
			PRINT 'Error detected, all changes reversed'
			SELECT  @IsSuccess = 'False';
		END
		SELECT
		    ERROR_NUMBER() AS ErrorNumber,
		    ERROR_SEVERITY() AS ErrorSeverity,
		    ERROR_STATE() AS ErrorState,
		    ERROR_PROCEDURE() AS ErrorProcedure,
		    ERROR_LINE() AS ErrorLine,
		    ERROR_MESSAGE() AS ErrorMessage
		END CATCH
END
GO