CREATE TABLE [dbo].[APPLICATION_ELDERLY_DISABLED_DETAIL]
(
	[APPLICATION_ELDERLY_DISABLED_DETAIL_ID] [BIGINT] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[APPLICATION_ID] [BIGINT] NOT NULL,
	[MSP_APPLICATION_ID] [BIGINT] NULL,
	[ELDERLY_DISABLED_PROGRAM_ID] [TINYINT] NULL,
	[EXPEDITE_FACILITY_PROVIDER_ID] [INT] NULL,
	[HOSPITAL_ADMISSION_DATE] [DATE] NOT NULL CONSTRAINT DF_APPLICATION_ELDERLY_DISABLED_DETAIL_HOSPITAL_ADMISSION_DATE DEFAULT('0001-01-01'),
	[NURSING_FACILITY_ADMISSION_DATE] [DATE] NOT NULL CONSTRAINT DF_APPLICATION_ELDERLY_DISABLED_DETAIL_NURSING_FACILITY_ADMISSION_DATE DEFAULT('0001-01-01'),
	[MARITAL_STATUS_DATE] [DATE] NOT NULL CONSTRAINT DF_APPLICATION_ELDERLY_DISABLED_DETAIL_MARITAL_STATUS_DATE DEFAULT('0001-01-01'),
	[FAX_NUMBER] [VARCHAR](20) NULL,
	[BLIND_IND] [BIT] NULL,
	[FAMILY_ALLOCATION_TOTAL_FAMILY_MEMBERS] [TINYINT] NULL,
	[CREATED_BY] [VARCHAR](50) NOT NULL CONSTRAINT DF_APPLICATION_ELDERLY_DISABLED_DETAIL_CREATED_BY DEFAULT(SUSER_NAME()),
	[CREATED_DATE] [DATETIME2] NOT NULL CONSTRAINT DF_APPLICATION_ELDERLY_DISABLED_DETAIL_CREATED_DATE DEFAULT(GETDATE()),
	[UPDATED_BY] [VARCHAR](50) NOT NULL CONSTRAINT DF_APPLICATION_ELDERLY_DISABLED_DETAIL_UPDATED_BY DEFAULT(SUSER_NAME()),
	[UPDATED_DATE] [DATETIME2] NOT NULL CONSTRAINT DF_APPLICATION_ELDERLY_DISABLED_DETAIL_UPDATED_DATE DEFAULT(GETDATE()),
 CONSTRAINT [PK_APPLICATION_ELDERLY_DISABLED_DETAIL] PRIMARY KEY CLUSTERED
(
	[APPLICATION_ELDERLY_DISABLED_DETAIL_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[APPLICATION_ELDERLY_DISABLED_DETAIL] ADD CONSTRAINT [FK_APPLICATION_ELDERLY_DISABLED_DETAIL_APPLICATION] FOREIGN KEY ([APPLICATION_ID]) REFERENCES [dbo].[APPLICATION] ([APPLICATION_ID])
GO

ALTER TABLE [dbo].[APPLICATION_ELDERLY_DISABLED_DETAIL] ADD CONSTRAINT [FK_APPLICATION_ELDERLY_DISABLED_DETAIL_MSP_APPLICATION] FOREIGN KEY ([MSP_APPLICATION_ID]) REFERENCES [dbo].[APPLICATION] ([APPLICATION_ID])
GO

ALTER TABLE [dbo].[APPLICATION_ELDERLY_DISABLED_DETAIL] ADD CONSTRAINT [FK_APPLICATION_ELDERLY_DISABLED_DETAIL_ELDERLY_DISABLED_PROGRAM_ID] FOREIGN KEY ([ELDERLY_DISABLED_PROGRAM_ID]) REFERENCES [dbo].[REF_ELDERLY_DISABLED_PROGRAM] ([ELDERLY_DISABLED_PROGRAM_ID])
GO

ALTER TABLE [dbo].[APPLICATION_ELDERLY_DISABLED_DETAIL] ADD CONSTRAINT [FK_APPLICATION_ELDERLY_DISABLED_DETAIL_REF_EXPEDITE_FACILITY_PROVIDER] FOREIGN KEY ([EXPEDITE_FACILITY_PROVIDER_ID]) REFERENCES [dbo].[REF_EXPEDITE_FACILITY_PROVIDER] ([EXPEDITE_FACILITY_PROVIDER_ID])
GO

CREATE UNIQUE NONCLUSTERED INDEX [UIX_APPLICATION_ELDERLY_DISABLED_DETAIL_APPLICATION_ID] ON [dbo].[APPLICATION_ELDERLY_DISABLED_DETAIL] ([APPLICATION_ID]) WITH (FILLFACTOR = 90) ON [Indexes]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Table to contain elderly or disabled details for an application.', @level0type='SCHEMA', @level0name=N'dbo', @level1type='TABLE', @level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=NULL, @level2name=NULL
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Primary Key' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'APPLICATION_ELDERLY_DISABLED_DETAIL_ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Application Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'APPLICATION_ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Application Id for an associated MSP create application' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'MSP_APPLICATION_ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'E&D program type' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'ELDERLY_DISABLED_PROGRAM_ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Expedite facility provider id (either a hospital provider id or nursing facility provider id).' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'EXPEDITE_FACILITY_PROVIDER_ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Hospital admission date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'HOSPITAL_ADMISSION_DATE'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Nursing facility admission date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'NURSING_FACILITY_ADMISSION_DATE'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Marital status date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'MARITAL_STATUS_DATE'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'The applicant''s fax number' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'FAX_NUMBER'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Blind indicator' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'BLIND_IND'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Total Family Members count for QIT/Allocation calculations' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'FAMILY_ALLOCATION_TOTAL_FAMILY_MEMBERS'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Created by' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'CREATED_BY'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Created date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'CREATED_DATE'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Updated by' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'UPDATED_BY'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Updated date' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'APPLICATION_ELDERLY_DISABLED_DETAIL', @level2type=N'COLUMN', @level2name=N'UPDATED_DATE'
GO