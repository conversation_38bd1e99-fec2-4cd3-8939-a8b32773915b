﻿@model Cares.Portal.Worker.Models.Models.PresumptiveEligibility.ViewModel.PepDeterminersViewModel
@using Cares.Api.Infrastructure
@using Cares.Portal.Infrastructure
@using System.Web.Optimization
@using Cares.Portal.Infrastructure.VGSecurity
@using System.Text.RegularExpressions;
<main role="main" class="container-fluid">
	<div class="row ml-2">
		<h4 class="mt-1">Determiner</h4>
		@* Only Medicaid Admin are allowed to add PEP Determiner *@
		@if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
		{
			<a class="btn btn-info btn-gradient shadow ml-4" id="btnAddPEPDeterminer" href="@Url.Action("AddPEPDeterminer","PresumptiveEligibility")">Add Determiner</a>
		}
	</div>
    <div id="divDeterminers" class="landingContent mt-2">
        <table id="tblDeterminers" class="table table-striped table-bordered compact">
            <thead>
                <tr>
                    <th>Determiner Name</th>
                    <th>Phone Number</th>
                    <th>Email Address</th>
                    <th>Provider</th>
                    <th>Status</th>
                    <th>Active Date</th>
                    <th>Deactivated Date</th>
                </tr>
            </thead>
            <tbody style="display:none">
                @foreach (var determiner in Model.Determiners)
                {
                    <tr>
                        <td>
                            @if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
                            {
                                @Html.ActionLink(determiner.DeterminerFullName, "AddPEPDeterminer", new { pepDeterminerId = determiner.DeterminerId })
                            }
                            else
                            {
                                @(determiner.DeterminerFullName)
                            }

                        </td>
                        <td>
                            @*Overriting the phone format to show always in same Pattern: ************ *@
                            @{
								var phoneNo = determiner.DeterminerPhoneNo.Replace("-", "");
                                phoneNo = Regex.Replace(phoneNo, @"^(\d{3})(\d{3})(\d{4})$", "$1-$2-$3");
                            }
                            @phoneNo
                        </td>
                        <td>
                            @determiner.DeterminerEmail
                        </td>
                        <td>
                            @determiner.Provider.ProviderName
                        </td>
                        <td>
                            @determiner.DeterminerStatusDesc
                        </td>
                        <td>
                            @determiner.ActiveDate
                        </td>
                        <td>
                            @determiner.DeactivatedDate
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</main>
<script language="javascript">
    // Setup - add a text input to each header cell
    $('#tblDeterminers thead tr')
        .clone(true)
        .addClass('filters')
        .appendTo('#tblDeterminers thead').hide();

    $(document).ready(function () {
        $('#tblDeterminers').DataTable({
            lengthMenu: [25, 20, 15, 10],
            orderCellsTop: true,
            fixedHeader: true,
            initComplete: function () {
                $('#tblDeterminers tbody').show();
                // Hide the top "search" bar.  We have filtering on each column
                $('#tblDeterminers_filter').hide();

                let trFilter = $('<tr class="filters">');
                let dtResult = $('#tblDeterminers').DataTable();

                // For each column
                dtResult.columns().every(function () {
                    let column = this;
                    let colIdx = column.index();

                    if (colIdx == 4) { // Active Column
                        //  Create select box filter
                        let statuses = ['Active', 'Inactive', 'Suspended'];
                        let thFilter = DataTablesAddFilter.AddSelectFilterUsingCustomData(column, 'Active', statuses, null, null, false);

                        $(trFilter).append(thFilter);
                    }
                    else { // All others:  Text box
                        let header = $(column.header()).html();
                        let thFilter = DataTablesAddFilter.AddTextBoxFilter(column, header);

                        $(trFilter).append($(thFilter));
                    }
                });

                //  Show the filter fields
                $('#tblDeterminers thead').append($(trFilter)).show();
            }
        });
    });
</script>