﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <connectionStrings>
    <add name="CaresApplicationDBEntities" connectionString="metadata=res://*/CaresApplicationDB.csdl|res://*/CaresApplicationDB.ssdl|res://*/CaresApplicationDB.msl;provider=System.Data.SqlClient;provider connection string='data source=CARESDEVSQL1\cares;initial catalog=db4_ee;Integrated Security=True;Encrypt=True;trustServerCertificate=true;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />
    <add name="CaresStagingDBEntities" connectionString="metadata=res://*/CaresStagingDB.csdl|res://*/CaresStagingDB.ssdl|res://*/CaresStagingDB.msl;provider=System.Data.SqlClient;provider connection string='data source=CARESDEVSQL1\cares;initial catalog=staging;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />
    <add name="Log4NetConnectionString" connectionString="data source=CARESDEVSQL1\cares;initial catalog=Log;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true" />
    <!--Used in the WP Dashboard:-->
    <add name="CaresLogDBEntities" connectionString="metadata=res://*/CaresLogDB.csdl|res://*/CaresLogDB.ssdl|res://*/CaresLogDB.msl;provider=System.Data.SqlClient;provider connection string='data source=CARESDEVSQL1\cares;initial catalog=Log;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true;MultipleActiveResultSets=True;App=EntityFramework'" providerName="System.Data.EntityClient" />
    <add name="AdoCaresStagingDB" connectionString="data source=CARESDEVSQL1\cares;initial catalog=staging;persist security info=True;Integrated Security=True;Encrypt=True;trustServerCertificate=true" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="log4net.Internal.Debug" value="false" />
    <add key="LoggingComponent" value="AppTier" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="LogHttpRequests" value="true" />
    <add key="smtpHost" value="email.medicaid.alabama.gov" />
    <add key="smtpPort" value="25" />
    <add key="noReply" value="<EMAIL>" />
    <add key="AccountCreation" value="Alabamacares : Account Creation" />
    <add key="IsMSPEnabled" value="true" />
    <add key="AccountingLettersFolderPath" value="\\caresstgats1.medicaid.al.gov\Dev-data\Letter\Completed\AccountingLetters" />
    <add key="LandingSearchResultsCount" value="500" />
    <!--Count of search results to be returned from the database-->
    <add key="WebMethodUserName" value="esbserviceuser" />
    <add key="WebMethodPassword" value="esbuserdev@123" />
    <add key="WebMethodUrl" value="https://caresdevis1.medicaid.al.gov:7000/rest/EEInHouseProcessor/restgateway/" />
    <add key="WebMethodLookup" value="https://caresdevis1.medicaid.al.gov:7000/rest/EEOnlineAppProcessor/EECreateApp/restgateway/" />
    <add key="WebMethodHubUrl" value="https://caresdevis1.medicaid.al.gov:7000/rest/EEHubProcessor/restGateway/" />
    <add key="LettersURL" value="https://caresdevis1.medicaid.al.gov:7000/rest/EELetterGenerator/restGateway/createLetter" />
    <add key="WebMethodBaseUrl" value="https://caresdevis1.medicaid.al.gov:7000/" />
    <add key="ELEEnrollmentWebmethodFullPath" value="rest/EEScheduledBatchJobs/expressLaneEligibilityEnrollment/restGateway/" />
    <add key="MaxRIIParallelThreads" value="10" />
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.7.2" />
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Novalys.VisualGuard.Security" publicKeyToken="8e423a8f05ffd0dc" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2024.4.2504.8" newVersion="2024.4.2504.8" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Permissions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Principal.Windows" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.AccessControl" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Caching.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <entityFramework>
    <interceptors>
      <interceptor type="Cares.Data.DataAbstractionLayer.TemporalTreeCommandInterceptor, Cares.Data" />
    </interceptors>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <log4net>
    <appender name="AdoNetAppender" type="log4net.Appender.AdoNetAppender">
      <bufferSize value="1" />
      <!-- Only use this for testing. Normal value should be 50 -->
      <connectionType value="System.Data.SqlClient.SqlConnection, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      <connectionStringName value="Log4NetConnectionString" />
      <!-- TokenID, Component, UserName, URL, Msg, Request, Response, BrowserInfo, IPAddress, ScreenAction are custom columns in the log table -->
      <commandText value="INSERT INTO Application_Log ([ApplicationLogDateTime],[TokenId],[Level],[Component],[Logger],[Message],[Exception],[UserName],[ApplicationId],[PersonId],[URL],[REQUEST],[RESPONSE],[BrowserInfo],[IPAddress],[ScreenAction]) VALUES(@Log_applicationlogdatetime,@TokenId,@Log_level,@Component,@Logger,IIF(LEN(@Msg) = 0, NULL, @Msg),IIF(LEN(@Exception) = 0, NULL, @Exception),IIF(LEN(@UserName) = 0, NULL, @UserName),IIF(@ApplicationId = 0, NULL, @ApplicationId),IIF(@PersonId = 0, NULL, @PersonId),IIF(LEN(@URL) = 0, NULL, @URL),@Request,@Response,IIF(LEN(@BrowserInfo) = 0, NULL, @BrowserInfo),IIF(LEN(@IPAddress) = 0, NULL, @IPAddress),IIF(LEN(@ScreenAction)=0,NULL,@ScreenAction));" />
      <parameter>
        <parameterName value="@log_applicationlogdatetime" />
        <dbType value="DateTime" />
        <size value="25" />
        <layout type="log4net.Layout.RawTimeStampLayout" />
      </parameter>
      <parameter>
        <parameterName value="@tokenid" />
        <dbType value="String" />
        <size value="50" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{tokenid}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_level" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%level" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@component" />
        <dbType value="String" />
        <size value="20" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{component}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logger" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%logger" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@message" />
        <dbType value="String" />
        <size value="4000" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@exception" />
        <dbType value="String" />
        <size value="2000" />
        <layout type="log4net.Layout.ExceptionLayout" />
      </parameter>
      <!-- Custom columns in the log table -->
      <parameter>
        <parameterName value="@username" />
        <dbType value="String" />
        <size value="255" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{username}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@applicationId" />
        <dbType value="Int64" />
        <size value="64" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{applicationId}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@personId" />
        <dbType value="Int64" />
        <size value="64" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{personId}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@url" />
        <dbType value="String" />
        <size value="255" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{url}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@msg" />
        <dbType value="String" />
        <size value="4000" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{msg}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@request" />
        <dbType value="Xml" />
        <size value="30000" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{request}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@response" />
        <dbType value="Xml" />
        <size value="30000" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{response}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@browserinfo" />
        <dbType value="String" />
        <size value="255" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{browserinfo}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@ipaddress" />
        <dbType value="String" />
        <size value="15" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{ipaddress}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@screenaction" />
        <dbType value="String" />
        <size value="400" />
        <layout type="Cares.Infrastructure.Log.CustomLayoutPattern, Cares.Infrastructure.Log">
          <conversionPattern value="%customInfo{screenaction}" />
        </layout>
      </parameter>
    </appender>
    <root>
      <level value="ALL" />
      <appender-ref ref="AdoNetAppender" />
    </root>
  </log4net>
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="textWriterTraceListener" type="System.Diagnostics.TextWriterTraceListener" initializeData="D:\Logs\log4net.txt" />
      </listeners>
    </trace>
  </system.diagnostics>
  <system.webServer>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>
</configuration>