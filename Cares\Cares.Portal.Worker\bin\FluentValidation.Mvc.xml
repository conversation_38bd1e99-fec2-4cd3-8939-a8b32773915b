<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FluentValidation.Mvc</name>
    </assembly>
    <members>
        <member name="P:FluentValidation.Mvc.CustomizeValidatorAttribute.RuleSet">
            <summary>
            Specifies the ruleset which should be used when executing this validator.
            This can be a comma separated list of rulesets. The string "*" can be used to indicate all rulesets.
            The string "default" can be used to specify those rules not in an explict ruleset.
            </summary>
        </member>
        <member name="P:FluentValidation.Mvc.CustomizeValidatorAttribute.Properties">
            <summary>
            Specifies a whitelist of properties that should be validated, as a comma-separated list.
            </summary>
        </member>
        <member name="P:FluentValidation.Mvc.CustomizeValidatorAttribute.Interceptor">
            <summary>
            Specifies an interceptor that can be used to customize the validation process.
            </summary>
        </member>
        <member name="P:FluentValidation.Mvc.CustomizeValidatorAttribute.Skip">
            <summary>
            Indicates whether this model should skip being validated. The default is false.
            </summary>
        </member>
        <member name="M:FluentValidation.Mvc.CustomizeValidatorAttribute.ToValidatorSelector">
            <summary>
            Builds a validator selector from the options specified in the attribute's properties.
            </summary>
        </member>
        <member name="T:FluentValidation.Mvc.FluentValidationModelValidator">
            <summary>
            ModelValidator implementation that uses FluentValidation.
            </summary>
        </member>
        <member name="T:FluentValidation.Mvc.FluentValidationModelValidatorProvider">
            <summary>
            Implementation of ModelValidatorProvider that uses FluentValidation.
            </summary>
        </member>
        <member name="M:FluentValidation.Mvc.FluentValidationModelValidatorProvider.Configure(System.Action{FluentValidation.Mvc.FluentValidationModelValidatorProvider})">
            <summary>
            Initializes the FluentValidationModelValidatorProvider using the default options and adds it in to the ModelValidatorProviders collection.
            </summary>
        </member>
        <member name="T:FluentValidation.Mvc.IValidatorInterceptor">
            <summary>
            Specifies an interceptor that can be used to provide hooks that will be called before and after MVC validation occurs.
            </summary>
        </member>
        <member name="M:FluentValidation.Mvc.IValidatorInterceptor.BeforeMvcValidation(System.Web.Mvc.ControllerContext,FluentValidation.ValidationContext)">
            <summary>
            Invoked before MVC validation takes place which allows the ValidationContext to be customized prior to validation.
            It should return a ValidationContext object.
            </summary>
            <param name="controllerContext">Controller Context</param>
            <param name="validationContext">Validation Context</param>
            <returns>Validation Context</returns>
        </member>
        <member name="M:FluentValidation.Mvc.IValidatorInterceptor.AfterMvcValidation(System.Web.Mvc.ControllerContext,FluentValidation.ValidationContext,FluentValidation.Results.ValidationResult)">
            <summary>
            Invoked after MVC validation takes place which allows the result to be customized.
            It should return a ValidationResult.
            </summary>
            <param name="controllerContext">Controller Context</param>
            <param name="validationContext">Validation Context</param>
            <param name="result">The result of validation.</param>
            <returns>Validation Context</returns>
        </member>
        <member name="T:FluentValidation.Mvc.RuleSetForClientSideMessagesAttribute">
            <summary>
            Specifies which ruleset should be used when deciding which validators should be used to generate client-side messages.
            </summary>
        </member>
        <member name="M:FluentValidation.Mvc.ValidationResultExtension.AddToModelState(FluentValidation.Results.ValidationResult,System.Web.Mvc.ModelStateDictionary,System.String)">
            <summary>
            Stores the errors in a ValidationResult object to the specified modelstate dictionary.
            </summary>
            <param name="result">The validation result to store</param>
            <param name="modelState">The ModelStateDictionary to store the errors in.</param>
            <param name="prefix">An optional prefix. If ommitted, the property names will be the keys. If specified, the prefix will be concatenatd to the property name with a period. Eg "user.Name"</param>
        </member>
        <member name="M:FluentValidation.Mvc.ValidationResultExtension.SetRulesetForClientsideMessages(System.Web.Mvc.ControllerContext,System.String[])">
            <summary>
            Sets the rulests used when generating clientside messages.
            </summary>
            <param name="context">Http context</param>
            <param name="ruleSets">Array of ruleset names</param>
        </member>
    </members>
</doc>
