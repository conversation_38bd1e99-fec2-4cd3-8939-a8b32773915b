using AlabamaConnectExpress.BLL.ElderlyDisabled;
using AlabamaConnectExpress.Classes;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Controllers.Application;
using Cares.Data.DataAbstractionLayer;
using Cares.Infrastructure.Log;
using Cares.Portal.Infrastructure;
using Cares.Portal.Infrastructure.VGSecurity;
using Cares.Portal.Worker.Models.Models;
using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using System.Web.Mvc.Html;
using static Cares.Infrastructure.Log.ScreenActionAttribute;
using ENUMS = Cares.Api.Infrastructure.Enums;

namespace AlabamaConnectExpress.Controllers.ElderlyAndDisabled
{
    public class ElderlyDisabledController : WpBaseController
    {
        #region constants
        public const string ElderlyDisabledApplicantFormName = "F_EDApplication";
        public const string ElderlyDisabledRepresentativeFormName = "F_EDRepresentative";
        public const string ElderlyDisabledNonMagiIncomeFormName = "F_EDNonMagiIncome";
        public const string ElderlyDisabledQitAndAllocationFormName = "F_EDQitAndAllocation";
        public const string ElderlyDisabledSpouseFormName = "F_EDSpouse";
        public const string ElderlyDisabledPropertyFormName = "F_EDProperty";
        public const string ElderlyDisabledVeteranFormName = "F_EDVeteran";
        public const string ElderlyDisabledResourceFormName = "F_EDResource";
        public const string ElderlyDisabledPersonalPropertyFormName = "F_EDPersonalProperty";
        public const string ElderlyDisabledMedicalInsuranceFormName = "F_EDMedicalInsurance";
        public const string ElderlyDisabledHouseholdMembersFormName = "F_EDHouseholdMembers";
        public const string ElderlyDisabledLifeInsuranceFormName = "F_EDLifeInsurance";
        public const string ElderlyDisabledLiabilityFormName = "F_EDLiability";
        public const string ElderlyDisabledLiabilityTestFormName = "F_EDLiabilityTest";
        public const string ElderlyDisabledEligibilityEnrollmentFormName = "F_EDEligEnroll";
        public const string ElderlyDisabledFinancialInstitutionFormName = "F_FinancialInstitutionEdit";
        private const string elderlyDisabledScreenName = "Elderly Disabled";
        private const string saveEDApplicationJavascriptFunction = "ValidateAndSaveApplication()";
        private const string saveEDRepresentativeJavascriptFunction = "ElderlyDisabledRepresentativeSave()";
        private const string saveEDSpouseJavascriptFunction = "BeginSaveEDSpouse()";
        private const string saveEDPropertyJavascriptFunction = "SavePropertyInfo()";
        private const string saveEDResourceJavascriptFunction = "SaveResourceInfo()";
        private const string saveEDPersonalPropertyJavascriptFunction = "SavePersonalPropertyInfo()";
        private const string saveEDVeteranJavascriptFunction = "SaveVeteranInfoAjax()";
        private const string saveEDHouseholdMembersJavascriptFunction = "SaveEDHouseholdMembers()";
        private const string saveEDNonMagiIncomesJavascriptFunction = "SaveNonMagiIncomes()";
        private const string saveEDQitAllocationJavascriptFunction = "SaveQitAllocationInfo()";
        private const string saveEDMedicalInsuranceJavascriptFunction = "SaveEDMedicalInsurance()";
        private const string saveEDLifeInsuranceJavascriptFunction = "SaveEDLifeInsurance()";
        private const string saveEDLiabilityJavascriptFunction = "SaveEDLiabilityTestData()";
        private const string saveEDEligibilityEnrollmentJavascriptFunction = "SaveEDEligiblityEnrollment()";
        #endregion constants

        private HumanReadableErrorMessages humanReadableErrors = new HumanReadableErrorMessages();

        /// <summary>
        /// Elderly Disabled Screen
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="newApp">The new application.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays an Elderly and Disabled application.")]
        public async Task<ActionResult> Application(long applicationId = 0, bool newApp = false)
        {
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDApplicationJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledApplicantFormName;
            ElderlyDisabledApplicationViewModel appViewModel = null;

            // When new app and no appid provided consider it as a New E&D application.
            if (newApp && applicationId == 0)
            {
                ViewBag.IsNewApplication = true;
                appViewModel = new ElderlyDisabledApplicationViewModel();
                appViewModel.Application.ApplicationSourceId = (byte)ENUMS.enumApplicationSource.Paper;
                appViewModel.Application.ApplicationStatusId = (byte)ENUMS.enumApplicationStatus.Received;
                appViewModel.Application.ApplicationTypeId = (byte)ENUMS.enumApplicationType.New;
                appViewModel.Application.ReceivedDate = DateTime.Today;
                appViewModel.Application.SubProgramCategoryId = (byte)Cares.Api.Infrastructure.Constants.SubProgramCategories.EandD;
                appViewModel.ApplicationDetail.RelAppFilerId = (int)ENUMS.enumRelationship.Self;
                appViewModel.Application.DataEnteredBy = this.CallingUsername;
            }
            else
            {
                // When new app and has appid set IsNewApplication to true then disable left menu.
                if (newApp && applicationId > 0)
                {
                    ViewBag.IsNewApplication = true;
                }
                else
                {
                    ViewBag.IsNewApplication = false;
                }
                ElderlyDisabledBll edBll = new ElderlyDisabledBll(this);

                appViewModel = await edBll.GetApplicationViewModel(applicationId, TokenId);

                // Determine/get liability segments, whether to show the DOD liability prompt or not.
                var liabilityInfo = await edBll.GetPersonLiabilityInfo(applicationId, appViewModel.ContactPersonId, CallingUsername, TokenId);
                var segments = (liabilityInfo.LiabilitySegments ?? new List<ElderlyDisabledLiabilityDetailViewModel>())
                    .Select(segment => new
                    {
                        segment.LiabilityStartDate,
                        segment.LiabilityEndDate
                    }).ToList();
                ViewBag.EnDLiabilities = JsonConvert.SerializeObject(segments);
            }

            if (appViewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View("ElderlyDisabled", appViewModel);
            }

            return RedirectToErrorPage(appViewModel);
        }

        /// <summary>
        /// Form-post save of an E&D application
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read, ActionTypes.Insert, ActionTypes.Update, ActionTypes.Delete },
            ActionDescription = "Saves an Elderly and Disabled application.")]
        public async Task<ActionResult> Application(ElderlyDisabledApplicationViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.Application.SubProgramCategoryId, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDApplicationJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledApplicantFormName;
            copyMailingAddressToHomeAddress(formData);
            ViewBag.IsNewApplication = false;

            if (ModelState.IsValid)
            {
                formData.UpdatedBy = CallingUsername;

                // Remove any deleted phone rows
                formData.PersonPhones.Phones = formData.PersonPhones.Phones?.Where(p => !p.IsDeleted).ToList();

                var result = await edBll.SaveApplication(formData, TokenId);

                if (result.IsSuccessful)
                {
                    TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                    return RedirectToAction("Application", new { applicationId = result.Id });
                }
                else
                {
                    Log.Error(result.ErrorMessage, TokenId);
                    ModelState.AddModelError(string.Empty, result.ErrorMessage);
                }
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.Application.ApplicationId);

            return View("ElderlyDisabled", formData);
        }

        /// <summary>
        /// Gets worker numbers by district office id.
        /// </summary>
        /// <param name="districtOfficeId">The district office identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Gets worker numbers by district office id.")]
        public async Task<JsonResult> GetWorkerNumbers(byte? districtOfficeId)
        {
            var workerNumbers = WorkerPortalAccountDal.GetWorkersByDistrictOffice(Convert.ToByte(districtOfficeId ?? 0)).Select(i => new
            {
                WorkerNumber = i.WORKER_NUMBER.ToString().Substring(2)
            }).OrderBy(i => i.WorkerNumber).ToList();

            return Json(workerNumbers, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Representative Screen
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly Disabled Representative screen.")]
        public async Task<ActionResult> ElderlyDisabledRepresentative(int applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDRepresentativeJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledRepresentativeFormName;

            ElderlyDisabledRepresentativesViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetRepresentativeInfo(applicationId, CallingUsername, TokenId);

                await gatheringDataForApplicantInfo(applicationId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledRepresentativesViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves representative data
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update, ActionTypes.Delete },
            ActionDescription = "Saves the Elderly Disabled Representative form data.")]
        public async Task<ActionResult> ElderlyDisabledRepresentative(ElderlyDisabledRepresentativesViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDRepresentativeJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledRepresentativeFormName;

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.SaveRepresentativeInfo(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledRepresentative", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }
            else
            {
                ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
                await GetApplicationInformationBarInfo(formData.ApplicationId);

                await gatheringDataForApplicantInfo(formData.ApplicationId);

                return View("ElderlyDisabledRepresentative", formData);
            }
        }

        /// <summary>
        /// Obtaining Applicant Info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        private async Task gatheringDataForApplicantInfo(long applicationId)
        {
            var result = await (new ElderlyDisabledBll(this)).GetApplicationViewModel(applicationId, TokenId);
            if (result.IsSuccessful)
            {
                var phonesInfo = result.PersonPhones;

                if (phonesInfo?.Phones?.Count() > 0)
                {
                    var namePrefix = "Sponsors[{index}].";
                    string phoneListNamePrefix = namePrefix + "PhoneList.";

                    //Cleaning Phone and Person Ids because of the SP not inserting them but updating under Person table only. --[usp_SAVE_SPONSOR_PHONE]
                    phonesInfo.Phones.ForEach(each =>
                    {
                        each.PhoneId = 0;
                        each.PersonId = 0;
                        each.PersonPhoneId = 0;
                    });

                    ViewData[PortalConstants.ViewDataConstants.IsPhoneRequired] = false;
                    ViewData[PortalConstants.ViewDataConstants.NamePrefix] = phoneListNamePrefix;
                    ViewData[PortalConstants.ViewDataConstants.OwningPhoneListIndex] = "{index}";
                    var htmlPhones = PartialView("_PersonPhoneList", phonesInfo).RenderToString(this);

                    ViewData[PortalConstants.ViewDataConstants.ApplicantPhonesHtmlString] = htmlPhones;
                }
                else
                {
                    ViewData[PortalConstants.ViewDataConstants.ApplicantPhonesHtmlString] = string.Empty;
                }

                var jsonResulted = Newtonsoft.Json.JsonConvert.SerializeObject(result);

                ViewData[PortalConstants.ViewDataConstants.ApplicantInfoJson] = jsonResulted;

                //If Nursing Home Program: take Nursing address, else: get person mailing address.
                if (result.ApplicationElderlyDisabledDetail.NursingHomeId != null && result.ApplicationElderlyDisabledDetail.NursingHomeId > 0)
                {
                    var nhInfo = await (new ElderlyDisabledBll(this)).GetExpediteFacilityById((int)result.ApplicationElderlyDisabledDetail.NursingHomeId, CallingUsername, TokenId);

                    ViewData[PortalConstants.ViewDataConstants.ApplicantAddressJson] = Newtonsoft.Json.JsonConvert.SerializeObject(nhInfo.FacilityAddress);
                }
                else
                {
                    ViewData[PortalConstants.ViewDataConstants.ApplicantAddressJson] = Newtonsoft.Json.JsonConvert.SerializeObject(result.PersonAddresses.Addresses.FirstOrDefault(a => a.AddressTypeId == (int)ENUMS.enumAddressType.Mailing)?.Address);
                }
            }
            else
            {
                ViewData[PortalConstants.ViewDataConstants.ApplicantInfoJson] = string.Empty;
                ViewData[PortalConstants.ViewDataConstants.ApplicantPhonesHtmlString] = string.Empty;
                ViewData[PortalConstants.ViewDataConstants.ApplicantAddressJson] = string.Empty;
            }
        }

        /// <summary>
        /// Saves representative data via AJAX
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update, ActionTypes.Delete },
            ActionDescription = "Saves the Elderly Disabled Representative form data.")]
        public async Task<JsonResult> ElderlyDisabledRepresentativeAjax(ElderlyDisabledRepresentativesViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayJsonNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDRepresentativeJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledRepresentativeFormName;
            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.SaveRepresentativeInfo(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return Json(new { success = true });
                    }
                    else
                    {
                        Log.Error(result.ErrorMessage, TokenId);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                ModelState.AddModelError(string.Empty, $"An error occurred while saving.  Log token={TokenId}");
            }

            string errorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            return Json(new { success = false, response = errorMessages, currentView = ViewData[PortalConstants.ViewDataConstants.FormId] });
        }

        /// <summary>
        /// Spouse Screen.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly and Disabled Spouse screen.")]
        public async Task<ActionResult> ElderlyDisabledSpouse(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDSpouseJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledSpouseFormName;

            ElderlyDisabledSpouseViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetSpouseInfo(applicationId, CallingUsername, TokenId);

                // Spouse IsCitizen needs AN answer (either true or false), otherwise validations fail
                // That's the only purpose for setting this value:
                viewModel.Spouse.IsUsCitizen = true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledSpouseViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves Spouse info
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update, ActionTypes.Delete },
            ActionDescription = "Saves the Elderly and Disabled Spouse data.")]
        public async Task<ActionResult> ElderlyDisabledSpouse(ElderlyDisabledSpouseViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDSpouseJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledSpouseFormName;

            // We always need a valid FormerSpouse list, even if empty:
            formData.FormerSpouses = formData.FormerSpouses ?? new List<ElderlyDisabledFormerSpouseViewModel>();

            if (ModelState.IsValid)
            {
                // Remove any deleted former spouse rows
                formData.FormerSpouses = formData.FormerSpouses.Where(p => !p.IsDeleted).ToList();

                var saveResult = await edBll.SaveSpouseInfo(formData, CallingUsername, TokenId);
                if (saveResult.IsSuccessful)
                {
                    TempData[PortalConstants.TempDataConstants.DataSaved] = true;

                    // The form retains the spouse data even after it gets deleted from the DB. Hence once the save is successful, we are redirecting the page.
                    // Performance wise, this solution is better than clearing/refreshing the page from the UI.
                    return RedirectToAction("ElderlyDisabledSpouse", new { applicationId = formData.ApplicationId });
                }
                else
                {
                    if (saveResult.CaresError == Cares.Api.Infrastructure.CaresError.DrasticAlert)
                    {
                        // Create a more informative error message if DrasticAlert was returned:
                        ModelState.AddModelError(string.Empty, "A person with that SSN already exists, but was not matched with the information provided.");
                    }
                    else
                    {
                        // Add some model state error
                        ModelState.AddModelError(string.Empty, $"An error occurred.  Log token = {TokenId} ");
                    }
                }
            }

            await GetApplicationInformationBarInfo(formData.ApplicationId);

            if (!ModelState.IsValid)
            {
                ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            }

            return View(formData);
        }

        /// <summary>
        /// Veteran Information Screen.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly Disabled Veteran Information screen.")]
        public async Task<ActionResult> ElderlyDisabledVeteranInformation(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDVeteranJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledVeteranFormName;
            ElderlyDisabledVeteranViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetVeteranDetails(applicationId, CallingUsername, TokenId);

            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledVeteranViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves veteran info
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly Disabled Veteran Information screen.")]
        public async Task<ActionResult> ElderlyDisabledVeteranInformation(ElderlyDisabledVeteranViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDVeteranJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledVeteranFormName;

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.SaveElderlyDisabledVeteranInfo(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledVeteranInformation", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Ajax version of save Veteran Info
        /// </summary>
        /// <param name="formData"></param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly Disabled Veteran Information screen.")]
        public async Task<JsonResult> ElderlyDisabledVeteranInformationAjax(ElderlyDisabledVeteranViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayJsonNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDVeteranJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledVeteranFormName;

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.SaveElderlyDisabledVeteranInfo(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return Json(new { success = true });
                    }
                    else
                    {
                        Log.Error(result.ErrorMessage, TokenId);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                ModelState.AddModelError(string.Empty, $"An error occurred while saving.  Log token={TokenId}");
            }

            string errorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            return Json(new { success = false, response = errorMessages, currentView = ViewData[PortalConstants.ViewDataConstants.FormId] });
        }

        /// <summary>
        /// Gets household members
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly Disabled Household Members.")]
        public async Task<ActionResult> ElderlyDisabledHouseholdMembers(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDHouseholdMembersJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledHouseholdMembersFormName;

            ElderlyDisabledHouseholdMembersViewModel viewModel = null;
            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetHouseholdMembers(applicationId, TokenId);

            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledHouseholdMembersViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves household members
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly Disabled Household Members.")]
        public async Task<ActionResult> ElderlyDisabledHouseholdMembers(ElderlyDisabledHouseholdMembersViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDHouseholdMembersJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledHouseholdMembersFormName;
            formData.HouseholdMembers = formData.HouseholdMembers ?? new List<ElderlyDisabledHouseholdMemberViewModel>();

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.SaveHouseholdMembers(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledHouseholdMembers", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Non-Magi Income Screen
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly Disabled Non-Magi Income screen.")]
        public async Task<ActionResult> ElderlyDisabledNonMagiIncome(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDNonMagiIncomesJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledNonMagiIncomeFormName;

            ElderlyDisabledNonMagiIncomeViewModel viewModel = null;
            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetNonMagiIncomeInfo(applicationId, CallingUsername, TokenId);

            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledNonMagiIncomeViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                //Storing Person Id to use in the Data Exchange population
                var contactPersonId = (ViewData[PortalConstants.ViewDataConstants.AppInfoBarVM] as ElderlyDisabledApplicationInfoBarViewModel).ContactPersonId;

                TempData[PortalConstants.TempDataConstants.PersonId] = contactPersonId;
                TempData[PortalConstants.TempDataConstants.ApplicationId] = applicationId;

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves Non-Magi income
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Non-Magi income.")]
        public async Task<ActionResult> ElderlyDisabledNonMagiIncome(ElderlyDisabledNonMagiIncomeViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDNonMagiIncomesJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledNonMagiIncomeFormName;

            formData.NonMagiIncomes = formData.NonMagiIncomes ?? new List<NonMagiIncomeViewModel>();

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.SaveNonMagiIncomes(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledNonMagiIncome", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }


        /// <summary>
        ///  Calls the calculation for VA Net amount
        /// </summary>
        /// <param name="applicationId">The app id.</param>
        /// <param name="applicationNonMagiIncomeId">The income id.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Get Net amount on VAs")]
        public async Task<ActionResult> CalculateVANetValues(long applicationId, int applicationNonMagiIncomeId)
        {
            var username = Convert.ToString(Session[PortalConstants.SessionVariable.VG_UserName]);

            var bll = new ElderlyDisabledBll(this);
            var success = await bll.CalculateVANetValues(applicationId, applicationNonMagiIncomeId, username, TokenId);

            return RedirectToAction("ElderlyDisabledNonMagiIncome", new { applicationId = applicationId });
        }

        /// <summary>
        /// Gets the page for the summary of Qit values and Allocation values per month.
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly Disabled Qit and Allocation Screen.")]
        public async Task<ActionResult> ElderlyDisabledQitAndAllocation(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDQitAllocationJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledQitAndAllocationFormName;

            ElderlyDisabledQitAndAllocationViewModel viewModel;
            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetQitAndAllocationByAppId(applicationId, CallingUsername, TokenId);
                viewModel.FamilyAllocationList = viewModel.FamilyAllocationList.OrderBy(d => d.Month).ToList();
                viewModel.SpouseAllocationList = viewModel.SpouseAllocationList.OrderBy(d => d.Month).ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledQitAndAllocationViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            await GetApplicationInformationBarInfo(applicationId);

            //Storing Person Id to use in the Data Exchange population
            var contactPersonId = (ViewData[PortalConstants.ViewDataConstants.AppInfoBarVM] as ElderlyDisabledApplicationInfoBarViewModel).ContactPersonId;

            TempData[PortalConstants.TempDataConstants.PersonId] = contactPersonId;
            TempData[PortalConstants.TempDataConstants.ApplicationId] = applicationId;

            return View(viewModel);
        }
        /// <summary>
        /// Submit the info for Spouse and Family Allocation values.
        /// </summary>
        /// <param name="formData">The data to be submitted for Allocation Spouse and Family</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Spouse and Family Allocation.")]
        public async Task<ActionResult> ElderlyDisabledQitAndAllocation(ElderlyDisabledQitAndAllocationViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDQitAllocationJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledQitAndAllocationFormName;

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.SaveQitAndAllocation(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledQitAndAllocation", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Property Screen
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly Disabled Property screen.")]
        public async Task<ActionResult> ElderlyDisabledProperty(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDPropertyJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledPropertyFormName;
            ElderlyDisabledPropertyViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetPropertyInformation(applicationId, CallingUsername, TokenId);

                // If Terminal, redirect the user
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledPropertyViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves property info
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Property info.")]
        public async Task<ActionResult> ElderlyDisabledProperty(ElderlyDisabledPropertyViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDPropertyJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledPropertyFormName;
            // Sanity check:  Ensure the lists exist even if no data was there:
            formData.PropertyParcels = formData.PropertyParcels ?? new List<ElderlyDisabledPropertyParcelViewModel>();
            formData.PreviousProperties = formData.PreviousProperties ?? new List<ElderlyDisabledPropertyPreviousViewModel>();
            formData.PropertyMobileHomes = formData.PropertyMobileHomes ?? new List<ElderlyDisabledPropertyMobileHomeViewModel>();

            if (ModelState.IsValid)
            {
                try
                {
                    // Clean up EDPropertyParcelDetail
                    foreach (var parcel in formData.PropertyParcels)
                    {
                        // Remove empty entries from EDPropertyParcelDetail
                        if (parcel.EDPropertyParcelDetail != null)
                        {
                            parcel.EDPropertyParcelDetail = parcel.EDPropertyParcelDetail.Where(d => !(
                                                    d.PropertyParcelDetailMonth == null)).ToList();
                        }
                    }

                    // remove empty Parcel rows
                    formData.PropertyParcels = formData.PropertyParcels.Where(p => !(
                                                    p.ParcelIdNumber == null &&
                                                    p.ParcelOwner == null &&
                                                    (p.AppraisedValue ?? 0) == 0 &&
                                                    (p.Equity ?? 0) == 0 &&
                                                    p.DoesSomeoneLiveThereNow == null &&
                                                    p.TenantName == null &&
                                                    p.TenantRelationshipTypeId == null &&
                                                    p.TenantOtherRelationshipDesc == null &&
                                                    p.IntendsToReturnHome == null &&
                                                    p.OwesMoneyOnParcel == null &&
                                                    p.HasReverseMortgage == null &&
                                                    p.HasLienOnParcel == null &&
                                                    p.HasLifeEstateOnParcel == null &&
                                                    p.HasHeirOnParcel == null &&
                                                    p.HasExcludedResource == null &&
                                                    p.ParcelExcludedResourceId == null &&
                                                    p.ParcelOtherExcludedResourceDesc == null &&
                                                    p.Address.AddressLine1 == null &&
                                                    p.Address.AddressLine2 == null &&
                                                    p.Address.City == null &&
                                                    p.Address.StateId == null &&
                                                    p.Address.ZipCode == null &&
                                                    p.Address.CountyId == null &&
                                                    p.Address.OutOfStateCounty == null)).ToList();

                    // Clean up EDPropertyMobileHomeDetail
                    foreach (var mobileHome in formData.PropertyMobileHomes)
                    {
                        // Remove empty entries from EDPropertyMobileHomeDetail
                        if (mobileHome.EDPropertyMobileHomeDetail != null)
                        {
                            mobileHome.EDPropertyMobileHomeDetail = mobileHome.EDPropertyMobileHomeDetail.Where(d => !(
                                                    d.MobileHomeDetailMonth == null)).ToList();
                        }
                    }

                    // remove empty MobileHome rows
                    formData.PropertyMobileHomes = formData.PropertyMobileHomes.Where(p => !(
                                                    p.LandownerName == null &&
                                                    (p.MobileHomeValueAmount ?? 0) == 0 &&
                                                    p.HasMobileHomeOnARentedLot == null &&
                                                    p.HasCountableResource == null &&
                                                    p.HasLeinOnMobileHome == null &&
                                                    p.MobileHomeHasExcludedResource == null &&
                                                    p.MobileHomeExcludedResourceId == null &&
                                                    p.MobileHomeOtherExcludedResourceDesc == null &&
                                                    p.Address.AddressLine1 == null &&
                                                    p.Address.AddressLine2 == null &&
                                                    p.Address.City == null &&
                                                    p.Address.StateId == null &&
                                                    p.Address.ZipCode == null &&
                                                    p.Address.CountyId == null &&
                                                    p.Address.OutOfStateCounty == null)).ToList();

                    // Clean up EDPreviousPropertyDetail
                    foreach (var previousProperties in formData.PreviousProperties)
                    {
                        // Remove empty entries from EDPreviousPropertyDetail
                        if (previousProperties.EDPreviousPropertyDetail != null)
                        {
                            previousProperties.EDPreviousPropertyDetail = previousProperties.EDPreviousPropertyDetail.Where(d => !(
                                                    (d.PreviousPropertyParcelAppraisedValue ?? 0) == 0 &&
                                                    (d.PreviousPropertyMobileHomeValueAmount ?? 0) == 0)).ToList();
                        }
                    }

                    // IsPreviousPropertyTypeParcel is selected but all other input fields are empty, then mark them as IsDeleted
                    foreach (var pp in formData.PreviousProperties)
                    {
                        if (pp.IsPreviousPropertyTypeParcel != null && IsEmptyPreviousProperty(pp))
                        {
                            pp.IsDeleted = true;
                        }
                    }

                    // remove empty previous property rows
                    formData.PreviousProperties = formData.PreviousProperties
                        .Where(pp => !(IsEmptyPreviousProperty(pp) && pp.IsPreviousPropertyTypeParcel == null))
                        .ToList();

                    var result = await edBll.SavePropertyInformation(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledProperty", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Helper method to determine if a property is "empty"
        /// </summary>
        /// <param name="pp">The property previous data.</param>
        /// <returns></returns>
        private bool IsEmptyPreviousProperty(ElderlyDisabledPropertyPreviousViewModel pp)
        {
            bool isParcelEmpty = pp.PreviousParcelOwnerName == null &&
                                 pp.PreviousParcelIdNumber == null &&
                                 pp.PreviousParcelAppraisedValue == null;

            bool isMobileHomeEmpty = pp.PreviousMobileHomeLandOwnerName == null &&
                                     pp.PreviousMobileHomeValueAmount == null;

            bool isAddressEmpty = pp.Address == null || (
                                     pp.Address.AddressLine1 == null &&
                                     pp.Address.AddressLine2 == null &&
                                     pp.Address.City == null &&
                                     pp.Address.StateId == null &&
                                     pp.Address.ZipCode == null &&
                                     pp.Address.CountyId == null &&
                                     pp.Address.OutOfStateCounty == null);

            bool isDeedInfoEmpty = pp.DeedDisposalDate == null &&
                                   pp.DeedListedOwner == null;

            return isParcelEmpty && isMobileHomeEmpty && isAddressEmpty && isDeedInfoEmpty;
        }

        /// <summary>
        /// Gets resource info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly and Disabled Resources screen.")]
        public async Task<ActionResult> ElderlyDisabledResource(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDResourceJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledResourceFormName;
            ElderlyDisabledResourceViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetResourceInformation(applicationId, CallingUsername, TokenId);
                var closedAccounts = viewModel.ResourceBankDetails.Where(d => d.DateClosed != null);
                var openAccounts = viewModel.ResourceBankDetails.Where(d => d.DateClosed == null);
                viewModel.ResourceBankDetails = openAccounts.OrderBy(d => d.BankName).ToList();
                viewModel.ResourceBankDetails.AddRange(closedAccounts.OrderBy(d => d.BankName).ToList());
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledResourceViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves resource info.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Resources.")]
        public async Task<ActionResult> ElderlyDisabledResource(ElderlyDisabledResourceViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDResourceJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledResourceFormName;

            // Sanity check:  Ensure the lists exist even if no data was there:
            formData.ResourceBankDetails = formData.ResourceBankDetails ?? new List<ElderlyDisabledResourceBankDetailViewModel>();
            formData.ResourceDetails = formData.ResourceDetails ?? new List<ElderlyDisabledResourceDetailViewModel>();
            formData.ResourceTransfers = formData.ResourceTransfers ?? new List<ElderlyDisabledResourceTransferViewModel>();

            if (ModelState.IsValid)
            {
                try
                {
                    // Clean up ResourceBankDetails
                    foreach (var bank in formData.ResourceBankDetails)
                    {
                        // Remove empty detail entries from ResourceBankDetails
                        if (bank.BankDetails != null)
                        {
                            bank.BankDetails = bank.BankDetails.Where(d => !(
                                            d.ResourceMonth == null &&
                                            (d.CurrentBalance ?? 0) == 0)).ToList();
                        }
                    }

                    // Clean up ResourceDetails
                    foreach (var resource in formData.ResourceDetails)
                    {
                        // Remove empty detail entries from ResourceDetails
                        if (resource.EDResourceMonthDetail != null)
                        {
                            resource.EDResourceMonthDetail = resource.EDResourceMonthDetail.Where(d => !(
                                            d.ResourceMonthDetail == null &&
                                            d.ResourceMonthDetailDateDisposed == null &&
                                            (d.ResourceMonthDetailApplicantAmount ?? 0) == 0)).ToList();
                        }
                    }

                    // Clean up ResourceTransfers
                    foreach (var resource in formData.ResourceTransfers)
                    {
                        // Remove empty Transfer detail entries from ResourceDetails
                        if (resource.EDTransferMonthDetail != null)
                        {
                            resource.EDTransferMonthDetail = resource.EDTransferMonthDetail.Where(d => !(
                                            d.ResourceMonth == null &&
                                            d.AmountGiven == null &&
                                            d.AmountReturned == null)).ToList();
                        }
                    }

                    var result = await edBll.SaveResourceInformation(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledResource", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Gets Life Insurance Info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly and Disabled Life Insurance screen.")]
        public async Task<ActionResult> ElderlyDisabledLifeInsurance(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledLifeInsuranceFormName;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDLifeInsuranceJavascriptFunction;
            ElderlyDisabledLifeInsuranceViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetLifeInsuranceInfo(applicationId, CallingUsername, TokenId);
                await SetupFinancialInstitutionsDropdown(applicationId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledLifeInsuranceViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// For ApplicantFinancialInstitutions dropdowns
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        private async Task SetupFinancialInstitutionsDropdown(long applicationId)
        {
            ElderlyDisabledResourceViewModel resources = await (new ElderlyDisabledBll(this)).GetResourceInformation(applicationId, CallingUsername, TokenId);
            var finanicalInstitions = resources?.ResourceBankDetails ?? new List<ElderlyDisabledResourceBankDetailViewModel>();

            List<SelectListItem> financialInstitutionsDropdownList = finanicalInstitions.ConvertAll(
                x =>
                {
                    return new SelectListItem
                    {
                        Text = x.BankName,
                        Value = x.BankName,
                        Selected = false
                    };
                });

            ViewBag.ApplicantFinancialInstitutions = financialInstitutionsDropdownList;
        }

        /// <summary>
        /// Saves Life Insurance Info
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Life Insurance info.")]
        public async Task<ActionResult> ElderlyDisabledLifeInsurance(ElderlyDisabledLifeInsuranceViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledLifeInsuranceFormName;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDLifeInsuranceJavascriptFunction;

            // Sanity check:  Ensure the lists exist even if no data was there:
            formData.LifeInsuranceDetails = formData.LifeInsuranceDetails ?? new List<ElderlyDisabledLifeInsuranceDetailViewModel>();
            formData.OtherBurialFundDetails = formData.OtherBurialFundDetails ?? new List<ElderlyDisabledOtherBurialFundsViewModel>();
            formData.AdditionalBurialFundDetails = formData.AdditionalBurialFundDetails ?? new List<ElderlyDisabledAdditionalBurialFundsViewModel>();

            if (ModelState.IsValid)
            {
                try
                {
                    // Remove deleted rows:
                    formData.LifeInsuranceDetails = formData.LifeInsuranceDetails.Where(p => !p.IsDeleted).ToList();
                    formData.OtherBurialFundDetails = formData.OtherBurialFundDetails.Where(p => !p.IsDeleted).ToList();
                    //formData.AdditionalBurialFundDetails = formData.AdditionalBurialFundDetails.Where(p => !p.IsDeleted).ToList();

                    foreach (var insuranceDetails in formData.LifeInsuranceDetails)
                    {
                        insuranceDetails.AdditionalLifeInsuranceDetails = insuranceDetails.AdditionalLifeInsuranceDetails.
                                                           Where(x => !(x.Month == null &&
                                                                        (x.PolicyValue ?? 0) == 0 &&
                                                                        (x.CashSurrenderValue ?? 0) == 0 &&
                                                                        x.DateDisposed == null &&
                                                                        x.IsContractRevocable == null &&
                                                                        x.HasCountableAmount == null &&
                                                                        (x.InsuranceDesignatedForBurial ?? 0) == 0
                                                                        )).ToList();

                        foreach (var insuranceAdditonalDetail in insuranceDetails.AdditionalLifeInsuranceDetails)
                        {
                           insuranceAdditonalDetail.ApplicationLifeInsuranceDetailId = insuranceDetails.ApplicationLifeInsuranceDetailId;
                        }
                    }

                    foreach (var otherBurialFundDetails in formData.OtherBurialFundDetails)
                    {
                        otherBurialFundDetails.OtherBurialFundsBudgetDetails = otherBurialFundDetails.OtherBurialFundsBudgetDetails
                                                                               .Where(x => !(x.EffectiveDate == null &&
                                                                                             (x.PrepaidBurialContractTotal ?? 0) == 0 &&
                                                                                             (x.ValueOfContractAfterDiscount ?? 0) == 0 &&
                                                                                             x.IsCountableAmount == null &&
                                                                                             x.DateDisposed == null &&
                                                                                             x.CountableAmountRemarks == null &&
                                                                                             (x.DeductInsuranceAmount ?? 0) == 0 &&
                                                                                             (x.NetAmountCountable ?? 0) == 0
                                                                                             )).ToList();
                        foreach (var budgetDetails in otherBurialFundDetails.OtherBurialFundsBudgetDetails)
                        {
                            budgetDetails.ApplicationOtherBurialFundsId = otherBurialFundDetails.ApplicationOtherBurialFundsId;
                            budgetDetails.PrepaidBurialSpaceDetails = budgetDetails.PrepaidBurialSpaceDetails.Where(
                                                                        x => !(x.PrepaidBurialContractId == null &&
                                                                               x.PrepaidBurialContractOtherDescription == null &&
                                                                               (x.BurialSpaceAmount ?? 0) == 0)).ToList();
                            foreach (var burialSpaceItem in budgetDetails.PrepaidBurialSpaceDetails)
                            {
                                burialSpaceItem.ApplicationOtherBurialFundsBudgetId = budgetDetails.ApplicationOtherBurialFundsBudgetId;
                            }
                        }
                    }

                    formData.AdditionalBurialFundDetails.ForEach(a =>
                        {
                            // fix the IDs
                            a.Budgets.ForEach(b => b.ApplicationAdditionalBurialFundsId = a.ApplicationAdditionalBurialFundsId);
                        }
                    );

                    var result = await edBll.SaveLifeInsuranceInfo(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledLifeInsurance", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            await SetupFinancialInstitutionsDropdown(formData.ApplicationId);

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Gets Personal Property Info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly and Disabled Personal Property screen.")]
        public async Task<ActionResult> ElderlyDisabledPersonalProperty(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDPersonalPropertyJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledPersonalPropertyFormName;
            ElderlyDisabledPersonalPropertyViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetPersonalPropertyInfo(applicationId, CallingUsername, TokenId);

            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledPersonalPropertyViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Saves Personal Property info.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Personal Property.")]
        public async Task<ActionResult> ElderlyDisabledPersonalProperty(ElderlyDisabledPersonalPropertyViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDPersonalPropertyJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledPersonalPropertyFormName;

            // Sanity check:  Ensure the lists exist even if no data was there:
            formData.AutoDetails = formData.AutoDetails ?? new List<ElderlyDisabledPersonalPropertyAutoViewModel>();
            formData.MachineDetails = formData.MachineDetails ?? new List<ElderlyDisabledPersonalPropertyMachineViewModel>();
            formData.CollectibleDetails = formData.CollectibleDetails ?? new List<ElderlyDisabledPersonalPropertyCollectibleViewModel>();

            if (ModelState.IsValid)
            {
                try
                {
                    // Remove deleted rows:
                    formData.AutoDetails = formData.AutoDetails.Where(p => !p.IsDeleted).ToList();
                    formData.MachineDetails = formData.MachineDetails.Where(p => !p.IsDeleted).ToList();
                    formData.CollectibleDetails = formData.CollectibleDetails.Where(p => !p.IsDeleted).ToList();

                    // Remove empty records
                    formData.AutoDetails = formData.AutoDetails.Where(p => !(p.AutoYear == null &&
                                                                             p.AutoMake == null &&
                                                                             p.AutoModel == null &&
                                                                             p.AutoValueAmount == null &&
                                                                             p.AutoUsage == null &&
                                                                             p.AutoOwedAmount == null &&
                                                                             p.AutoAdditionalInformation == null &&
                                                                             // TODO : Update/Drop the "AutoEquity", once final decision is made (whether to save the value to DB or not for summary UI purpose).
                                                                             p.AutoEquity == null &&
                                                                             p.HasAutoExcludedResource == null)).ToList();

                    foreach (var autoInfo in formData.AutoDetails)
                    {
                        autoInfo.AutoAdditionalDetails = autoInfo.AutoAdditionalDetails.
                                                            Where(x => !(x.AutoDetailMonth == null &&
                                                                         (x.AutoValueAmount ?? 0) == 0 &&
                                                                         (x.AutoOwedAmount ?? 0) == 0 &&
                                                                         x.DateDisposed == null &&
                                                                         x.HasAutoExcludedResource == null
                                                                         )).ToList();

                        foreach (var autoAdditonalDetail in autoInfo.AutoAdditionalDetails)
                        {
                            autoAdditonalDetail.PersonalPropertyAutoId = autoInfo.PersonalPropertyAutoId ?? 0;
                        }

                    }

                    formData.MachineDetails = formData.MachineDetails.Where(p => !(p.MachineType == null &&
                                                                                   p.MachinePurchasedYear == null &&
                                                                                   p.MachineValueAmount == null &&
                                                                                   p.MachineOwedAmount == null &&
                                                                                   p.MachineExcludedAmount == null)).ToList();
                    formData.CollectibleDetails = formData.CollectibleDetails.Where(p => !(p.CollectibleDescription == null &&
                                                                                           p.CollectibleValueAmount == null &&
                                                                                           p.CollectibleExcludedAmount == null &&
                                                                                           p.CollectibleCountableAmount == null)).ToList();

                    var result = await edBll.SavePersonalPropertyInfo(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledPersonalProperty", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Get medical insurance info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly Disabled Medical Insurance screen.")]
        public async Task<ActionResult> ElderlyDisabledMedicalInsurance(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDMedicalInsuranceJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledMedicalInsuranceFormName;
            ElderlyDisabledMedicalInsuranceViewModel viewModel = null;

            try
            {
                viewModel = await (new ElderlyDisabledBll(this)).GetMedicalInsuranceInfo(applicationId, CallingUsername, TokenId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledMedicalInsuranceViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Save medical insurance information.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Medical Insurance info.")]
        public async Task<ActionResult> ElderlyDisabledMedicalInsurance(ElderlyDisabledMedicalInsuranceViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewBag.SaveFunction = saveEDMedicalInsuranceJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledMedicalInsuranceFormName;
            // Sanity check:  Ensure the lists exist even if no data was there:
            formData.MedicalInsuranceDetails = formData.MedicalInsuranceDetails ?? new List<ElderlyDisabledMedicalInsuranceDetailViewModel>();
            if (ModelState.IsValid)
            {
                try
                {
                    // Remove deleted rows:
                    formData.MedicalInsuranceDetails = formData.MedicalInsuranceDetails?.Where(mid => !mid.IsDeleted).ToList();

                    // Remove empty records from the list of medical insurance details.
                    formData.MedicalInsuranceDetails = formData.MedicalInsuranceDetails?.Where(mid => !(mid.MedicalInsuranceCompanyName == null &&
                                                                             mid.MedicalInsurancePolicyNumber == null &&
                                                                             mid.MedicalInsuranceType == null &&
                                                                             mid.IsPremiumPayerSelfOther == null &&
                                                                             mid.OtherPayerName == null &&
                                                                             mid.MedicalInsurancePremiumAmount == null &&
                                                                             mid.HasDeductionInd == null &&
                                                                             mid.PremiumFrequencyId == null &&
                                                                             mid.Address.AddressLine1 == null &&
                                                                             mid.Address.AddressLine2 == null &&
                                                                             mid.Address.City == null &&
                                                                             mid.Address.CountyId == null &&
                                                                             mid.Address.OutOfStateCounty == null &&
                                                                             mid.Address.StateId == null &&
                                                                             mid.Address.ZipCode == null)).ToList();
                    // Remove deleted rows
                    formData.MedicalLongTermCareInsuranceDetails = formData.MedicalLongTermCareInsuranceDetails?.Where(m => !(m.IsDeleted && m.ApplicationMedicalLTCInsuranceDetailId < 1)).ToList();

                    // Remove empty rows for long term care insurance details.
                    formData.MedicalLongTermCareInsuranceDetails = formData.MedicalLongTermCareInsuranceDetails?.
                                                                    Where(m => !(m.EffectiveDate == null &&
                                                                    m.BenefitAmount == null &&
                                                                    m.DateDisposed == null &&
                                                                    m.IsCountableAmount == null)).ToList();

                    formData.MedicalLongTermCareInsuranceDetails?.ForEach(m =>
                    {
                        m.ApplicationMedicalInsuranceId = formData.ApplicationMedicalInsuranceId;
                    });
                    formData.PartDBudgets?.ForEach(m =>
                    {
                        m.ApplicationMedicalInsuranceId = formData.ApplicationMedicalInsuranceId ?? 0;
                    });
                    var result = await edBll.SaveMedicalInsuranceInfo(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledMedicalInsurance", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        /// <summary>
        /// Gets liability info.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
           ActionDescription = "Displays the Elderly and Disabled Liability screen.")]
        public async Task<ActionResult> ElderlyDisabledLiability(long applicationId, long personId)
        {
            ElderlyDisabledLiabilityViewModel viewModel = null;
            try
            {
                viewModel = await buildLiabilityViewModel(applicationId, personId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledLiabilityViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                return View("ElderlyDisabledLiability", viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        /// <summary>
        /// Builds the Liability ViewModel for both the Get and the Save methods
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="personId"></param>
        /// <returns></returns>
        private async Task<ElderlyDisabledLiabilityViewModel> buildLiabilityViewModel(long applicationId, long personId)
        {
            ElderlyDisabledLiabilityViewModel viewModel = new ElderlyDisabledLiabilityViewModel();
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledLiabilityFormName;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDLiabilityJavascriptFunction;
            // Notice that we have to pass the correct method name to this:
            ElderlyDisabledApplicationInfoBarViewModel appInfo = await GetApplicationInformationBarInfo(applicationId, nameof(ElderlyDisabledController.ElderlyDisabledLiability));

            var bll = new ElderlyDisabledBll(this);

            var appLiab = bll.GetPersonLiabilityInfo(applicationId, personId, CallingUsername, TokenId);
            var appElig = bll.GetElderlyDisabledEligibilityEnrollment(applicationId, CallingUsername, TokenId);

            await Task.WhenAll(appLiab, appElig);

            viewModel = appLiab.Result;
            var _elig = appElig.Result;

            var firstAwarded = _elig.EnDDeterminations.Count > 0 ? _elig.EnDDeterminations.FirstOrDefault(d => d.DenialReasons.Count == 0) : null;
            var appStatus = ENUMS.ApplicationStatus.GetApplicationStatusById(appInfo.ApplicationStatusId);
            var isActiveApp = appStatus.IsActive && firstAwarded?.CancelDate > DateTime.Today;
            var hasEnrollComplete = appStatus.IsEnrollmentComplete;
            var isAnyChange = isActiveApp && (viewModel.LiabilitySegments.Any(l => l.CreatedDate > firstAwarded?.AwardDate
                                                                || l.UpdatedDate >= firstAwarded?.AwardDate));
            ViewBag.IsActiveEnroll = isActiveApp;
            ViewBag.IsEnrollComplete = hasEnrollComplete;
            ViewBag.IsAnyChange = isAnyChange;
            return viewModel;
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Liability Test data.")]
        public async Task<ActionResult> SaveElderlyDisabledLiabilityTests(ElderlyDisabledLiabilityViewModel formData, byte? ElderlyDisabledProgramId)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayJsonNotAuthorizedPage();
            }

            // Ensure these are not null
            formData.Infrequent_IrregularLiabilityTests = formData.Infrequent_IrregularLiabilityTests ?? new List<ElderlyDisabledLiabilityTestViewModel>();
            formData.OtherLiabilityTests = formData.OtherLiabilityTests ?? new List<ElderlyDisabledLiabilityTestViewModel>();
            formData.VA_Aid_AttendanceLiabilityTests = formData.VA_Aid_AttendanceLiabilityTests ?? new List<ElderlyDisabledLiabilityTestViewModel>();

            ViewBag.SaveFunction = saveEDLiabilityJavascriptFunction;
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.UpsertLiabilityTests(formData, CallingUsername, TokenId);
                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledLiability", new { applicationId = formData.ApplicationId, personId = formData.ContactPersonId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            // We need to create a full viewmodel to return to the browser
            var viewModel = await buildLiabilityViewModel(formData.ApplicationId, formData.ContactPersonId);
            // Now copy in the user's edits to this one
            viewModel.Infrequent_IrregularLiabilityTests = formData.Infrequent_IrregularLiabilityTests;
            viewModel.OtherLiabilityTests = formData.OtherLiabilityTests;
            viewModel.VA_Aid_AttendanceLiabilityTests = formData.VA_Aid_AttendanceLiabilityTests;

            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledLiabilityTestFormName;
            return View(nameof(ElderlyDisabledController.ElderlyDisabledLiability), viewModel);
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Saves the Elderly and Disabled Liability Segment.")]
        public async Task<JsonResult> SaveElderlyDisabledLiability(ElderlyDisabledLiabilityDetailViewModel formData, byte? ElderlyDisabledProgramId)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayJsonNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledLiabilityFormName;
            if (ModelState.IsValid)
            {
                try
                {
                    formData.UpdatedBy = CallingUsername;
                    ViewData[PortalConstants.ViewDataConstants.EandDProgramId] = ElderlyDisabledProgramId;

                    var vm = await edBll.UpsertLiabilitySegment(formData, CallingUsername, TokenId);

                    if (vm.IsSuccessful)
                    {
                        ModelState.Clear(); // Known issue with HiddenFor not binding correctly: https://stackoverflow.com/a/3607188 Darren Oster's response.

                        var html = RenderView("~/Views/ElderlyDisabled/Liability/_LiabilitySegment.cshtml", vm, ControllerContext);

                        return Json(new { success = true, liabilitySegment = html, liabilitySegmentId = vm.PersonLiabilityId });
                    }
                    else
                    {
                        if (vm.CaresError == Cares.Api.Infrastructure.CaresError.ValidationError)
                        {
                            // Add to ModelState
                            ModelState.AddModelError(string.Empty, vm.ErrorMessageForUser);
                        }
                        else
                        {
                            Log.Error(vm.ErrorMessageForLog, TokenId);
                            ModelState.AddModelError(string.Empty, $"An error occurred while saving.  Log token={TokenId}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                    ModelState.AddModelError(string.Empty, $"An error occurred while saving.  Log token={TokenId}");
                }
            }

            string errorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = errorMessages, currentView = ViewData[PortalConstants.ViewDataConstants.FormId] });
        }

        /// <summary>
        /// Deletes the liability segment
        /// </summary>
        /// <param name="personLiabilityId"></param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Delete },
            ActionDescription = "Deletes the Elderly and Disabled Liability Segment.")]
        public async Task<JsonResult> DeleteElderlyDisabledLiability(long personLiabilityId)
        {
            try
            {
                var result = await (new ElderlyDisabledBll(this)).DeleteLiabilitySegment(personLiabilityId, CallingUsername, TokenId);
                if (result.IsSuccessful)
                {
                    return Json(new { success = true, deletedId = personLiabilityId }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    Log.Error(result.ErrorMessage, TokenId);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
            }

            return Json(new { success = false }, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName },
                ActionType = new string[] { ActionTypes.Read }, ActionDescription = "Get the eligibility and enrollment information.")]
        public async Task<ActionResult> ElderlyDisabledEligibilityEnrollment(long applicationId)
        {
            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = applicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledEligibilityEnrollmentFormName;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDEligibilityEnrollmentJavascriptFunction;

            ElderlyDisabledEligibilityEnrollmentViewModel viewModel = null;

            try
            {
                viewModel = await new ElderlyDisabledBll(this).GetEligibilityEnrollment(applicationId, CallingUsername, TokenId);
                ViewBag.ContactPersonId = viewModel.ContactPersonId;

                foreach (var item in viewModel.EnDDeterminations)
                {
                    item.IsAward = item.StateAidCategoryId != null || item.AwardOption != null;
                }

                // If Terminal, redirect the user
                if (isTerminalStatus(viewModel))
                {
                    return redirectOnTerminal(viewModel);
                }
            }
            catch (Exception ex)
            {

                Log.Error(ex, TokenId);
                viewModel = new ElderlyDisabledEligibilityEnrollmentViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                await GetApplicationInformationBarInfo(applicationId);

                return View(viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName },
            ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update }, ActionDescription = "Save the eligibility determination temporarily.")]
        public async Task<ActionResult> ElderlyDisabledEligibilityEnrollment(ElderlyDisabledEligibilityEnrollmentViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            // If Terminal, redirect the user
            if (isTerminalStatus(formData))
            {
                return redirectOnTerminal(formData);
            }

            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledEligibilityEnrollmentFormName;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDEligibilityEnrollmentJavascriptFunction;

            // EnD specific validations
            // Sanity check:  Ensure the lists exist even if no data was there:
            formData.MspDeterminations = formData.MspDeterminations ?? new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();
            formData.EnDDeterminations = formData.EnDDeterminations ?? new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();

            if (ModelState.IsValid)
            {
                // Remove deleted rows:
                formData.MspDeterminations = formData.MspDeterminations.Where(d => !d.IsDeleted).ToList();
                formData.EnDDeterminations = formData.EnDDeterminations.Where(d => !d.IsDeleted).ToList();

                //  Reset fields
                resetEligibilityFieldNotDisplayed(formData);

                try
                {
                    var result = await edBll.SaveEligibilityInformation(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        // If User selected YES on the Popup to review Liability info.
                        if (formData.RedirectToLiability)
                        {
                            return RedirectToAction("ElderlyDisabledLiability", new { applicationId = formData.ApplicationId, personId = formData.ContactPersonId });
                        }

                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return RedirectToAction("ElderlyDisabledEligibilityEnrollment", new { applicationId = formData.ApplicationId });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return RedirectToGenericErrorPage(TokenId);
            }

            // Remove deleted rows:
            formData.MspDeterminations = formData.MspDeterminations.Where(d => !d.IsDeleted).ToList();
            formData.EnDDeterminations = formData.EnDDeterminations.Where(d => !d.IsDeleted).ToList();

            ViewData[PortalConstants.ViewDataConstants.FormValidationErrors] = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            await GetApplicationInformationBarInfo(formData.ApplicationId);

            return View(formData);
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName },
            ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update }, ActionDescription = "Enroll the client in an MSP application.")]
        public async Task<JsonResult> ElderlyDisabledMSPEnrollComplete(ElderlyDisabledEligibilityEnrollmentViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayJsonNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }

            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledEligibilityEnrollmentFormName;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDEligibilityEnrollmentJavascriptFunction;

            // Sanity check:  Ensure the lists exist even if no data was there:
            formData.MspDeterminations = formData.MspDeterminations ?? new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();
            formData.EnDDeterminations = formData.EnDDeterminations ?? new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();

            // Check for overlap
            var check = edBll.CheckForEnrollmentOverlaps(formData.MspDeterminations, formData.ContactPersonId);
            if (check.HasOverlaps)
            {
                var overlappedApps = check.OverlappedApplications;
                var overlappedAppIds = overlappedApps.Select(x => x.ApplicationID.ToString()).ToList();
                ModelState.AddModelError(string.Empty, $"Already enrolled in application(s) {string.Join(", ", overlappedAppIds)}");
            }

            if (ModelState.IsValid)
            {
                bool showGenericModelValidationErrorMessage = true;

                try
                {
                    // Remove deleted rows:
                    formData.MspDeterminations = formData.MspDeterminations.Where(d => !d.IsDeleted).ToList();
                    formData.EnDDeterminations = formData.EnDDeterminations.Where(d => !d.IsDeleted).ToList();

                    // Reset fields
                    resetEligibilityFieldNotDisplayed(formData);

                    var result = await new ElderlyDisabledBll(this).MSPEnrollComplete(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        TempData[PortalConstants.TempDataConstants.DataSaved] = true;
                        return Json(new { success = true, applicationId = formData.ApplicationId });
                    }
                    else
                    {
                        // If CaresError.ValidationError was returned, show that to the user
                        if (result.CaresError == Cares.Api.Infrastructure.CaresError.ValidationError && !string.IsNullOrEmpty(result.ErrorMessage))
                        {
                            // Strip off the "001|" off the front if it is there
                            string cleanMessage = result.ErrorMessage.Substring(result.ErrorMessage.IndexOf("|") + 1);
                            ModelState.AddModelError(string.Empty, cleanMessage);
                            showGenericModelValidationErrorMessage = false;
                        }
                        Log.Error(result.ErrorMessage, TokenId);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                if (showGenericModelValidationErrorMessage)
                {
                    ModelState.AddModelError(string.Empty, $"An error occurred while completing the MSP eligibility process. Log token = {TokenId}");
                }
            }

            string errorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = errorMessages, currentView = ElderlyDisabledEligibilityEnrollmentFormName });
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName },
            ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update }, ActionDescription = "Save the final eligibility determination.")]
        public async Task<ActionResult> ElderlyDisabledEnrollComplete(ElderlyDisabledEligibilityEnrollmentViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (await edBll.PerformEandDValidations(formData.ApplicationId, TokenId))
            {
                return DisplayJsonNotAuthorizedPage();
            }
            else
            {
                performCommonValidation(formData.SubProgramCatogory, true);
            }
            // If Terminal, redirect the user
            if (isTerminalStatus(formData))
            {
                return redirectOnTerminal(formData);
            }

            if (!formData.IsEnDEnrollComplete)
            {
                // This would only occur if a user maliciously set the IsEnDEnrollComplete to false.
                // This value must be set to true for the proper validations to fire
                ModelState.AddModelError(string.Empty, $"IsEnDEnrollComplete manipulation detected.");
            }

            ViewData[PortalConstants.ViewDataConstants.ApplicationId] = formData.ApplicationId;
            ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledEligibilityEnrollmentFormName;
            ViewData[PortalConstants.ViewDataConstants.SaveFunction] = saveEDEligibilityEnrollmentJavascriptFunction;

            // Sanity check: Ensure the lists exist even if no data was there:
            formData.MspDeterminations = formData.MspDeterminations ?? new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();
            formData.EnDDeterminations = formData.EnDDeterminations ?? new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();

            // Check for SSI overlap
            var check = edBll.CheckForEnrollmentOverlaps(formData.EnDDeterminations, formData.ContactPersonId);
            if (check.HasOverlaps && check.OverlappedApplications.Any(x => x.CertifyingAgencyCode == SearchConstants.CertifyingAgency.SSA))
            {
                var overlappedSSIApps = check.OverlappedApplications.Where(a => a.CertifyingAgencyCode == SearchConstants.CertifyingAgency.SSA);
                var overlappedSSIAppIds = overlappedSSIApps.Select(x => x.ApplicationID).ToList();
                ModelState.AddModelError(string.Empty, $"Already enrolled in SSI application(s) {string.Join(", ", overlappedSSIAppIds)}");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Remove deleted rows:
                    formData.MspDeterminations = formData.MspDeterminations.Where(d => !d.IsDeleted).ToList();
                    formData.EnDDeterminations = formData.EnDDeterminations.Where(d => !d.IsDeleted).ToList();

                    //  Reset fields
                    resetEligibilityFieldNotDisplayed(formData);

                    var result = await edBll.EnDEnrollComplete(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        return Json(new { success = true, applicationId = formData.ApplicationId });
                    }
                    else
                    {
                        Log.Error(result.ErrorMessage, null, TokenId);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                ModelState.AddModelError(string.Empty, $"An error occurred while completing the eligibility process.  Log token={TokenId}");
            }

            string errorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);
            return Json(new { success = false, response = errorMessages, currentView = ElderlyDisabledEligibilityEnrollmentFormName });
        }

        /// <summary>
        /// Get the facility by string.
        /// </summary>
        /// <param name="searchText">The search text</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
          ActionDescription = "Gets facilities for Expedite Facilities By a string")]
        public async Task<JsonResult> GetExpediteFacilityByString(string searchText)
        {
            ElderlyDisabledBll edBll = new ElderlyDisabledBll(this);
            ExpediteFacilityProvidersViewModel facilities = await edBll.GetExpediteFacilitiesByStr(searchText, CallingUsername, TokenId);

            return Json(new { success = true, facilities = facilities.Facilities }, JsonRequestBehavior.AllowGet);
        }

        #region Financial Institutions
        /// <summary>
        /// Get the financial institutions for the search text introduced weather description or code.
        /// </summary>
        /// <param name="searchText">The search text</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
          ActionDescription = "Gets financial institutions By a string")]
        public async Task<JsonResult> GetFinancialInstitutionsByString(string searchText)
        {
            ElderlyDisabledBll edBll = new ElderlyDisabledBll(this);
            FinancialInstitutionsViewModel institutions = await edBll.GetFinancialInstitutionsByStr(searchText, CallingUsername, TokenId);

            return Json(new { success = true, institutions = institutions.FinancialInstitutions }, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Edit financial institution.
        /// </summary>
        /// <param name="financialInstitutionId">The financial institution identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Edit financial institution")]
        public async Task<ActionResult> EditFinancialInstitution(int financialInstitutionId)
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                ViewData[PortalConstants.ViewDataConstants.FormId] = ElderlyDisabledFinancialInstitutionFormName;

                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            FinancialInstitutionsViewModel vm = await new ElderlyDisabledBll(this).GetFinancialInstitutions(financialInstitutionId, CallingUsername, TokenId);

            var model = vm.FinancialInstitutions.FirstOrDefault();

            return View("~/Views/ElderlyDisabled/FinancialInstitution/EditFinancialInstitution.cshtml", model);
        }

        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Save the financial institution info.")]
        public async Task<JsonResult> EditFinancialInstitution(FinancialInstitutionViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return Json(new { success = false, response = Cares.Api.Infrastructure.CaresError.NotAuthorized, currentView = ViewData[PortalConstants.ViewDataConstants.FormId] });
            }

            if (ModelState.IsValid)
            {
                try
                {
                    formData.UpdatedBy = CallingUsername;
                    var result = await edBll.UpdateFinancialInstitution(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        return Json(new { success = true });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return Json(new { success = false });
            }

            var ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = ErrorMessages, currentView = ElderlyDisabledFinancialInstitutionFormName });
        }

        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
          ActionDescription = "Gets all financial institutions")]
        public async Task<ActionResult> FinancialInstitutionAdminPage()
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.EandDRoles))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            ElderlyDisabledBll edBll = new ElderlyDisabledBll(this);
            FinancialInstitutionsViewModel viewModel = null;

            try
            {
                viewModel = await edBll.GetFinancialInstitutions(null, CallingUsername, TokenId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, TokenId);
                viewModel = new FinancialInstitutionsViewModel()
                {
                    IsSuccessful = false,
                    TokenId = TokenId
                };
            }

            if (viewModel.IsSuccessful)
            {
                return View("~/Views/ElderlyDisabled/FinancialInstitution/FinancialInstitutionAdminPage.cshtml", viewModel);
            }

            return RedirectToErrorPage(viewModel);
        }

        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Add financial institution.")]
        public ActionResult AddFinancialInstitution()
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            var vm = new FinancialInstitutionViewModel();

            return View("~/Views/ElderlyDisabled/FinancialInstitution/AddFinancialInstitution.cshtml", vm);
        }

        /// <summary>
        /// Saves the financial institution info.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Add the financial institution info.")]
        public async Task<JsonResult> AddFinancialInstitution(FinancialInstitutionViewModel formData)
        {
            var edBll = new ElderlyDisabledBll(this);

            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return Json(new { success = false, response = Cares.Api.Infrastructure.CaresError.NotAuthorized, currentView = "F_FinancialInstitutionAdd" });
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await edBll.InsertFinancialInstitution(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        return Json(new { success = true });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return Json(new { success = false });
            }

            var ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = ErrorMessages, currentView = "F_FinancialInstitutionAdd" });
        }
        #endregion Financial Institutions

        #region Expedite Facility Admin
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
          ActionDescription = "Gets facilities for Expedite Facilities Admin page")]
        public async Task<ActionResult> ExpediteFacilityAdminPage()
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.EandDRoles))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            ElderlyDisabledBll edBll = new ElderlyDisabledBll(this);
            ExpediteFacilityProvidersViewModel vm = await edBll.GetExpediteFacilityProviders(CallingUsername, TokenId);

            return View("~/Views/ElderlyDisabled/FacilityProvider/ExpediteFacilityAdminPage.cshtml", vm);
        }

        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Expedite add Facility page")]
        public ActionResult AddExpediteFacility()
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            var vm = new ExpediteFacilityViewModel();
            return View("~/Views/ElderlyDisabled/FacilityProvider/AddExpediteFacility.cshtml", vm);
        }

        /// <summary>
        /// Saves the expedite facility info.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Add the expedite facility info.")]
        public async Task<JsonResult> AddExpediteFacility(ExpediteFacilityViewModel formData)
        {
            // EandD Data Admin, EanD Manger, EandD Supervisor can Create Facility
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return Json(new { success = false, response = Cares.Api.Infrastructure.CaresError.NotAuthorized, currentView = "F_ExpediteFacilityAdd" });
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var result = await new ElderlyDisabledBll(this).InsertExpediteFacility(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        return Json(new { success = true });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return Json(new { success = false });
            }

            var ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = ErrorMessages, currentView = "F_ExpediteFacilityAdd" });
        }

        /// <summary>
        /// Edit expedite facility.
        /// </summary>
        /// <param name="expediteFacilityId">The expedite facility identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Expedite edit Facility page")]
        public async Task<ActionResult> EditExpediteFacility(int expediteFacilityId)
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            ExpediteFacilityViewModel vm = await new ElderlyDisabledBll(this).GetExpediteFacilityById(expediteFacilityId, CallingUsername, TokenId);

            return View("~/Views/ElderlyDisabled/FacilityProvider/EditExpediteFacility.cshtml", vm);
        }

        /// <summary>
        /// Saves the expedite facility info.
        /// </summary>
        /// <param name="formData">The form data.</param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Insert, ActionTypes.Update },
            ActionDescription = "Save the expedite facility info.")]
        public async Task<JsonResult> EditExpediteFacility(ExpediteFacilityViewModel formData)
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return Json(new { success = false, response = Cares.Api.Infrastructure.CaresError.NotAuthorized, currentView = "F_ExpediteFacilityEdit" });
            }

            if (ModelState.IsValid)
            {
                try
                {
                    formData.UpdatedBy = CallingUsername;
                    var result = await new ElderlyDisabledBll(this).UpdateExpediteFacility(formData, CallingUsername, TokenId);

                    if (result.IsSuccessful)
                    {
                        return Json(new { success = true });
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, TokenId);
                }

                return Json(new { success = false });
            }

            var ErrorMessages = humanReadableErrors.ProcessErrors(ModelState, TokenId);

            return Json(new { success = false, response = ErrorMessages, currentView = "F_ExpediteFacilityEdit" });
        }

        /// <summary>
        /// Allows user to manually select an Expedite Facility provider
        /// </summary>
        /// <param name="expediteFacilityId"></param>
        /// <returns></returns>
        [HttpGet]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Read },
            ActionDescription = "Allows user to manually select an Expedite Facility provider")]
        public async Task<ActionResult> SelectExpediteFacilityProvider(int expediteFacilityId)
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }

            ElderlyDisabledBll edBll = new ElderlyDisabledBll(this);
            SelectExpediteFacilityProviderViewModel facilityVM = await edBll.GetSelectExpediteFacilityProviderViewModel(expediteFacilityId, CallingUsername, TokenId);

            if (facilityVM.IsSuccessful)
            {
                return View("~/Views/ElderlyDisabled/FacilityProvider/SelectExpediteFacilityProvider.cshtml", facilityVM);
            }
            else
            {
                return RedirectToErrorPage(facilityVM);
            }
        }

        /// <summary>
        /// Called from the SelectExpediteFacilityProvider page for matching a Provider to an Expedite Facility
        /// </summary>
        /// <param name="expediteFacilityId"></param>
        /// <param name="providerId"></param>
        /// <returns></returns>
        [HttpPost]
        [ScreenAction(ScreenName = new string[] { elderlyDisabledScreenName }, ActionType = new string[] { ActionTypes.Update },
            ActionDescription = "Matches a Provider to an Expedite Facility")]
        public async Task<ActionResult> MatchExpediteFacilityProvider(int expediteFacilityId, int providerId)
        {
            if (!CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Data_Admin) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Supervisor) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.EandD_Manager) && !CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    CaresError = Cares.Api.Infrastructure.CaresError.NotAuthorized
                });
            }
            ExpediteFacilityDto data = new ExpediteFacilityDto()
            {
                ExpediteFacilityProviderId = expediteFacilityId,
                ProviderId = providerId
            };
            ElderlyDisabledBll edBll = new ElderlyDisabledBll(this);
            var result = await edBll.SetExpediteFacilityProvider(data, CallingUsername, TokenId);

            if (result.IsSuccessful)
            {
                return RedirectToAction("EditExpediteFacility", new { expediteFacilityId = expediteFacilityId });
            }
            else
            {
                return RedirectToErrorPage(new BaseViewModel()
                {
                    IsSuccessful = result.IsSuccessful,
                    CaresError = result.CaresError
                });
            }
        }
        #endregion

        /// <summary>
        /// Resets the eligibility fields that are not displayed.
        /// </summary>
        /// <param name="vm">The view model.</param>
        private void resetEligibilityFieldNotDisplayed(ElderlyDisabledEligibilityEnrollmentViewModel vm)
        {
            if (vm.EnDDeterminations.Any(a => a.IsAward == true))
            {
                //  Displaying Award, so reset Denial fields.
                vm.AwardDeny.DenialReasons?.Clear();
            }
            else if (vm.AwardDeny.DisplayingDenial)
            {
                //  Displaying Deny, so reset Award fields.
                vm.AwardDeny.StartDate = null;
                vm.AwardDeny.CancelDate = null;
                vm.AwardDeny.StateAidCategoryId = null;
            }
            else
            {
                //  Not displaying either, so make sure all fields are clear.
                vm.AwardDeny.DenialReasons?.Clear();
                vm.AwardDeny.StartDate = null;
                vm.AwardDeny.CancelDate = null;
                vm.AwardDeny.StateAidCategoryId = null;
            }
        }

        /// <summary>
        /// Returns true if this app is in terminal status
        /// </summary>
        /// <param name="viewModel"></param>
        /// <returns></returns>
        private bool isTerminalStatus(BaseViewModel viewModel)
        {
            return Cares.Api.Infrastructure.Helper.ApplicationHelper.IsEnrollmentComplete(viewModel.ApplicationStatusId);
        }

        /// <summary>
        /// Where to redirect the user when app is in Terminal status
        /// </summary>
        /// <param name="viewModel"></param>
        /// <returns></returns>
        private ActionResult redirectOnTerminal(BaseViewModel viewModel)
        {
            // Send them to the Application screen
            return RedirectToAction("Application", new { applicationId = viewModel.ApplicationId });
        }

        /// <summary>
        /// Copies the mailing address to home address, if they are supposed to be the same.
        /// </summary>
        /// <param name="applicantVM">The applicant vm.</param>
        private void copyMailingAddressToHomeAddress(ElderlyDisabledApplicationViewModel applicantVM)
        {
            if (applicantVM.PersonAddresses.IsHomeAddressSameAsMailingAddress == true)
            {
                applicantVM.PersonAddresses.Addresses.FirstOrDefault(a => a.AddressTypeId == (byte)ENUMS.enumAddressType.Home).Address =
                    applicantVM.PersonAddresses.Addresses.FirstOrDefault(a => a.AddressTypeId == (byte)ENUMS.enumAddressType.Mailing).Address;
            }
        }

        /// <summary>
        /// Enable/Disable Fields in the UI based on UserRole
        /// </summary>
        /// <param name="vm"></param>
        /// <param name="callingMethodName"></param>
        protected override void setUIEditability(ElderlyDisabledApplicationInfoBarViewModel vm, string callingMethodName)
        {
            bool status = new ElderlyDisabledBll(this).IsUserReadOnly(vm, callingMethodName);
            ViewBag.IsReadOnly = status ? "true" : "false";
        }
    }
}