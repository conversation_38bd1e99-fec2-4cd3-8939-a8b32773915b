﻿namespace Cares.Portal.Infrastructure
{
    /// <summary>
    /// Class for constants used in the Portals
    /// </summary>
    public static class PortalConstants
    {
        /// <summary>
        /// The format for currency, including using a negative sign.
        /// </summary>
        public const string CURRENCYFORMAT = "$#,##0.00;-$#,##0.00";

        /// <summary>
        /// For replacing rootpath on snapshot styling and images
        /// </summary>
        public const string SnapshotRootPathReplaceKey = "[WEBROOT]";

        public const string WorkerReminderFormName = "F_WorkerReminder";
        public const string CompleteWorkerReminderFormName = "F_CompleteWorkerReminder";

        // For use where Session[] variables are accessed
        public static class SessionVariable
        {
            public const string ApplicationId = "ApplicationId";
            public const string ApplicationName = "ApplicationName";
            public const string AuthorizedPersonId = "AuthorizedPersonId";
            public const string ContactId = "ContactId";

            public const string EE_ApplicationId = "EE_ApplicationId";
            public const string EE_AppSource = "EE_AppSource";
            public const string EE_AppStatus = "EE_AppStatus";
            public const string EE_ContactBalance = "EE_ContactBalance";
            public const string EE_ContactId = "EE_ContactId";
            public const string EE_ContactName = "EE_ContactName";
            public const string EE_HouseholdNbr = "EE_HouseholdNbr";
            public const string EE_Letter_ApplicationId = "EE_Letter_ApplicationId";
            public const string EE_MaxOOP = "EE_MaxOOP";
            public const string EE_PersonId = "EE_PersonId";
            public const string EE_SubProgramCategory = "EE_SubProgramCategory";


            // Visual Guard related Session variables:
            public const string VG_ApplicationName = "VG_ApplicationName";
            // Holds the instance of IVGMembershipUser:
            public const string VG_User = "VG_User";
            public const string VG_UserRole = "VG_UserRole";
            public const string VG_UserName = "VG_UserName";
            public const string VG_UserFullName = "VG_UserFullName";
            public const string WP_WorkerNumber = "WP_WorkerNumber";
            public const string WP_DistrictOfficeId = "WP_DistrictOfficeId";
            public const string WP_DistrictOfficeNumber = "WP_DistrictOfficeNumber";

            public const string PersonId = "PersonId";
            public const string SaveData = "SaveData";

            // Used exclusively by RCC
            public const string RCCCurrentApplicationId = "RCCCurrentApplicationId";

            //Used on User Account
            public const string Current_UserRoleId = "Current_UserRoleId";

            // Used for enforcing session fixation.
            public const string SessionToken = "AuthToken";
        }

        /// <summary>
        /// For cookie names use
        /// </summary>
        public static class CookieNames
        {
            public const string SessionCookieName = "ASP.NET_SessionId";
            public const string TokenSessionName = "AuthToken";
        }

        /// <summary>
        /// For use where configuration (web/application) settings are accessed.
        /// </summary>
        public static class ConfigurationSettings
        {
            public const string LandingSearchResultsCount = "LandingSearchResultsCount";
            public const string VgAdminPassword = "VGAdminPassword";
            public const string VgAdminUserName = "VGAdminUserName";
            public const string VgAppId = "VGAppId";
            public const string VgSqlConnectionString = "VGConnectionString";
            public const string VgServerFlag = "UseVGServer";
            public const string VgServerUrl = "VGServerURL";
            public const string VgServerUrlPort = "VGServerURLPort";
            public const string VgTraceLogging = "VGTraceLogging";
            public const string VgTraceLogPath = "VGTraceLogPath";
            public const string MaxRIIParallelThreads = "MaxRIIParallelThreads";
            public const string UseCaptchaForLogin = "UseCaptchaForLogin";
        }

        /// <summary>
        /// Constant to access TempData variables.
        /// </summary>
        public static class TempDataConstants
        {
            public const string SessionExpired = "SessionExpired";
            public const string DataSaved = "DataSaved";
            public const string PersonId = "PersonId";
            public const string ApplicationId = "ApplicationId";
        }

        /// <summary>
        /// Constants for ViewData[...]
        /// </summary>
        public static class ViewDataConstants
        {
            // Used for creating the proper 'name' attribute in order to facilitate proper form serialization.
            // It's necessary when we have a root View that includes partial views that represent a subset of the out model.
            // When the form posts, it needs to be able to place the partial view into the proper root ViewModel.
            //  Example:  For ElderlyDisabledApplicationViewModel's PersonAddress sub object.
            //           In _ElderlyDisabledAddress, it sets this ViewData value to: "PersonAddresses.Addresses[" + i + "].Address."
            //           and passes that to the partial view _Address.
            // Example usage:
            //         @Html.Partial("_Address", Model.PersonAddresses.Addresses[i].Address, new ViewDataDictionary { { PortalConstants.ViewDataConstants.NamePrefix, namePrefix } })
            public const string NamePrefix = "namePrefix";
            public const string NameDetailPrefix = "nameDetailPrefix";
            public const string SpouseDetailPrefix = "spouseDetailPrefix";
            public const string FamilyDetailPrefix = "familyDetailPrefix";
            public const string PersonalNeedsAllowancePrefix = nameof(PersonalNeedsAllowancePrefix);
            public const string FormId = "FormId";
            public const string SaveFunction = "SaveFunction";
            public const string FormValidationErrors = "FormValidationErrors";
            public const string ShowDelete = "showDelete";
            public const string PhoneIndex = "phoneIndex";
            public const string OwningPhoneListIndex = "owningPhoneListIndex";
            public const string AddressType = "addressType";
            public const string ShowRequired = "showRequired";
            public const string AddressIndex = "addressIndex";
            public const string FormerSpouseIndex = "formerSpouseIndex";
            public const string HouseholdMemberIndex = "householdMemberIndex";
            public const string ShowFirstForm = "showFirstForm";
            public const string IncludeUnknownState = "includeUnknownState";
            public const string ShowOutOfStateCounty = "showOutOfStateCounty";
            public const string ShowOnlyStateAndCounty = "showOnlyStateAndCounty";
            public const string ShowUnknownCountyButton = "showUnknownCountyButton";
            public const string AllowPartialAddress = "allowPartialAddress";
            public const string PropertyParcelIndex = "propertyParcelIndex";
            public const string PropertyParcelDetailIndex = "propertyParcelDetailIndex";
            public const string IsPreviousPropertyTypeParcel = "false";
            public const string PropertyPreviousIndex = "propertyPreviousIndex";
            public const string PropertyPreviousDetailIndex = "propertyPreviousDetailIndex";
            public const string PropertyMobileHomeIndex = "propertyMobileHomeIndex";
            public const string PropertyMobileHomeDetailIndex = "propertyMobileHomeDetailIndex";
            public const string SponsorIndex = "sponsorIndex";
            public const string BankDetailIndex = "bankDetailIndex";
            public const string EnDSegmentIndex = "endSegmentIndex";
            public const string ResourceDetailIndex = "resourceDetailIndex";
            public const string ResourceMonthDetailIndex = "resourceMonthDetailIndex";
            public const string BankSubDetailIndex = "bankSubDetailIndex";
            public const string TransferResourceIndex = "transferResourceIndex";
            public const string TransferResourceDetailIndex = "transferResourceDetailIndex";
            public const string AutoDetailIndex = "autoDetailIndex";
            public const string AutoAdditionalDetailIndex = nameof(AutoAdditionalDetailIndex);
            public const string MachineDetailIndex = "machineDetailIndex";
            public const string MachineSubDetailIndex = "machineSubDetailIndex";
            public const string CollectibleDetailIndex = "collectibleDetailIndex";
            public const string CollectibleSubDetailIndex = "collectibleSubDetailIndex";
            public const string ShowWhoOwnsPhone = "showWhoOwnsPhone";
            public const string HideFirstPhoneRowDeletes = "hideFirstPhoneRowDeletes";
            public const string IsPhoneRequired = "isPhoneRequired";
            public const string nonMagiIncomeIndex = "NonMagiIncomeIndex";
            public const string HeaderVaIndicator = "HeaderVaIndicator";
            public const string HeaderIncomeTypeId = "HeaderIncomeTypeId";
            public const string nonMagiIncomeDetailIndex = "NonMagiIncomeDetailIndex";
            public const string spouseAllocationIndex = "spouseAllocationIndex";
            public const string familyAllocationIndex = "familyAllocationIndex";
            public const string IncomeTypeId = "IncomeTypeId";
            public const string PersonalNeedsAllowanceIndex = nameof(PersonalNeedsAllowanceIndex);
            public const string RefugeeIncomeIndex = "RefugeeIncomeIndex";
            public const string MedicalInsuranceDetailIndex = "medicalInsuranceDetailIndex";
            public const string MedicalInsuranceMonthDetailIndex = "medicalInsuranceMonthDetailIndex";
            public const string MedicalInsurancePartDBudgetIndex = "medicalInsurancePartDBudgetIndex";
            public const string RefugeeInsuranceDetailIndex = "RefugeeInsuranceDetailIndex";
            public const string LifeInsuranceDetailIndex = "lifeInsuranceDetailIndex";
            public const string MedicalLTCInsuranceDetailIndex = nameof(MedicalLTCInsuranceDetailIndex);
            public const string LifeInsuranceAdditionalDetailIndex = nameof(LifeInsuranceAdditionalDetailIndex);
            public const string OtherBurialFund = "otherBurialFund";
            public const string OtherBurialFundDetailIndex = nameof(OtherBurialFundDetailIndex);
            public const string PrepaidBurialSpaceDetailIndex = nameof(PrepaidBurialSpaceDetailIndex);
            public const string AdditionalBurialFund = "additionalBurialFund";
            public const string AdditionalBurialFundIndex = "additionalBurialFundIndex";
            public const string AdditionalBurialFundBudgetIndex = "additionalBurialFundBudgetIndex";
            public const string DeleteModalSectionNameId = "deleteModalSectionNameId";
            public const string DeleteModalTitle = "deleteModalTitle";
            public const string DeleteModalMessage = "deleteModalMessage";
            public const string AppInfoBarVM = "appInfoBarVM";
            public const string BindDatePicker = "BindDatePicker";
            public const string LayoutUrl = "layoutUrl";
            public const string IsSpouseLivesWith = "isSpouseLivesWith";
            public const string ReminderFormId = "ReminderFormId";
            public const string ReminderDashboardGridType = "ReminderDashboardGridType";
            public const string ApplicantInfoJson = "ApplicantInfoJson";
            public const string ApplicantAddressJson = "ApplicantAddressJson";
            public const string ApplicantPhonesHtmlString = "ApplicantPhonesHtmlString";
            public const string MSPEnrolledType = "mspEnrolledType";
            public const string ApplicantFinancialInstitutions = nameof(ApplicantFinancialInstitutions);
            // Useful for things like the left menu pane:
            public const string ApplicationId = "ApplicationId";
            public const string AppTypeId = "AppTypeId";
            public const string ContactId = "ContactId";
            public const string SubProgramCategory = "SubProgramCategory";
            public const string EandDProgramId = "EandDProgramId";
            public const string NotesDisplayTextPlural = "NotesDisplayTextPlural";
            public const string NotesDisplayTextSingular = "NotesDisplayTextSingular";
            public const string IsRecordOfContact = "IsRecordOfContact";
            public const string NetIncomeAmountClass = nameof(NetIncomeAmountClass);
            public const string LiabilityTestType = nameof(LiabilityTestType);
            public const string LiabilityTestIndex = "LiabilityTestIndex";
            public const string IsTemplate = "IsTemplate";
        }
        public static class DataExchange
        {
            public const string SVES = "SVES";
            public const string BENDEX = "BENDEX";
            public const string SDX = "SDX";
            public const string AMAES = "AMAES";
            public const string TBQ = "TBQ";
        }

        public static class Nav
        {
            // Common Navs
            public const string Income = "Income";
            public const string EligEnrollment = "Elig/Enrollment";
            public const string EnrollHistory = "Enroll History";
            public const string Notes = "Notes";
            public const string Letters = "Letters";

            // Common Navs between RMA and EandD
            public const string Applicant = "Applicant";
            public const string Spouse = "Spouse";
            public const string MedicalInsurance = "Medical Insurance";

            // Only EandD specific
            public const string Representative = "Representative";
            public const string VeteranInformation = "Veteran Information";
            public const string Household = "Household";
            public const string Property = "Property";
            public const string Resources = "Resources";
            public const string LifeInsurance = "Life Insurance";
            public const string PersonalProperty = "Personal Property";
            public const string Liability = "Liability";

        }
    }
}