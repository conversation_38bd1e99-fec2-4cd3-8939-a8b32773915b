CREATE TABLE [dbo].[REF_FAMILY_NEEDS_ALLOWANCE]
(
[FAMILY_NEEDS_ALLOWANCE_ID] [tinyint] NOT NULL,
[NUMBER_OF_FAMILY_MEMBERS] [tinyint] NOT NULL,
[FAMILY_NEEDS_ALLOWANCE_AMOUNT] [decimal] NOT NULL,
[IS_ACTIVE] [bit] NOT NULL,
[CHIP_ADJUST_REASON_ID] [smallint] NULL,
[CREATED_BY] [varchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[CREATED_DATE] [datetime2] NULL,
[UPDATED_BY] [varchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
[UPDATED_DATE] [datetime2] NULL
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[REF_FAMILY_NEEDS_ALLOWANCE] ADD CONSTRAINT [PK_REF_FAMILY_NEEDS_ALLOWANCE] PRIMARY KEY CLUSTERED  ([FAMILY_NEEDS_ALLOWANCE_ID], [IS_ACTIVE]) ON [PRIMARY]
GO
ALTER TABLE [dbo].[REF_FAMILY_NEEDS_ALLOWANCE] ADD CONSTRAINT [DF_REF_FAMILY_NEEDS_ALLOWANCE_CREATED_BY] DEFAULT (suser_sname()) FOR [CREATED_BY]
GO
ALTER TABLE [dbo].[REF_FAMILY_NEEDS_ALLOWANCE] ADD CONSTRAINT [DF_REF_FAMILY_NEEDS_ALLOWANCE_CREATED_DATE] DEFAULT (getdate()) FOR [CREATED_DATE]
GO
ALTER TABLE [dbo].[REF_FAMILY_NEEDS_ALLOWANCE] ADD CONSTRAINT [DF_REF_FAMILY_NEEDS_ALLOWANCE_UPDATED_BY] DEFAULT (suser_sname()) FOR [UPDATED_BY]
GO
ALTER TABLE [dbo].[REF_FAMILY_NEEDS_ALLOWANCE] ADD CONSTRAINT [DF_REF_FAMILY_NEEDS_ALLOWANCE_UPDATED_DATE] DEFAULT (getdate()) FOR [UPDATED_DATE]
GO
ALTER TABLE [dbo].[REF_FAMILY_NEEDS_ALLOWANCE] ADD CONSTRAINT [DF_REF_FAMILY_NEEDS_ALLOWANCE_IS_ACTIVE] DEFAULT ((0)) FOR [IS_ACTIVE]
GO
EXEC sys.sp_addextendedproperty N'MS_Description', N'Family needs allowance for budget calculations.', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', NULL, NULL
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'Number of family members', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'NUMBER_OF_FAMILY_MEMBERS'
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'Family needs allowance', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'FAMILY_NEEDS_ALLOWANCE_AMOUNT'
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'Primary Key', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'FAMILY_NEEDS_ALLOWANCE_ID'
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'Defines who created the record', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'CREATED_BY'
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'Provides the date on which the record was created', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'CREATED_DATE'
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'An Indicator that the record is in an active status', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'IS_ACTIVE'
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'Defines who updated the record', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'UPDATED_BY'
GO
EXEC sys.sp_addextendedproperty N'MS_Description', 'Provides the date on which the record was updated', 'SCHEMA', N'dbo', 'TABLE', N'REF_FAMILY_NEEDS_ALLOWANCE', 'COLUMN', N'UPDATED_DATE'
GO
