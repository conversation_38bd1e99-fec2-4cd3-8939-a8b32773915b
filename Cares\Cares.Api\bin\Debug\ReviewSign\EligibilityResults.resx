﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="eligibilityHelpText" xml:space="preserve">
    <value>If you are approved or denied, a notice of action will be sent to your mailing address.&lt;br/&gt;
If you are approved for ALL Kids, you will receive a card within 5 - 10 business days.&lt;br/&gt;
If you are approved for Medicaid, you will receive a Medicaid ID card and information about Medicaid within 10 days. &lt;b&gt; It may take longer for eligibility to show active in the Medicaid Claims system.&lt;/b&gt;&lt;br/&gt;
If your status is pending, you may be contacted for more information to complete the determination of your eligibility.&lt;br/&gt;
If you are approved for Medicaid and need medical services, write down the Medicaid ID number listed above to give the hospital, pharmacist or doctor to check your eligibility.&lt;br/&gt;
Keep your Medicaid number private and in a secure place.&lt;br/&gt;
If you have questions about Medicaid, call 1-800-362-1504.&lt;br.&gt;
If you have questions about ALL Kids, call 1-888-373-5437.&lt;br/&gt;</value>
  </data>
  <data name="eligibilityResults" xml:space="preserve">
    <value>Eligibility Results</value>
  </data>
  <data name="eligibleFor" xml:space="preserve">
    <value>Eligible for</value>
  </data>
  <data name="hhsGovUrl" xml:space="preserve">
    <value>&lt;a class="blue-link" href="http://www.hhs.gov/ocr/office/file" target="_blank"&gt;www.hhs.gov/ocr/office/file&lt;/a&gt;</value>
    <comment>DO NOT TRANSLATE</comment>
  </data>
  <data name="howToAppeal" xml:space="preserve">
    <value>If I think the Medicaid/Children's Health Insurance Program (ALL Kids) has made a mistake, I can appeal its decision.  To appeal means to tell someone at the Medicaid/ALL Kids that I think the action is wrong and ask for a fair review of the action.  I know that I can find out how to appeal by contacting the Medicaid/ALL Kids at 1-888-373-KIDS (5437).  I know that I can be represented in the process by someone other than myself.  My eligibility and other information will be explained to me.  Find out more about how to appeal.</value>
  </data>
  <data name="moreInformationAppeals" xml:space="preserve">
    <value>More information &amp; appeals</value>
  </data>
  <data name="noDiscrimination" xml:space="preserve">
    <value>Following federal law, discrimination isn't permitted on the basis or race, color, national origin, sex, age, sexual orientation, gender identity, or disability.  I can file a complaint of discrimination by visiting [HHSGOVURL].</value>
  </data>
  <data name="notApplyingForCoverage" xml:space="preserve">
    <value>Not applying for coverage</value>
  </data>
  <data name="notEligibleFor" xml:space="preserve">
    <value>Not eligible for</value>
  </data>
  <data name="program" xml:space="preserve">
    <value>Coverage  Type</value>
  </data>
  <data name="registerToVote" xml:space="preserve">
    <value>Does anyone in the household want to register to vote?</value>
  </data>
  <data name="registerToVoteHere" xml:space="preserve">
    <value>Register to vote by clicking here.</value>
  </data>
  <data name="registerToVoteURL" xml:space="preserve">
    <value>http://alabamavotes.gov/GetRegForm.aspx?m=voters</value>
    <comment>DO NOT TRANSLATE</comment>
  </data>
  <data name="requestDisabilityDetermination" xml:space="preserve">
    <value>Do any of these people want to request a determination for Medicaid for [NAME] as conducted by Alabama Medicaid Agency on the basis of disability, blindness, or recurring medical needs or bills?</value>
  </data>
  <data name="sendAppToMedicaid" xml:space="preserve">
    <value>It looks like [NAMES] [ISN'T/AREN'T] eligible for Medicaid.  [HE/SHE/THEY] can still continue with Medicaid applications if we send this application to the Alabama Medicaid Agency (AMA).  Do [NAMES] want us to send their information to the AMA so they can check on Medicaid eligibility?</value>
  </data>
  <data name="tempResultsMsg" xml:space="preserve">
    <value>Thank you for your application submission.  We will notify you in writing when your eligibility for health care programs has been determined.</value>
  </data>
</root>