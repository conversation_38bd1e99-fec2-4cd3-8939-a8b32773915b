﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Disaster {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Disaster {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Disaster() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Disaster.Disaster", typeof(Disaster).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alien or I-94 number.
        /// </summary>
        public static string AlienI94Number {
            get {
                return ResourceManager.GetString("AlienI94Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alien Number.
        /// </summary>
        public static string AlienNumber {
            get {
                return ResourceManager.GetString("AlienNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer is required.
        /// </summary>
        public static string AnswerRequired {
            get {
                return ResourceManager.GetString("AnswerRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was a problem with the application process..
        /// </summary>
        public static string ApplicationProcessError {
            get {
                return ResourceManager.GetString("ApplicationProcessError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of authorized representative:.
        /// </summary>
        public static string AuthRepName {
            get {
                return ResourceManager.GetString("AuthRepName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organization Id (if applicable).
        /// </summary>
        public static string AuthRepOrgId {
            get {
                return ResourceManager.GetString("AuthRepOrgId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organization name (if applicable).
        /// </summary>
        public static string AuthRepOrgName {
            get {
                return ResourceManager.GetString("AuthRepOrgName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number:.
        /// </summary>
        public static string AuthRepPhone {
            get {
                return ResourceManager.GetString("AuthRepPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have been awarded coverage..
        /// </summary>
        public static string Award {
            get {
                return ResourceManager.GetString("Award", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Best contact phone number.
        /// </summary>
        public static string BestContactPhone {
            get {
                return ResourceManager.GetString("BestContactPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By clicking here, you allow this person to sign your application, get official information about this application, and act for you on all future matters with the Alabama Medicaid Agency..
        /// </summary>
        public static string ByClickingHereAuthRep {
            get {
                return ResourceManager.GetString("ByClickingHereAuthRep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Card number or passport number.
        /// </summary>
        public static string CardNumberOrPassport {
            get {
                return ResourceManager.GetString("CardNumberOrPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Number.
        /// </summary>
        public static string CertificateNumber {
            get {
                return ResourceManager.GetString("CertificateNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By clicking here, you are swearing that everything you wrote on this form is true as far as you know.  We will keep your information secure and private..
        /// </summary>
        public static string ESign {
            get {
                return ResourceManager.GetString("ESign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you have any insurance coverage?.
        /// </summary>
        public static string HaveInsurance {
            get {
                return ResourceManager.GetString("HaveInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you have an Alabama Medicaid Card?.
        /// </summary>
        public static string HaveMedicaidCard {
            get {
                return ResourceManager.GetString("HaveMedicaidCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you, or your spouse or parent, an honorably discharged veteran or an active-duty member of the US military?.
        /// </summary>
        public static string HonorableDischargeOrActiveDuty {
            get {
                return ResourceManager.GetString("HonorableDischargeOrActiveDuty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document expiration date.
        /// </summary>
        public static string ImmigrationDocExpDate {
            get {
                return ResourceManager.GetString("ImmigrationDocExpDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Immigration document type.
        /// </summary>
        public static string ImmigrationDocType {
            get {
                return ResourceManager.GetString("ImmigrationDocType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other (Category code or country of issuance).
        /// </summary>
        public static string ImmigrationOther {
            get {
                return ResourceManager.GetString("ImmigrationOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status type (optional).
        /// </summary>
        public static string ImmigrationStatusType {
            get {
                return ResourceManager.GetString("ImmigrationStatusType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to US Citizen, national or legal immigrant?.
        /// </summary>
        public static string IsCitizen {
            get {
                return ResourceManager.GetString("IsCitizen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If homeless, check the box &amp; tell us where we can reach you..
        /// </summary>
        public static string IsHomeless {
            get {
                return ResourceManager.GetString("IsHomeless", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Have you lived in the US since 1996?.
        /// </summary>
        public static string LivedInUsSince1996 {
            get {
                return ResourceManager.GetString("LivedInUsSince1996", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medicaid Nbr.
        /// </summary>
        public static string MedicaidNbr {
            get {
                return ResourceManager.GetString("MedicaidNbr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name as it appears on your immigration document.
        /// </summary>
        public static string NameAsItAppears {
            get {
                return ResourceManager.GetString("NameAsItAppears", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you a naturalized or derived citizen?.
        /// </summary>
        public static string NaturalizedOrDerived {
            get {
                return ResourceManager.GetString("NaturalizedOrDerived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you are not a US Citizen or National, do you have eligible immigration status?.
        /// </summary>
        public static string NotCitizenHasImmStatus {
            get {
                return ResourceManager.GetString("NotCitizenHasImmStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Other) phone number.
        /// </summary>
        public static string OtherPhone {
            get {
                return ResourceManager.GetString("OtherPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What language do you read best?.
        /// </summary>
        public static string ReadingLanguage {
            get {
                return ResourceManager.GetString("ReadingLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Would you like to receive information by email?.
        /// </summary>
        public static string ReceiveInfoByEmail {
            get {
                return ResourceManager.GetString("ReceiveInfoByEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SEVIS ID.
        /// </summary>
        public static string SevisId {
            get {
                return ResourceManager.GetString("SevisId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By clicking here, you are swearing that everything you wrote on this form is true as far as you know.
        ///We will keep your information secure and private..
        /// </summary>
        public static string SignatureText {
            get {
                return ResourceManager.GetString("SignatureText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SIGNATURE:.
        /// </summary>
        public static string SignatureTitle {
            get {
                return ResourceManager.GetString("SignatureTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What language do you speak best?.
        /// </summary>
        public static string SpokenLanguage {
            get {
                return ResourceManager.GetString("SpokenLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TODO: Some form of &quot;An unknown error occurred&quot; message here..
        /// </summary>
        public static string UnknownError {
            get {
                return ResourceManager.GetString("UnknownError", resourceCulture);
            }
        }
    }
}
