﻿using Cares.Api.Infrastructure.Enums;
using Cares.Portal.Infrastructure;
using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel;
using FluentValidation;
using FluentValidation.Mvc;
using FluentValidation.Results;
using System.Linq;
using System.Web.Mvc;
using ElderlyDisabledRes = Cares.Portal.Worker.Resources.ElderlyDisabled.ElderlyDisabled;
using ValidationRes = Cares.Portal.Worker.Resources.Shared.Validation;

namespace Cares.Portal.Worker.Models.Validators.ElderlyDisabled
{
    public class ElderlyDisabledLiabilityViewModelValidator : AbstractValidator<ElderlyDisabledLiabilityViewModel>, IValidatorInterceptor
    {
        public ElderlyDisabledLiabilityViewModelValidator()
        {
            validateLiability();
        }

        public ValidationResult AfterMvcValidation(ControllerContext controllerContext, ValidationContext validationContext, ValidationResult result)
        {
            return ValidatorHelper.DistinctValidationErrors(result);
        }

        public ValidationContext BeforeMvcValidation(ControllerContext controllerContext, ValidationContext validationContext)
        {
            return validationContext;
        }

        private void validateLiability()
        {
            RuleForEach(s => s.Infrequent_IrregularLiabilityTests).SetValidator(new ElderlyDisabledLiabilityTestViewModelValidator());
            RuleForEach(s => s.OtherLiabilityTests).SetValidator(new ElderlyDisabledLiabilityTestViewModelValidator());
            RuleForEach(s => s.VA_Aid_AttendanceLiabilityTests).SetValidator(new ElderlyDisabledLiabilityTestViewModelValidator());
        }
    }
}