﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AnswerNumberToLong" xml:space="preserve">
    <value>The answer number can not be over two characters long.</value>
  </data>
  <data name="AnswerNumberToShort" xml:space="preserve">
    <value>The answer number must be at least one character long.</value>
  </data>
  <data name="BirthdateSizeIncorrect" xml:space="preserve">
    <value>The person's birth date must be exactly ten characters long.</value>
  </data>
  <data name="CityNameIncorrectLength" xml:space="preserve">
    <value>The person's city name must be between one and 38 characters long.</value>
  </data>
  <data name="GivenNameContainsIllegalChars" xml:space="preserve">
    <value>The person's given name can not contain spaces or special characters.</value>
  </data>
  <data name="GivenNameIncorrectLength" xml:space="preserve">
    <value>request.PrimaryRequest.Person.PersonName.PersonGivenName.Length &gt; 50</value>
  </data>
  <data name="LanguagePreferenceInvalid" xml:space="preserve">
    <value>The person's language preference must be either 'eng' or 'spa'.</value>
  </data>
  <data name="MiddleNameContainsIllegalChars" xml:space="preserve">
    <value>The person's middle name can not contain spaces or special characters.</value>
  </data>
  <data name="MiddleNameToLong" xml:space="preserve">
    <value>The person's middle name can not be over 50 characters long.</value>
  </data>
  <data name="PhoneNumberToLong" xml:space="preserve">
    <value>The person's telephone number can not be over 13 characters long.</value>
  </data>
  <data name="PostalCodeIncorrectLength" xml:space="preserve">
    <value>The person's postal code must be exactly five characters long.</value>
  </data>
  <data name="PostalExtensionCodeIncorrectLength" xml:space="preserve">
    <value>The person's postal extension code must be exactly four characters long.</value>
  </data>
  <data name="ProofingCodeInvalid" xml:space="preserve">
    <value>The person's level of proofing code must be 'LevelTwo', 'LevelThree', or 'OptionThree'.</value>
  </data>
  <data name="QuestionNumberToLong" xml:space="preserve">
    <value>The question number can not be at over two characters long.</value>
  </data>
  <data name="QuestionNumberToShort" xml:space="preserve">
    <value>The question number must be at least one character long.</value>
  </data>
  <data name="SessionIDToLong" xml:space="preserve">
    <value>The session identification can not be over 70 characters long.</value>
  </data>
  <data name="SessionIDToShort" xml:space="preserve">
    <value>The session identification must be at least one character long.</value>
  </data>
  <data name="SSNLengthIncorrect" xml:space="preserve">
    <value>The person's SSN must have exactly 9 numbers.</value>
  </data>
  <data name="StateAbbrevIncorrectLength" xml:space="preserve">
    <value>The person's state abbreviation must be exactly two chracters long.</value>
  </data>
  <data name="StreetNameIncorrectLength" xml:space="preserve">
    <value>The person's street name must be between one and 68 characters long.</value>
  </data>
  <data name="SubscriberNumberLengthInvalid" xml:space="preserve">
    <value>The person's subscriber number must be exactly seven characters long.</value>
  </data>
  <data name="SuffixIncorrect" xml:space="preserve">
    <value>The person's suffix may only be 'SR', 'JR', '2', 'II', '3', 'III', '4', or 'IV'</value>
  </data>
  <data name="SurNameContainsIllegalChars" xml:space="preserve">
    <value>The person's surname can not contain spaces or special characters.</value>
  </data>
  <data name="SurNameMisplacedApostrophe" xml:space="preserve">
    <value>The person's surname may only have an apostrophe following the letters 'O', 'D', and 'I'.</value>
  </data>
  <data name="SurNameIncorrectLength" xml:space="preserve">
    <value>The person's surname must be between one and 50 characters long.</value>
  </data>
  <data name="SurNameToManyApostrophes" xml:space="preserve">
    <value>The person's surname may only contain a maximum of one apostrophe.</value>
  </data>
  <data name="SurNameToManyHyphens" xml:space="preserve">
    <value>The person's surname may only contain a maximum of one hyphen.</value>
  </data>
</root>