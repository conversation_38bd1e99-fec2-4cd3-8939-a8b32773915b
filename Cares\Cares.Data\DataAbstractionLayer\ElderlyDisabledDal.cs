using Cares.Api.Infrastructure;
using Cares.Api.Infrastructure.Enums;
using Cares.Api.Infrastructure.Helper;
using Cares.Api.Infrastructure.WebAPI;
using Cares.Api.Messages.Applications;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Api.Messages.Landing;
using Cares.Api.Messages.Person;
using Cares.Data.DataAbstractionLayer.Landing;
using Cares.Data.DataAbstractionLayer.Person;
using Cares.Data.DataAbstractionLayer.Representatives;
using Cares.Data.Helpers;
using Cares.Infrastructure.Log;
using Cares.Models.Application;
using Cares.Models.Application.Insurance;
using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel;
using Cares.Portal.Worker.Models.Models.Snapshot;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Validation;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Transactions;
using ENUMS = Cares.Api.Infrastructure.Enums;
using RefValues = Cares.Api.Infrastructure.Constants.ReferenceValues;

namespace Cares.Data.DataAbstractionLayer
{
    public static class ElderlyDisabledDal
    {
        static readonly ILog _log;

        static ElderlyDisabledDal()
        {
            _log = new Infrastructure.Log.Log(typeof(ElderlyDisabledDal));
        }

        #region Public get/select methods
        /// <summary>
        /// Get Application Information
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledApplicationDto GetApplication(long applicationId, Guid tokenId)
        {
            ElderlyDisabledApplicationDto appResponse = new ElderlyDisabledApplicationDto();
            APPLICATION app = ApplicationDal.GetApplicationFull(applicationId);
            if (app != null)
            {
                // Is this an E&D app
                if (app.SUB_PROGRAM_CATEGORY_ID == (int)Api.Infrastructure.Constants.SubProgramCategories.EandD)
                {
                    ApplicationDal.SetBaseDtoValues(appResponse, app);

                    // Fill in the appResponse.Application
                    SqlToDtoMapper.SqlToDto(app, appResponse.Application);

                    // Get the application with Received Date, Determine Date, Complete Date
                    ApplicationInformation applicationInformation = new ApplicationSnapshotDAL().GetApplicationInformation(appResponse.Application.ApplicationId, tokenId);
                    if (applicationInformation != null)
                    {
                        appResponse.Application.ReceivedDate = applicationInformation.ReceivedDate;
                    }

                    // Get the application with district office id
                    ENUMS.DistrictOffice refDistrictOffice = ENUMS.DistrictOffice.GetDistrictOfficeIdByDistrictOfficeCode(appResponse.Application.WorkerCountyNumber);
                    if (refDistrictOffice != null)
                    {
                        appResponse.Application.DistrictOfficeId = refDistrictOffice.Id;
                    }

                    // Get the review date of an approved E&D application
                    if (appResponse.Application.ApplicationStatusId == (byte)ENUMS.enumApplicationStatus.Approved)
                    {
                        // Get the application enrollment info
                        var enrollment = getEnrollmentInfoByAppIdAndPersonId(appResponse.Application.ApplicationId, appResponse.Application.ContactPersonId);

                        if (enrollment != null)
                        {
                            // Get E&D state aid categories
                            var eanddStateAidCategories = Cares.Api.Infrastructure.Constants.EandDStateAidCategories;

                            var isEandDApp = enrollment.StateAidCategory != null &&
                                    eanddStateAidCategories.Any(c => c.Contains(enrollment.StateAidCategory.Trim()));

                            // Check if the enrollment is for E&D app and cancel date has value
                            if (isEandDApp && enrollment.DoReviewDate.HasValue)
                            {
                                appResponse.Application.ReviewDate = (enrollment.DoReviewDate.Value).ToString("MM/dd/yyyy");
                            }
                        }
                    }

                    // Get the Application Detail for the main applicant
                    appResponse.ApplicationDetail = ApplicationDal.GetApplicationDetailsList<ElderlyDisabledApplicationDetailDto>(applicationId)
                        .FirstOrDefault(ad => ad.PersonId == appResponse.Application.ContactPersonId);
                    // If none was found, ensure one exists:
                    if (appResponse.ApplicationDetail == null)
                    {
                        appResponse.ApplicationDetail = new ElderlyDisabledApplicationDetailDto()
                        {
                            ApplicationId = appResponse.Application.ApplicationId,
                            PersonId = appResponse.Application.ContactPersonId,
                            RelAppFilerId = (int)ENUMS.enumRelationship.Self,
                        };
                    }

                    // Get all Application status history for the Application
                    List<APPLICATION_STATUS_HISTORY> appStatusHistory = ApplicationDal.GetApplicationStatusHistoryFull(applicationId);
                    foreach (var ash in appStatusHistory)
                    {
                        ApplicationStatusHistory appStatusHistoryResponse = new ApplicationStatusHistory();
                        SqlToDtoMapper.SqlToDto(appStatusHistory, appStatusHistoryResponse);
                        appResponse.ApplicationStatusHistory.Add(appStatusHistoryResponse);
                    }

                    // Get Application Elderly Disabled Detail
                    appResponse.ApplicationElderlyDisabledDetail = GetApplicationElderlyDisabledDetail(applicationId, tokenId);

                    // Get Residency Info
                    appResponse.ApplicationResidencyInformation = ApplicationDal.GetApplicationResidencyInformation(applicationId, tokenId);

                    // Get Living Arrangement
                    appResponse.ApplicationLivingArrangement = ApplicationDal.GetApplicationLivingArrangement(applicationId, tokenId);

                    // Get Person data by personId
                    appResponse.Person = GetEdPersonByPersonId(app.CONTACT_PERSON_ID);

                    // Get PersonDetail data by personId
                    appResponse.PersonDetail = GetEdPersonDetailByPersonId(app.CONTACT_PERSON_ID);

                    // Get PersonRace
                    appResponse.PersonRaces = PersonDal.GetPersonRace(app.CONTACT_PERSON_ID, true);

                    // Get Person Address and Addresses
                    appResponse.PersonAddresses = PersonDal.GetPersonAddresses(app.CONTACT_PERSON_ID);

                    // Get person Phones
                    appResponse.PersonPhones = PersonDal.GetPersonPhones(app.CONTACT_PERSON_ID);

                    // Get Person Contact Preference
                    appResponse.PersonContactPreference = PersonDal.GetPersonContactPreference(app.CONTACT_PERSON_ID, true);

                    // Get Spouse and FormerSpouse information, which is used by the application screen when prompting the user during marital status changes
                    using (var context = new CaresApplicationDBEntities())
                    {
                        appResponse.SpouseId = getSpouseId(context, applicationId, app.CONTACT_PERSON_ID);
                        appResponse.HasFormerSpouseData = context.APPLICATION_FORMER_SPOUSE.Any(fp => fp.APPLICATION_ID == applicationId);
                    }

                    // Check if there is an active QMB enrollment for the applicant
                    appResponse.IsAlreadyEnrolledQMBOnly = hasActiveQMBEnrollment(app.CONTACT_PERSON_ID);
                    bool isAlreadyEnrolled = false;
                    string latestCancelDate = string.Empty;

                    getQMBEnrollmentData(app.CONTACT_PERSON_ID, out isAlreadyEnrolled, out latestCancelDate);
                    appResponse.IsAlreadyEnrolledQMBOnly = isAlreadyEnrolled;
                    appResponse.LastDateOnQMBOnly = latestCancelDate;

                    // Get Person Alerts
                    appResponse.PersonAlert = PersonDal.GetPersonsAlerts((int)app.CONTACT_PERSON_ID);
                }
                else
                {
                    appResponse.IsSuccessful = false;
                    appResponse.CaresError = CaresError.WrongApplicationType;
                }
            }
            else
            {
                appResponse.IsSuccessful = false;
                appResponse.CaresError = CaresError.NotFound;
            }

            return appResponse;
        }

        /// <summary>
        /// Get ED Person
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledPerson GetEdPersonByPersonId(long personId)
        {
            ElderlyDisabledPerson person = new ElderlyDisabledPerson()
            {
                PersonId = personId
            };

            using (var context = new CaresApplicationDBEntities())
            {
                sysnl_donotmodify__PERSON dbPerson = (from row in context.sysnl_donotmodify__PERSON
                                                      where row.PERSON_ID == personId
                                                      select row).FirstOrDefault();
                if (dbPerson != null)
                {
                    SqlToDtoMapper.SqlToDto(dbPerson, person);
                }
            }

            return person;
        }

        /// <summary>
        ///  Get ED PersonDetail by personId
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledPersonDetail GetEdPersonDetailByPersonId(long personId)
        {
            ElderlyDisabledPersonDetail person = new ElderlyDisabledPersonDetail()
            {
                PersonId = personId
            };

            using (var context = new CaresApplicationDBEntities())
            {
                var dbPersonDetail = (from row in context.t_PERSON_DETAIL
                                      where row.PERSON_ID == personId
                                      select row).FirstOrDefault();
                if (dbPersonDetail != null)
                {
                    SqlToDtoMapper.SqlToDto(dbPersonDetail, person);
                }
            }

            // Gets the MBI (Medicare Beneficiary Identifier) number
            person.MBINumber = PersonDal.GetMBINumberByPersonId(personId);

            return person;
        }

        /// <summary>
        /// Gets the ApplicationElderlyDisabledDetail.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ApplicationElderlyDisabledDetail GetApplicationElderlyDisabledDetail(long applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var aedd = new ApplicationElderlyDisabledDetail();
                APPLICATION_ELDERLY_DISABLED_DETAIL aeddRecord = context.APPLICATION_ELDERLY_DISABLED_DETAIL.FirstOrDefault(a => a.APPLICATION_ID == applicationId);
                if (aeddRecord != null)
                {
                    SqlToDtoMapper.SqlToDto(aeddRecord, aedd);

                    // Set the aedd.ProgramOrCategory based on the aeddRecord.ELDERLY_DISABLED_PROGRAM_ID
                    var enDProgram = ENUMS.ElderlyDisabledProgram.GetProgramById(aeddRecord.ELDERLY_DISABLED_PROGRAM_ID ?? 0);
                    var otherprograms = ENUMS.ElderlyDisabledProgram.GetElderlyDisabledOtherPrograms();
                    if (enDProgram == null)
                    {
                        aedd.ProgramOrCategory = ENUMS.enumElderlyDisabledMedicaidPrograms.Unknown;
                    }
                    else if (enDProgram.IsSSIRelated)
                    {
                        aedd.ProgramOrCategory = ENUMS.enumElderlyDisabledMedicaidPrograms.SSIRelatedPrograms;
                    }
                    else if (otherprograms.Any(p => p.Id == (byte)aedd.ElderlyDisabledProgramId))
                    {
                        // These programs are Nursing Home related so we have to keep showing the NH selected. PBI: 212661
                        if (ENUMS.ElderlyDisabledProgram.IsNursingHomeRelatedProgram(aedd.ElderlyDisabledProgramId))
                        {
                            aedd.NursingHomeId = aeddRecord.EXPEDITE_FACILITY_PROVIDER_ID;
                        }
                        else
                        {
                            aedd.HospitalId = aeddRecord.EXPEDITE_FACILITY_PROVIDER_ID;
                        }
                        aedd.ProgramOrCategory = ENUMS.enumElderlyDisabledMedicaidPrograms.OtherPrograms;
                    }
                    else if (enDProgram.IsHomeCommunityWaiver)
                    {
                        aedd.ProgramOrCategory = ENUMS.enumElderlyDisabledMedicaidPrograms.HomeAndCommunityBasedWaiverProgram;
                    }
                    else if (enDProgram == ENUMS.ElderlyDisabledProgram.Hospital_Medicaid)//Hospital
                    {
                        aedd.ProgramOrCategory = ENUMS.enumElderlyDisabledMedicaidPrograms.Hospital;
                    }
                    else//All remaining are considered nursing home
                    {
                        aedd.ProgramOrCategory = ENUMS.enumElderlyDisabledMedicaidPrograms.NursingHome;
                    }

                    //  Since the facility id is single field, use the program applying to determine where the expedite facility provider id needs to be mapped.
                    if (aedd.ProgramOrCategory == ENUMS.enumElderlyDisabledMedicaidPrograms.Hospital)
                    {
                        aedd.HospitalId = aeddRecord.EXPEDITE_FACILITY_PROVIDER_ID;
                    }
                    else if (aedd.ProgramOrCategory == ENUMS.enumElderlyDisabledMedicaidPrograms.NursingHome)
                    {
                        aedd.NursingHomeId = aeddRecord.EXPEDITE_FACILITY_PROVIDER_ID;
                    }
                }
                return aedd;
            }
        }

        /// <summary>
        /// Get SpouseId.
        /// </summary>
        /// <param name="context">The context</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="contactPersonId">The contact person identifier.</param>
        /// <returns></returns>
        private static long? getSpouseId(CaresApplicationDBEntities context, long applicationId, long contactPersonId)
        {
            return (from ah in context.APPLICATION_HOUSEHOLD
                    where ah.APPLICATION_ID == applicationId && ah.PERSON_ID == contactPersonId
                    select ah.SPOUSE_ID).FirstOrDefault();
        }

        /// <summary>
        ///  Get enrollment info by application id and person id
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <returns></returns>
        private static LandingEnrollment getEnrollmentInfoByAppIdAndPersonId(long applicationId, long personId)
        {
            var enrollments = new PersonDetailDal().GetEnrollmentInfo(personId).ToList();
            return enrollments.FirstOrDefault(e => e.ApplicationId == applicationId);
        }

        private static bool hasActiveQMBEnrollment(long personId)
        {
            var enrollments = new PersonDetailDal().GetEnrollmentInfo(personId);
            DateTime today = DateTime.Now.Date;

            var activeQMBEnrollment = enrollments
            .Where(e => e.StateAidCategory.Trim() == "95")
            .Where(e => (e.EnrollmentStatus == Api.Messages.Enums.EnrollmentStatus.Active
                    || (e.StartDate <= today && (e.CancelDate == null || e.CancelDate >= today))));

            return activeQMBEnrollment.Any();
        }

        /// <summary>
        /// Get two values, if the last MSP-QMB is active, and the last cancel date as string for the last QMB enrolled.
        /// </summary>
        /// <param name="personId"></param>
        /// <returns></returns>
        private static void getQMBEnrollmentData(long personId, out bool isQMBEnrolled, out string lastCancelDate)
        {
            var enrollments = new PersonDetailDal().GetEnrollmentInfo(personId);
            DateTime today = DateTime.Now.Date;

            var activeQMBEnrollment = enrollments
            .Where(e => e.StateAidCategory.Trim() == ProgramSubCategory._95DOQMBOnly.State_Aid_Category_Id)
            .Where(e => (e.EnrollmentStatus == Api.Messages.Enums.EnrollmentStatus.Active
                    || (e.StartDate <= today && (e.CancelDate == null || e.CancelDate >= today))));

            var lastQMBCancelDate = enrollments
            .Where(e => e.StateAidCategory.Trim() == ProgramSubCategory._95DOQMBOnly.State_Aid_Category_Id)
            .OrderByDescending(e => e.AwardDate).FirstOrDefault()?.CancelDate?.Date.ToString("yyyy-MM-dd");

            isQMBEnrolled = activeQMBEnrollment.Any();
            lastCancelDate = lastQMBCancelDate;
        }

        /// <summary>
        /// Get Spouse Info by app Id
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledSpouseDto GetSpouseInfo(long applicationId, Guid tokenId)
        {
            ElderlyDisabledSpouseDto spouseInfo = new ElderlyDisabledSpouseDto()
            {
                ApplicationId = applicationId
            };

            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                // Find the app
                var theApp = context.APPLICATIONs.FirstOrDefault(app => app.APPLICATION_ID == applicationId);
                if (theApp != null)
                {
                    ApplicationDal.SetBaseDtoValues(spouseInfo, theApp);
                    spouseInfo.ContactPersonMaritalStatusId = theApp.CONTACT_PERSON_MARITAL_STATUS_ID;

                    // Spouse data
                    long? spouseId = getSpouseId(context, applicationId, spouseInfo.ContactPersonId);
                    if (spouseId != null)
                    {
                        spouseInfo.Spouse = GetEdPersonByPersonId(spouseId.Value);

                        // Special conversion: A DOB of 01/01/1900 is "empty".  Set to null
                        if (spouseInfo.Spouse.DateOfBirth == Constants.EmptyDate)
                        {
                            spouseInfo.Spouse.DateOfBirth = null;
                        }

                        spouseInfo.SpouseDetail = GetEdPersonDetailByPersonId(spouseId.Value);
                        // Create a default object for address:
                        spouseInfo.SpouseAddress = new DependentPersonAddress
                        {
                            AddressSameAsPrimary = null,
                            PersonAddress = new PersonAddress()
                        };

                        // Get Primary and Spouse Address data in order to determine AddressSameAsPrimary
                        var primaryAddresses = PersonDal.GetPersonAddresses(spouseInfo.ContactPersonId);
                        var spouseAddresses = PersonDal.GetPersonAddresses(spouseId.Value, false);
                        if (spouseAddresses.Addresses.Count > 0)
                        {
                            // Assume not the same
                            spouseInfo.SpouseAddress.AddressSameAsPrimary = false;
                            spouseAddresses.Addresses.ForEach(adrs =>
                            {
                                if (primaryAddresses.Addresses.Any(adrs2 => adrs2.AddressId == adrs.AddressId))
                                {
                                    // Found matching addresses
                                    spouseInfo.SpouseAddress.AddressSameAsPrimary = true;
                                }
                            });
                            if (!spouseInfo.SpouseAddress.AddressSameAsPrimary.Value)
                            {
                                spouseInfo.SpouseAddress.PersonAddress = spouseAddresses.Addresses[0];
                            }
                        }

                        // Get Phone data
                        var phones = PersonDal.GetPersonPhones(spouseId.Value);
                        if (phones.Count > 0)
                        {
                            spouseInfo.SpousePhone = phones[0];
                        }

                        // Get contact preference data
                        spouseInfo.SpouseContactPreference = PersonDal.GetPersonContactPreference(spouseId.Value, true);
                    }

                    // Get former spouses
                    spouseInfo.FormerSpouses = GetFormerSpouses(applicationId, tokenId);
                }
                else
                {
                    // If we can't find the associated app, don't continue
                    spouseInfo.IsSuccessful = false;
                    spouseInfo.CaresError = CaresError.NotFound;
                    return spouseInfo;
                }
            }

            return spouseInfo;
        }

        /// <summary>
        /// Get former spouses
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static List<ElderlyDisabledFormerSpouseDto> GetFormerSpouses(long applicationId, Guid tokenId)
        {
            List<ElderlyDisabledFormerSpouseDto> fsList = new List<ElderlyDisabledFormerSpouseDto>();

            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var fss = context.APPLICATION_FORMER_SPOUSE.Where(fp => fp.APPLICATION_ID == applicationId).ToList();
                foreach (var fp in fss)
                {
                    ElderlyDisabledFormerSpouseDto newFS = new ElderlyDisabledFormerSpouseDto();
                    SqlToDtoMapper.SqlToDto(fp, newFS);
                    fsList.Add(newFS);
                }
            }

            return fsList;
        }

        /// <summary>
        /// Selects household members
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static List<ElderlyDisabledHouseholdMemberDto> SelectHouseholdMembers(long applicationId, Guid tokenId)
        {
            List<ElderlyDisabledHouseholdMemberDto> hmList = new List<ElderlyDisabledHouseholdMemberDto>();

            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var dbHouseholdMember = context.APPLICATION_NON_MAGI_NO_SSN_PERSON.Where(hm => hm.APPLICATION_ID == applicationId).ToList();
                if (dbHouseholdMember != null)
                {
                    foreach (var hm in dbHouseholdMember)
                    {
                        ElderlyDisabledHouseholdMemberDto newHM = new ElderlyDisabledHouseholdMemberDto()
                        {
                            ApplicationNonMagiNoSsnPersonId = hm.APPLICATION_NON_MAGI_NO_SSN_PERSON_ID,
                            FirstName = hm.FIRST_NAME,
                            MiddleName = hm.MIDDLE_NAME,
                            LastName = hm.LAST_NAME,
                            SuffixId = hm.SUFFIX_ID,
                            RelationshipTypeId = hm.RELATIONSHIP_TYPE_ID,
                            Age = hm.AGE
                        };
                        hmList.Add(newHM);
                    }
                }
            }

            return hmList;
        }

        /// <summary>
        /// Updates household members
        /// </summary>
        /// <param name="memberInfo">The member information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool UpdateHouseholdMembers(ElderlyDisabledHouseholdMembersDto memberInfo, Guid tokenId)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                MSPIntakeModel mspIntake = new MSPIntakeModel()
                {
                    ApplicationId = (int)memberInfo.ApplicationId,
                    UpdatedBy = memberInfo.UpdatedBy,
                    ApplicationDetails = new MSPIntakeApplicationDetails() { FamilyDetails = new List<FamilyDetail>() }
                };

                foreach (var memberDetail in memberInfo.HouseholdMembers)
                {
                    mspIntake.ApplicationDetails.FamilyDetails.Add(new FamilyDetail()
                    {
                        AssociatedFamilyId = memberDetail.ApplicationNonMagiNoSsnPersonId,
                        Relationship = memberDetail.RelationshipTypeId.ToString(),
                        Age = memberDetail.Age,
                        FirstName = memberDetail.FirstName,
                        MiddleName = memberDetail.MiddleName,
                        LastName = memberDetail.LastName,
                        SuffixId = memberDetail.SuffixId
                    });
                }

                // MSPPersonalInformationDAL has been designed to accept context object as one of the parameters. Technically DAL should handle this,
                // but because of the way it is designed and E&D is re-using it, we are passing context object to DAL.
                new MSPPersonalInformationDAL().SaveAssociatedFamily(mspIntake, context);
            }
            return true;
        }

        /// <summary>
        /// Deletes household member
        /// </summary>
        /// <param name="applicationNonMagiNoSsnPersonId">The application non-magi no SSN person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool DeleteHouseholdMember(int applicationNonMagiNoSsnPersonId, Guid tokenId)
        {
            FamilyDetail familyDetail = new FamilyDetail() { AssociatedFamilyId = applicationNonMagiNoSsnPersonId };
            new MSPPersonalInformationDAL().DeleteAssociatedFamilyMember(familyDetail, tokenId);
            return true;
        }

        /// <summary>
        /// Gets the property information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledPropertyDto GetPropertyInformation(long applicationId, Guid tokenId)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var objparamPropertyJson = new ObjectParameter("ApplicantProperty", typeof(string));
                context.usp_SELECT_APPLICATION_PROPERTY_INFORMATION(applicationId, objparamPropertyJson);
                ElderlyDisabledPropertyDto result;

                if (!string.IsNullOrEmpty(objparamPropertyJson.Value.ToString()))
                {
                    //  Property information was found
                    result = JsonConvert.DeserializeObject<ElderlyDisabledPropertyDto>(objparamPropertyJson.Value.ToString());
                }
                else
                {
                    //  No data found
                    result = new ElderlyDisabledPropertyDto { ApplicationId = applicationId };
                }

                ApplicationDal.SetBaseDtoValues(result, applicationId);
                return result;
            }
        }

        /// <summary>
        /// select the resource information by appId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledResourceDto SelectResourceInformation(long applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                ObjectParameter applicantResourceInfo = new ObjectParameter("ApplicantResourceInfo", typeof(string));
                var result = context.usp_SELECT_RESOURCE_INFORMATION(applicationId, applicantResourceInfo);
                ElderlyDisabledResourceDto appResourceInfo;

                if (!string.IsNullOrEmpty(applicantResourceInfo.Value.ToString()))
                {
                    appResourceInfo = JsonConvert.DeserializeObject<ElderlyDisabledResourceDto>(applicantResourceInfo.Value.ToString());
                }
                else
                {
                    appResourceInfo = new ElderlyDisabledResourceDto { ApplicationId = applicationId };
                }

                // Ensure these lists exist, mostly for unit tests
                appResourceInfo.ResourceDetails = appResourceInfo.ResourceDetails ?? new List<ElderlyDisabledResourceDetailDto>();
                appResourceInfo.ResourceBankDetails = appResourceInfo.ResourceBankDetails ?? new List<ElderlyDisabledResourceBankDetailDto>();
                appResourceInfo.ResourceTransferDetails = appResourceInfo.ResourceTransferDetails ?? new List<ElderlyDisabledResourceTransferDto>();

                foreach (var resourceTransferDetail in appResourceInfo.ResourceTransferDetails)
                {
                    // Currently NULLs are not allowed in the DB for SoldGivenDate.  It's set to SQLDefaultDate when NULL during the save
                    // Check for default date (01/01/1900) and switch it back to null here:
                    if (resourceTransferDetail.SoldGivenDate == Api.Infrastructure.Constants.SQLDefaultDate)
                    {
                        resourceTransferDetail.SoldGivenDate = null;
                    }
                }

                AddLatestResourceValuesToHeader(appResourceInfo);

                ApplicationDal.SetBaseDtoValues(appResourceInfo, applicationId);
                return appResourceInfo;
            }
        }

        private static void AddLatestResourceValuesToHeader(ElderlyDisabledResourceDto appResourceInfo)
        {
            if (appResourceInfo.ResourceBankDetails != null || appResourceInfo.ResourceBankDetails.Count > 0)
            {
                foreach (var item in appResourceInfo.ResourceBankDetails)
                {
                    if (item.BankDetails == null || item.BankDetails.Count == 0)
                    {
                        continue;
                    }

                    var latestBankDetails = item.BankDetails.OrderByDescending(x => x.ResourceMonth).FirstOrDefault();
                    item.CountableAmount = latestBankDetails.CountableAmount;
                }
            }

            if (appResourceInfo.ResourceTransferDetails != null || appResourceInfo.ResourceTransferDetails.Count > 0)
            {
                foreach (var item in appResourceInfo.ResourceTransferDetails)
                {
                    if (item.EDTransferMonthDetail == null || item.EDTransferMonthDetail.Count == 0)
                    {
                        continue;
                    }

                    var latestTransferDetails = item.EDTransferMonthDetail.OrderByDescending(x => x.ResourceMonth).FirstOrDefault();
                    item.CountableAmount = latestTransferDetails.CountableAmount;
                }
            }

        }

        /// <summary>
        /// Get E&D life insurance information by applicationId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledLifeInsuranceDto GetLifeInsuranceInfo(long applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                ObjectParameter applicationInsuranceInfo = new ObjectParameter("ApplicationInsuranceInfo", typeof(string));
                var result = context.usp_SELECT_LIFE_INSURANCE_INFORMATION(applicationId, applicationInsuranceInfo);
                ElderlyDisabledLifeInsuranceDto appInsuranceInfo;

                if (!string.IsNullOrEmpty(applicationInsuranceInfo.Value.ToString()))
                {
                    appInsuranceInfo = JsonConvert.DeserializeObject<ElderlyDisabledLifeInsuranceDto>(applicationInsuranceInfo.Value.ToString());
                    AddLatestInsuranceDetailsToTheHeader(appInsuranceInfo);
                    AddLatestOtherBurialFundToTheHeader(appInsuranceInfo);
                }
                else
                {
                    appInsuranceInfo = new ElderlyDisabledLifeInsuranceDto { ApplicationId = applicationId };
                }

                // Ensure these lists are not null:
                appInsuranceInfo.AdditionalBurialFundDetails = appInsuranceInfo.AdditionalBurialFundDetails ?? new List<ElderlyDisabledAdditionalBurialFundsDto>();
                appInsuranceInfo.LifeInsuranceDetails = appInsuranceInfo.LifeInsuranceDetails ?? new List<ElderlyDisabledLifeInsuranceDetailDto>();
                appInsuranceInfo.OtherBurialFundDetails = appInsuranceInfo.OtherBurialFundDetails ?? new List<ElderlyDisabledOtherBurialFundsDto>();
                appInsuranceInfo.OtherBurialFundDetails.ForEach(a =>
                {
                    a.OtherBurialFundsBudgetDetails = a.OtherBurialFundsBudgetDetails ?? new List<ElderlyDisabledOtherBurialFundsDetailsDto>();
                    a.OtherBurialFundsBudgetDetails.ForEach(b =>
                    {
                        b.PrepaidBurialSpaceDetails = b.PrepaidBurialSpaceDetails ?? new List<ElderlyDisabledPrepaidBurialSpaceDetailDto>();
                    });
                });
               
                appInsuranceInfo.AdditionalBurialFundDetails.ForEach(a =>
                    a.Budgets = a.Budgets ?? new List<ElderlyDisabledAdditionalBurialFundsBudgetDto>());
                appInsuranceInfo.LifeInsuranceDetails.ForEach(a =>
                    a.AdditionalLifeInsuranceDetails = a.AdditionalLifeInsuranceDetails ?? new List<ElderlyDisabledLifeInsuranceAdditionalDetailDto>());
                appInsuranceInfo.LifeInsuranceDetails.ForEach(a =>
                    a.AdditionalLifeInsuranceDetails = a.AdditionalLifeInsuranceDetails ?? new List<ElderlyDisabledLifeInsuranceAdditionalDetailDto>());

                // Convert Budget EffectiveDate
                appInsuranceInfo.AdditionalBurialFundDetails.ForEach(a =>
                    a.Budgets.ForEach(b =>
                        b.EffectiveDate = b.EffectiveDate == DateTime.MinValue ? null : b.EffectiveDate
                        )
                    );

                ApplicationDal.SetBaseDtoValues(appInsuranceInfo, applicationId);
                return appInsuranceInfo;
            }
        }

        private static void AddLatestInsuranceDetailsToTheHeader(ElderlyDisabledLifeInsuranceDto appInsuranceInfo)
        {
            if (appInsuranceInfo.LifeInsuranceDetails == null || appInsuranceInfo.LifeInsuranceDetails.Count == 0)
            {
                return;
            }

            foreach (var item in appInsuranceInfo.LifeInsuranceDetails)
            {
                if (item.AdditionalLifeInsuranceDetails == null || item.AdditionalLifeInsuranceDetails.Count == 0)
                {
                    continue;
                }

                var latestInsuranceDetails = item.AdditionalLifeInsuranceDetails.OrderByDescending(x => x.Month).FirstOrDefault();
                item.PolicyValue = latestInsuranceDetails.PolicyValue;
                item.CashSurrenderValue = latestInsuranceDetails.CashSurrenderValue;
                item.HasCountableAmount = latestInsuranceDetails.HasCountableAmount;
                item.IsContractRevocable = latestInsuranceDetails.IsContractRevocable;
                item.DateDisposed = latestInsuranceDetails.DateDisposed;
            }
        }

        private static void AddLatestOtherBurialFundToTheHeader(ElderlyDisabledLifeInsuranceDto appInsuranceInfo)
         {
            if (appInsuranceInfo.OtherBurialFundDetails == null || appInsuranceInfo.OtherBurialFundDetails.Count == 0) 
            { 
                return; 
            }

            foreach (var otherBurialFundDetail in appInsuranceInfo.OtherBurialFundDetails)
            {
                if (otherBurialFundDetail.OtherBurialFundsBudgetDetails == null || otherBurialFundDetail.OtherBurialFundsBudgetDetails.Count == 0)
                {
                    continue;
                }

                var latestDetails = otherBurialFundDetail.OtherBurialFundsBudgetDetails.OrderByDescending(x => x.EffectiveDate).FirstOrDefault();
                otherBurialFundDetail.DateDisposed = latestDetails.DateDisposed;
                otherBurialFundDetail.OtherBurialFundsAmount = latestDetails.PrepaidBurialContractTotal;
                otherBurialFundDetail.HasCountableAmount = latestDetails.IsCountableAmount;
            }
        }

        /// <summary>
        /// Get E&D Eligibility Enrollment Denials for Snapshot
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static List<ElderlyDisabledEligibilityDeterminationDto> GetElderlyDisabledEnrollmentSegmentsDenials(long applicationId, Guid tokenId)
        {
            List<ElderlyDisabledEligibilityDeterminationDto> response = new List<ElderlyDisabledEligibilityDeterminationDto>();
            using (var context = new CaresApplicationDBEntities())
            {
                var edResult = context.APPLICATION_ELIGIBILITY_DENIAL.Where(ee => ee.APPLICATION_ID == applicationId);
                // The APPLICATION_ELIGIBILITY_DENIAL table does not allow for the storing of multiple denial codes, so multiple rows
                // are created for segments with multiple denial reasons.
                // Use a Dictionary to consolidate those back together, based on a common StartDate
                Dictionary<DateTime, ElderlyDisabledEligibilityDeterminationDto> determinations = new Dictionary<DateTime, ElderlyDisabledEligibilityDeterminationDto>();
                foreach (var elig in edResult)
                {
                    if (determinations.ContainsKey(elig.START_DATE))
                    {
                        // Already found this segment, just add the Denial reason to the one
                        determinations[elig.START_DATE].DenialReasons.Add(elig.STATE_REASON_ID);
                    }
                    else
                    {
                        ElderlyDisabledEligibilityDeterminationDto newDet = new ElderlyDisabledEligibilityDeterminationDto()
                        {
                            ApplicationId = elig.APPLICATION_ID,
                            StartDate = elig.START_DATE,
                            CancelDate = elig.CANCEL_DATE,
                            DenialReasons = new List<short> { elig.STATE_REASON_ID },
                            IsAward = false
                        };
                        determinations.Add(elig.START_DATE, newDet);
                        response.Add(newDet);
                    }
                }
            }

            return response;
        }

        /// <summary>
        /// Gets E&D Enrollment segments.
        /// </summary>
        /// <param name="applicationId">The applicationId identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        public static List<ElderlyDisabledEligibilityDeterminationDto> GetEnrollmentEligibilitySegments(long applicationId, Guid tokenId)
        {
            List<ElderlyDisabledEligibilityDeterminationDto> response = new List<ElderlyDisabledEligibilityDeterminationDto>();
            using (var context = new CaresApplicationDBEntities())
            {
                var edElig = context.vw_ELIGIBILITY_ENROLLMENT.AsNoTracking().Where(e => e.APPLICATION_ID == applicationId).OrderBy(e => e.START_DATE).ToList();
                foreach (var elig in edElig)
                {
                    response.Add(new ElderlyDisabledEligibilityDeterminationDto()
                    {
                        ApplicationId = elig.APPLICATION_ID,
                        ProgramId = elig.PROGRAM_ID,
                        ProgramSubCategoryId = elig.PROGRAM_SUB_CATEGORY_ID,
                        OverrideReasonId = elig.OVERRIDE_REASON_ID,
                        StartDate = elig.START_DATE,
                        CancelDate = elig.CANCEL_DATE,
                        CancelReasonId = elig.CANCEL_REASON_ID,
                        OverrideBy = elig.OVERRIDE_BY,
                        OverrideDate = elig.OVERRIDE_DATE,
                        AwardDate = elig.APPLICATION_ENROLLMENT_CREATED_DATE,
                        IsAward = true,
                    });
                }
            }
            return response;
        }

        /// <summary>
        /// Returns Enrollment End date for any application
        /// </summary>
        /// <param name="applicationid"></param>
        /// <returns></returns>
        public static DateTime GetEnrollmentEndDateByApplicationId(long applicationid)
        {
            APPLICATION_ENROLLMENT enrollment = new APPLICATION_ENROLLMENT();
            using (var context = new CaresApplicationDBEntities())
            {
                enrollment = context.APPLICATION_ENROLLMENT
                                .AsNoTracking().Where(e => e.APPLICATION_ID == applicationid).FirstOrDefault();
            }
            if (enrollment != null)
            {
                return (enrollment.CANCEL_DATE.HasValue ? enrollment.CANCEL_DATE.Value : DateTime.MinValue);
            }
            else
            {
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// Select personal property information by applicationId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledPersonalPropertyDto SelectPersonalPropertyInfo(long applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                ObjectParameter applicationPersonalPropertyInfo = new ObjectParameter("ApplicationPersonalPropertyInfo", typeof(string));
                var result = context.usp_SELECT_PERSONAL_PROPERTY_INFORMATION(applicationId, applicationPersonalPropertyInfo);
                ElderlyDisabledPersonalPropertyDto appPersonalPropertyInfo;
                if (!string.IsNullOrEmpty(applicationPersonalPropertyInfo.Value.ToString()))
                {
                    appPersonalPropertyInfo = JsonConvert.DeserializeObject<ElderlyDisabledPersonalPropertyDto>
                        (applicationPersonalPropertyInfo.Value.ToString());

                    AddLatestAutoDetailsToTheHeader(appPersonalPropertyInfo);
                    AddLatestCollectibleAndMachineDetailsToTheHeader(appPersonalPropertyInfo);
                }
                else
                {
                    appPersonalPropertyInfo = new ElderlyDisabledPersonalPropertyDto { ApplicationId = applicationId };
                }
                ApplicationDal.SetBaseDtoValues(appPersonalPropertyInfo, applicationId);
                return appPersonalPropertyInfo;
            }
        }

        /// <summary>
        /// Get E&D medical insurance information by applicationId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledMedicalInsuranceDto SelectMedicalInsuranceInfo(long applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                ObjectParameter applicationMedicalInsuranceInfo = new ObjectParameter("ApplicationMedicalInsuranceInfo", typeof(string));
                var result = context.usp_SELECT_MEDICAL_INSURANCE_INFORMATION(applicationId, applicationMedicalInsuranceInfo);
                ElderlyDisabledMedicalInsuranceDto appMedicalInsuranceInfo;

                if (!string.IsNullOrEmpty(applicationMedicalInsuranceInfo.Value.ToString()))
                {
                    appMedicalInsuranceInfo = JsonConvert.DeserializeObject<ElderlyDisabledMedicalInsuranceDto>(applicationMedicalInsuranceInfo.Value.ToString());
                }
                else
                {
                    appMedicalInsuranceInfo = new ElderlyDisabledMedicalInsuranceDto { ApplicationId = applicationId };
                }

                // Ensure various objects are not null (or corrected to null)
                appMedicalInsuranceInfo.PartDBudgets = appMedicalInsuranceInfo.PartDBudgets == null ? new List<ElderlyDisabledMedicalInsurancePartDBudgetDto>() : appMedicalInsuranceInfo.PartDBudgets;
                appMedicalInsuranceInfo.MedicalInsuranceDetails = appMedicalInsuranceInfo.MedicalInsuranceDetails == null ? new List<ElderlyDisabledMedicalInsuranceDetailDto>() : appMedicalInsuranceInfo.MedicalInsuranceDetails;
                appMedicalInsuranceInfo.TBQMedicarePartDInfo = appMedicalInsuranceInfo.TBQMedicarePartDInfo == null ?
                    new ElderlyDisabledTBQMedicarePartDInfoDto() : appMedicalInsuranceInfo.TBQMedicarePartDInfo;
                foreach (var medInsDetail in appMedicalInsuranceInfo.MedicalInsuranceDetails)
                {
                    medInsDetail.MonthDetails = medInsDetail.MonthDetails ?? new List<ElderlyDisabledMedicalInsuranceMonthDetailDto>();
                    foreach (var monthDetail in medInsDetail.MonthDetails)
                    {
                        // Convert back to null if necessary
                        monthDetail.ResourceMonth = monthDetail.ResourceMonth == DateTime.MinValue ? null : monthDetail.ResourceMonth;
                    }
                }
                foreach (var partDBugdet in appMedicalInsuranceInfo.PartDBudgets)
                {
                    // Convert back to null if necessary
                    partDBugdet.EffectiveDate = partDBugdet.EffectiveDate == DateTime.MinValue ? null : partDBugdet.EffectiveDate;
                }

                // Get TBQ Medicare Part-D information
                ObjectParameter tBQMedicarePartDInfo = new ObjectParameter("TBQMedicarePartDInfo", typeof(string));
                var tbqMedicarePartDResult = context.usp_SELECT_TBQ_MEDICARE_PART_D_INFO(applicationId, tBQMedicarePartDInfo);

                if (!string.IsNullOrEmpty(tBQMedicarePartDInfo.Value.ToString()))
                {
                    appMedicalInsuranceInfo.TBQMedicarePartDInfo = JsonConvert.DeserializeObject<ElderlyDisabledTBQMedicarePartDInfoDto>(tBQMedicarePartDInfo.Value.ToString());
                }

                // If there is no Medicare Part-D info found in the TBQ (Territorial Beneficiary Query), then set as 'N/A'
                setNotApplicable(appMedicalInsuranceInfo.TBQMedicarePartDInfo);

                ApplicationDal.SetBaseDtoValues(appMedicalInsuranceInfo, applicationId);

                if (appMedicalInsuranceInfo.MedicalLongTermCareInsuranceDetails != null)
                {
                    var latestMedicalLTCDetails = appMedicalInsuranceInfo.MedicalLongTermCareInsuranceDetails.OrderByDescending(x => x.EffectiveDate).FirstOrDefault();
                    if (latestMedicalLTCDetails != null)
                    {
                        appMedicalInsuranceInfo.LTCBenefitAmount = latestMedicalLTCDetails.BenefitAmount;
                        appMedicalInsuranceInfo.LTCIsCountableAmount = latestMedicalLTCDetails.IsCountableAmount;
                        appMedicalInsuranceInfo.DateDisposed = latestMedicalLTCDetails.DateDisposed;
                    }
                }
              
                return appMedicalInsuranceInfo;
            }
        }

        /// <summary>
        /// Gets the person liability information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledLiabilityDto GetPersonLiabilityInfo(long applicationId, long personId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                ElderlyDisabledLiabilityDto appLiabilityInfo = new ElderlyDisabledLiabilityDto()
                {
                    ApplicationId = applicationId,
                    ContactPersonId = personId
                };

                // Check if DOD is reported.
                t_PERSON_DETAIL existingPersonDetail = context.t_PERSON_DETAIL.FirstOrDefault(p => p.PERSON_ID == personId);
                appLiabilityInfo.IsDoDReported = existingPersonDetail != null && existingPersonDetail.DATE_OF_DEATH.HasValue;

                // Get the app's elderly disabled program id
                APPLICATION_ELDERLY_DISABLED_DETAIL aedd = context.APPLICATION_ELDERLY_DISABLED_DETAIL.FirstOrDefault(a => a.APPLICATION_ID == applicationId);
                if (aedd != null)
                {
                    appLiabilityInfo.ElderlyDisabledProgramId = aedd.ELDERLY_DISABLED_PROGRAM_ID;
                }

                // Get the app's status
                var app = context.APPLICATIONs.FirstOrDefault(a => a.APPLICATION_ID == applicationId);
                appLiabilityInfo.ApplicationStatusId = app.APPLICATION_STATUS_ID ?? 0;

                // Get the Liability Test sections
                appLiabilityInfo.LiabilityTests = getLiabilityTestData(personId, tokenId, context);

                // Get representative info by primary contact id
                appLiabilityInfo.RepresentativeInfo = getPrimarySponsorInfo(personId, tokenId);

                // Get liability info
                // Note that 0 must be passed to the second argument.  Needing all segments for this person
                appLiabilityInfo.LiabilitySegments = getPersonLiabilityInfo(personId, 0, tokenId, context);

                return appLiabilityInfo;
            }
        }

        /// <summary>
        /// Upserts Liability Test data
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public static BaseApiResponse UpsertLiabilityTestData(ElderlyDisabledLiabilityDto personLiabilityInfo, string username, Guid tokenId)
        {
            BaseApiResponse response = new BaseApiResponse();

            // A bit of a sanity check, mostly needed for automated tests:
            if (string.IsNullOrEmpty(personLiabilityInfo.UpdatedBy))
            {
                personLiabilityInfo.UpdatedBy = "Test";
            }

            using (var context = new CaresApplicationDBEntities())
            {
                bool hadAChange = false;

                // Perform deletes here
                var existingTests = context.PERSON_LIABILITY_TEST.Where(t => t.PERSON_ID == personLiabilityInfo.ContactPersonId);
                foreach (var row in existingTests)
                {
                    var hasMatch = personLiabilityInfo.LiabilityTests.Any(t => t.PersonLiabilityTestId == row.PERSON_LIABILITY_TEST_ID);
                    if (!hasMatch)
                    {
                        context.PERSON_LIABILITY_TEST.Remove(row);
                        hadAChange = true;
                    }
                }

                // Update?
                foreach (var lt in personLiabilityInfo.LiabilityTests)
                {
                    var liabilityTestRecord = context.PERSON_LIABILITY_TEST.FirstOrDefault(l => l.PERSON_LIABILITY_TEST_ID == lt.PersonLiabilityTestId);
                    if (liabilityTestRecord != null)
                    {
                        liabilityTestConverter(lt, liabilityTestRecord, username, DateTime.Now);
                        hadAChange = true;
                    }
                    else
                    {
                        // Don't add blank records
                        if (lt.EffectiveDate.HasValue || lt.Amount.HasValue || !string.IsNullOrEmpty(lt.Description) || lt.EndDate.HasValue)
                        {
                            // INSERT
                            PERSON_LIABILITY_TEST newTest = new PERSON_LIABILITY_TEST();
                            liabilityTestConverter(lt, newTest, username, DateTime.Now);
                            newTest.ORIGINATING_APPLICATION_ID = personLiabilityInfo.ApplicationId;
                            newTest.PERSON_ID = personLiabilityInfo.ContactPersonId;
                            newTest.CREATED_BY = username;
                            newTest.CREATED_DATE = DateTime.Now;
                            context.PERSON_LIABILITY_TEST.Add(newTest);
                            hadAChange = true;
                        }
                    }
                }

                if (hadAChange)
                {
                    context.SaveChanges();
                }

                return response;
            }
        }

        /// <summary>
        /// Copy method
        /// </summary>
        /// <param name="source"></param>
        /// <param name="dest"></param>
        /// <param name="updatedBy"></param>
        /// <param name="updatedDate"></param>
        private static void liabilityTestConverter(ElderlyDisabledLiabilityTestDto source, PERSON_LIABILITY_TEST dest, string updatedBy, DateTime updatedDate)
        {
            if (source != null && dest != null)
            {
                dest.ORIGINATING_APPLICATION_ID = source.OriginatingApplicationId;
                dest.PERSON_ID = source.PersonId;
                dest.LIABILITY_TEST_TYPE_ID = (byte)source.LiabilityTestTypeId;
                dest.AMOUNT = source.Amount ?? 0;
                dest.EFFECTIVE_DATE = source.EffectiveDate ?? DateTime.MinValue;
                dest.DESCRIPTION = source.Description;
                dest.END_DATE = source.EndDate;
                dest.UPDATED_BY = updatedBy;
                dest.UPDATED_DATE = updatedDate;
            }
        }

        /// <summary>
        /// Performs Upsert on a single liability segment
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public static ElderlyDisabledLiabilityDetailDto UpsertLiabilitySegment(ElderlyDisabledLiabilityDetailDto personLiabilityInfo, string username, Guid tokenId)
        {
            APPLICATION app = ApplicationDal.GetApplicationFull(personLiabilityInfo.ApplicationId);
            bool isTerminalStatus = ApplicationHelper.IsEnrollmentComplete(app?.APPLICATION_STATUS_ID ?? 0);
            ElderlyDisabledLiabilityDetailDto existingLiability = null;
            ElderlyDisabledLiabilityDetailDto response = null;

            // A bit of a sanity check, mostly needed for automated tests:
            if (string.IsNullOrEmpty(personLiabilityInfo.CreatedBy))
            {
                personLiabilityInfo.CreatedBy = personLiabilityInfo.UpdatedBy;
            }

            using (var context = new CaresApplicationDBEntities())
            {
                if (isTerminalStatus)
                {
                    var preEditLiabilitylist = getPersonLiabilityInfo(0, personLiabilityInfo.PersonLiabilityId, tokenId, context);
                    existingLiability = preEditLiabilitylist.FirstOrDefault();
                }

                string asJson = JsonConvert.SerializeObject(personLiabilityInfo);
                var result = context.usp_UPSERT_LIABILITY_SEGMENT(asJson).FirstOrDefault();

                if (!string.IsNullOrEmpty(result))
                {
                    // The JSON returns an array with a single ElderlyDisabledLiabilityDetailDto:
                    ElderlyDisabledLiabilityDetailDto[] resultAsObjects = JsonConvert.DeserializeObject<ElderlyDisabledLiabilityDetailDto[]>(result);

                    if (resultAsObjects.Length == 1)
                    {
                        response = resultAsObjects[0];
                        // Helpful for automated tests:
                        response.ApplicationId = personLiabilityInfo.ApplicationId;
                        response.ContactPersonId = personLiabilityInfo.ContactPersonId;
                    }
                    else
                    {
                        response = new ElderlyDisabledLiabilityDetailDto()
                        {
                            IsSuccessful = false,
                            ErrorMessage = "usp_UPSERT_LIABILITY_SEGMENT call failed.  No record was returned."
                        };
                    }
                }

                if (isTerminalStatus)
                {
                    // Determine if Liability was edited for a Terminal app
                    // Do so with a json string compare.  Doing so requires matching up these fields.  These may differ even with no actual change:
                    if (existingLiability != null)
                    {
                        existingLiability.UpdatedBy = personLiabilityInfo.UpdatedBy;
                        existingLiability.UpdatedDate = personLiabilityInfo.UpdatedDate;
                        existingLiability.PersonLiabilityId = personLiabilityInfo.PersonLiabilityId;
                        existingLiability.ContactPersonId = personLiabilityInfo.ContactPersonId;
                        existingLiability.PersonId = personLiabilityInfo.PersonId;
                        existingLiability.ApplicationId = personLiabilityInfo.ApplicationId;
                        existingLiability.ProgramName = personLiabilityInfo.ProgramName;
                    }
                    string preAsJson = JsonConvert.SerializeObject(existingLiability);
                    string postAsJson = JsonConvert.SerializeObject(personLiabilityInfo);
                    if (preAsJson != postAsJson)
                    {
                        PersonDal.ClearInsuranceSendIndicatorForPerson(app.CONTACT_PERSON_ID, username);
                    }
                }

                return response;
            }
        }

        /// <summary>
        ///  Delete liability segment
        /// </summary>
        /// <param name="personLiabilityId"></param>
        public static void DeleteLiabilitySegment(long personLiabilityId, string username)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_DELETE_LIABILITY_SEGMENT(personLiabilityId, username);
            }
        }

        /// <summary>
        /// Deletes ALL liability segments for an individual
        /// </summary>
        /// <param name="personId"></param>
        public static void DeleteLiabilitySegments(long personId, long originalAppId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_DELETE_LIABILITY_SEGMENTS(personId, originalAppId);
            }
        }

        /// <summary>
        /// Gets user's current E&E determinations, and MSP settings
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public static ElderlyDisabledEligibilityDeterminationsDto GetEligibilityDeterminations(long applicationId, long personId, Guid tokenId)
        {
            ElderlyDisabledEligibilityDeterminationsDto response = new ElderlyDisabledEligibilityDeterminationsDto();

            using (var context = new CaresApplicationDBEntities())
            {
                // Load all the determination rows:
                var appEDDeterminations = context.APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION.Where(a => a.APPLICATION_ID == applicationId);
                foreach (var appEDDetermination in appEDDeterminations)
                {
                    var newDet = new ElderlyDisabledEligibilityDeterminationDto();
                    SqlToDtoMapper.SqlToDto(appEDDetermination, newDet);
                    if (!string.IsNullOrEmpty(appEDDetermination.DENIAL_REASONS_JSON))
                    {
                        newDet.DenialReasons = JsonConvert.DeserializeObject<short[]>(appEDDetermination.DENIAL_REASONS_JSON).ToList();
                    }
                    response.Determinations.Add(newDet);
                }

                // Grab any previous MSP enrollments
                // First get the MSP App ID:
                long? mspApplicationId = context.APPLICATION_ELDERLY_DISABLED_DETAIL.FirstOrDefault(
                    a => a.APPLICATION_ID == applicationId)?.MSP_APPLICATION_ID;
                if (mspApplicationId != null)
                {
                    response.MspAwardedAppId = mspApplicationId.Value;
                    // VERY IMPORANT NOTE HERE:  Notice the .AsNoTracking()
                    // Without this, EF was taking the first non-nullable column and making that the PK
                    // The .AsNoTracking() call fixes that.
                    var mspElig = context.vw_ELIGIBILITY_ENROLLMENT.AsNoTracking().Where(e => e.APPLICATION_ID == mspApplicationId).ToList();
                    foreach (var elig in mspElig)
                    {
                        response.MspEnrolledSegments.Add(new ElderlyDisabledEligibilityDeterminationDto()
                        {
                            ApplicationId = elig.APPLICATION_ID,
                            ProgramId = elig.PROGRAM_ID,
                            ProgramSubCategoryId = elig.PROGRAM_SUB_CATEGORY_ID,
                            StartDate = elig.START_DATE,
                            CancelDate = elig.CANCEL_DATE,
                            OverrideReasonId = elig.OVERRIDE_REASON_ID,
                        });
                    }
                }
            }

            return response;
        }

        /// <summary>
        /// Gets the primary sponsor info.
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static RepresentativeInfoDto GetPrimarySponsorInfo(long personId, Guid tokenId)
        {
            // TODO: This returns null when there is no sponsor info.  And the caller returns IsSuccess = false, which logs it as an error.
            // We should try handle this more gracefully so that it doesn't return an Error, but just NotFound
            var first = getPrimarySponsorInfo(personId, tokenId).FirstOrDefault();
            if (first != null)
            {
                return first;
            }
            else
            {
                return new RepresentativeInfoDto()
                {
                    IsSuccessful = true,
                    CaresError = CaresError.NotFound
                };
            }
        }

        /// <summary>
        /// Gets the elderly disabled non-magi income info for Elig/Enroll screen.
        /// </summary>
        /// <param name="applicationId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static List<NonMagiIncome> GetElderlyDisabledNonMagiIncomeInfo(long applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var results = context.usp_SELECT_ELDERLY_DISABLED_NON_MAGI_INCOME(applicationId).ToList();

                List<NonMagiIncome> nonMagiIncomes = results.Select(i => new NonMagiIncome()
                {
                    IncomeRelationshipDesc = !string.IsNullOrEmpty(i.RELATIONSHIP_TYPE_DESC) ? i.RELATIONSHIP_TYPE_DESC : string.Empty,
                    IncomeTypeDesc = !string.IsNullOrEmpty(i.INCOME_DESC) ? i.INCOME_DESC : string.Empty,
                    GrossIncomeAmount = i.MONTHLY_TOTAL_GROSS_INCOME_AMOUNT ?? 0.00m,
                    NetIncomeAmount = i.MONTHLY_TOTAL_NET_INCOME_AMOUNT ?? 0.00m,
                    NotProvidedIncomeIndicator = i.NOT_PROVIDED_INCOME_INDICATOR,
                }).ToList();

                return nonMagiIncomes;
            }
        }

        /// <summary>
        /// Returns pure table-data Non-Magi Income for an app
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        public static List<APPLICATION_NON_MAGI_INCOME> GetNonMagiIncome(long applicationId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                List<APPLICATION_NON_MAGI_INCOME> income = context.APPLICATION_NON_MAGI_INCOME.Where(i => i.APPLICATION_ID == applicationId).ToList();
                return income;
            }
        }

        /// <summary>
        /// Checks enrollment overlaps for a specific person by comparing given start and end dates with existing enrollments in vw_ELIGIBILITY_ENROLLMENT.
        /// </summary>
        /// <param name="personId">The ID of the person whose enrollments are to be checked.</param>
        /// <param name="startDatesParam">A comma-separated string of start dates to check for overlaps.</param>
        /// <param name="endDatesParam">A comma-separated string of end dates to check for overlaps.</param>
        /// <returns>A list of overlapping enrollments, including the Application_ID, start and end dates of the new and existing enrollments.</returns>
        /// <remarks>
        /// The method leverages a stored procedure within the database to efficiently determine the overlaps. It is used to ensure that there are no conflicting enrollments for the specified person.
        /// The 'startDatesParam' and 'endDatesParam' should contain the same number of elements, and corresponding start and end dates should have the same index in the comma-separated strings.
        /// </remarks>
        public static List<usp_Select_Overlap_Enrollments_Result> CheckEnrollmentOverlaps(int personId, string startDatesParam, string endDatesParam)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                return context.usp_Select_Overlap_Enrollments(personId, startDatesParam, endDatesParam).ToList();
            }
        }
        #endregion

        #region Public save/update/upsert methods
        /// <summary>
        /// Gets the application information bar information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledApplicationInfoBarDto GetApplicationInformationBarInfo(long applicationId, Guid tokenId)
        {
            //For further information on TransactionScope Read the article: https://particular.net/blog/transactionscope-and-async-await-be-one-with-the-flow
            using (new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { Timeout = TransactionManager.MaximumTimeout, IsolationLevel = IsolationLevel.ReadUncommitted },
                TransactionScopeAsyncFlowOption.Enabled))
            {
                using (var context = new CaresApplicationDBEntities())
                {
                    return context.APPLICATIONs
                            .Join(context.sysnl_donotmodify__PERSON, a => a.CONTACT_PERSON_ID, p => p.PERSON_ID, (a, p) => new
                            {
                                a.APPLICATION_ID,
                                a.APPLICATION_STATUS_ID,
                                a.SUB_PROGRAM_CATEGORY_ID,
                                a.WORKER_NUMBER,
                                a.WORKER_COUNTY_NUMBER,
                                p.PERSON_ID,
                                p.FIRST_NAME,
                                p.MIDDLE_NAME,
                                p.LAST_NAME,
                                p.SUFFIX_ID
                            })
                            .Join(context.REF_APPLICATION_STATUS, a => a.APPLICATION_STATUS_ID, ras => ras.APPLICATION_STATUS_ID, (a, ras) => new
                            {
                                a.APPLICATION_ID,
                                a.APPLICATION_STATUS_ID,
                                a.WORKER_NUMBER,
                                a.WORKER_COUNTY_NUMBER,
                                ras.APPLICATION_STATUS_DESC,
                                a.SUB_PROGRAM_CATEGORY_ID,
                                a.PERSON_ID,
                                a.FIRST_NAME,
                                a.MIDDLE_NAME,
                                a.LAST_NAME,
                                a.SUFFIX_ID
                            })
                            .GroupJoin(context.APPLICATION_ELDERLY_DISABLED_DETAIL, a => a.APPLICATION_ID, aedd => aedd.APPLICATION_ID, (a, aedd) => new
                            {
                                a.APPLICATION_ID,
                                a.APPLICATION_STATUS_ID,
                                a.WORKER_NUMBER,
                                a.WORKER_COUNTY_NUMBER,
                                a.APPLICATION_STATUS_DESC,
                                a.SUB_PROGRAM_CATEGORY_ID,
                                ELDERLY_DISABLED_PROGRAM_ID = aedd.Select(s => s.ELDERLY_DISABLED_PROGRAM_ID).FirstOrDefault(),
                                a.PERSON_ID,
                                a.FIRST_NAME,
                                a.MIDDLE_NAME,
                                a.LAST_NAME,
                                a.SUFFIX_ID
                            })
                            .GroupJoin(context.REF_DISTRICT_OFFICE, a => a.WORKER_COUNTY_NUMBER, d => d.DO_CODE, (a, d) => new
                            {
                                a.APPLICATION_ID,
                                a.APPLICATION_STATUS_ID,
                                a.APPLICATION_STATUS_DESC,
                                a.WORKER_NUMBER,
                                DISTRICT_OFFICE_ID = d.Select(s => s.DISTRICT_OFFICE_ID).FirstOrDefault(),
                                a.SUB_PROGRAM_CATEGORY_ID,
                                a.ELDERLY_DISABLED_PROGRAM_ID,
                                a.PERSON_ID,
                                a.FIRST_NAME,
                                a.MIDDLE_NAME,
                                a.LAST_NAME,
                                a.SUFFIX_ID
                            })
                            .GroupJoin(context.REF_SUFFIX, a => a.SUFFIX_ID, rs => rs.SUFFIX_ID, (a, rs) => new
                            {
                                a.APPLICATION_ID,
                                a.APPLICATION_STATUS_ID,
                                a.APPLICATION_STATUS_DESC,
                                a.WORKER_NUMBER,
                                a.DISTRICT_OFFICE_ID,
                                a.SUB_PROGRAM_CATEGORY_ID,
                                a.ELDERLY_DISABLED_PROGRAM_ID,
                                a.PERSON_ID,
                                a.FIRST_NAME,
                                a.MIDDLE_NAME,
                                a.LAST_NAME,
                                SUFFIX_NAME = rs.Select(s => s.SUFFIX_NAME).FirstOrDefault()
                            })
                            .Where(a => a.APPLICATION_ID == applicationId)
                            .Select(a => new ElderlyDisabledApplicationInfoBarDto
                            {
                                ApplicationId = a.APPLICATION_ID,
                                ApplicationStatusId = a.APPLICATION_STATUS_ID ?? 0,
                                ApplicationStatus = a.APPLICATION_STATUS_DESC,
                                SubProgramCatogory = (Api.Infrastructure.Constants.SubProgramCategories)a.SUB_PROGRAM_CATEGORY_ID,
                                ContactPersonId = a.PERSON_ID,
                                FirstName = a.FIRST_NAME,
                                MiddleName = a.MIDDLE_NAME,
                                LastName = a.LAST_NAME,
                                Suffix = a.SUFFIX_NAME,
                                DistrictOfficeId = a.DISTRICT_OFFICE_ID,
                                WorkerNumber = a.WORKER_NUMBER,
                                ElderlyDisabledProgramId = a.ELDERLY_DISABLED_PROGRAM_ID
                            })
                            .FirstOrDefault();
                }
            }
        }

        /// <summary>
        /// Saves the application.
        /// </summary>
        /// <param name="application">The application.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static BaseApiResponse SaveApplication(ElderlyDisabledApplicationDto application, string username, Guid tokenId)
        {
            BaseApiResponse returnValue = new BaseApiResponse();
            List<NotesDal.InsertNotesEntry> notesList = new List<NotesDal.InsertNotesEntry>();
            var representativeDAL = new RepresentativeDAL();
            bool isNewApp = application.Application.ApplicationId == 0;
            bool isTerminalStatus = ApplicationHelper.IsEnrollmentComplete(application.ApplicationStatusId);
            string applicantsFullName = PersonHelper.GetCombinedName(
                application.Person.FirstName, application.Person.MiddleName, application.Person.LastName, application.Person.SuffixId);
            bool shouldTriggerMmisSend = false;

            // Ensure ALL sub objects have the UpdatedBy and UpdatedDate
            application.CascadeSetAllUpdatedBy();

            // Perform Data Integrity Check
            performDataIntegrityCheckApplication(application);

            // Is new app?
            if (isNewApp)
            {
                string email = string.Empty; // Not sure what to put here yet
                // Find or create the person
                long newPersonId;
                CaresError result = FindOrCreateNewElderlyDisabledPerson(application.Person, application.PersonPhones,
                    application.PersonAddresses, email, username, tokenId, out newPersonId, notesList);

                if (result == CaresError.None)
                {
                    // Store that new ID:
                    application.Application.ContactPersonId = newPersonId;
                    application.ApplicationDetail.PersonId = newPersonId;
                    application.PersonDetail.PersonId = newPersonId;
                    application.PersonAddresses.Addresses.ForEach(ad => ad.PersonId = newPersonId);
                    application.PersonPhones.ForEach(p => p.PersonId = newPersonId);
                    application.PersonRaces.PersonId = newPersonId;
                    application.PersonContactPreference.PersonId = newPersonId;
                }
                else
                {
                    // Don't continue
                    return new BaseApiResponse()
                    {
                        IsSuccessful = false,
                        CaresError = result
                    };
                }
            }

            //   Ensure the Is Applying is true for the applicant
            if (!application.ApplicationDetail.IsApplying)
            {
                application.ApplicationDetail.IsApplying = true;
            }

            //   Ensure the Is Contact is true for the applicant
            if (!application.ApplicationDetail.IsContact)
            {
                application.ApplicationDetail.IsContact = true;
            }

            // Before updating the App, determine if the spouse will need to be deleted
            byte? currentMaritalStatusId = ElderlyDisabledDal.currentMaritalStatus(application.ApplicationId) ?? (byte)ENUMS.enumMaritalStatus.Unknown;
            bool spouseNeedsDeleting = ElderlyDisabledDal.spouseNeedsDeleting(application, currentMaritalStatusId);
            bool formerSpousesNeedDeleting = ElderlyDisabledDal.formerSpousesNeedDeleting(application, currentMaritalStatusId);
            // Upsert the app
            application.Application.ApplicationId = ApplicationDal.UpsertApplication(application.Application, notesList, username);
            ApplicationDal.UpdateApplicationReceivedDate(application.Application.ApplicationId, application.Application.ReceivedDate);

            // Do some spouse processing if this is NOT a new application:
            if (!isNewApp)
            {
                // Delete spouse?
                if (spouseNeedsDeleting)
                {
                    DeleteCurrentSpouse(application.ApplicationId, username);
                }
                // Delete former spouse?
                if (formerSpousesNeedDeleting)
                {
                    DeleteFormerSpouses(application.ApplicationId, username);
                }
                // Create a note?
                if (!isTerminalStatus && currentMaritalStatusId != application.Application.ContactPersonMaritalStatusId)
                {
                    ENUMS.enumMaritalStatus currentMaritalStatus = (ENUMS.enumMaritalStatus)currentMaritalStatusId;
                    ENUMS.enumMaritalStatus newMaritalStatus = (ENUMS.enumMaritalStatus)application.Application.ContactPersonMaritalStatusId;
                    string message;
                    // switching FROM single
                    if (currentMaritalStatus == ENUMS.enumMaritalStatus.Single)
                    {
                        message = NotesDescriptions.AddMaritalStatusNote(newMaritalStatus.ToString(), null);
                    }
                    else
                    {
                        message = NotesDescriptions.ChangeMaritalStatusNote(newMaritalStatus.ToString(), application.ApplicationElderlyDisabledDetail.MaritalStatusDate, null);
                    }
                    notesList.Add(new NotesDal.InsertNotesEntry()
                    {
                        NotesDesc = message
                    });
                }
            }

            if (isNewApp)
            {
                // If new app, now that we have an app ID, add this person to the household (for address updates)
                addPersonToHoushold(application.Application.ApplicationId, application.Application.ContactPersonId, username);

                // Generating the Medicaid Id while creating new application
                GenerateMedicaidId(application.Application.ContactPersonId, username, tokenId);
            }

            // Application Details:
            application.ApplicationDetail.ApplicationId = application.Application.ApplicationId;
            // The relationship on this appDetail MUST be self.  Force it:
            application.ApplicationDetail.RelAppFilerId = (int)ENUMS.enumRelationship.Self;
            ApplicationDal.UpsertApplicationDetail(application.ApplicationDetail, tokenId);

            // ApplicationElderlyDisabledDetail:
            application.ApplicationElderlyDisabledDetail.ApplicationId = application.Application.ApplicationId;
            UpsertApplicationElderlyDisabledDetail(application.ApplicationElderlyDisabledDetail, isTerminalStatus, application.PersonDetail.PersonId, username, tokenId);

            // Residency Info:
            application.ApplicationResidencyInformation.ApplicationId = application.Application.ApplicationId;
            ApplicationDal.UpsertApplicationResidencyInformation(application.ApplicationResidencyInformation, tokenId);

            // Living Arrangement:
            application.ApplicationLivingArrangement.ApplicationId = application.Application.ApplicationId;
            ApplicationDal.UpsertApplicationLivingArrangement(application.ApplicationLivingArrangement, notesList, isTerminalStatus, tokenId);

            // Update person
            UpdateElderlyDisabledPerson(application.Person, notesList, ref shouldTriggerMmisSend, tokenId);

            // Person Details:
            UpsertElderlyDisabledPersonDetail(application.Application.ApplicationId, application.PersonDetail, ref shouldTriggerMmisSend, isTerminalStatus, notesList,
                application.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId, username, tokenId);

            // Person Race:
            PersonDal.UpsertPersonRaces(application.Application.ContactPersonId, application.PersonRaces, tokenId);

            // NOTE: Upserts on Address are done in the BLL that call this function

            // Phones:
            PersonDal.UpsertPersonPhones(application.Application.ContactPersonId, application.PersonPhones, tokenId);

            // Person Contact Preference:
            PersonDal.UpsertPersonContactPreference(application.PersonContactPreference, tokenId);

            // Update Representative Information
            representativeDAL.UpdateSelfSponsorInfo(application, tokenId, username);

            // Insert the notes.
            prepAndSendNotes(application.Application.ApplicationId, application.Application.ContactPersonId,
                username, notesList, tokenId, applicantsFullName);

            // Update MMIS?
            if (isTerminalStatus && shouldTriggerMmisSend)
            {
                PersonDal.ClearInsuranceSendIndicatorForPerson(application.Application.ContactPersonId, username);
            }

            returnValue.Id = application.Application.ApplicationId;
            return returnValue;
        }

        /// <summary>
        /// Preps notes list and saves them
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="notesList">The note list.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <param name="applicantsFullName">The applicants full name.</param>
        private static void prepAndSendNotes(long applicationId, long personId, string username,
            List<NotesDal.InsertNotesEntry> notesList, Guid tokenId, string applicantsFullName = null)
        {
            // Loop through and finish the notes entries
            foreach (var note in notesList)
            {
                note.ApplicationId = applicationId;
                note.PersonId = personId;
                note.NotesRefId = note.NotesRefId ?? (byte)ENUMS.enumNoteType.GENERAL_NOTE;
                // Insert the full name if needed:
                if (!string.IsNullOrEmpty(applicantsFullName))
                {
                    note.NotesDesc = note.NotesDesc.Replace(NotesDescriptions.Replacements.FullName, applicantsFullName);
                }
                // Insert the worker name if needed:
                note.NotesDesc = note.NotesDesc.Replace(NotesDescriptions.Replacements.WorkerName, username);
                note.CreatedBy = username;
                // Replace [applicationId], in case a note was added prior to the app being created
                note.NotesDesc = note.NotesDesc.Replace(NotesDescriptions.Replacements.ApplicationId, applicationId.ToString());
            }
            NotesDal.InsertNotesEntries(notesList, tokenId);
        }

        /// <summary>
        /// Given an appId, returns the current marital status
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        private static byte? currentMaritalStatus(long applicationId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var currentMaritalStatus = (from a in context.APPLICATIONs
                                            where a.APPLICATION_ID == applicationId
                                            select a.CONTACT_PERSON_MARITAL_STATUS_ID).FirstOrDefault();
                return currentMaritalStatus ?? (byte)ENUMS.enumMaritalStatus.Unknown;
            }
        }

        /// <summary>
        /// Given an app, determines if the marital status is going from Married or Separated to neither.
        /// </summary>
        /// <param name="application">The application DTO.</param>
        /// <param name="currentMaritalStatus">The current marital status.</param>
        /// <returns></returns>
        private static bool spouseNeedsDeleting(ElderlyDisabledApplicationDto application, byte? currentMaritalStatus)
        {
            bool spouseNeedsDeleting = false;
            if (currentMaritalStatus != null)
            {
                bool currentlyMarried = ((currentMaritalStatus.Value) == (int)ENUMS.enumMaritalStatus.Married ||
                    (currentMaritalStatus.Value) == (int)ENUMS.enumMaritalStatus.Separated);
                spouseNeedsDeleting = currentlyMarried &&
                    application.Application.ContactPersonMaritalStatusId != (int)ENUMS.enumMaritalStatus.Married &&
                    application.Application.ContactPersonMaritalStatusId != (int)ENUMS.enumMaritalStatus.Separated;
            }
            return spouseNeedsDeleting;
        }

        /// <summary>
        /// Given an app, determines if former spouses need to be deleted
        /// </summary>
        /// <param name="application">The application DTO.</param>
        /// <param name="currentMaritalStatus">The current marital status.</param>
        /// <returns></returns>
        private static bool formerSpousesNeedDeleting(ElderlyDisabledApplicationDto application, byte? currentMaritalStatus)
        {
            bool formerSpousesNeedDeleting = false;
            if (currentMaritalStatus != null)
            {
                // Switching from non-single to single?
                bool previouslyNotSingle = ((currentMaritalStatus.Value) != (int)ENUMS.enumMaritalStatus.Single &&
                    (currentMaritalStatus.Value) != (int)ENUMS.enumMaritalStatus.Unknown);
                formerSpousesNeedDeleting = previouslyNotSingle &&
                    application.Application.ContactPersonMaritalStatusId == (int)ENUMS.enumMaritalStatus.Single;
            }
            return formerSpousesNeedDeleting;
        }

        /// <summary>
        /// Adds a person to the household.
        /// Currently this is ONLY used to ensure address updates to the primary also update the spouse.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The username.</param>
        private static void addPersonToHoushold(long applicationId, long personId, string username)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var hm = context.APPLICATION_HOUSEHOLD.Create();
                hm.APPLICATION_ID = applicationId;
                hm.PERSON_ID = personId;
                hm.IS_ACTIVE = "Y";
                hm.CREATED_BY = username;
                hm.CREATED_DATE = DateTime.Now;
                hm.UPDATED_BY = username;
                hm.UPDATED_DATE = DateTime.Now;
                context.APPLICATION_HOUSEHOLD.Add(hm);
                context.SaveChanges();
            }
        }

        /// <summary>
        /// Upsert Application Elderly Disabled Detail
        /// </summary>
        /// <param name="appElderlyDisabledDetailDto">The application elderly disabled detail.</param>
        /// <param name="isTerminalStatus">Is terminal status.</param>
        /// <param name="personId">PesonId, for possibly deleting Liability segments</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static long UpsertApplicationElderlyDisabledDetail(ApplicationElderlyDisabledDetail appElderlyDisabledDetailDto,
            bool isTerminalStatus, long personId, string username, Guid tokenId)
        {
            long appElderlyDisabledDetailId = 0;
            bool switchingFromNursingHome = false;

            using (var context = new CaresApplicationDBEntities())
            {
                APPLICATION_ELDERLY_DISABLED_DETAIL applicationElderlyDisabledDetail = context.APPLICATION_ELDERLY_DISABLED_DETAIL.
                        FirstOrDefault(a => a.APPLICATION_ELDERLY_DISABLED_DETAIL_ID == appElderlyDisabledDetailDto.ApplicationElderlyDisabledDetailId);

                // If not found this is an insert:
                if (applicationElderlyDisabledDetail == null)
                {
                    applicationElderlyDisabledDetail = new APPLICATION_ELDERLY_DISABLED_DETAIL
                    {
                        APPLICATION_ELDERLY_DISABLED_DETAIL_ID = 0,
                        CREATED_BY = appElderlyDisabledDetailDto.UpdatedBy,
                        CREATED_DATE = appElderlyDisabledDetailDto.UpdatedDate
                    };
                    context.APPLICATION_ELDERLY_DISABLED_DETAIL.Add(applicationElderlyDisabledDetail);
                }
                else
                {
                    // If current program was NH related && New program id is not NH related then "switchingFromNursingHome" = true;
                    switchingFromNursingHome = !isTerminalStatus &&
                        ENUMS.ElderlyDisabledProgram.IsNursingHomeRelatedProgram(applicationElderlyDisabledDetail.ELDERLY_DISABLED_PROGRAM_ID) &&
                        !ENUMS.ElderlyDisabledProgram.IsNursingHomeRelatedProgram(appElderlyDisabledDetailDto.ElderlyDisabledProgramId);
                }

                // Copy in ONLY data from the DTO.
                // This will leave existing column values
                SqlToDtoMapper.DtoToSql(appElderlyDisabledDetailDto, applicationElderlyDisabledDetail);

                applicationElderlyDisabledDetail.EXPEDITE_FACILITY_PROVIDER_ID = appElderlyDisabledDetailDto.HospitalId ?? appElderlyDisabledDetailDto.NursingHomeId;

                context.SaveChanges();
                appElderlyDisabledDetailId = applicationElderlyDisabledDetail.APPLICATION_ELDERLY_DISABLED_DETAIL_ID;

                // Delete Liability data?
                if (switchingFromNursingHome)
                {
                    DeleteLiabilitySegments(personId, appElderlyDisabledDetailDto.ApplicationId);
                }
            }

            return appElderlyDisabledDetailId;
        }

        /// <summary>
        /// Update on an existing elderly disabled person.
        /// Returns null if person found
        /// </summary>
        /// <param name="elderlyDisabledPerson">The elderly disabled person.</param>
        /// <param name="notesList">The notes list.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static string UpdateElderlyDisabledPerson(ElderlyDisabledPerson elderlyDisabledPerson,
            List<NotesDal.InsertNotesEntry> notesList, ref bool dataChangeRequiringMmisUpdate, Guid tokenId)
        {
            string errorMessage = null;
            string prevFullName = null;
            string newFullName = PersonHelper.GetCombinedName(
                elderlyDisabledPerson.FirstName, elderlyDisabledPerson.MiddleName, elderlyDisabledPerson.LastName, elderlyDisabledPerson.SuffixId);
            string prevDoB = null;
            string newDoB = elderlyDisabledPerson.DateOfBirth.HasValue ? elderlyDisabledPerson.DateOfBirth.Value.ToString("MM/dd/yyyy") : null;
            string prevSsn = null;
            byte? prevGender = null;
            byte? newGender = elderlyDisabledPerson.GenderId;
            string newSsn = elderlyDisabledPerson.Ssn;

            sysnl_donotmodify__PERSON person = new sysnl_donotmodify__PERSON();
            SqlToDtoMapper.DtoToSql(elderlyDisabledPerson, person);

            using (var context = new CaresApplicationDBEntities())
            {
                sysnl_donotmodify__PERSON existingPerson = context.sysnl_donotmodify__PERSON.FirstOrDefault(p => p.PERSON_ID == person.PERSON_ID);

                if (existingPerson != null)
                {
                    // Get the previous full name & DOB
                    prevFullName = PersonHelper.GetCombinedName(existingPerson.FIRST_NAME, existingPerson.MIDDLE_NAME,
                        existingPerson.LAST_NAME, existingPerson.SUFFIX_ID);
                    prevDoB = existingPerson.DOB.HasValue ? existingPerson.DOB.Value.ToString("MM/dd/yyyy") : null;
                    prevSsn = existingPerson.SSN;
                    prevGender = existingPerson.GENDER_ID;

                    // retain these values:
                    person.CREATED_BY = existingPerson.CREATED_BY;
                    person.CREATED_DATE = existingPerson.CREATED_DATE;
                    context.Entry(existingPerson).CurrentValues.SetValues(person);
                    context.SaveChanges();
                }
                else
                {
                    errorMessage = Constants.NotFound;
                }
            }

            // Do we need to add Notes?
            if (!string.IsNullOrEmpty(prevFullName) && prevFullName != newFullName)
            {
                dataChangeRequiringMmisUpdate = true;
                notesList.Add(new NotesDal.InsertNotesEntry()
                {
                    NotesDesc = NotesDescriptions.NameNote(prevFullName, newFullName)
                });
            }
            if (!string.IsNullOrEmpty(prevDoB) && prevDoB != newDoB)
            {
                dataChangeRequiringMmisUpdate = true;
                notesList.Add(new NotesDal.InsertNotesEntry()
                {
                    NotesDesc = NotesDescriptions.DateOfBirthNote(newFullName, prevDoB, newDoB)
                });
            }
            if (!string.IsNullOrEmpty(prevSsn) && prevSsn != newSsn)
            {
                dataChangeRequiringMmisUpdate = true;
                notesList.Add(new NotesDal.InsertNotesEntry()
                {
                    NotesDesc = NotesDescriptions.SsnNote(newFullName, prevSsn, newSsn)
                });
            }
            if (prevGender != newGender)
            {
                dataChangeRequiringMmisUpdate = true;
            }

            return errorMessage;
        }

        /// <summary>
        /// Upserts the elderly disabled person detail.
        /// </summary>
        /// <param name="appId">The application identifier.</param>
        /// <param name="elderlyDisabledPersonDetail">The elderly disabled person detail.</param>
        /// <param name="notesList">The note list.</param>
        /// <param name="elderlyDisabledProgramId">The elderly disabled program id.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static long UpsertElderlyDisabledPersonDetail(long appId, ElderlyDisabledPersonDetail elderlyDisabledPersonDetail,
            ref bool dataChangeRequiringMmisUpdate, bool isTerminalStatus,
            List<NotesDal.InsertNotesEntry> notesList, byte? elderlyDisabledProgramId, string username, Guid tokenId)
        {
            DateTime? previousDateOfDeath = null;
            long personDetailId = 0;

            using (var context = new CaresApplicationDBEntities())
            {
                t_PERSON_DETAIL existingPersonDetail = context.t_PERSON_DETAIL.FirstOrDefault(p => p.PERSON_DETAIL_ID == elderlyDisabledPersonDetail.PersonDetailId
                                                                || p.PERSON_ID == elderlyDisabledPersonDetail.PersonId);
                previousDateOfDeath = existingPersonDetail?.DATE_OF_DEATH;

                //  Does not exist, insert, then set that as the existing.
                if (existingPersonDetail == null)
                {
                    existingPersonDetail = insertElderlyDisabledPersonDetail(context, elderlyDisabledPersonDetail.PersonId, elderlyDisabledPersonDetail.UpdatedBy);
                }

                // Before copying from the DTO to the EF object, update the DTO with the PERSON_DETAIL_ID, so that the PERSON_DETAIL_ID does not get cleared
                // This is needed on two accounts:
                //    1) This was an insert and it didn't have the PERSON_DETAIL_ID,
                //    2) the object passed in had the personId but not the PERSON_DETAIL_ID
                elderlyDisabledPersonDetail.PersonDetailId = existingPersonDetail.PERSON_DETAIL_ID;

                // Copy in only the values in elderlyDisabledPersonDetail and save
                SqlToDtoMapper.DtoToSql(elderlyDisabledPersonDetail, existingPersonDetail);
                context.SaveChanges();

                personDetailId = elderlyDisabledPersonDetail.PersonDetailId;

                // Insert Death related notes if DateOfDeath changed (including first added)
                if (previousDateOfDeath != elderlyDisabledPersonDetail.DateOfDeath && elderlyDisabledPersonDetail.DateOfDeath != default(DateTime))
                {
                    // Truncate the liability if DOD has value.
                    if (elderlyDisabledPersonDetail.DateOfDeath.HasValue)
                    {
                        TruncateLiabilitySegments(elderlyDisabledPersonDetail.DateOfDeath.Value, elderlyDisabledPersonDetail.PersonId, username, tokenId);

                        //Truncating Enrollment because of deceased reason.
                        EnrollmentDal.TruncateElderlyAndMSPAppForDeceased(appId, elderlyDisabledPersonDetail.PersonId, elderlyDisabledPersonDetail.DateOfDeath.Value, context, username, tokenId);
                    }

                    string message;
                    // Removed?
                    if (elderlyDisabledPersonDetail.DateOfDeath == null)
                    {
                        message = NotesDescriptions.Messages.DeathRemovedNote;
                    }
                    else
                    {
                        message = NotesDescriptions.DeathIndicatedNote(null, elderlyDisabledPersonDetail.DateOfDeath.Value);
                    }
                    notesList.Add(new NotesDal.InsertNotesEntry()
                    {
                        NotesDesc = message
                    });

                    if (isTerminalStatus)
                    {
                        dataChangeRequiringMmisUpdate = true;
                    }
                }
            }

            return personDetailId;
        }

        /// <summary>
        /// Performs a simple insert to t_PERSON_DETAIL (only personId)
        /// </summary>
        /// <param name="context">Because this is called from code that already has a context.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="updatedby">The updated by.</param>
        /// <returns></returns>
        private static t_PERSON_DETAIL insertElderlyDisabledPersonDetail(CaresApplicationDBEntities context, long personId, string updatedby)
        {
            var existingPersonDetail = new t_PERSON_DETAIL()
            {
                PERSON_DETAIL_ID = 0,   //  Set the PK field is to 0, it will be set by the insert
                PERSON_ID = personId,
                UPDATED_BY = updatedby,
                UPDATED_DATE = DateTime.Now,
                CREATED_BY = updatedby,
                CREATED_DATE = DateTime.Now,
                IS_ACTIVE = "Y",
            };
            context.t_PERSON_DETAIL.Add(existingPersonDetail);
            context.SaveChanges();
            return existingPersonDetail;
        }

        /// <summary>
        /// Performs an upsert on SpouseInfo
        /// </summary>
        /// <param name="spouseInfo">The spouse information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static BaseApiResponse UpsertElderlyDisabledSpouseInfo(ElderlyDisabledSpouseDto spouseInfo, bool isTerminalStatus, string username, Guid tokenId)
        {
            List<NotesDal.InsertNotesEntry> notesList = new List<NotesDal.InsertNotesEntry>();
            BaseApiResponse response = new BaseApiResponse();
            bool dataChangeRequiringMmisUpdate = false;
            bool skipPhoneUpdate = false;

            // Special conversion: A null DOB needs to be set at 01/01/1900
            if (spouseInfo.Spouse.DateOfBirth == null)
            {
                spouseInfo.Spouse.DateOfBirth = Constants.EmptyDate;
            }

            // Ensure ALL sub objects have the UpdatedBy and UpdatedDate
            spouseInfo.CascadeSetAllUpdatedBy();
            long spousePersonId = 0;

            // Did the user request to delete spouse data?
            if (spouseInfo.DeleteSpouseData)
            {
                dataChangeRequiringMmisUpdate = true;
                DeleteCurrentSpouse(spouseInfo.ApplicationId, username);
            }
            // Else, if Marital Status is Married or Separated, update current spouse info
            else if ((spouseInfo.ContactPersonMaritalStatusId ?? 0) == (int)ENUMS.enumMaritalStatus.Married ||
                (spouseInfo.ContactPersonMaritalStatusId ?? 0) == (int)ENUMS.enumMaritalStatus.Separated)
            {
                List<Api.Messages.Person.PhoneAndPersonPhone> phones = new List<PhoneAndPersonPhone>();
                if (spouseInfo?.SpousePhone != null)
                {
                    phones.Add(spouseInfo.SpousePhone);
                }
                PersonAddresses addresses = new PersonAddresses();
                if (spouseInfo.SpouseAddress != null)
                {
                    addresses.Addresses.Add(spouseInfo.SpouseAddress.PersonAddress);
                }

                // Is this a new person?
                if (spouseInfo.Spouse.PersonId == 0)
                {
                    dataChangeRequiringMmisUpdate = true;
                    // Find or create the person
                    CaresError result = FindOrCreateNewElderlyDisabledPerson(spouseInfo.Spouse, phones, addresses,
                        spouseInfo.SpouseContactPreference.ContactEmail, username, tokenId, out spousePersonId, notesList);

                    if (result == CaresError.DrasticAlert)
                    {
                        return new BaseApiResponse
                        {
                            IsSuccessful = false,
                            CaresError = result
                        };
                    }

                    if (result == CaresError.None)
                    {
                        // Pass along this personId
                        spouseInfo.SpouseDetail.PersonId = spousePersonId;
                        spouseInfo.SpouseAddress.PersonAddress.PersonId = spousePersonId;
                        spouseInfo.SpousePhone.PersonId = spousePersonId;

                        // Some other bookkeeping
                        using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
                        {
                            // Store this Spouse Id in the primary's APPLICATION_HOUSEHOLD
                            var contactAH = context.APPLICATION_HOUSEHOLD.FirstOrDefault(ah => ah.APPLICATION_ID == spouseInfo.ApplicationId
                                && ah.PERSON_ID == spouseInfo.ContactPersonId);
                            if (contactAH != null)
                            {
                                contactAH.SPOUSE_ID = spousePersonId;
                                context.SaveChanges();
                            }
                            else
                            {
                                response.IsSuccessful = false;
                                response.CaresError = CaresError.Unknown;
                                response.ErrorMessage = $"Could not find spouse's primary contact.";
                                _log.Info($"In UpsertElderlyDisabledSpouseInfo, no APPLICATION_HOUSEHOLD exists for appId {spouseInfo.ApplicationId}", tokenId);
                            }
                        }

                        // Get this person's contact info, so that we don't override those with nulls,
                        // unless the user actually gave those values
                        var existingContactPref = PersonDal.GetPersonContactPreference(spousePersonId, false);
                        if (existingContactPref != null && spouseInfo.SpouseContactPreference != null)
                        {
                            if (string.IsNullOrEmpty(spouseInfo.SpouseContactPreference.ContactEmail))
                            {
                                spouseInfo.SpouseContactPreference.ContactEmail = existingContactPref.ContactEmail;
                                spouseInfo.SpouseContactPreference.ContactEmailId = existingContactPref.ContactEmailId;
                            }
                            if (spouseInfo.SpouseContactPreference.ContactPhoneId != null)
                            {
                                spouseInfo.SpouseContactPreference.ContactPhoneId = existingContactPref.ContactPhoneId;
                            }
                        }

                        // If the user did not provide a different phone, skip the phone update below
                        // Note that we will only check this inside THIS code block, during a NEW spouse
                        if (string.IsNullOrEmpty(spouseInfo.SpousePhone?.PhoneNo))
                        {
                            skipPhoneUpdate = true;
                        }

                        // Add this person to the household.
                        // Currently these are ONLY needed to ensure address updates also update the spouse address.
                        // The address process looks are the household members.
                        addPersonToHoushold(spouseInfo.ApplicationId, spousePersonId, username);

                        // Add spouse info into application_detail, needed for MMIS
                        if (spouseInfo.SpouseDetail.PersonId > 0)
                        {
                            long? appDetailId = InsertSpouseIntoApplicationDetail(spouseInfo.ApplicationId, username);
                            if (appDetailId == null)
                            {
                                response.IsSuccessful = false;
                                response.CaresError = CaresError.Unknown;
                                response.ErrorMessage = $"Could not insert spouse info into application detail";
                                _log.Info($"In UpsertElderlyDisabledSpouseInfo, Could not insert spouse info into application detail for appId {spouseInfo.ApplicationId}", tokenId);
                            }
                        }
                    }
                    else
                    {
                        response.CaresError = result;
                        response.IsSuccessful = false;
                    }
                }
                else
                {
                    spousePersonId = spouseInfo.Spouse.PersonId;
                    // Just update the spouse
                    string result = UpdateElderlyDisabledPerson(spouseInfo.Spouse, notesList, ref dataChangeRequiringMmisUpdate, tokenId);
                    if (!string.IsNullOrEmpty(result))
                    {
                        response.IsSuccessful = false;
                        response.CaresError = CaresError.Unknown;
                        spousePersonId = -1;
                        _log.Info($"In UpsertElderlyDisabledSpouseInfo, spouseId={spousePersonId}: {result}", tokenId);
                    }
                }

                // Can we continue
                if (spousePersonId > 0)
                {
                    // NOTE: Currently the Spouse screen does not include editing of PersonDetail information (such as Date of Death).
                    //       Which is why the following Upsert is commented out (yet left here to make that clear)
                    //UpsertElderlyDisabledPersonDetail(spouseInfo.SpouseDetail, tokenId);

                    if (!skipPhoneUpdate)
                    {
                        PersonDal.UpsertPersonPhones(spousePersonId, phones, tokenId);
                    }

                    // Upsert Spouse Contact Preference
                    PersonContactPreference personContactPreference = new PersonContactPreference()
                    {
                        ContactPreId = spouseInfo.SpouseContactPreference.ContactPreId,
                        PersonId = spouseInfo.Spouse.PersonId,
                        SpokenLanguageId = spouseInfo.SpouseContactPreference.SpokenLanguageId,
                        WrittenLanguageId = spouseInfo.SpouseContactPreference.WrittenLanguageId,
                        OtherSpokenLanguage = spouseInfo.SpouseContactPreference.OtherSpokenLanguage,
                        ContactPref = spouseInfo.SpouseContactPreference.ContactPref,
                        ContactPhoneId = spouseInfo.SpouseContactPreference.ContactPhoneId,
                        ContactEmailId = spouseInfo.SpouseContactPreference.ContactEmailId,
                        ContactEmail = spouseInfo.SpouseContactPreference.ContactEmail,
                        Pin = spouseInfo.SpouseContactPreference.Pin,
                        PinCreatedDate = spouseInfo.SpouseContactPreference.PinCreatedDate,
                        UpdatedBy = spouseInfo.Spouse.UpdatedBy,
                        UpdatedDate = spouseInfo.Spouse.UpdatedDate
                    };
                    PersonDal.UpsertPersonContactPreference(personContactPreference, tokenId);
                }
            }

            response.Id = spousePersonId;

            // Update MMIS?
            if (isTerminalStatus && dataChangeRequiringMmisUpdate)
            {
                PersonDal.ClearInsuranceSendIndicatorForPerson(spouseInfo.ContactPersonId, username);
            }

            if (response.CaresError == CaresError.None)
            {
                // Former Spouses
                if (!UpsertFormerSpouses(spouseInfo, tokenId))
                {
                    response.IsSuccessful = false;
                    response.CaresError = CaresError.Unknown;
                }
                else
                {
                    response.IsSuccessful = true;
                }
            }

            // Send notes
            prepAndSendNotes(spouseInfo.ApplicationId, spousePersonId, username, notesList, tokenId);

            return response;
        }

        /// <summary>
        /// Inserts an application's spouse info into application_detail
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="updatedBy">The updated by.</param>
        /// <returns></returns>
        public static long? InsertSpouseIntoApplicationDetail(long applicationId, string updatedBy)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var appDetailId = new ObjectParameter("appDetailId", typeof(long));
                var result = context.usp_INSERT_ELDERLY_DISABLED_SPOUSE_INTO_APPLICATION_DETAIL(applicationId, updatedBy, appDetailId);
                return appDetailId.Value is long ? (long)appDetailId.Value : (long?)null;
            }
        }

        /// <summary>
        /// Deletes an application's spouse
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="updatedBy">The updated by.</param>
        /// <returns></returns>
        public static void DeleteCurrentSpouse(long applicationId, string updatedBy)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                context.usp_DELETE_ELDERLY_DISABLED_SPOUSE(applicationId, updatedBy);
            }
        }

        /// <summary>
        /// Deletes all former spouses
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="updatedBy">The updated by.</param>
        public static void DeleteFormerSpouses(long applicationId, string updatedBy)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                context.usp_DELETE_ELDERLY_DISABLED_FORMER_SPOUSES(applicationId, updatedBy);
            }
        }

        /// <summary>
        /// Upsert FormerSpouse data
        /// </summary>
        /// <param name="spouseInfo">The spouse information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns>Is successful</returns>
        public static bool UpsertFormerSpouses(ElderlyDisabledSpouseDto spouseInfo, Guid tokenId)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                spouseInfo.FormerSpouses.Where(fs => fs.Ssn == null).ToList().ForEach(fs => fs.Ssn = string.Empty);

                // Get existing info
                var existingFormerSpouses = context.APPLICATION_FORMER_SPOUSE
                    .Where(fs => fs.APPLICATION_ID == spouseInfo.ApplicationId).ToList();

                // Delete existing ones that aren't on this new list
                foreach (var formerSpouseRow in existingFormerSpouses.ToArray())
                {
                    if (!spouseInfo.FormerSpouses.Any(fp => fp.ApplicationFormerSpouseId == formerSpouseRow.APPLICATION_FORMER_SPOUSE_ID))
                    {
                        context.APPLICATION_FORMER_SPOUSE.Remove(formerSpouseRow);
                        existingFormerSpouses.Remove(formerSpouseRow);
                    }
                }
                context.SaveChanges();

                // Updates and adds
                foreach (var formerSpouse in spouseInfo.FormerSpouses)
                {
                    var existingFP = existingFormerSpouses.FirstOrDefault(fp => fp.APPLICATION_FORMER_SPOUSE_ID == formerSpouse.ApplicationFormerSpouseId);
                    if (existingFP != null)
                    {
                        // Update
                        SqlToDtoMapper.DtoToSql(formerSpouse, existingFP);
                    }
                    else
                    {
                        // Add
                        if (!string.IsNullOrEmpty(formerSpouse.FirstName) && !string.IsNullOrEmpty(formerSpouse.LastName))
                        {
                            APPLICATION_FORMER_SPOUSE newFP = new APPLICATION_FORMER_SPOUSE();
                            SqlToDtoMapper.DtoToSql(formerSpouse, newFP);
                            newFP.CREATED_BY = formerSpouse.UpdatedBy;
                            newFP.CREATED_DATE = formerSpouse.UpdatedDate;
                            context.APPLICATION_FORMER_SPOUSE.Add(newFP);
                        }
                    }
                }

                context.SaveChanges();
            }

            return true;
        }

        /// <summary>
        /// Saves the property information.
        /// </summary>
        /// <param name="propertyInfo">The property information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <param name="username">The username.</param>
        /// <returns></returns>
        public static bool SavePropertyInformation(ElderlyDisabledPropertyDto propertyInfo, string username, Guid tokenId)
        {
            // Ensure ALL sub objects have the UpdatedBy and UpdatedDate
            propertyInfo.CascadeSetAllUpdatedBy();

            performDataIntegrityCheckProperty(propertyInfo);

            using (var context = new CaresApplicationDBEntities())
            {
                // Handle deleted items
                handleDeletedItems(context, propertyInfo);

                // Serialize cleaned property info
                var propertyJson = JsonConvert.SerializeObject(propertyInfo);
                context.usp_UPSERT_PROPERTY_INFORMATION(propertyJson);

                // Update latest detail values
                UpdateParcelWithLatestDetailValue(context, propertyInfo.ApplicationId, username);
                UpdatePreviousPropertyWithLatestDetailValue(context, propertyInfo.ApplicationId, username);
                UpdateMobileHomeWithLatestDetailValue(context, propertyInfo.ApplicationId, username);

                return true;
            }
        }

        /// <summary>
        /// Handles the property deletes
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="propertyInfo">The property info object.</param>
        private static void handleDeletedItems(CaresApplicationDBEntities context, ElderlyDisabledPropertyDto propertyInfo)
        {
            // Handle delete for parcels
            if (propertyInfo.PropertyParcels?.Any() == true)
            {
                bool isParcelDeleted = propertyInfo.PropertyParcels.Any(p => p?.IsDeleted == true);

                bool isParcelDetailDeleted = propertyInfo.PropertyParcels
                       .Where(p => p?.EDPropertyParcelDetail != null)
                       .SelectMany(p => p.EDPropertyParcelDetail)
                       .Any(d => d?.IsEDPropertyParcelDetailDeleted == true);

                // Perform delete operations first, if applicable
                if (isParcelDeleted || isParcelDetailDeleted)
                {
                    deletePropertyParcel(context, propertyInfo);

                    // Remove deleted property parcels or property parcel details
                    foreach (var parcel in propertyInfo.PropertyParcels.ToList())
                    {
                        if (parcel.IsDeleted)
                        {
                            // Move parcel header and month-wise details to the previous property
                            var movedParcel = MapParcelToPrevious(parcel);
                            propertyInfo.PreviousProperties.Add(movedParcel);
                            propertyInfo.PropertyParcels.Remove(parcel);
                        }
                        else
                        {
                            if (parcel.EDPropertyParcelDetail != null)
                            {
                                parcel.EDPropertyParcelDetail = parcel.EDPropertyParcelDetail.Where(d => !d.IsEDPropertyParcelDetailDeleted).ToList();
                            }
                        }
                    }
                }
            }

            // Handle delete for previous property
            if (propertyInfo.PreviousProperties?.Any() == true)
            {
                bool isPreviousPropertyDeleted = propertyInfo.PreviousProperties.Any(p => p?.IsDeleted == true);

                bool isPreviousPropertyDetailDeleted = propertyInfo.PreviousProperties
                       .Where(p => p?.EDPreviousPropertyDetail != null)
                       .SelectMany(p => p.EDPreviousPropertyDetail)
                       .Any(d => d?.IsEDPreviousPropertyDetailDeleted == true);

                // Perform delete operations first, if applicable
                if (isPreviousPropertyDeleted || isPreviousPropertyDetailDeleted)
                {
                    deletePreviousProperty(context, propertyInfo);

                    // Remove deleted previous property or previous property details
                    foreach (var previousProperty in propertyInfo.PreviousProperties.ToList())
                    {
                        if (previousProperty.IsDeleted)
                        {
                            propertyInfo.PreviousProperties.Remove(previousProperty);
                        }
                        else
                        {
                            if (previousProperty.EDPreviousPropertyDetail != null)
                            {
                                previousProperty.EDPreviousPropertyDetail = previousProperty.EDPreviousPropertyDetail.Where(d => !d.IsEDPreviousPropertyDetailDeleted).ToList();
                            }
                        }
                    }
                }
            }

            // Handle delete for mobile homes
            if (propertyInfo.PropertyMobileHomes?.Any() == true)
            {
                bool isMobileHomeDeleted = propertyInfo.PropertyMobileHomes.Any(p => p?.IsDeleted == true);

                bool isMobileHomeDetailDeleted = propertyInfo.PropertyMobileHomes
                       .Where(p => p?.EDPropertyMobileHomeDetail != null)
                       .SelectMany(p => p.EDPropertyMobileHomeDetail)
                       .Any(d => d?.IsEDMobileHomeDetailDeleted == true);

                // Perform delete operations first, if applicable
                if (isMobileHomeDeleted || isMobileHomeDetailDeleted)
                {
                    deletePropertyMobileHome(context, propertyInfo);

                    // Remove deleted property mobile homes or property mobile homes details
                    foreach (var mobileHome in propertyInfo.PropertyMobileHomes.ToList())
                    {
                        if (mobileHome.IsDeleted)
                        {
                            // Move mobile home header and month-wise details to the previous property
                            var movedMobileHome = MapMobileHomeToPrevious(mobileHome);
                            propertyInfo.PreviousProperties.Add(movedMobileHome);
                            propertyInfo.PropertyMobileHomes.Remove(mobileHome);
                        }
                        else
                        {
                            if (mobileHome.EDPropertyMobileHomeDetail != null)
                            {
                                mobileHome.EDPropertyMobileHomeDetail = mobileHome.EDPropertyMobileHomeDetail.Where(d => !d.IsEDMobileHomeDetailDeleted).ToList();
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Deletes the property parcels and property parcels details
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="propertyInfo">The property info object.</param>
        private static void deletePropertyParcel(CaresApplicationDBEntities context, ElderlyDisabledPropertyDto propertyInfo)
        {
            if (propertyInfo.ApplicationPropertyId == null)
                return;

            // Get existing parcels
            var existingParcels = context.APPLICATION_PROPERTY_PARCEL
                .Where(ep => ep.APPLICATION_PROPERTY_ID == propertyInfo.ApplicationPropertyId)
                .ToList();

            var parcelIds = existingParcels.Select(ep => ep.APPLICATION_PROPERTY_PARCEL_ID).ToList();

            // Get existing parcels detail
            var existingDetails = context.APPLICATION_PROPERTY_PARCEL_DETAIL
                .Where(detail => parcelIds.Contains(detail.APPLICATION_PROPERTY_PARCEL_ID))
                .ToList();

            var incomingPropertiesIds = new HashSet<long?>(propertyInfo.PropertyParcels.Select(n => n.ApplicationPropertyParcelId));

            foreach (var existing in existingParcels)
            {
                // Check if the Properties is marked as deleted in the incoming data
                var incomingProperties = propertyInfo.PropertyParcels.FirstOrDefault(i => i.ApplicationPropertyParcelId == existing.APPLICATION_PROPERTY_PARCEL_ID);
                if (incomingProperties != null && incomingProperties.IsDeleted)
                {
                    // Ensure there are related details exists
                    var relatedDetails = existingDetails
                        .Where(d => d.APPLICATION_PROPERTY_PARCEL_ID == existing.APPLICATION_PROPERTY_PARCEL_ID)
                        .ToList();

                    if (relatedDetails.Any())
                    {
                        context.APPLICATION_PROPERTY_PARCEL_DETAIL.RemoveRange(relatedDetails);
                        context.SaveChanges();
                    }

                    // Delete the parcel from APPLICATION_PROPERTY_PARCEL
                    context.APPLICATION_PROPERTY_PARCEL.Remove(existing);
                    context.SaveChanges();
                }

                // Check if any associated details are marked as deleted in the incoming data
                var relatedPropertiesDetails = existingDetails
                    .Where(d => d.APPLICATION_PROPERTY_PARCEL_ID == existing.APPLICATION_PROPERTY_PARCEL_ID)
                    .ToList();

                foreach (var detail in relatedPropertiesDetails)
                {
                    var propertiesDetail = propertyInfo.PropertyParcels
                        .SelectMany(i => i.EDPropertyParcelDetail)
                        .FirstOrDefault(d => d.ApplicationPropertyParcelDetailId == detail.APPLICATION_PROPERTY_PARCEL_DETAIL_ID);

                    if (propertiesDetail != null && propertiesDetail.IsEDPropertyParcelDetailDeleted)
                    {
                        // Delete the associated detail from APPLICATION_PROPERTY_PARCEL_DETAIL if marked as deleted
                        context.APPLICATION_PROPERTY_PARCEL_DETAIL.Remove(detail);
                        context.SaveChanges();
                    }
                }
            }
        }

        /// <summary>
        /// Deletes the previous property and previous property details
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="propertyInfo">The property info object.</param>
        private static void deletePreviousProperty(CaresApplicationDBEntities context, ElderlyDisabledPropertyDto propertyInfo)
        {
            if (propertyInfo.ApplicationPropertyId == null)
                return;

            // Get existing previous properties
            var existingParcels = context.APPLICATION_PROPERTY_PREVIOUS
                .Where(ep => ep.APPLICATION_PROPERTY_ID == propertyInfo.ApplicationPropertyId)
                .ToList();

            var parcelIds = existingParcels.Select(ep => ep.APPLICATION_PROPERTY_PREVIOUS_ID).ToList();

            // Get existing previous properties detail
            var existingDetails = context.APPLICATION_PROPERTY_PREVIOUS_DETAIL
                .Where(detail => parcelIds.Contains(detail.APPLICATION_PROPERTY_PREVIOUS_ID))
                .ToList();

            var incomingPropertiesIds = new HashSet<long?>(propertyInfo.PreviousProperties.Select(n => n.ApplicationPropertyPreviousId));

            foreach (var existing in existingParcels)
            {
                // Check if the previous properties is marked as deleted in the incoming data
                var incomingProperties = propertyInfo.PreviousProperties.FirstOrDefault(i => i.ApplicationPropertyPreviousId == existing.APPLICATION_PROPERTY_PREVIOUS_ID);
                if (incomingProperties != null && incomingProperties.IsDeleted)
                {
                    // Ensure there are related details exists
                    var relatedDetails = existingDetails
                        .Where(d => d.APPLICATION_PROPERTY_PREVIOUS_ID == existing.APPLICATION_PROPERTY_PREVIOUS_ID)
                        .ToList();

                    if (relatedDetails.Any())
                    {
                        context.APPLICATION_PROPERTY_PREVIOUS_DETAIL.RemoveRange(relatedDetails);
                        context.SaveChanges();
                    }

                    // Delete the properties from APPLICATION_PROPERTY_PREVIOUS
                    context.APPLICATION_PROPERTY_PREVIOUS.Remove(existing);
                    context.SaveChanges();
                }

                // Check if any associated details are marked as deleted in the incoming data
                var relatedPropertiesDetails = existingDetails
                    .Where(d => d.APPLICATION_PROPERTY_PREVIOUS_ID == existing.APPLICATION_PROPERTY_PREVIOUS_ID)
                    .ToList();

                foreach (var detail in relatedPropertiesDetails)
                {
                    var propertiesDetail = propertyInfo.PreviousProperties
                        .SelectMany(i => i.EDPreviousPropertyDetail)
                        .FirstOrDefault(d => d.ApplicationPropertyPreviousDetailId == detail.APPLICATION_PROPERTY_PREVIOUS_DETAIL_ID);

                    if (propertiesDetail != null && propertiesDetail.IsEDPreviousPropertyDetailDeleted)
                    {
                        // Delete the associated detail from APPLICATION_PROPERTY_PREVIOUS_DETAIL if marked as deleted
                        context.APPLICATION_PROPERTY_PREVIOUS_DETAIL.Remove(detail);
                        context.SaveChanges();
                    }
                }
            }
        }

        /// <summary>
        /// Deletes the property mobile homes and property mobile homes details
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="propertyInfo">The property info object.</param>
        private static void deletePropertyMobileHome(CaresApplicationDBEntities context, ElderlyDisabledPropertyDto propertyInfo)
        {
            if (propertyInfo.ApplicationPropertyId == null)
                return;

            // Get existing mobile homes
            var existingMobileHomes = context.APPLICATION_PROPERTY_MOBILE_HOME
                .Where(emh => emh.APPLICATION_PROPERTY_ID == propertyInfo.ApplicationPropertyId)
                .ToList();

            var mobileHomeIds = existingMobileHomes.Select(emh => emh.APPLICATION_PROPERTY_MOBILE_HOME_ID).ToList();

            // Get existing mobile homes detail
            var existingDetails = context.APPLICATION_PROPERTY_MOBILE_HOME_DETAIL
                .Where(detail => mobileHomeIds.Contains(detail.APPLICATION_PROPERTY_MOBILE_HOME_ID))
                .ToList();

            var incomingMobileHomesIds = new HashSet<long?>(propertyInfo.PropertyMobileHomes.Select(n => n.ApplicationPropertyMobileHomeId));

            foreach (var existing in existingMobileHomes)
            {
                // Check if the Mobile Homes are marked as deleted in the incoming data
                var incomingMobileHomes = propertyInfo.PropertyMobileHomes.FirstOrDefault(i => i.ApplicationPropertyMobileHomeId == existing.APPLICATION_PROPERTY_MOBILE_HOME_ID);
                if (incomingMobileHomes != null && incomingMobileHomes.IsDeleted)
                {
                    // Ensure there are related details exists
                    var relatedDetails = existingDetails
                        .Where(d => d.APPLICATION_PROPERTY_MOBILE_HOME_ID == existing.APPLICATION_PROPERTY_MOBILE_HOME_ID)
                        .ToList();

                    if (relatedDetails.Any())
                    {
                        context.APPLICATION_PROPERTY_MOBILE_HOME_DETAIL.RemoveRange(relatedDetails);
                        context.SaveChanges();
                    }

                    // Delete the mobile home from APPLICATION_PROPERTY_MOBILE_HOME
                    context.APPLICATION_PROPERTY_MOBILE_HOME.Remove(existing);
                    context.SaveChanges();
                }

                // Check if any associated details are marked as deleted in the incoming data
                var relatedMobileHomesDetails = existingDetails
                    .Where(d => d.APPLICATION_PROPERTY_MOBILE_HOME_ID == existing.APPLICATION_PROPERTY_MOBILE_HOME_ID)
                    .ToList();

                foreach (var detail in relatedMobileHomesDetails)
                {
                    var mobileHomesDetail = propertyInfo.PropertyMobileHomes
                        .SelectMany(i => i.EDPropertyMobileHomeDetail)
                        .FirstOrDefault(d => d.ApplicationPropertyMobileHomeDetailId == detail.APPLICATION_PROPERTY_MOBILE_HOME_DETAIL_ID);

                    if (mobileHomesDetail != null && mobileHomesDetail.IsEDMobileHomeDetailDeleted)
                    {
                        // Delete the associated detail from APPLICATION_PROPERTY_MOBILE_HOME_DETAIL if marked as deleted
                        context.APPLICATION_PROPERTY_MOBILE_HOME_DETAIL.Remove(detail);
                        context.SaveChanges();
                    }
                }
            }
        }

        /// <summary>
        /// Maps parcel from current to previous
        /// </summary>
        /// <param name="parcel">The parcel info.</param>
        private static ElderlyDisabledPropertyPreviousDto MapParcelToPrevious(ElderlyDisabledPropertyParcelDto parcel)
        {
            return new ElderlyDisabledPropertyPreviousDto
            {
                PreviousPropertyStateId = parcel.ParcelStateId,
                PreviousPropertyInStateCountyId = parcel.ParcelInStateCountyId,
                PreviousPropertyOutOfStateCountyName = parcel.ParcelOutOfStateCountyName,
                PreviousPropertyAddressLine1 = parcel.ParcelAddressLine1,
                PreviousPropertyAddressLine2 = parcel.ParcelAddressLine2,
                PreviousPropertyCity = parcel.ParcelCity,
                PreviousPropertyZipCode = parcel.ParcelZipCode,
                HasPropertyMovedFlag = true,
                IsPreviousPropertyTypeParcel = true,
                PreviousParcelOwnerName = parcel.ParcelOwner,
                PreviousParcelIdNumber = parcel.ParcelIdNumber,
                PreviousParcelAppraisedValue = parcel.AppraisedValue,
                EDPreviousPropertyDetail = MapParcelDetailsToPreviousDetails(parcel.EDPropertyParcelDetail)
            };
        }

        /// <summary>
        /// Maps parcel month-wise details from current to previous
        /// </summary>
        /// <param name="parcelDetails">The parcel details info.</param>
        private static List<ElderlyDisabledPropertyPreviousDetailDto> MapParcelDetailsToPreviousDetails(List<ElderlyDisabledPropertyParcelDetailDto> parcelDetails)
        {
            return parcelDetails?
                .Where(d => !d.IsEDPropertyParcelDetailDeleted)
                .Select(detail => new ElderlyDisabledPropertyPreviousDetailDto
                {
                    PreviousPropertyDetailMonth = detail.PropertyParcelDetailMonth,
                    PreviousPropertyParcelAppraisedValue = detail.AppraisedValue,
                    PreviousPropertyMobileHomeValueAmount = 0.00m
                }).ToList() ?? new List<ElderlyDisabledPropertyPreviousDetailDto>();
        }

        /// <summary>
        /// Maps mobile home from current to previous
        /// </summary>
        /// <param name="mobileHome">The mobile home info.</param>
        private static ElderlyDisabledPropertyPreviousDto MapMobileHomeToPrevious(ElderlyDisabledPropertyMobileHomeDto mobileHome)
        {
            return new ElderlyDisabledPropertyPreviousDto
            {
                PreviousPropertyStateId = mobileHome.MobileHomeStateId,
                PreviousPropertyInStateCountyId = mobileHome.MobileHomeInStateCountyId,
                PreviousPropertyOutOfStateCountyName = mobileHome.MobileHomeOutOfStateCountyName,
                PreviousPropertyAddressLine1 = mobileHome.MobileHomeLandAddressLine1,
                PreviousPropertyAddressLine2 = mobileHome.MobileHomeLandAddressLine2,
                PreviousPropertyCity = mobileHome.MobileHomeLandCity,
                PreviousPropertyZipCode = mobileHome.MobileHomeLandZipCode,
                HasPropertyMovedFlag = true,
                IsPreviousPropertyTypeParcel = false,
                PreviousMobileHomeLandOwnerName = mobileHome.LandownerName,
                PreviousMobileHomeValueAmount = mobileHome.MobileHomeValueAmount,
                EDPreviousPropertyDetail = MapMobileHomeDetailsToPreviousDetails(mobileHome.EDPropertyMobileHomeDetail)
            };
        }

        /// <summary>
        /// Maps mobile home month-wise details from current to previous
        /// </summary>
        /// <param name="mobileHomelDetails">The mobile home details info.</param>
        private static List<ElderlyDisabledPropertyPreviousDetailDto> MapMobileHomeDetailsToPreviousDetails(List<ElderlyDisabledPropertyMobileHomeDetailDto> mobileHomelDetails)
        {
            return mobileHomelDetails?
                .Where(d => !d.IsEDMobileHomeDetailDeleted)
                .Select(detail => new ElderlyDisabledPropertyPreviousDetailDto
                {
                    PreviousPropertyDetailMonth = detail.MobileHomeDetailMonth,
                    PreviousPropertyMobileHomeValueAmount = detail.MobileHomeValueAmount,
                    PreviousPropertyParcelAppraisedValue = 0.00m,

                }).ToList() ?? new List<ElderlyDisabledPropertyPreviousDetailDto>();
        }

        /// Updates the parcel header with the most recent detail record
        /// <param name="context">The DB context.</param>
        /// <param name="applicationId">The application id.</param>
        /// <param name="username">The username.</param>
        private static void UpdateParcelWithLatestDetailValue(CaresApplicationDBEntities context, long applicationId, string username)
        {
            // Check if property exists for the given application id
            var applicationProperty = context.APPLICATION_PROPERTY
                .FirstOrDefault(ap => ap.APPLICATION_ID == applicationId);

            if (applicationProperty == null)
                return;

            // Check if parcel exists
            var parcels = context.APPLICATION_PROPERTY_PARCEL
                .Where(p => p.APPLICATION_PROPERTY_ID == applicationProperty.APPLICATION_PROPERTY_ID)
                .ToList();

            // Check if parcel detail exists
            foreach (var parcel in parcels)
            {
                var latestDetail = context.APPLICATION_PROPERTY_PARCEL_DETAIL
                    .Where(d => d.APPLICATION_PROPERTY_PARCEL_ID == parcel.APPLICATION_PROPERTY_PARCEL_ID)
                    .OrderByDescending(d => d.PARCEL_MONTH)
                    .FirstOrDefault();

                if (latestDetail != null)
                {
                    parcel.APPRAISED_VALUE = latestDetail.APPRAISED_VALUE;
                    parcel.HAS_EXCLUDED_RESOURCE = latestDetail.HAS_EXCLUDED_RESOURCE;
                    parcel.EXCLUDED_RESOURCE_ID = latestDetail.EXCLUDED_RESOURCE_ID;
                    parcel.OTHER_EXCLUDED_RESOURCE_DESC = latestDetail.OTHER_EXCLUDED_RESOURCE_DESC ?? string.Empty;
                    parcel.UPDATED_BY = username;
                    parcel.UPDATED_DATE = DateTime.Now;
                }
            }

            context.SaveChanges();
        }

        /// Updates the previous property header with the most recent detail record
        /// <param name="context">The DB context.</param>
        /// <param name="applicationId">The application id.</param>
        /// <param name="username">The username.</param>
        private static void UpdatePreviousPropertyWithLatestDetailValue(CaresApplicationDBEntities context, long applicationId, string username)
        {
            // Check if property exists for the given application id
            var applicationProperty = context.APPLICATION_PROPERTY
                .FirstOrDefault(ap => ap.APPLICATION_ID == applicationId);

            if (applicationProperty == null)
                return;

            // Check if previous property exists
            var previousProperties = context.APPLICATION_PROPERTY_PREVIOUS
                .Where(p => p.APPLICATION_PROPERTY_ID == applicationProperty.APPLICATION_PROPERTY_ID)
                .ToList();

            // Check if previous property detail exists
            foreach (var previousProperty in previousProperties)
            {
                var latestDetail = context.APPLICATION_PROPERTY_PREVIOUS_DETAIL
                    .Where(d => d.APPLICATION_PROPERTY_PREVIOUS_ID == previousProperty.APPLICATION_PROPERTY_PREVIOUS_ID)
                    .OrderByDescending(d => d.PREVIOUS_PROPERTY_MONTH)
                    .FirstOrDefault();

                if (latestDetail != null)
                {
                    previousProperty.PARCEL_APPRAISED_VALUE = latestDetail.PARCEL_APPRAISED_VALUE;
                    previousProperty.MOBILE_HOME_VALUE_AMOUNT = latestDetail.MOBILE_HOME_VALUE_AMOUNT;
                    previousProperty.UPDATED_BY = username;
                    previousProperty.UPDATED_DATE = DateTime.Now;
                }
            }

            context.SaveChanges();
        }

        /// Updates the mobile home header with the most recent detail record
        /// <param name="context">The DB context.</param>
        /// <param name="applicationId">The application id.</param>
        /// <param name="username">The username.</param>
        private static void UpdateMobileHomeWithLatestDetailValue(CaresApplicationDBEntities context, long applicationId, string username)
        {
            // Check if property exists for the given application id
            var applicationProperty = context.APPLICATION_PROPERTY
                .FirstOrDefault(ap => ap.APPLICATION_ID == applicationId);

            if (applicationProperty == null)
                return;

            // Check if mobile home exists
            var mobileHomes = context.APPLICATION_PROPERTY_MOBILE_HOME
                .Where(p => p.APPLICATION_PROPERTY_ID == applicationProperty.APPLICATION_PROPERTY_ID)
                .ToList();

            // Check if mobile home detail exists
            foreach (var mobileHome in mobileHomes)
            {
                var latestDetail = context.APPLICATION_PROPERTY_MOBILE_HOME_DETAIL
                    .Where(d => d.APPLICATION_PROPERTY_MOBILE_HOME_ID == mobileHome.APPLICATION_PROPERTY_MOBILE_HOME_ID)
                    .OrderByDescending(d => d.MOBILE_HOME_MONTH)
                    .FirstOrDefault();

                if (latestDetail != null)
                {
                    mobileHome.MOBILE_HOME_VALUE_AMOUNT = latestDetail.MOBILE_HOME_VALUE;
                    mobileHome.HAS_EXCLUDED_RESOURCE = latestDetail.HAS_EXCLUDED_RESOURCE;
                    mobileHome.EXCLUDED_RESOURCE_ID = latestDetail.EXCLUDED_RESOURCE_ID;
                    mobileHome.OTHER_EXCLUDED_RESOURCE_DESC = latestDetail.OTHER_EXCLUDED_RESOURCE_DESC ?? string.Empty;
                    mobileHome.UPDATED_BY = username;
                    mobileHome.UPDATED_DATE = DateTime.Now;
                }
            }

            context.SaveChanges();
        }

        /// <summary>
        /// Saves the resource information.
        /// </summary>
        /// <param name="applicationResourceInfo">The application resource information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool SaveResourceInformation(ElderlyDisabledResourceDto applicationResourceInfo, string username, Guid tokenId)
        {
            // Ensure ALL sub objects have the UpdatedBy and UpdatedDate
            applicationResourceInfo.CascadeSetAllUpdatedBy();

            //First perform the data integrity check before doing update's/insert's for E&D application resource information.
            performDataIntegrityCheckResource(applicationResourceInfo);

            // Update Header Values with latest Monthly Detail values
            performResourceHeaderUpdatesWithLatestMonthlyDetails(applicationResourceInfo);

            using (var context = new CaresApplicationDBEntities())
            {
                // Handle deleted items
                handleDeletedResourceItems(context, applicationResourceInfo);

                // Serialize cleaned resource info
                var applicantResourceInfoJson = JsonConvert.SerializeObject(applicationResourceInfo);
                context.usp_UPSERT_RESOURCE_INFORMATION(applicantResourceInfoJson);

                // Update latest detail values
                UpdateResourceWithLatestDetailValue(context, applicationResourceInfo.ApplicationId, username);
                UpdateResourceDetailsWithLatestDetailValue(context, applicationResourceInfo.ApplicationId, username);

                return true;
            }
        }

        /// <summary>
        /// Handles the resource items deletes
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="propertyInfo">The property info object.</param>
        private static void handleDeletedResourceItems(CaresApplicationDBEntities context, ElderlyDisabledResourceDto applicationResourceInfo)
        {
            // Handle delete for banks
            if (applicationResourceInfo.ResourceBankDetails?.Any() == true)
            {
                bool isBankDeleted = applicationResourceInfo.ResourceBankDetails.Any(i => i.IsDeleted);
                bool isBankDetailDeleted = applicationResourceInfo.ResourceBankDetails.Where(i => i.BankDetails != null).SelectMany(i => i.BankDetails).Any(d => d.IsDeleted);

                // Perform delete operations first, if applicable
                if (isBankDeleted || isBankDetailDeleted)
                {
                    deleteResourceBank(context, applicationResourceInfo);

                    // Remove deleted property parcels or property parcel details
                    foreach (var resource in applicationResourceInfo.ResourceBankDetails.ToList())
                    {
                        if (resource.IsDeleted)
                        {
                            resource.BankDetails.RemoveAll(c => true);
                            applicationResourceInfo.ResourceBankDetails.Remove(resource);
                        }
                        else
                        {
                            if (resource.BankDetails != null)
                            {
                                resource.BankDetails = resource.BankDetails.Where(d => !d.IsDeleted).ToList();
                            }
                        }
                    }
                }
            }

            // Handle delete for resources
            if (applicationResourceInfo.ResourceDetails?.Any() == true)
            {
                bool isResourceDetailsDeleted = applicationResourceInfo.ResourceDetails.Any(i => i.IsDeleted);
                bool isResourceMonthDetailDeleted = applicationResourceInfo.ResourceDetails.Where(i => i.EDResourceMonthDetail != null).SelectMany(i => i.EDResourceMonthDetail).Any(d => d.IsResourceMonthDetailDeleted);

                // Perform delete operations first, if applicable
                if (isResourceDetailsDeleted || isResourceMonthDetailDeleted)
                {
                    deleteResourceDetails(context, applicationResourceInfo);

                    // Remove deleted resource details or resource month details
                    foreach (var resource in applicationResourceInfo.ResourceDetails.ToList())
                    {
                        if (resource.IsDeleted)
                        {
                            applicationResourceInfo.ResourceDetails.Remove(resource);
                        }
                        else
                        {
                            if (resource.EDResourceMonthDetail != null)
                            {
                                resource.EDResourceMonthDetail = resource.EDResourceMonthDetail.Where(d => !d.IsResourceMonthDetailDeleted).ToList();
                            }
                        }
                    }
                }
            }

            // Handle delete for Transfers
            if (applicationResourceInfo.ResourceTransferDetails?.Any() == true)
            {
                bool isTransferDeleted = applicationResourceInfo.ResourceTransferDetails.Any(i => i.IsDeleted);
                bool isTransferDetailDeleted = applicationResourceInfo.ResourceTransferDetails.Where(i => i.EDTransferMonthDetail != null).SelectMany(i => i.EDTransferMonthDetail).Any(d => d.IsDeleted);

                // Perform delete operations first, if applicable
                if (isTransferDeleted || isTransferDetailDeleted)
                {
                    deleteResourceTransfer(context, applicationResourceInfo);

                    // Remove deleted transfers and details
                    foreach (var resource in applicationResourceInfo.ResourceTransferDetails.ToList())
                    {
                        if (resource.EDTransferMonthDetail != null)
                        {
                            if (resource.IsDeleted)
                            {
                                resource.EDTransferMonthDetail.RemoveAll(c => true);
                                applicationResourceInfo.ResourceTransferDetails.Remove(resource);
                            }
                            else
                            {
                                resource.EDTransferMonthDetail = resource.EDTransferMonthDetail.Where(d => !d.IsDeleted).ToList();
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Deletes on Med Ins details
        /// </summary>
        /// <param name="context"></param>
        /// <param name="medicalInsuranceInfo"></param>
        private static void handleDeletedMedInsuranceItems(CaresApplicationDBEntities context, ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo)
        {
            // Handle delete for Med Ins Details
            if (medicalInsuranceInfo.MedicalInsuranceDetails?.Any() == true)
            {
                bool isMedInsDeleted = medicalInsuranceInfo.MedicalInsuranceDetails.Any(i => i.IsDeleted);
                bool isMedInsMonthDetailDeleted = medicalInsuranceInfo.MedicalInsuranceDetails.Where(i => i.MonthDetails != null).SelectMany(i => i.MonthDetails).Any(d => d.IsDeleted);

                // Perform delete operations first, if applicable
                if (isMedInsDeleted || isMedInsMonthDetailDeleted)
                {
                    deleteMedInsDetails(context, medicalInsuranceInfo);

                    // Remove deleted medIns and details
                    foreach (var medInsDetail in medicalInsuranceInfo.MedicalInsuranceDetails.ToList()) // Notice the .ToList(), because we may be removing from the original
                    {
                        if (medInsDetail.IsDeleted)
                        {
                            medInsDetail.MonthDetails?.RemoveAll(c => true);
                            medicalInsuranceInfo.MedicalInsuranceDetails.Remove(medInsDetail);
                        }
                        else
                        {
                            medInsDetail.MonthDetails = medInsDetail.MonthDetails.Where(d => !d.IsDeleted).ToList();
                        }
                    }
                }
            }

            //Handle delete for medical long term care insurance details
            if (medicalInsuranceInfo.MedicalLongTermCareInsuranceDetails?.Any(x=>x.IsDeleted) == true)
            {
                var deletedLTCInsuranceIds = medicalInsuranceInfo.MedicalLongTermCareInsuranceDetails.Where(x => x.IsDeleted).Select(x=>x.ApplicationMedicalLTCInsuranceDetailId).ToList();

                medicalInsuranceInfo.MedicalLongTermCareInsuranceDetails.RemoveAll(x => x.IsDeleted);
               
                var deletedRecords = context.APPLICATION_MEDICAL_INSURANCE_LTC_BUDGET
                    .Where(x => deletedLTCInsuranceIds.Contains(x.APPLICATION_MEDICAL_INSURANCE_LTC_BUDGET_ID)).ToList();
                context.APPLICATION_MEDICAL_INSURANCE_LTC_BUDGET.RemoveRange(deletedRecords);
                context.SaveChanges();
            }

            // Handle delete for Part D Budgets
            if (medicalInsuranceInfo.PartDBudgets?.Any() == true)
            {
                bool isPartDBudgetDeleted = medicalInsuranceInfo.PartDBudgets.Any(i => i.IsDeleted);

                // Perform delete operations first, if applicable
                if (isPartDBudgetDeleted)
                {
                    deletePartDBudget(context, medicalInsuranceInfo);

                    // Remove deleted 
                    foreach (var budget in medicalInsuranceInfo.PartDBudgets.ToList()) // Notice the .ToList(), because we may be removing from the original
                    {
                        if (budget.IsDeleted)
                        {
                            medicalInsuranceInfo.PartDBudgets.Remove(budget);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Deletes the resource bank and bank details
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="resourceBankInfo">The resource info object.</param>
        private static void deleteResourceBank(CaresApplicationDBEntities context, ElderlyDisabledResourceDto resourceBankInfo)
        {
            if (resourceBankInfo.ApplicationResourceId == null)
                return;

            // Get existing banks
            var existingBanks = context.APPLICATION_RESOURCE_BANK
                .Where(ep => ep.APPLICATION_RESOURCE_ID == resourceBankInfo.ApplicationResourceId)
                .ToList();

            var BankIds = existingBanks.Select(ep => ep.APPLICATION_RESOURCE_BANK_ID).ToList();

            // Get existing bank details
            var existingDetails = context.APPLICATION_RESOURCE_BANK_DETAIL
                .Where(detail => BankIds.Contains(detail.APPLICATION_RESOURCE_BANK_ID))
                .ToList();

            var incomingBankIds = resourceBankInfo.ResourceBankDetails.Select(n => n.ApplicationResourceBankId).ToList();

            foreach (var existing in existingBanks)
            {
                // Check if the bank details is marked as deleted in the incoming data
                var incomingResources = resourceBankInfo.ResourceBankDetails.FirstOrDefault(i => i.ApplicationResourceBankId == existing.APPLICATION_RESOURCE_BANK_ID);
                if (incomingResources != null && incomingResources.IsDeleted)
                {
                    // Ensure there are related details exists
                    var relatedDetails = existingDetails
                        .Where(d => d.APPLICATION_RESOURCE_BANK_ID == existing.APPLICATION_RESOURCE_BANK_ID)
                        .ToList();

                    if (relatedDetails.Any())
                    {
                        context.APPLICATION_RESOURCE_BANK_DETAIL.RemoveRange(relatedDetails);
                        context.SaveChanges();
                    }

                    // Delete the bank details from APPLICATION_RESOURCE_BANK
                    context.APPLICATION_RESOURCE_BANK.Remove(existing);
                    context.SaveChanges();
                }

                // Check if any associated details are marked as deleted in the incoming data
                var relatedResourceDetails = existingDetails
                    .Where(d => d.APPLICATION_RESOURCE_BANK_ID == existing.APPLICATION_RESOURCE_BANK_ID)
                    .ToList();

                foreach (var detail in relatedResourceDetails)
                {
                    var propertiesDetail = resourceBankInfo.ResourceBankDetails
                        .SelectMany(i => i.BankDetails)
                        .FirstOrDefault(d => d.ApplicationResourceBankDetailId == detail.APPLICATION_RESOURCE_BANK_DETAIL_ID);

                    if (propertiesDetail != null && propertiesDetail.IsDeleted)
                    {
                        // Delete the associated detail from APPLICATION_RESOURCE_BANK_DETAIL if marked as deleted
                        context.APPLICATION_RESOURCE_BANK_DETAIL.Remove(detail);
                        context.SaveChanges();
                    }
                }
            }
        }

        /// <summary>
        /// Deletes the resource transfer and transfer month details
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="resourceInfo">The resource info object.</param>
        private static void deleteResourceTransfer(CaresApplicationDBEntities context, ElderlyDisabledResourceDto resourceInfo)
        {
            if (resourceInfo.ApplicationResourceId == null)
                return;

            // Get existing transfers
            var existingTransfers = context.APPLICATION_RESOURCE_TRANSFER
                .Where(ep => ep.APPLICATION_RESOURCE_ID == resourceInfo.ApplicationResourceId).ToList();

            var TransferIds = existingTransfers.Select(ep => ep.APPLICATION_RESOURCE_TRANSFER_ID).ToList();

            // Get existing transfer details
            var existingDetails = context.APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL
                .Where(detail => TransferIds.Contains(detail.APPLICATION_RESOURCE_TRANSFER_ID))
                .ToList();

            foreach (var existing in existingTransfers)
            {
                // Check if the transfer details is marked as deleted in the incoming data
                var incomingResources = resourceInfo.ResourceTransferDetails.FirstOrDefault(i => i.ApplicationResourceTransferId == existing.APPLICATION_RESOURCE_TRANSFER_ID);
                if (incomingResources != null && incomingResources.IsDeleted)
                {
                    // Ensure there are related details
                    var relatedDetails = existingDetails
                        .Where(d => d.APPLICATION_RESOURCE_TRANSFER_ID == existing.APPLICATION_RESOURCE_TRANSFER_ID);

                    if (relatedDetails.Any())
                    {
                        context.APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL.RemoveRange(relatedDetails);
                        context.SaveChanges();
                    }

                    // Delete the transfer details from APPLICATION_RESOURCE_TRANSFER
                    context.APPLICATION_RESOURCE_TRANSFER.Remove(existing);
                    context.SaveChanges();
                }

                // Check if any associated details are marked as deleted in the incoming data
                var relatedTransferDetails = existingDetails
                    .Where(d => d.APPLICATION_RESOURCE_TRANSFER_ID == existing.APPLICATION_RESOURCE_TRANSFER_ID);

                foreach (var detail in relatedTransferDetails)
                {
                    var transferMonthDetail = resourceInfo.ResourceTransferDetails
                        .SelectMany(i => i.EDTransferMonthDetail)
                        .FirstOrDefault(d => d.ElderlyDisabledResourceTransferMonthDetailId == detail.APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL_ID);

                    if (transferMonthDetail != null && transferMonthDetail.IsDeleted)
                    {
                        // Delete the associated detail from APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL if marked as deleted
                        context.APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL.Remove(detail);
                        context.SaveChanges();
                    }
                }
            }
        }

        /// <summary>
        /// Database deletes for Med Ins Details and month details
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="resourceInfo">The resource info object.</param>
        private static void deleteMedInsDetails(CaresApplicationDBEntities context, ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo)
        {
            if (medicalInsuranceInfo.ApplicationMedicalInsuranceId == null)
                return;

            // Get existing med ins details
            var existingMedInsDetails = context.APPLICATION_MEDICAL_INSURANCE_DETAIL
                .Where(ep => ep.APPLICATION_MEDICAL_INSURANCE_ID == medicalInsuranceInfo.ApplicationMedicalInsuranceId).ToList();

            var MedInsDetailIds = existingMedInsDetails.Select(ep => ep.APPLICATION_MEDICAL_INSURANCE_DETAIL_ID).ToList();

            // Get existing MedIns Month details
            var existingMonthDetails = context.APPLICATION_MEDICAL_INSURANCE_MONTH_DETAIL
                .Where(detail => MedInsDetailIds.Contains(detail.APPLICATION_MEDICAL_INSURANCE_DETAIL_ID))
                .ToList();

            foreach (var existing in existingMedInsDetails)
            {
                // Check if the medIns details is marked as deleted in the incoming data
                var incomingResources = medicalInsuranceInfo.MedicalInsuranceDetails.FirstOrDefault(i => i.ApplicationMedicalInsuranceDetailId == existing.APPLICATION_MEDICAL_INSURANCE_DETAIL_ID);
                if (incomingResources != null && incomingResources.IsDeleted)
                {
                    // Ensure there are related month details
                    var relatedDetails = existingMonthDetails
                        .Where(d => d.APPLICATION_MEDICAL_INSURANCE_DETAIL_ID == existing.APPLICATION_MEDICAL_INSURANCE_DETAIL_ID);

                    if (relatedDetails.Any())
                    {
                        context.APPLICATION_MEDICAL_INSURANCE_MONTH_DETAIL.RemoveRange(relatedDetails);
                        context.SaveChanges();
                    }

                    // Delete the MedIns details from APPLICATION_MEDICAL_INSURANCE_DETAIL
                    context.APPLICATION_MEDICAL_INSURANCE_DETAIL.Remove(existing);
                    context.SaveChanges();
                }

                // Check if any associated details are marked as deleted in the incoming data
                var relatedTransferDetails = existingMonthDetails
                    .Where(d => d.APPLICATION_MEDICAL_INSURANCE_DETAIL_ID == existing.APPLICATION_MEDICAL_INSURANCE_DETAIL_ID);

                foreach (var detail in relatedTransferDetails)
                {
                    var monthDetail = medicalInsuranceInfo.MedicalInsuranceDetails
                        .SelectMany(i => i.MonthDetails)
                        .FirstOrDefault(d => d.ApplicationMedicalInsuranceMonthDetailId == detail.APPLICATION_MEDICAL_INSURANCE_MONTH_DETAIL_ID);

                    if (monthDetail != null && monthDetail.IsDeleted)
                    {
                        // Delete the associated detail from APPLICATION_MEDICAL_INSURANCE_DETAIL_MONTH_DETAIL if marked as deleted
                        context.APPLICATION_MEDICAL_INSURANCE_MONTH_DETAIL.Remove(detail);
                        context.SaveChanges();
                    }
                }
            }
        }

        /// <summary>
        /// Database deletes for Med Ins PartD Budgets
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="resourceInfo">The resource info object.</param>
        private static void deletePartDBudget(CaresApplicationDBEntities context, ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo)
        {
            if (medicalInsuranceInfo.ApplicationMedicalInsuranceId == null)
                return;

            // Get existing 
            var existingPartDBudgets = context.APPLICATION_MEDICAL_INSURANCE_PART_D_BUDGET
                .Where(ep => ep.APPLICATION_MEDICAL_INSURANCE_ID == medicalInsuranceInfo.ApplicationMedicalInsuranceId).ToList();

            var PartDBudgetIds = existingPartDBudgets.Select(ep => ep.APPLICATION_MEDICAL_INSURANCE_PART_D_BUDGET_ID).ToList();

            foreach (var existing in existingPartDBudgets)
            {
                // Check if the medIns details is marked as deleted in the incoming data
                var locatedPartDBudget = medicalInsuranceInfo.PartDBudgets.FirstOrDefault(i => i.ApplicationMedicalInsurancePartDBudgetId == existing.APPLICATION_MEDICAL_INSURANCE_PART_D_BUDGET_ID);
                if (locatedPartDBudget != null && locatedPartDBudget.IsDeleted)
                {
                    // Delete the MedIns details from APPLICATION_MEDICAL_INSURANCE_DETAIL
                    context.APPLICATION_MEDICAL_INSURANCE_PART_D_BUDGET.Remove(existing);
                    context.SaveChanges();
                }
            }
        }

        /// Updates the Resource header with the most recent detail record
        /// <param name="context">The DB context.</param>
        /// <param name="applicationId">The application id.</param>
        /// <param name="username">The username.</param>
        private static void UpdateResourceWithLatestDetailValue(CaresApplicationDBEntities context, long applicationId, string username)
        {
            // Check if resource exists for the given application id
            var applicationResourceBank = context.APPLICATION_RESOURCE
                .FirstOrDefault(ap => ap.APPLICATION_ID == applicationId);

            if (applicationResourceBank == null)
                return;

            // Check if resource Bank exists
            var banksHeader = context.APPLICATION_RESOURCE_BANK
                .Where(p => p.APPLICATION_RESOURCE_ID == applicationResourceBank.APPLICATION_RESOURCE_ID)
                .ToList();

            // Check if bank detail exists
            foreach (var bank in banksHeader)
            {
                var latestDetail = context.APPLICATION_RESOURCE_BANK_DETAIL
                    .Where(d => d.APPLICATION_RESOURCE_BANK_ID == bank.APPLICATION_RESOURCE_BANK_ID)
                    .OrderByDescending(d => d.RESOURCE_MONTH)
                    .FirstOrDefault();

                if (latestDetail != null)
                {
                    bank.OPEN_ACCOUNT_BALANCE = latestDetail.CURRENT_BALANCE;
                    bank.UPDATED_BY = username;
                    bank.UPDATED_DATE = DateTime.Now;
                }
            }

            context.SaveChanges();
        }

        /// <summary>
        /// Deletes the resource details and resource month details
        /// </summary>
        /// <param name="context">The DB context.</param>
        /// <param name="resourceBankInfo">The resource info object.</param>
        private static void deleteResourceDetails(CaresApplicationDBEntities context, ElderlyDisabledResourceDto resourceInfo)
        {
            if (resourceInfo.ApplicationResourceId == null)
                return;

            // Get existing resource details
            var existingRD = context.APPLICATION_RESOURCE_DETAIL
                .Where(ep => ep.APPLICATION_RESOURCE_ID == resourceInfo.ApplicationResourceId)
                .ToList();

            var rdIds = existingRD.Select(ep => ep.APPLICATION_RESOURCE_DETAIL_ID).ToList();

            // Get existing resource details
            var existingDetails = context.APPLICATION_RESOURCE_MONTH_DETAIL
                .Where(detail => rdIds.Contains(detail.APPLICATION_RESOURCE_DETAIL_ID))
                .ToList();

            var incomingRDIds = resourceInfo.ResourceDetails.Select(n => n.ApplicationResourceDetailId).ToList();

            foreach (var existing in existingRD)
            {
                // Check if the resource details is marked as deleted in the incoming data
                var incomingResources = resourceInfo.ResourceDetails.FirstOrDefault(i => i.ApplicationResourceDetailId == existing.APPLICATION_RESOURCE_DETAIL_ID);
                if (incomingResources != null && incomingResources.IsDeleted)
                {
                    // Ensure there are related details exists
                    var relatedDetails = existingDetails
                        .Where(d => d.APPLICATION_RESOURCE_DETAIL_ID == existing.APPLICATION_RESOURCE_DETAIL_ID)
                        .ToList();

                    if (relatedDetails.Any())
                    {
                        context.APPLICATION_RESOURCE_MONTH_DETAIL.RemoveRange(relatedDetails);
                        context.SaveChanges();
                    }

                    // Delete the resource details from APPLICATION RESOURCE DETAIL
                    context.APPLICATION_RESOURCE_DETAIL.Remove(existing);
                    context.SaveChanges();
                }

                // Check if any associated details are marked as deleted in the incoming data
                var relatedResourceDetails = existingDetails
                    .Where(d => d.APPLICATION_RESOURCE_DETAIL_ID == existing.APPLICATION_RESOURCE_DETAIL_ID)
                    .ToList();

                foreach (var detail in relatedResourceDetails)
                {
                    var resourceMonthDetail = resourceInfo.ResourceDetails
                        .SelectMany(i => i.EDResourceMonthDetail)
                        .FirstOrDefault(d => d.ApplicationResourceMonthDetailId == detail.APPLICATION_RESOURCE_MONTH_DETAIL_ID);

                    if (resourceMonthDetail != null && resourceMonthDetail.IsResourceMonthDetailDeleted)
                    {
                        // Delete the associated detail from APPLICATION_RESOURCE_MONTH_DETAIL if marked as deleted
                        context.APPLICATION_RESOURCE_MONTH_DETAIL.Remove(detail);
                        context.SaveChanges();
                    }
                }
            }
        }

        /// Updates the Resource header with the most recent detail record
        /// <param name="context">The DB context.</param>
        /// <param name="applicationId">The application id.</param>
        /// <param name="username">The username.</param>
        private static void UpdateResourceDetailsWithLatestDetailValue(CaresApplicationDBEntities context, long applicationId, string username)
        {
            // Check if resource exists for the given application id
            var applicationResource = context.APPLICATION_RESOURCE
                .FirstOrDefault(ap => ap.APPLICATION_ID == applicationId);

            if (applicationResource == null)
                return;

            // Check if resource detail exists
            var detailHeader = context.APPLICATION_RESOURCE_DETAIL
                .Where(p => p.APPLICATION_RESOURCE_ID == applicationResource.APPLICATION_RESOURCE_ID)
                .ToList();

            // Check if resource month detail exists
            foreach (var dh in detailHeader)
            {
                var latestDetail = context.APPLICATION_RESOURCE_MONTH_DETAIL
                    .Where(d => d.APPLICATION_RESOURCE_DETAIL_ID == dh.APPLICATION_RESOURCE_DETAIL_ID)
                    .OrderByDescending(d => d.RESOURCE_MONTH)
                    .FirstOrDefault();

                if (latestDetail == null)
                {
                    dh.DATE_RESOURCE_DISPOSED = null;
                }
                else
                {
                    dh.APPLICANT_RESOURCE_AMOUNT = latestDetail.APPLICANT_RESOURCE_AMOUNT;
                    dh.DATE_RESOURCE_DISPOSED = latestDetail.DATE_RESOURCE_DISPOSED;
                    dh.UPDATED_BY = username;
                    dh.UPDATED_DATE = DateTime.Now;
                }
            }

            context.SaveChanges();
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled life insurance information.
        /// </summary>
        /// <param name="applicationInsuranceInfo">The insurance information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool SaveLifeInsuranceInfo(ElderlyDisabledLifeInsuranceDto applicationInsuranceInfo, Guid tokenId)
        {
            //Ensure ALL sub objects have the UpdatedBy and UpdatedDate.
            applicationInsuranceInfo.CascadeSetAllUpdatedBy();

            //Perform the data integrity check before doing update's/insert's/delete's for E&D application insurance information.
            performDataIntegrityCheckLifeInsurance(applicationInsuranceInfo);
            updateAdditionBurialHeaderFromBudget(applicationInsuranceInfo);

            using (var context = new CaresApplicationDBEntities())
            {
                HandleInsuranceDetailDeletedItems(context, applicationInsuranceInfo);
                HandleOtherBurialFundDetailsDeletedItems(context, applicationInsuranceInfo);
                HandleAdditionalBurialFundBudgetsDeletedItems(context, applicationInsuranceInfo);
                var applicationInsuranceInfoJson = JsonConvert.SerializeObject(applicationInsuranceInfo);

                context.usp_UPSERT_LIFE_INSURANCE_INFORMATION(applicationInsuranceInfoJson);

                return true;
            }
        }

        /// <summary>
        /// Updates additional burial fund header info from the latest budget entry
        /// </summary>
        /// <param name="applicationInsuranceInfo"></param>
        private static void updateAdditionBurialHeaderFromBudget(ElderlyDisabledLifeInsuranceDto applicationInsuranceInfo)
        {
            foreach (var ad in applicationInsuranceInfo.AdditionalBurialFundDetails)
            {
                // Get the latest month detail
                var latestMonthDetail = ad.Budgets?.Where(md => !md.IsDeleted).OrderByDescending(md => md.EffectiveDate).FirstOrDefault();
                if (latestMonthDetail != null)
                {
                    ad.AdditionalBurialFundsAmount = latestMonthDetail.Amount;
                    ad.DateClosed = latestMonthDetail.DateClosed;
                    ad.DateDisposed = latestMonthDetail.DateDisposed;
                    ad.IsAccountActive = latestMonthDetail.IsAccountActive;
                    ad.HasCountableAmount = latestMonthDetail.IsCountableTowardsBurial;
                    ad.AdditionalBurialFundsRemarks = latestMonthDetail.OtherSpecify ?? string.Empty;
                    ad.CountableAmountRemarks = latestMonthDetail.CountableAmountRemarks;
                }
            }
        }

        private static void HandleInsuranceDetailDeletedItems(CaresApplicationDBEntities context, ElderlyDisabledLifeInsuranceDto insuranceInfo)
        {
            if (insuranceInfo.LifeInsuranceDetails?.Any() == false)
            {
                return;
            }

            var deletedAdditionalInsuranceDetails = insuranceInfo.LifeInsuranceDetails.
                                               Where(p => p?.IsDeleted == true && p.ApplicationLifeInsuranceDetailId > 0 && p?.AdditionalLifeInsuranceDetails != null)
                                               .SelectMany(x => x.AdditionalLifeInsuranceDetails)
                                               .Select(x => x.ApplicationLifeInsuranceAdditionalDetailId).ToList();

            insuranceInfo.LifeInsuranceDetails = insuranceInfo.LifeInsuranceDetails.Where(p => p?.IsDeleted == false).ToList();

            deletedAdditionalInsuranceDetails.AddRange(insuranceInfo.LifeInsuranceDetails.Where(p => p?.AdditionalLifeInsuranceDetails != null)
                                                    .SelectMany(p => p.AdditionalLifeInsuranceDetails)
                                                    .Where(x => x?.IsDeleted == true && x.ApplicationLifeInsuranceAdditionalDetailId > 0)
                                                    .Select(x => x.ApplicationLifeInsuranceAdditionalDetailId).ToList());

            insuranceInfo.LifeInsuranceDetails.ForEach((insuranceDetails) =>
            {
                insuranceDetails.AdditionalLifeInsuranceDetails = insuranceDetails.AdditionalLifeInsuranceDetails.Where(x => x?.IsDeleted == false).ToList();
            });

            if (deletedAdditionalInsuranceDetails.Count == 0)
            {
                return;
            }

            var recordsToBeDeleted = context.APPLICATION_LIFE_INSURANCE_ADDITIONAL_DETAIL.Where(x => deletedAdditionalInsuranceDetails.Contains(x.APPLICATION_LIFE_INSURANCE_ADDITIONAL_DETAIL_ID));

            context.APPLICATION_LIFE_INSURANCE_ADDITIONAL_DETAIL.RemoveRange(recordsToBeDeleted);
            context.SaveChanges();
        }

        private static void HandleOtherBurialFundDetailsDeletedItems(CaresApplicationDBEntities context, ElderlyDisabledLifeInsuranceDto insuranceInfo)
        {
            if (insuranceInfo.OtherBurialFundDetails?.Any() == false)
            {
                return;
            }

            var deletedOtherBurialFundDetailsIds = insuranceInfo.OtherBurialFundDetails.
                                               Where(p => p?.IsDeleted == true && p.ApplicationOtherBurialFundsId > 0 && p?.ApplicationOtherBurialFundsId != null)
                                               .SelectMany(x => x.OtherBurialFundsBudgetDetails)
                                               .Select(x => x.ApplicationOtherBurialFundsBudgetId).ToList();

            insuranceInfo.OtherBurialFundDetails = insuranceInfo.OtherBurialFundDetails.Where(p => p?.IsDeleted == false).ToList();

            deletedOtherBurialFundDetailsIds.AddRange(insuranceInfo.OtherBurialFundDetails.Where(p => p?.OtherBurialFundsBudgetDetails != null)
                                                    .SelectMany(p => p.OtherBurialFundsBudgetDetails)
                                                    .Where(x => x?.IsDeleted == true && x.ApplicationOtherBurialFundsBudgetId > 0)
                                                    .Select(x => x.ApplicationOtherBurialFundsBudgetId).ToList());
            List<long> deletedBurialSpaceItemIds = new List<long>();

            insuranceInfo.OtherBurialFundDetails.ForEach((otherBurialFundDetails) =>
            {
            otherBurialFundDetails.OtherBurialFundsBudgetDetails.ForEach
                
               (x => deletedBurialSpaceItemIds.AddRange(x.PrepaidBurialSpaceDetails.Where(y => y?.IsDeleted == true).Select(k => k.ApplicationPrepaidBurialSpaceDetailId).ToList()));
                otherBurialFundDetails.OtherBurialFundsBudgetDetails = otherBurialFundDetails.OtherBurialFundsBudgetDetails.Where(x => x?.IsDeleted == false).ToList();
                otherBurialFundDetails.OtherBurialFundsBudgetDetails.ForEach(k =>
               k.PrepaidBurialSpaceDetails = k.PrepaidBurialSpaceDetails.Where(n => n?.IsDeleted == false).ToList());
            });

            var recordsToBeDeleted = context.APPLICATION_OTHER_BURIAL_FUNDS_BUDGET.Where(x => deletedOtherBurialFundDetailsIds.Contains(x.APPLICATION_OTHER_BURIAL_FUNDS_BUDGET_ID));
            var deletedBurialSpaceItems = context.APPLICATION_PREPAID_BURIAL_SPACE_DETAIL.Where(x => deletedOtherBurialFundDetailsIds.Contains(x.APPLICATION_OTHER_BURIAL_FUNDS_BUDGET_ID));
            var deletedBurialItems = context.APPLICATION_PREPAID_BURIAL_SPACE_DETAIL.Where(x => deletedBurialSpaceItemIds.Contains(x.APPLICATION_PREPAID_BURIAL_SPACE_DETAIL_ID));

            context.APPLICATION_OTHER_BURIAL_FUNDS_BUDGET.RemoveRange(recordsToBeDeleted);
            context.APPLICATION_PREPAID_BURIAL_SPACE_DETAIL.RemoveRange(deletedBurialSpaceItems);
            context.APPLICATION_PREPAID_BURIAL_SPACE_DETAIL.RemoveRange(deletedBurialItems);


            context.SaveChanges();
        }

        private static void HandleAdditionalBurialFundBudgetsDeletedItems(CaresApplicationDBEntities context, ElderlyDisabledLifeInsuranceDto insuranceInfo)
        {
            if (insuranceInfo.AdditionalBurialFundDetails?.Any() == false)
            {
                return;
            }

            var deletedAdditionalBurialFundDetailsIds = insuranceInfo.AdditionalBurialFundDetails.
                                               Where(p => p?.IsDeleted == true && p.ApplicationAdditionalBurialFundsId > 0 && p?.ApplicationAdditionalBurialFundsId != null)
                                               .SelectMany(x => x.Budgets)
                                               .Select(x => x.ApplicationAdditionalBurialFundsBudgetId).ToList();

            insuranceInfo.AdditionalBurialFundDetails = insuranceInfo.AdditionalBurialFundDetails.Where(p => p?.IsDeleted == false).ToList();

            deletedAdditionalBurialFundDetailsIds.AddRange(insuranceInfo.AdditionalBurialFundDetails.Where(p => p?.Budgets != null)
                                                    .SelectMany(p => p.Budgets)
                                                    .Where(x => x?.IsDeleted == true && x.ApplicationAdditionalBurialFundsBudgetId > 0)
                                                    .Select(x => x.ApplicationAdditionalBurialFundsBudgetId).ToList());

            insuranceInfo.AdditionalBurialFundDetails.ForEach((additionalBurialFundDetails) =>
            {
                additionalBurialFundDetails.Budgets = additionalBurialFundDetails.Budgets.Where(x => x?.IsDeleted == false).ToList();
            });

            if (deletedAdditionalBurialFundDetailsIds.Count == 0)
            {
                return;
            }

            var recordsToBeDeleted = context.APPLICATION_ADDITIONAL_BURIAL_FUNDS_BUDGET.Where(x => deletedAdditionalBurialFundDetailsIds.Contains(x.APPLICATION_ADDITIONAL_BURIAL_FUNDS_BUDGET_ID));

            context.APPLICATION_ADDITIONAL_BURIAL_FUNDS_BUDGET.RemoveRange(recordsToBeDeleted);
            context.SaveChanges();
        }

        /// <summary>
        /// Perform upserts on the personal property screen.
        /// </summary>
        /// <param name="personalPropertyInfo">The personal property information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool UpsertPersonalPropertyInfo(ElderlyDisabledPersonalPropertyDto personalPropertyInfo, Guid tokenId)
        {
            // Ensure ALL sub objects have the UpdatedBy and UpdatedDate
            personalPropertyInfo.CascadeSetAllUpdatedBy();

            // First perform the data integrity check before doing update's/insert's for personal property information.
            performDataIntegrityCheckPersonalProperty(personalPropertyInfo);

            using (var context = new CaresApplicationDBEntities())
            {
                HandleAutoDetailDeletedItems(context, personalPropertyInfo);
                var personalPropertyInfoJson = JsonConvert.SerializeObject(personalPropertyInfo);

                context.usp_UPSERT_PERSONAL_PROPERTY_INFORMATION(personalPropertyInfoJson);

                return true;
            }
        }

        private static void HandleAutoDetailDeletedItems(CaresApplicationDBEntities context, ElderlyDisabledPersonalPropertyDto personalPropertyInfo)
        {
            if (personalPropertyInfo.AutoDetails?.Any() == true)
            {
                var deletedAdditionalAutoDetails = personalPropertyInfo.AutoDetails.
                                                   Where(p => p?.IsDeleted == true && p.PersonalPropertyAutoId > 0 && p?.AutoAdditionalDetails != null)
                                                   .SelectMany(x => x.AutoAdditionalDetails)
                                                   .Select(x => x.PersonalPropertyAutoDetailId).ToList();

                personalPropertyInfo.AutoDetails = personalPropertyInfo.AutoDetails.Where(p => p?.IsDeleted == false).ToList();

                deletedAdditionalAutoDetails.AddRange(personalPropertyInfo.AutoDetails.Where(p => p?.AutoAdditionalDetails != null)
                                                        .SelectMany(p => p.AutoAdditionalDetails)
                                                        .Where(x => x?.IsAutoDetailDeleted == true && x.PersonalPropertyAutoDetailId > 0)
                                                        .Select(x => x.PersonalPropertyAutoDetailId).ToList());

                personalPropertyInfo.AutoDetails.ForEach((autoDetail) =>
                {
                    autoDetail.AutoAdditionalDetails = autoDetail.AutoAdditionalDetails.Where(x => x?.IsAutoDetailDeleted == false).ToList();
                });

                if (deletedAdditionalAutoDetails.Count > 0)
                {
                    var recordsToBeDeleted = context.APPLICATION_PERSONAL_PROPERTY_AUTO_DETAIL.Where(x => deletedAdditionalAutoDetails.Contains(x.APPLICATION_PERSONAL_PROPERTY_AUTO_DETAIL_ID));

                    context.APPLICATION_PERSONAL_PROPERTY_AUTO_DETAIL.RemoveRange(recordsToBeDeleted);
                }
            }


            // Deleting Machine details
            if (personalPropertyInfo.MachineDetails?.Any() == true)
            {
                var deletedAdditionalMachineDetails = personalPropertyInfo.MachineDetails.
                                                   Where(p => p?.IsDeleted == true && p.PersonalPropertyMachineId > 0 && p?.MachineMonthDetails != null)
                                                   .SelectMany(x => x.MachineMonthDetails)
                                                   .Select(x => x.PersonalPropertyMachineDetailId).ToList();

                personalPropertyInfo.MachineDetails = personalPropertyInfo.MachineDetails.Where(p => p?.IsDeleted == false).ToList();

                deletedAdditionalMachineDetails.AddRange(personalPropertyInfo.MachineDetails.Where(p => p?.MachineMonthDetails != null)
                                                        .SelectMany(p => p.MachineMonthDetails)
                                                        .Where(x => x?.IsDeleted == true && x.PersonalPropertyMachineDetailId > 0)
                                                        .Select(x => x.PersonalPropertyMachineDetailId).ToList());

                personalPropertyInfo.MachineDetails.ForEach((machineDetail) =>
                {
                    machineDetail.MachineMonthDetails = machineDetail.MachineMonthDetails.Where(x => x?.IsDeleted == false).ToList();
                });

                if (deletedAdditionalMachineDetails.Count > 0)
                {
                    var mrecordsToBeDeleted = context.APPLICATION_PERSONAL_PROPERTY_MACHINE_DETAIL.Where(x => deletedAdditionalMachineDetails.Contains(x.APPLICATION_PERSONAL_PROPERTY_MACHINE_DETAIL_ID)).ToList();

                    context.APPLICATION_PERSONAL_PROPERTY_MACHINE_DETAIL.RemoveRange(mrecordsToBeDeleted);
                }
            }

            // Deleting Collectible details
            if (personalPropertyInfo.CollectibleDetails?.Any() == true)
            {
                var deletedAdditionalCollectibleDetails = personalPropertyInfo.CollectibleDetails.
                                                   Where(p => p?.IsDeleted == true && p.PersonalPropertyCollectibleId > 0 && p?.CollectibleMonthDetails != null)
                                                   .SelectMany(x => x.CollectibleMonthDetails)
                                                   .Select(x => x.PersonalPropertyCollectibleDetailId).ToList();

                personalPropertyInfo.CollectibleDetails = personalPropertyInfo.CollectibleDetails.Where(p => p?.IsDeleted == false).ToList();

                deletedAdditionalCollectibleDetails.AddRange(personalPropertyInfo.CollectibleDetails.Where(p => p?.CollectibleMonthDetails != null)
                                                        .SelectMany(p => p.CollectibleMonthDetails)
                                                        .Where(x => x?.IsDeleted == true && x.PersonalPropertyCollectibleDetailId > 0)
                                                        .Select(x => x.PersonalPropertyCollectibleDetailId).ToList());

                personalPropertyInfo.CollectibleDetails.ForEach((collectibleDetail) =>
                {
                    collectibleDetail.CollectibleMonthDetails = collectibleDetail.CollectibleMonthDetails.Where(x => x?.IsDeleted == false).ToList();
                });

                if (deletedAdditionalCollectibleDetails.Count > 0)
                {
                    var crecordsToBeDeleted = context.APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE_DETAIL.Where(x => deletedAdditionalCollectibleDetails.Contains(x.APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE_DETAIL_ID));

                    context.APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE_DETAIL.RemoveRange(crecordsToBeDeleted);
                }
            }

            context.SaveChanges();
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled medical insurance information.
        /// </summary>
        /// <param name="medicalInsuranceInfo">The medical insurance info.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool UpsertMedicalInsuranceInfo(ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo, Guid tokenId)
        {
            //Ensure ALL sub objects have the UpdatedBy and UpdatedDate.
            medicalInsuranceInfo.CascadeSetAllUpdatedBy();

            //Perform the data integrity check before doing update's/insert's/delete's for E&D application medical insurance information.
            performDataIntegrityCheckMedicalInsurance(medicalInsuranceInfo);

            performMedicalInsuranceHeaderUpdateFromDetail(medicalInsuranceInfo);

            using (var context = new CaresApplicationDBEntities())
            {
                handleDeletedMedInsuranceItems(context, medicalInsuranceInfo);

                var medicalInsuranceInfoJson = JsonConvert.SerializeObject(medicalInsuranceInfo);

                context.usp_UPSERT_MEDICAL_INSURANCE_INFORMATION(medicalInsuranceInfoJson);

                return true;
            }
        }

        /// <summary>
        /// For Med Ins, perfrom some copies from the MonthDetail into the Header
        /// </summary>
        /// <param name="medicalInsuranceInfo"></param>
        private static void performMedicalInsuranceHeaderUpdateFromDetail(ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo)
        {
            foreach (var medInsDetail in medicalInsuranceInfo.MedicalInsuranceDetails)
            {
                // Get the latest month detail
                ElderlyDisabledMedicalInsuranceMonthDetailDto latestMonthDetail = medInsDetail.MonthDetails?.Where(md => !md.IsDeleted).OrderByDescending(md => md.ResourceMonth).FirstOrDefault();
                if (latestMonthDetail != null)
                {
                    medInsDetail.DateDisposed = latestMonthDetail.DateDisposed;
                    medInsDetail.HasDeductionInd = true;
                    medInsDetail.MedicalInsuranceDeductedAmount = latestMonthDetail.DeductedAmount;
                }
            }

            // Update PartD from latest Budget
            var latestBudget = medicalInsuranceInfo.PartDBudgets?.Where(md => !md.IsDeleted).OrderByDescending(md => md.EffectiveDate).FirstOrDefault();
            if (latestBudget != null)
            {
                medicalInsuranceInfo.MedicarePartDDateDisposed = latestBudget.DateDisposed;
                medicalInsuranceInfo.MedicarePartDDeductedAmount = latestBudget.DeductedAmount;
            }
        }

        /// <summary>
        /// Get renew application.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static long RenewApplication(long applicationId, string userName, Guid tokenId)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var newAppId = new ObjectParameter("newAppId", typeof(long));
                context.usp_COPY_ELDERLY_DISABLED_APPLICATION(applicationId, userName, newAppId);

                return Convert.ToInt64(newAppId.Value.ToString());
            }
        }

        /// <summary>
        /// Get the application non Magi income.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ElderlyDisabledNonMagiIncomesDto GetNonMagiIncome(long applicationId, Guid tokenId)
        {
            ElderlyDisabledNonMagiIncomesDto incomeDto = new ElderlyDisabledNonMagiIncomesDto()
            {
                ApplicationId = applicationId,
            };

            // Get the Income. This comes from ApplicationDal:
            incomeDto.NonMagiIncomes = ApplicationDal.GetNonMagiIncome(applicationId, tokenId);
            ApplicationDal.SetBaseDtoValues(incomeDto, applicationId);

            // Get the Income Detail values:
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var incomeDetail = context.APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL.FirstOrDefault(a => a.APPLICATION_ID == applicationId);
                if (incomeDetail != null)
                {
                    incomeDto.QitIndicator = incomeDetail.QIT_INDICATOR;
                    incomeDto.SpousalAllocationAmount = incomeDetail.SPOUSAL_ALLOCATION_AMOUNT;
                    incomeDto.SpousalImpoverishmentIndicator = incomeDetail.SPOUSAL_IMPOVERISHMENT_INDICATOR;
                    incomeDto.FamilyAllocationAmount = incomeDetail.FAMILY_ALLOCATION_AMOUNT;
                }

                var personalNeedsAllowances = context.APPLICATION_PERSONAL_NEEDS_ALLOWANCE.Where(x => x.APPLICATION_ID == applicationId).ToList();
                if (personalNeedsAllowances != null)
                {
                    foreach (var pNA in personalNeedsAllowances)
                    {
                        var pnaDto = new PersonalNeedsAllowanceDto
                        {
                            ApplicationPersonalNeedsAllowanceId = pNA.APPLICATION_PERSONAL_NEEDS_ALLOWANCE_ID,
                            IncomeMonth = pNA.INCOME_MONTH,
                            PersonalNeedsAllowance = pNA.PERSONAL_NEEDS_ALLOWANCE
                        };
                        incomeDto.PersonalNeedsAllowances.Add(pnaDto);
                    }
                }
            }

            return incomeDto;
        }

        /// <summary>
        /// Get the application non Magi income details for QIT true.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static List<ElderlyDisabledNonMagiIncomeDetailDto> GetNonMagiDetailForQitByAppId(long applicationId)
        {
            var alldetails = new List<ElderlyDisabledNonMagiIncomeDetailDto>();

            // Get the Allocation Detail info:
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                alldetails = (from h in context.APPLICATION_NON_MAGI_INCOME
                              join d in context.APPLICATION_NON_MAGI_INCOME_DETAIL
                                    on h.APPLICATION_NON_MAGI_INCOME_ID
                                    equals d.APPLICATION_NON_MAGI_INCOME_ID
                              where h.APPLICATION_ID == applicationId && d.QIT_INDICATOR && h.INCOME_RELATIONSHIP_TYPE_ID == (byte)enumRelationship.Self
                              group new { h, d } by new { d.INCOME_MONTH } into g
                              select new ElderlyDisabledNonMagiIncomeDetailDto
                              {
                                  GrossIncomeAmount = g.Sum(s => s.d.GROSS_INCOME_AMOUNT),
                                  MonthlyCountableNetIncomeAmount = g.Sum(s => s.d.MONTHLY_COUNTABLE_NET_INCOME_AMOUNT),
                                  IncomeMonth = g.Key.INCOME_MONTH
                              }).ToList();
            }

            return alldetails;
        }

        /// <summary>
        /// Get the spousal allocation details by application id.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        public static List<ElderlyDisabledSpouseAllocationDetail> GetSpouseAllocationDetailsByAppId(long applicationId)
        {
            var details = new List<ElderlyDisabledSpouseAllocationDetail>();

            // Get the Allocation Detail info:
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                details = (from d in context.APPLICATION_SPOUSE_ALLOCATION_DETAIL
                           where d.APPLICATION_ID == applicationId
                           select new ElderlyDisabledSpouseAllocationDetail
                           {
                               AllocationAmount = d.ALLOCATION_AMOUNT,
                               ApplicationSpouseAllocationId = d.APPLICATION_SPOUSE_ALLOCATION_DETAIL_ID,
                               SpouseImpoverishmentInd = d.SPOUSE_IMPOVERISHMENT_IND ?? false,
                               SpouseParticipationInd = d.SPOUSE_PARTICIPATION_IND ?? false,
                               Month = d.MONTH
                           }).ToList();
            }

            return details;
        }

        /// <summary>
        /// Get the spousal allocation details by application id.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        public static List<ElderlyDisabledFamilyAllocationDetail> GetFamilyAllocationDetailsByAppId(long applicationId)
        {
            var details = new List<ElderlyDisabledFamilyAllocationDetail>();

            // Get the Allocation Detail info:
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                details = (from d in context.APPLICATION_FAMILY_ALLOCATION_DETAIL
                           where d.APPLICATION_ID == applicationId
                           select new ElderlyDisabledFamilyAllocationDetail
                           {
                               AllocationAmount = d.ALLOCATION_AMOUNT,
                               ApplicationFamilyAllocationId = d.APPLICATION_FAMILY_ALLOCATION_DETAIL_ID,
                               FamilyImpoverishmentInd = d.FAMILY_IMPOVERISHMENT_IND ?? false,
                               FamilyParticipationInd = d.FAMILY_PARTICIPATION_IND ?? false,
                               Month = d.MONTH
                           }).ToList();
            }

            return details;
        }

        /// <summary>
        /// Upserts the application non Magi income.
        /// </summary>
        /// <param name="incomeDto">The application non Magi income.</param>
        /// <param name="tokenId">The token identifier.</param>
        public static BaseApiMessage UpsertNonMagiIncome(ElderlyDisabledNonMagiIncomesDto incomeDto, string username, Guid tokenId)
        {
            BaseApiMessage response = new BaseApiMessage();
            incomeDto.CascadeSetAllUpdatedBy();

            // Update the income in ApplicationDal:
            // Cascade this Id in case it wasn't:
            incomeDto.ApplicationId = incomeDto.ApplicationId;
            ApplicationDal.UpsertNonMagiIncome(incomeDto, username, tokenId);

            // Insert/Update the Income Detail values
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var existingIncomeDetail = context.APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL.FirstOrDefault(a => a.APPLICATION_ID == incomeDto.ApplicationId);
                if (existingIncomeDetail != null)
                {
                    // Update existing:
                    SqlToDtoMapper.DtoToSql(incomeDto, existingIncomeDetail);
                }
                else
                {
                    // Create new:
                    APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL newIncomeDetail = new APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL();
                    SqlToDtoMapper.DtoToSql(incomeDto, newIncomeDetail);
                    newIncomeDetail.APPLICATION_ID = incomeDto.ApplicationId;
                    newIncomeDetail.CREATED_BY = incomeDto.UpdatedBy;
                    newIncomeDetail.CREATED_DATE = incomeDto.UpdatedDate;
                    context.APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL.Add(newIncomeDetail);
                }
                context.SaveChanges();
            }

            UpsertPersonalNeedsAllowance(incomeDto, username);

            return response;
        }

        /// <summary>
        /// Upserts the application Allocation Data.
        /// </summary>
        /// <param name="allocationDto">The application non Magi income.</param>
        /// <param name="tokenId">The token identifier.</param>
        public static BaseApiMessage UpsertAllocationInfo(ElderlyDisabledQitAndAllocationDto allocationDto, string username, Guid tokenId)
        {
            BaseApiMessage response = new BaseApiMessage();
            allocationDto.CascadeSetAllUpdatedBy();

            // Insert/Update the Income Detail values
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                // Save the Family Size
                APPLICATION_ELDERLY_DISABLED_DETAIL aeddRecord = context.APPLICATION_ELDERLY_DISABLED_DETAIL.FirstOrDefault(a => a.APPLICATION_ELDERLY_DISABLED_DETAIL_ID == allocationDto.EnDApplicationDetailId);
                if (aeddRecord != null)
                {
                    aeddRecord.FAMILY_ALLOCATION_TOTAL_FAMILY_MEMBERS = allocationDto.FamilySize;
                }

                var spouseRecords = context.APPLICATION_SPOUSE_ALLOCATION_DETAIL.Where(s => s.APPLICATION_ID == allocationDto.ApplicationId);
                var familyRecords = context.APPLICATION_FAMILY_ALLOCATION_DETAIL.Where(f => f.APPLICATION_ID == allocationDto.ApplicationId);

                foreach (var detail in allocationDto.SpouseAllocationList.Where(d => !d.IsDeleted))
                {
                    //Updating existing record
                    var existingAllocation = spouseRecords.FirstOrDefault(d => d.APPLICATION_SPOUSE_ALLOCATION_DETAIL_ID == detail.ApplicationSpouseAllocationId);
                    if (existingAllocation != null)
                    {
                        existingAllocation.ALLOCATION_AMOUNT = detail.AllocationAmount;
                        existingAllocation.MONTH = detail.Month;
                        existingAllocation.SPOUSE_PARTICIPATION_IND = detail.SpouseParticipationInd;
                        existingAllocation.CREATED_BY = existingAllocation.CREATED_BY == string.Empty ? username : existingAllocation.CREATED_BY;
                        existingAllocation.CREATED_DATE = existingAllocation.CREATED_DATE.ToShortDateString() == "01/01/0001" ? DateTime.Now : existingAllocation.CREATED_DATE;
                        existingAllocation.UPDATED_BY = username;
                        existingAllocation.UPDATED_DATE = DateTime.Now;
                    }
                    else
                    {
                        //Inserting new record
                        var newAllocation = new APPLICATION_SPOUSE_ALLOCATION_DETAIL();

                        SqlToDtoMapper.DtoToSql(detail, newAllocation);
                        newAllocation.APPLICATION_ID = allocationDto.ApplicationId;
                        newAllocation.ALLOCATION_AMOUNT = detail.AllocationAmount;
                        newAllocation.MONTH = detail.Month;
                        newAllocation.CREATED_BY = username;
                        newAllocation.CREATED_DATE = DateTime.Now;
                        newAllocation.UPDATED_BY = username;
                        newAllocation.UPDATED_DATE = DateTime.Now;

                        context.APPLICATION_SPOUSE_ALLOCATION_DETAIL.Add(newAllocation);
                    }
                }

                foreach (var detail in allocationDto.FamilyAllocationList.Where(d => !d.IsDeleted))
                {
                    //Updating existing record
                    var existingAllocation = familyRecords.FirstOrDefault(d => d.APPLICATION_FAMILY_ALLOCATION_DETAIL_ID == detail.ApplicationFamilyAllocationId);
                    if (existingAllocation != null)
                    {
                        existingAllocation.ALLOCATION_AMOUNT = detail.AllocationAmount;
                        existingAllocation.MONTH = detail.Month;
                        existingAllocation.FAMILY_PARTICIPATION_IND = detail.FamilyParticipationInd;
                        existingAllocation.CREATED_BY = existingAllocation.CREATED_BY == string.Empty ? username : existingAllocation.CREATED_BY;
                        existingAllocation.CREATED_DATE = existingAllocation.CREATED_DATE.ToShortDateString() == "01/01/0001" ? DateTime.Now : existingAllocation.CREATED_DATE;
                        existingAllocation.UPDATED_BY = username;
                        existingAllocation.UPDATED_DATE = DateTime.Now;
                    }
                    else
                    {
                        //Inserting new record
                        var newAllocation = new APPLICATION_FAMILY_ALLOCATION_DETAIL();

                        SqlToDtoMapper.DtoToSql(detail, newAllocation);
                        newAllocation.APPLICATION_ID = allocationDto.ApplicationId;
                        newAllocation.ALLOCATION_AMOUNT = detail.AllocationAmount;
                        newAllocation.MONTH = detail.Month;
                        newAllocation.CREATED_BY = username;
                        newAllocation.CREATED_DATE = DateTime.Now;
                        newAllocation.UPDATED_BY = username;
                        newAllocation.UPDATED_DATE = DateTime.Now;

                        context.APPLICATION_FAMILY_ALLOCATION_DETAIL.Add(newAllocation);
                    }
                }

                //Removing deleted records for Spouse Allocation
                foreach (var detail in allocationDto.SpouseAllocationList.Where(d => d.IsDeleted && d.ApplicationSpouseAllocationId > 0))
                {
                    var existingAllocation = spouseRecords.FirstOrDefault(d => d.APPLICATION_SPOUSE_ALLOCATION_DETAIL_ID == detail.ApplicationSpouseAllocationId);
                    context.APPLICATION_SPOUSE_ALLOCATION_DETAIL.Remove(existingAllocation);
                }

                //Removing deleted records for Family Allocation
                foreach (var detail in allocationDto.FamilyAllocationList.Where(d => d.IsDeleted && d.ApplicationFamilyAllocationId > 0))
                {
                    var existingAllocation = familyRecords.FirstOrDefault(d => d.APPLICATION_FAMILY_ALLOCATION_DETAIL_ID == detail.ApplicationFamilyAllocationId);
                    context.APPLICATION_FAMILY_ALLOCATION_DETAIL.Remove(existingAllocation);
                }

                context.SaveChanges(); // Save all related detail records in one go
            }

            return response;
        }

        /// <summary>
        /// Gets Family Allocation family size, saved in the App Details.  Specific to QIT
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="tokenId"></param>
        /// <returns>A Tuple with: (long appDetailId, byte? familySize)</returns>
        public static Tuple<long, byte?> GetFamilyAllocationTotalFamilyMembers(long applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var aedd = new ApplicationElderlyDisabledDetail();
                APPLICATION_ELDERLY_DISABLED_DETAIL aeddRecord = context.APPLICATION_ELDERLY_DISABLED_DETAIL.FirstOrDefault(a => a.APPLICATION_ID == applicationId);
                if (aeddRecord != null)
                {
                    return new Tuple<long, byte?>(aeddRecord.APPLICATION_ELDERLY_DISABLED_DETAIL_ID, aeddRecord.FAMILY_ALLOCATION_TOTAL_FAMILY_MEMBERS);
                }
            }
            return new Tuple<long, byte?>(0, null);
        }

        public static void UpsertPersonalNeedsAllowance(ElderlyDisabledNonMagiIncomesDto incomeDto, string username)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var existingPersonalNeedsAllowances = context.APPLICATION_PERSONAL_NEEDS_ALLOWANCE.Where(x => x.APPLICATION_ID == incomeDto.ApplicationId).ToList();

                if (!existingPersonalNeedsAllowances.Any())
                {
                    foreach (var pna in incomeDto.PersonalNeedsAllowances.Where(x => !x.IsPersonalNeedsAllowanceDeleted && x.IncomeMonth > DateTime.MinValue))
                    {
                        var newRecord = new APPLICATION_PERSONAL_NEEDS_ALLOWANCE();
                        SqlToDtoMapper.DtoToSql(pna, newRecord);
                        newRecord.APPLICATION_ID = incomeDto.ApplicationId;
                        newRecord.CREATED_BY = username;
                        newRecord.CREATED_DATE = DateTime.Now;
                        newRecord.UPDATED_BY = string.Empty;

                        context.APPLICATION_PERSONAL_NEEDS_ALLOWANCE.Add(newRecord);
                    }

                    context.SaveChanges();
                    return;
                }

                var deletedIncomeMonths = incomeDto.PersonalNeedsAllowances.Where(x => x.IsPersonalNeedsAllowanceDeleted).Select(x => x.IncomeMonth);
                var deletedRecords = existingPersonalNeedsAllowances.Where(x => deletedIncomeMonths.Contains(x.INCOME_MONTH));

                foreach (var item in deletedRecords)
                {
                    context.APPLICATION_PERSONAL_NEEDS_ALLOWANCE.Remove(item);
                }

                foreach (var pna in incomeDto.PersonalNeedsAllowances.Where(x => !x.IsPersonalNeedsAllowanceDeleted && x.IncomeMonth > DateTime.MinValue))
                {
                    var existingRecord = existingPersonalNeedsAllowances.FirstOrDefault(x => x.APPLICATION_PERSONAL_NEEDS_ALLOWANCE_ID == pna.ApplicationPersonalNeedsAllowanceId);

                    if (existingRecord != null)
                    {
                        existingRecord.INCOME_MONTH = pna.IncomeMonth;
                        existingRecord.PERSONAL_NEEDS_ALLOWANCE = pna.PersonalNeedsAllowance;
                        existingRecord.UPDATED_BY = username;
                        existingRecord.UPDATED_DATE = DateTime.Now;
                    }
                    else
                    {
                        var newRecord = new APPLICATION_PERSONAL_NEEDS_ALLOWANCE();
                        SqlToDtoMapper.DtoToSql(pna, newRecord);
                        newRecord.APPLICATION_ID = incomeDto.ApplicationId;
                        newRecord.CREATED_BY = username;
                        newRecord.CREATED_DATE = DateTime.Now;
                        newRecord.UPDATED_BY = string.Empty;

                        context.APPLICATION_PERSONAL_NEEDS_ALLOWANCE.Add(newRecord);
                    }
                }

                context.SaveChanges();
            }

        }

        /// <summary>
        /// Upserts the eligibility determinations.
        /// </summary>
        /// <param name="eligibilityDto">The eligibility DTO.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static BaseApiResponse UpsertEligibilityDeterminations(ElderlyDisabledEligibilityDeterminationsDto eligibilityDto, Guid tokenId)
        {
            //  Get current application status and check if it is a terminal status. Return an error if it is in a terminal status.
            if ((new ApplicationSnapshotDAL()).CheckApplicationIsInTerminalStatus(eligibilityDto.ApplicationId, tokenId))
            {
                return new BaseApiResponse
                {
                    IsSuccessful = false,
                    ErrorMessage = "The application is already in a terminal status."
                };
            }

            var awardDeterminations = new List<ElderlyDisabledEligibilityDeterminationDto>();
            var denyDeterminations = new List<ElderlyDisabledEligibilityDeterminationDto>();
            var allDeterminations = new List<ElderlyDisabledEligibilityDeterminationDto>();

            eligibilityDto.CascadeSetAllUpdatedBy();

            // Do some data integrity checks
            foreach (var det in eligibilityDto.Determinations)
            {
                // Append to award List
                if (det.ProgramSubCategoryId.HasValue || det.AwardOption.HasValue)
                {
                    // If Override, ensure all override fields are set:
                    if (det.OverrideReasonId.HasValue && det.OverrideReasonId > 0)
                    {
                        det.OverrideBy = det.OverrideBy ?? det.UpdatedBy;
                        det.OverrideDate = det.OverrideDate ?? DateTime.Now;
                    }
                    awardDeterminations.Add(det);
                }
                // Append to deny List
                if (det.DenialReasons.Count > 0)
                {
                    denyDeterminations.Add(det);
                }
            }

            // Consolidate segments
            if (denyDeterminations.Count > 0)
            {
                denyDeterminations = consolidateSegments(denyDeterminations, false);
            }

            if (awardDeterminations.Count > 0)
            {
                awardDeterminations = consolidateSegments(awardDeterminations, true);
            }

            allDeterminations.AddRange(denyDeterminations);
            allDeterminations.AddRange(awardDeterminations);

            using (var context = new CaresApplicationDBEntities())
            {
                // Get the existing list of determinations
                var existingDeterminations = context.APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION
                    .Where(d => d.APPLICATION_ID == eligibilityDto.ApplicationId).ToList();

                // Determine the determination deletes
                // Note the ToArray is used to make a copy.  We may delete from the original list
                foreach (var det in existingDeterminations.ToArray())
                {
                    var hasMatchingEntry = allDeterminations.Any(d =>
                        d.AppEligibilityElderlyDisabledDeterminationId == det.APP_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION_ID);
                    if (!hasMatchingEntry)
                    {
                        context.APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION.Remove(det);
                    }
                }
                context.SaveChanges();

                // Perform updates and inserts
                foreach (var det in allDeterminations)
                {
                    var existingElig = context.APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION.FirstOrDefault(e => e.APP_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION_ID == det.AppEligibilityElderlyDisabledDeterminationId);

                    if (existingElig != null)
                    {
                        //  Upsert
                        SqlToDtoMapper.DtoToSql(det, existingElig);

                        mapDenialReasonsToEFObject(existingElig, det.DenialReasons);
                    }
                    else
                    {
                        //  Insert
                        var newElig = new APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION();

                        SqlToDtoMapper.DtoToSql(det, newElig);

                        newElig.CREATED_BY = eligibilityDto.UpdatedBy;
                        newElig.CREATED_DATE = eligibilityDto.UpdatedDate;
                        mapDenialReasonsToEFObject(newElig, det.DenialReasons);

                        context.APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION.Add(newElig);
                    }
                }
                context.SaveChanges();

                //  Update the application status, if needed, to Enrollment Determination
                var app = context.APPLICATIONs.Include(nameof(APPLICATION_STATUS_HISTORY))
                    .FirstOrDefault(a => a.APPLICATION_ID == eligibilityDto.ApplicationId
                        && a.APPLICATION_STATUS_ID != (byte)ENUMS.enumApplicationStatus.Enrollment_Determination);

                if (app != null)
                {
                    //  Application is not in Enrollment Determination.
                    //  Check if the existing application status is in the history table.
                    var appStatusHistory = app.APPLICATION_STATUS_HISTORY.FirstOrDefault(ash => ash.APPLICATION_STATUS_ID == app.APPLICATION_STATUS_ID);

                    if (appStatusHistory == null)
                    {
                        //  Need to "snapshot" the current application status.
                        var newStatusHistory = new APPLICATION_STATUS_HISTORY
                        {
                            APPLICATION_ID = app.APPLICATION_ID,
                            APPLICATION_STATUS_ID = app.APPLICATION_STATUS_ID,
                            STATUS_DATE = app.APPLICATION_STATUS_DATE,
                            CREATED_BY = eligibilityDto.UpdatedBy,
                            CREATED_DATE = DateTime.Now,
                            UPDATED_BY = eligibilityDto.UpdatedBy,
                            UPDATED_DATE = DateTime.Now,
                            IS_ACTIVE = "Y"
                        };

                        context.APPLICATION_STATUS_HISTORY.Add(newStatusHistory);
                    }

                    //  Update the application's status to enrollment determination.
                    app.APPLICATION_STATUS_ID = (byte)ENUMS.enumApplicationStatus.Enrollment_Determination;
                    app.APPLICATION_STATUS_DATE = DateTime.Now;

                    context.SaveChanges();
                }
            }

            return new BaseApiResponse();
        }

        /// <summary>
        /// Updates the MSP_APPLICATION_ID for an E&D app
        /// </summary>
        /// <param name="endAppId"></param>
        /// <param name="mspAppId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public static BaseApiResponse StoreMspAppId(long endAppId, long mspAppId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var appDetails = context.APPLICATION_ELDERLY_DISABLED_DETAIL.FirstOrDefault(a => a.APPLICATION_ID == endAppId);
                if (appDetails != null)
                {
                    appDetails.MSP_APPLICATION_ID = mspAppId;
                    context.SaveChanges();
                    return new BaseApiResponse();
                }

                return new BaseApiResponse()
                {
                    IsSuccessful = false,
                    CaresError = CaresError.NotFound
                };
            }
        }

        /// TODO: TEMP!  Until MAC2 finishes their version
        public static BaseApiResponse EnrollMsp(ElderlyDisabledEligibilityDeterminationsDto eligibilityDto, bool mspEnrollCallSucceeded, Guid tokenId)
        {
            if (!mspEnrollCallSucceeded)
            {
                return new BaseApiResponse()
                {
                    IsSuccessful = false,
                    CaresError = CaresError.Unknown
                };
            }

            using (var context = new CaresApplicationDBEntities())
            {
                var latestMsp = context.APPLICATIONs.OrderByDescending(a => a.APPLICATION_ID).FirstOrDefault(a => a.SUB_PROGRAM_CATEGORY_ID == (byte)Cares.Api.Infrastructure.Constants.SubProgramCategories.MSP);
                if (latestMsp != null)
                {
                    return new BaseApiResponse()
                    {
                        Id = latestMsp.APPLICATION_ID
                    };
                }
            }

            return new BaseApiResponse()
            {
                IsSuccessful = false,
                CaresError = CaresError.NotFound
            };
        }

        /// <summary>
        /// Returns MSP Eligibility for E&D denial segment
        /// </summary>
        /// <param name="subprogramcategoryid"></param>
        /// <returns></returns>
        public static byte GetMspEligibilityForEandDDenials(byte? subprogramcategoryid)
        {
            byte subProgramCategoryReturnId = 0;
            if (ENUMS.ProgramSubCategory.EandDQmbCategories.Contains((enumProgramSubCategory)subprogramcategoryid))
            {
                subProgramCategoryReturnId = (byte)enumProgramSubCategory._95DOQMBOnly;
            }
            else if (ENUMS.ProgramSubCategory.EandDSlimbCategories.Contains((enumProgramSubCategory)subprogramcategoryid))
            {
                subProgramCategoryReturnId = (byte)enumProgramSubCategory._92DOSLIMBOnly;
            }
            return subProgramCategoryReturnId;
        }

        /// <summary>
        /// Completes the eligibility process.
        /// </summary>
        /// <param name="eligibilityDto">The eligibility data transformation object.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static BaseApiResponse EnrollComplete(ElderlyDisabledEligibilityDeterminationsDto eligibilityDto, string username, Guid tokenId)
        {
            //  Get current application status and check if it is a terminal status. Return an error if it is in a terminal status.
            if ((new ApplicationSnapshotDAL()).CheckApplicationIsInTerminalStatus(eligibilityDto.ApplicationId, tokenId))
            {
                return new BaseApiResponse
                {
                    IsSuccessful = false,
                    ErrorMessage = "The application is already in a terminal status."
                };
            }

            // We need to return whether an award or deny happened.
            // Per the ENUMS.enum: 0 = Unknown/Error, 1 = Award, 2 = (full) Deny
            Constants.AwardOrDeny isAwardOrDeny = Constants.AwardOrDeny.Unknown;
            var awardDeterminations = new List<ElderlyDisabledEligibilityDeterminationDto>();
            var denyDeterminations = new List<ElderlyDisabledEligibilityDeterminationDto>();
            long personId = eligibilityDto.ContactPersonId;

            eligibilityDto.CascadeSetAllUpdatedBy();

            foreach (var det in eligibilityDto.Determinations)
            {
                // Append to award List
                if (det.ProgramSubCategoryId.HasValue)
                {
                    // If Override, ensure all override fields are set:
                    if (det.OverrideReasonId.HasValue)
                    {
                        det.OverrideBy = det.OverrideBy ?? det.UpdatedBy;
                        det.OverrideDate = det.OverrideDate ?? DateTime.Now;
                    }
                    awardDeterminations.Add(det);
                }
                // Append to deny List
                if (det.DenialReasons.Count > 0)
                {
                    denyDeterminations.Add(det);
                }
            }

            if (denyDeterminations.Count > 0)
            {
                // Consolidation segments if needed.
                denyDeterminations = consolidateSegments(denyDeterminations, false).ToList();
                insertDenialSegments(denyDeterminations, eligibilityDto.ApplicationId, personId, eligibilityDto.UpdatedBy, eligibilityDto.UpdatedDate, awardDeterminations.Count != 0);

                //This is for inserting MSP awarded segment for E&D denied
                if (awardDeterminations.Count != 0)
                {
                    var awardMspForEandDDenialperiodsStartDates = denyDeterminations.Select(d => d.StartDate).Distinct().ToList();
                    var getAwardSegmentsForDenails = awardDeterminations.Where(ad => awardMspForEandDDenialperiodsStartDates.Contains(ad.CancelDate)).ToList();
                    foreach (var createmspsegments in getAwardSegmentsForDenails)
                    {
                        if (GetMspEligibilityForEandDDenials(createmspsegments.ProgramSubCategoryId) > 0)
                        {
                            var insertsegment = denyDeterminations.First(dd => dd.StartDate == createmspsegments.CancelDate);
                            ElderlyDisabledEligibilityDeterminationDto mspEnrollmentInsert = new ElderlyDisabledEligibilityDeterminationDto()
                            {
                                ApplicationId = insertsegment.ApplicationId,
                                StartDate = insertsegment?.StartDate,
                                CancelDate = insertsegment?.CancelDate,
                                ProgramSubCategoryId = GetMspEligibilityForEandDDenials(createmspsegments.ProgramSubCategoryId),
                                IsActive = insertsegment?.IsActive,
                            };
                            awardDeterminations.Add(mspEnrollmentInsert);

                            var context = new CaresApplicationDBEntities();
                            var currentPerson = context.sysnl_donotmodify__PERSON.FirstOrDefault(p => p.PERSON_ID == personId);

                            string denialreasontxt = string.Empty;
                            foreach (var denialreason in insertsegment.DenialReasons)
                            {
                                var subProgram = ENUMS.StateReasonCode.GetStateReasonCodeById(denialreason).Description;
                                denialreasontxt += "\n * " + subProgram;
                            }
                            var ProgramSubCategoryId = GetMspEligibilityForEandDDenials(createmspsegments.ProgramSubCategoryId);
                            var noteText = currentPerson.FIRST_NAME + " " + currentPerson.LAST_NAME + " flipped to '" + ENUMS.ProgramSubCategory.GetProgramSubCategoryByProgramSubCategoryId(ProgramSubCategoryId).Description + "' from '" + ENUMS.ProgramSubCategory.GetProgramSubCategoryByProgramSubCategoryId(createmspsegments.ProgramSubCategoryId).Description + "' due to following denial reason(s): " + denialreasontxt;
                            // Add a note by MSPFlipAdmin as "Applicant flipped to MSP Program from EnD Program due to Denial Reason(s)"
                            NotesDal.AddNote(eligibilityDto.ApplicationId, personId, ENUMS.enumNoteType.GENERAL_NOTE, noteText, "MSPFlipAdmin", tokenId);
                        }
                    }
                }

                // Is this a full denial?
                if (awardDeterminations.Count == 0)
                {
                    // Create denied note if application is denied.
                    addDeniedNote(denyDeterminations, eligibilityDto.ApplicationId, personId, username, tokenId);
                    // Send back that this was a denial:
                    isAwardOrDeny = Constants.AwardOrDeny.Deny;
                }
            }

            if (awardDeterminations.Count > 0)
            {
                // Consolidation segments if needed.
                awardDeterminations = consolidateSegments(awardDeterminations, true).ToList();
                insertAwardSegments(awardDeterminations, eligibilityDto.ApplicationId, eligibilityDto.UpdatedBy, eligibilityDto.UpdatedDate);
                addAwardNote(denyDeterminations, awardDeterminations, eligibilityDto.ApplicationId, personId, username, tokenId);
                isAwardOrDeny = Constants.AwardOrDeny.Award;

                // If a person doesn't have Medicaid Id, then generate one when an E&D enrollment is awarded
                GenerateMedicaidId(personId, username, tokenId);
            }
            // Update application status history
            saveAppStatusHistoryandUpdateApplicationsED(eligibilityDto.ApplicationId, username, awardDeterminations.Count != 0);

            // If E&D Enrollment is terminated/awarded for a closed period, then update the Liability segment's end date
            // to the month prior to the updated enrollment end date otherwise delete the liability segments
            if (isAwardOrDeny == Constants.AwardOrDeny.Award)
            {
                // Truncate the liability segments
                TruncateLiabilitySegments(awardDeterminations, personId, username, tokenId);
            }

            // If E&D enrollment is denied, then make sure to delete the liability segments
            if (isAwardOrDeny == Constants.AwardOrDeny.Deny)
            {
                // Add a note and delete the liability segments
                NotesDal.AddNote(eligibilityDto.ApplicationId, personId, ENUMS.enumNoteType.GENERAL_NOTE, NotesDescriptions.Messages.LiabilitySegmentsUpdatedNote, username, tokenId);
                DeleteLiabilitySegments(personId, eligibilityDto.ApplicationId);
            }

            syncEnrollments(eligibilityDto);
            return new BaseApiResponse()
            {
                Id = (int)isAwardOrDeny
            };
        }

        private static void syncEnrollments(ElderlyDisabledEligibilityDeterminationsDto eligibilityDto)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_SYNC_ELDERLY_DISABLED(eligibilityDto.ApplicationId, eligibilityDto.ContactPersonId);
            }

        }

        private static bool canConsolidateForAwardSegments(ElderlyDisabledEligibilityDeterminationDto a, ElderlyDisabledEligibilityDeterminationDto b)
        {
            if (a.IsInterimMSP == 1)
            {
                return a.ProgramSubCategoryId == b.ProgramSubCategoryId && a.OverrideReasonId == b.OverrideReasonId;
            }
            else
            {
                return a.AwardOption == b.AwardOption && a.OverrideReasonId == b.OverrideReasonId;
            }
        }

        private static bool canConsolidateForDenialSegments(ElderlyDisabledEligibilityDeterminationDto a, ElderlyDisabledEligibilityDeterminationDto b)
        {
            var denialReasonsA = new HashSet<short>(a.DenialReasons);
            foreach (var reasonId in b.DenialReasons)
            {
                if (!denialReasonsA.Contains(reasonId))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// Consolidate segments if needs.
        /// </summary>
        /// <param name="determinations">The determination list.</param>
        /// <param name="isAward">The isAward check.</param>
        /// <returns></returns>
        private static List<ElderlyDisabledEligibilityDeterminationDto> consolidateSegments(List<ElderlyDisabledEligibilityDeterminationDto> determinations, bool isAward)
        {
            // Order determinations ascending according to Start Date
            determinations = determinations.OrderBy(d => d.StartDate).ToList();
            var result = new List<ElderlyDisabledEligibilityDeterminationDto>();
            // Set up temp variable to keep track on potential consolidate segments
            var potentialConsolidateSegment = determinations[0];
            for (int i = 1; i < determinations.Count; i++)
            {
                if (determinations[i].IsInterimMSP == 0)
                {
                    if (isAward &&
                        canConsolidateForAwardSegments(potentialConsolidateSegment, determinations[i]) &&
                        potentialConsolidateSegment.CancelDate == determinations[i].StartDate)
                    {
                        potentialConsolidateSegment.CancelDate = determinations[i].CancelDate;
                        potentialConsolidateSegment.CancelReasonId = determinations[i].CancelReasonId;
                        potentialConsolidateSegment.ExparteProgramSubCategoryId = determinations[i].ExparteProgramSubCategoryId;
                    }
                    else if (!isAward &&
                        canConsolidateForDenialSegments(potentialConsolidateSegment, determinations[i]) &&
                        potentialConsolidateSegment.CancelDate == determinations[i].StartDate)
                    {
                        potentialConsolidateSegment.CancelDate = determinations[i].CancelDate;
                    }
                    else
                    {
                        result.Add(potentialConsolidateSegment);
                        potentialConsolidateSegment = determinations[i];
                    }
                }
                else
                {
                    result.Add(determinations[i]);
                }
            }
            // Add final consolidated segment
            result.Add(potentialConsolidateSegment);
            return result;
        }

        /// <summary>
        /// Insert denial segments into denial table.
        /// Insert eligibility table and update application info if isAward is false.
        /// </summary>
        /// <param name="denialSegments">The denial segments.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The contact person identifier.</param>
        /// <param name="createBy">The create by.</param>
        /// <param name="createDate">The create date.</param>
        /// <param name="isAward">The isAward check.</param>
        /// <returns></returns>
        private static void insertDenialSegments(List<ElderlyDisabledEligibilityDeterminationDto> denialSegments, long applicationId, long personId, string createBy, DateTime createDate, bool isAward)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                foreach (var detDeny in denialSegments)
                {
                    foreach (var reasons in detDeny.DenialReasons)
                    {
                        var newEligDeny = new APPLICATION_ELIGIBILITY_DENIAL();
                        SqlToDtoMapper.DtoToSql(detDeny, newEligDeny);
                        newEligDeny.STATE_REASON_ID = reasons;
                        newEligDeny.CREATED_BY = createBy;
                        newEligDeny.CREATED_DATE = createDate;

                        context.APPLICATION_ELIGIBILITY_DENIAL.Add(newEligDeny);
                    }
                }
                // In case there is no award segments at all.
                if (!isAward)
                {
                    var newEligwithAllDenialSegments = new APPLICATION_ELIGIBILITY()
                    {
                        APPLICATION_ID = applicationId,
                        PERSON_ID = personId,
                        PROGRAM_ID = (byte)ENUMS.enumProgram.DENIED,
                        CREATED_BY = createBy,
                        CREATED_DATE = createDate,
                        IS_ACTIVE = "Y"
                    };
                    context.APPLICATION_ELIGIBILITY.Add(newEligwithAllDenialSegments);
                }
                context.SaveChanges();
            }
        }

        /// <summary>
        /// Insert latest award segment into enrollment table and insert the rest award segments into previous enrollment table.
        /// Insert eligibility table and update application info.
        /// </summary>
        /// <param name="awardSegments">The denial segments.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="createBy">The create by.</param>
        /// <param name="createDate">The create date.</param>
        /// <returns></returns>
        private static void insertAwardSegments(List<ElderlyDisabledEligibilityDeterminationDto> awardSegments, long applicationId, string createBy, DateTime createDate)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var application = context.APPLICATIONs.Where(a => a.APPLICATION_ID == applicationId).FirstOrDefault();

                // Insert all segments into previous enrollment EXCEPT latest one.
                for (var i = 0; i < awardSegments.Count - 1; i++)
                {
                    var detAward = awardSegments[i];
                    var newEligPreviousAward = new APPLICATION_PREVIOUS_ENROLLMENT();
                    SqlToDtoMapper.DtoToSql(detAward, newEligPreviousAward);
                    newEligPreviousAward.PERSON_ID = application.CONTACT_PERSON_ID;
                    newEligPreviousAward.CREATED_BY = createBy;
                    newEligPreviousAward.CREATED_DATE = createDate;

                    context.APPLICATION_PREVIOUS_ENROLLMENT.Add(newEligPreviousAward);
                }
                var latestSegment = awardSegments[awardSegments.Count - 1];
                var newEligAward = new APPLICATION_ENROLLMENT();
                SqlToDtoMapper.DtoToSql(latestSegment, newEligAward);
                newEligAward.PERSON_ID = application.CONTACT_PERSON_ID;
                newEligAward.WORKER_COUNTY_NUMBER = application.WORKER_COUNTY_NUMBER;
                newEligAward.WORKER_NUMBER = application.WORKER_NUMBER;
                newEligAward.CREATED_BY = createBy;
                newEligAward.CREATED_DATE = createDate;
                newEligAward.DO_REVIEW_DATE = latestSegment.CancelDate?.AddMonths(-1);
                context.APPLICATION_ENROLLMENT.Add(newEligAward);

                // Whenever having award segment, then update PROGRAM_ID in APPLICATION_ELIGIBILITY Table as MEDICAID and update Application status as approved.
                // Insert latest into app eligibility table
                var newEligwithAwardSegments = new APPLICATION_ELIGIBILITY();
                SqlToDtoMapper.DtoToSql(latestSegment, newEligwithAwardSegments);

                newEligwithAwardSegments.PERSON_ID = application.CONTACT_PERSON_ID;
                newEligwithAwardSegments.PROGRAM_ID = (byte)ENUMS.enumProgram.MEDICAID;
                newEligwithAwardSegments.CREATED_BY = createBy;
                newEligwithAwardSegments.CREATED_DATE = createDate;
                context.APPLICATION_ELIGIBILITY.Add(newEligwithAwardSegments);

                // Insert latest into application enrollment history
                var newEligEnrollmentHistory = new APPLICATION_ENROLLMENT_HISTORY();
                SqlToDtoMapper.DtoToSql(latestSegment, newEligEnrollmentHistory);
                newEligEnrollmentHistory.PERSON_ID = application.CONTACT_PERSON_ID;
                newEligEnrollmentHistory.PROGRAM_ID = (byte)ENUMS.enumProgram.MEDICAID;
                newEligEnrollmentHistory.CREATED_BY = createBy;
                newEligEnrollmentHistory.CREATED_DATE = createDate;
                newEligEnrollmentHistory.DO_REVIEW_DATE = latestSegment.CancelDate?.AddMonths(-1);
                context.APPLICATION_ENROLLMENT_HISTORY.Add(newEligEnrollmentHistory);

                context.SaveChanges();
            }
        }

        /// <summary>
        /// Save application status history and update applications
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="userName">The user name.</param>
        /// <param name="isAward">The isAward check.</param>
        /// <returns></returns>
        private static void saveAppStatusHistoryandUpdateApplicationsED(long applicationId, string userName, bool isAward)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                // check if status history has enrollment determination record
                var app = context.APPLICATION_STATUS_HISTORY.Include(nameof(APPLICATION))
                    .FirstOrDefault(ash => ash.APPLICATION_ID == applicationId
                        && ash.APPLICATION_STATUS_ID == (byte)ENUMS.enumApplicationStatus.Enrollment_Determination);

                // if not, create status history record for enrollment determination
                if (app == null)
                {
                    context.usp_INSERT_APPLICATION_STATUS_HISTORY((int)applicationId, (byte)ENUMS.enumApplicationStatus.Enrollment_Determination, userName);
                    context.usp_UPDATE_APPLICATION_STATUS((int)applicationId, (byte)ENUMS.enumApplicationStatus.Enrollment_Determination, userName);
                }

                // create status history record for approved or denial
                if (isAward)
                {
                    context.usp_INSERT_APPLICATION_STATUS_HISTORY((int)applicationId, (byte)ENUMS.enumApplicationStatus.Approved, userName);
                    context.usp_UPDATE_APPLICATION_STATUS((int)applicationId, (byte)ENUMS.enumApplicationStatus.Approved, userName);
                }
                else
                {
                    context.usp_INSERT_APPLICATION_STATUS_HISTORY((int)applicationId, (byte)ENUMS.enumApplicationStatus.Denied, userName);
                    context.usp_UPDATE_APPLICATION_STATUS((int)applicationId, (byte)ENUMS.enumApplicationStatus.Denied, userName);
                }
            }
        }

        /// <summary>
        /// Add denial note for application
        /// </summary>
        /// <param name="denialSegments">The denial segments.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The contact person identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        private static void addDeniedNote(List<ElderlyDisabledEligibilityDeterminationDto> denialSegments, long applicationId, long personId, string username, Guid tokenId)
        {
            string fullName = string.Empty;
            // Get full name
            using (var context = new CaresApplicationDBEntities())
            {
                sysnl_donotmodify__PERSON currentPerson = context.sysnl_donotmodify__PERSON.FirstOrDefault(p => p.PERSON_ID == personId);
                fullName = PersonHelper.GetCombinedName(currentPerson.FIRST_NAME, currentPerson.MIDDLE_NAME, currentPerson.LAST_NAME, currentPerson.SUFFIX_ID);
            }
            // Get all denial reasons from denial segments
            var edDenialIds = new List<short>();
            foreach (var eachSeg in denialSegments)
            {
                foreach (var reason in eachSeg.DenialReasons)
                {
                    if (!edDenialIds.Contains(reason))
                    {
                        edDenialIds.Add(reason);
                    }
                }
            }
            addDenialSegmentNote(edDenialIds, applicationId, personId, fullName, string.Empty, username, tokenId);
            string description2ndNote = NotesDescriptions.ApprovedorDeniedApplicationNote(applicationId.ToString(), "Denied");
            NotesDal.AddNote(applicationId, personId, ENUMS.enumNoteType.COMPLETE, description2ndNote, username, tokenId);
        }

        /// <summary>
        /// Add award note for application
        /// </summary>
        /// <param name="denialSegments">The denial segments.</param>
        /// <param name="awardSegments">The award segments.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The contact person identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        private static void addAwardNote(List<ElderlyDisabledEligibilityDeterminationDto> denialSegments, List<ElderlyDisabledEligibilityDeterminationDto> awardSegments,
            long applicationId, long personId, string username, Guid tokenId)
        {
            var allSegments = new List<ElderlyDisabledEligibilityDeterminationDto>();
            string fullName = string.Empty;
            string programDesc = string.Empty;

            // Gets data from db
            using (var context = new CaresApplicationDBEntities())
            {
                // Get contact person full name
                var currentPerson = context.sysnl_donotmodify__PERSON.FirstOrDefault(p => p.PERSON_ID == personId);

                fullName = PersonHelper.GetCombinedName(currentPerson.FIRST_NAME, currentPerson.MIDDLE_NAME, currentPerson.LAST_NAME, currentPerson.SUFFIX_ID);

                // Get program name for current application
                var enDDetail = context.APPLICATION_ELDERLY_DISABLED_DETAIL.FirstOrDefault(ed => ed.APPLICATION_ID == applicationId);
                var enDProgramInfo = ENUMS.ElderlyDisabledProgram.GetProgramById(enDDetail.ELDERLY_DISABLED_PROGRAM_ID);

                programDesc = enDProgramInfo.Description;
            }

            // Combine all award & denial segments and sort them by start date
            allSegments.AddRange(awardSegments);
            allSegments.AddRange(denialSegments);
            allSegments = allSegments.OrderBy(s => s.StartDate).ToList();

            foreach (var segment in allSegments)
            {
                string fromStartDateToCancelDate = $"\nfrom {string.Format("{0:MM/dd/yyyy}", segment.StartDate)} to {string.Format("{0:MM/dd/yyyy}", segment.CancelDate)}";

                // if segment is denial segment. Add denial note
                if (segment.DenialReasons.Count > 0)
                {
                    addDenialSegmentNote(segment.DenialReasons, applicationId, personId, fullName, fromStartDateToCancelDate, username, tokenId);
                }

                // if segment is award segment. Add award note
                if (segment.ProgramSubCategoryId.HasValue || segment.AwardOption.HasValue)
                {
                    addAwardSegmentNote(segment, applicationId, personId, fullName, programDesc, fromStartDateToCancelDate, username, tokenId);
                }

                // Generate note for closed period segment.
                if ((segment.ProgramSubCategoryId.HasValue || segment.AwardOption.HasValue) && segment.CancelDate < segment.StartDate.Value.AddYears(1) && segment.CancelReasonId.HasValue)
                {
                    addClosedPeriodSegmentNote(segment, applicationId, personId, fullName, fromStartDateToCancelDate, username, tokenId);
                }
            }

            string description2ndNote = NotesDescriptions.ApprovedorDeniedApplicationNote(applicationId.ToString(), "Approved");

            NotesDal.AddNote(applicationId, personId, ENUMS.enumNoteType.COMPLETE, description2ndNote, username, tokenId);
        }

        /// <summary>
        /// E&D closed period segment note.
        /// </summary>
        /// <param name="segment">The elderly disabled eligibility determination dto.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="fullName">The full name.</param>
        /// <param name="fromDatetoDate">The from and to dates.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        private static void addClosedPeriodSegmentNote(ElderlyDisabledEligibilityDeterminationDto segment, long applicationId, long personId,
            string fullName, string fromDatetoDate, string username, Guid tokenId)
        {
            string subCategoryDesc = ENUMS.ProgramSubCategory.GetProgramSubCategoryDescByProgramSubCategoryId(segment.ProgramSubCategoryId);
            string terminationReasonDesc = RefTablesDAL.GetCancelReasonDescByCancelReasonId(segment.CancelReasonId ?? 0);

            string descriptionNote = NotesDescriptions.ClosedPeriodEdNote(fullName, subCategoryDesc, fromDatetoDate, terminationReasonDesc);
            NotesDal.AddNote(applicationId, personId, ENUMS.enumNoteType.ELIG_ENROLL_DETER, descriptionNote, username, tokenId);
        }

        /// <summary>
        /// E&D award period segment note.
        /// </summary>
        /// <param name="segment">The elderly disabled eligibility determination dto.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="fullName">The full name.</param>
        /// <param name="programDesc">The program description.</param>
        /// <param name="fromDatetoDate">The from and to dates.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        private static void addAwardSegmentNote(ElderlyDisabledEligibilityDeterminationDto segment, long applicationId, long personId,
            string fullName, string programDesc, string fromDatetoDate, string username, Guid tokenId)
        {
            string subCategoryDesc = string.Empty;
            string statusOverrideDesc = "Eligibility Determination";
            subCategoryDesc = ENUMS.ProgramSubCategory.GetProgramSubCategoryDescById(segment.ProgramSubCategoryId);
            if (segment.OverrideReasonId.HasValue)
            {
                string overrideReasonDesc = ENUMS.OverrideReason.GetOverrideReasonById(segment.OverrideReasonId)?.Description ?? string.Empty;
                statusOverrideDesc = "Override - " + overrideReasonDesc;
            }

            string descriptionNote = NotesDescriptions.ApprovedEdNote(applicationId.ToString(), statusOverrideDesc, fullName, programDesc, subCategoryDesc, fromDatetoDate);
            if (segment.IsInterimMSP == null)
            {
                descriptionNote = NotesDescriptions.ApprovedEdNote(applicationId.ToString(), statusOverrideDesc, fullName, "", subCategoryDesc, fromDatetoDate);
                descriptionNote = descriptionNote.Replace("under ", "");
            }

            NotesDal.AddNote(applicationId, personId, ENUMS.enumNoteType.ELIG_ENROLL_DETER, descriptionNote, username, tokenId);
        }

        /// <summary>
        /// E&D denial period segment note.
        /// </summary>
        /// <param name="denialReasons">The list of denial reasons.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="fullName">The full name.</param>
        /// <param name="fromDatetoDate">The from and to dates.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        private static void addDenialSegmentNote(List<short> denialReasons, long applicationId, long personId,
            string fullName, string fromDatetoDate, string username, Guid tokenId)
        {
            var denials = ENUMS.StateReasonCode.StateReasonCodes.Where(d => denialReasons.Contains(d.Id));
            var denialsDesc = new List<string>();
            foreach (var denial in denials)
            {
                denialsDesc.Add(denial.Description);
            }
            string denialMessages = string.Join("\n", denialsDesc);
            string description1stNote = NotesDescriptions.DeniedEdNote(fullName, denialMessages, fromDatetoDate);
            NotesDal.AddNote(applicationId, personId, ENUMS.enumNoteType.INELIGIBILITY_REASON, description1stNote, username, tokenId);
        }

        /// <summary>
        /// If E&D Nursing Home Enrollment is terminated/awarded for a closed period, then update the Liability segment's end date to the month prior to the updated enrollment end date
        /// </summary>
        /// <param name="enrollmentInfo">The list of elderly disabled eligibility determination dto.</param>
        /// <param name="personId">The contact person identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static void TruncateLiabilitySegments(List<ElderlyDisabledEligibilityDeterminationDto> enrollmentInfo, long personId, string username, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                // Filter terminated award. There cannot be more than one terminated award, hence selecting the segments by .FirstOrDefault()
                var closedPeriodAward = enrollmentInfo.Where(ad => ad.CancelReasonId != null && ad.CancelReasonId.HasValue && ad.CancelReasonId.Value > 0).FirstOrDefault();

                if (closedPeriodAward != null)
                {
                    // Terminate the liability segment end date based on the enrollment end date
                    if (closedPeriodAward.CancelDate.HasValue)
                    {
                        var firstOfPrevMonth = closedPeriodAward.CancelDate.Value.AddMonths(-1);

                        // Get liability info
                        // Note that 0 must be passed to the second argument. Needing all segments for this person
                        var personLiabilitySegments = getPersonLiabilityInfo(personId, 0, tokenId, context);

                        if (personLiabilitySegments != null && personLiabilitySegments.Count() > 0)
                        {
                            NotesDal.AddNote(closedPeriodAward.ApplicationId, personId, ENUMS.enumNoteType.GENERAL_NOTE, NotesDescriptions.Messages.LiabilitySegmentsUpdatedNote, username, tokenId);

                            foreach (var fls in personLiabilitySegments.OrderBy(pls => pls.LiabilityStartDate))
                            {
                                // Filter liability info that has start date less than or equal to the enrollment cancel date and future end date or open-ended end date
                                if (fls.LiabilityStartDate <= closedPeriodAward.CancelDate && (fls.LiabilityEndDate == null || fls.LiabilityEndDate >= closedPeriodAward.CancelDate))
                                {
                                    fls.LiabilityEndDate = firstOfPrevMonth;
                                    fls.PersonId = personId; //Person Id doesn't get populated from the getPersonLiabilityInfo function, hence mapping it manually in order to insert notes
                                    fls.UpdatedBy = username;
                                    fls.UpdatedDate = DateTime.Now;
                                    // Update liability info
                                    UpsertLiabilitySegment(fls, username, tokenId);
                                }
                                else if (fls.LiabilityStartDate > closedPeriodAward.CancelDate)
                                {
                                    // Delete future liability segment
                                    DeleteLiabilitySegment(fls.PersonLiabilityId, username);
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Update the Current & Delete the Future Liability Segments for Date of Death (DOD).
        /// </summary>
        /// <param name="dateOfDeath">The date of death date</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static void TruncateLiabilitySegments(DateTime dateOfDeath, long personId, string username, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                var endOfMonth = new DateTime(dateOfDeath.Year, dateOfDeath.Month, 1).AddMonths(1).AddDays(-1);

                // Get liability info
                // Note that 0 must be passed to the second argument. Needing all segments for this person.
                var personLiabilitySegments = getPersonLiabilityInfo(personId, 0, tokenId, context);

                if (personLiabilitySegments != null && personLiabilitySegments.Count() > 0)
                {
                    HashSet<long> appIds = new HashSet<long>();
                    foreach (var fls in personLiabilitySegments.OrderBy(pls => pls.LiabilityStartDate))
                    {
                        // Filter liability info that has start date less than or equal to the date of death and future end date or open-ended.
                        if (fls.LiabilityStartDate <= dateOfDeath && (fls.LiabilityEndDate == null || (fls.LiabilityEndDate.Value.Year >= dateOfDeath.Year && fls.LiabilityEndDate.Value.Month >= dateOfDeath.Month)))
                        {
                            fls.LiabilityEndDate = endOfMonth;
                            fls.PersonId = personId; // Person Id doesn't get populated from the getPersonLiabilityInfo function, hence mapping it manually in order to insert notes.
                            fls.UpdatedBy = username;
                            fls.UpdatedDate = DateTime.Now;
                            var firstliability = fls.LiabilityChangeCodes.First();
                            firstliability.LiabilityChangeCodeId = (byte)enumLiabilityChangeCode.TheClaimantIsDeceased;
                            fls.LiabilityChangeCodes = new List<ElderlyDisabledLiabilityDetailChangeCodeDto> { firstliability };
                            // Update liability info
                            UpsertLiabilitySegment(fls, username, tokenId);
                            appIds.Add(fls.OriginatingApplicationId);
                        }
                        else if (fls.LiabilityStartDate > dateOfDeath)
                        {
                            // Delete future liability segment
                            DeleteLiabilitySegment(fls.PersonLiabilityId, username);
                            appIds.Add(fls.OriginatingApplicationId);
                        }
                    }

                    // Insert notes for DOD.
                    foreach (var appId in appIds)
                    {
                        NotesDal.AddNote(appId, personId, ENUMS.enumNoteType.GENERAL_NOTE, NotesDescriptions.Messages.LiabilitySegmentsUpdatedNoteForDOD, username, tokenId);
                    }
                }
            }
        }

        /// <summary>
        /// Truncate the liability segments on DOD update.
        /// </summary>
        /// <param name="dateOfDeath">The date of death date</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static void TruncateLiabilitySegmentsOnDateOfDeathUpdate(DateTime dateOfDeath, long personId, string username, Guid tokenId)
        {
            if (dateOfDeath != default(DateTime))
            {
                using (var context = new CaresApplicationDBEntities())
                {
                    t_PERSON_DETAIL existingPersonDetail = context.t_PERSON_DETAIL.FirstOrDefault(p => p.PERSON_ID == personId);
                    var previousDateOfDeath = existingPersonDetail?.DATE_OF_DEATH;

                    if (previousDateOfDeath != dateOfDeath)
                    {
                        TruncateLiabilitySegments(dateOfDeath, personId, username, tokenId);
                    }
                }
            }
        }

        /// <summary>
        /// Truncate the Enrollment on DOD update.
        /// </summary>
        /// <param name="dateOfDeath">The date of death date</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static void TruncateEnrollmentOnDateOfDeathUpdate(DateTime dateOfDeath, long applicationId, long personId, string username, Guid tokenId)
        {
            if (dateOfDeath != default(DateTime))
            {
                using (var context = new CaresApplicationDBEntities())
                {
                    t_PERSON_DETAIL existingPersonDetail = context.t_PERSON_DETAIL.FirstOrDefault(p => p.PERSON_ID == personId);
                    var previousDateOfDeath = existingPersonDetail?.DATE_OF_DEATH;

                    if (previousDateOfDeath != dateOfDeath)
                    {
                        EnrollmentDal.TruncateElderlyAndMSPAppForDeceased(applicationId, personId, dateOfDeath, context, username, tokenId);
                    }
                }
            }
        }

        /// <summary>
        /// Generates a new Medicaid id for a person
        /// </summary>
        /// <param name="personId">The contact person identifier.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static void GenerateMedicaidId(long personId, string username, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_GENERATE_MEDICAID_ID_CHECK_DIGIT(personId, username);
            }
        }

        /// <summary>
        /// Returns providers from the REF_NH_PROVIDER table
        /// </summary>
        /// <returns></returns>
        public static ProvidersDto GetProviders()
        {
            ProvidersDto results = new ProvidersDto();

            using (var context = new CaresApplicationDBEntities())
            {
                var states = context.REF_STATE.ToDictionary(s => s.STATE_ID);

                List<REF_NH_PROVIDER> providers = context.REF_NH_PROVIDER.ToList();
                foreach (var p in providers)
                {
                    REF_STATE state = states[p.STATE_ID];
                    ProviderDto newP = new ProviderDto();
                    SqlToDtoMapper.SqlToDto(p, newP, mappingParams: SqlToDtoMapper.MappingParams.TrimStrings);
                    // Address mapping needs to be done separately:
                    newP.ProviderAddress.AddressLine1 = p.ADDRESS_LINE1.Trim();
                    newP.ProviderAddress.AddressLine2 = p.ADDRESS_LINE2.Trim();
                    newP.ProviderAddress.City = p.CITY.Trim();
                    newP.ProviderAddress.StateId = p.STATE_ID;
                    newP.ProviderAddress.StateName = state != null ? state.STATE_ABREV : string.Empty;
                    newP.ProviderAddress.ZipCode = p.ZIPCODE.Trim();
                    newP.ProviderAddress.CountyName = p.COUNTY_DESC.Trim();
                    results.Providers.Add(newP);
                }
            }

            return results;
        }

        /// <summary>
        /// Gets the expedite facilities and providers
        /// </summary>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ExpediteFacilityProvidersDto GetExpediteFacilityProviders(Guid tokenId)
        {
            ExpediteFacilityProvidersDto expediteFacilities = new ExpediteFacilityProvidersDto();

            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var dbExpediteFacilities = context.usp_SELECT_EXPEDITE_FACILITY_PROVIDER_INFO().ToList();

                if (dbExpediteFacilities != null)
                {
                    foreach (var ef in dbExpediteFacilities)
                    {
                        expediteFacilities.Facilities.Add(new ExpediteFacilityProviderDto()
                        {
                            Facility = new ExpediteFacilityDto()
                            {
                                ExpediteFacilityProviderId = ef.ExpediteFacilityProviderId,
                                FacilityCode = ef.FacilityCode,
                                FacilityName = ef.FacilityName,
                                FacilityAddress = new Address()
                                {
                                    AddressLine1 = ef.FacilityAddressLine1,
                                    AddressLine2 = ef.FacilityAddressLine2,
                                    City = ef.FacilityCity,
                                    StateName = ef.FacilityState,
                                    CountyName = ef.FacilityCounty,
                                    ZipCode = ef.FacilityZipcode
                                },
                                FacilityIsActive = ef.FacilityIsActive
                            },
                            Provider = new ProviderDto()
                            {
                                ProviderId = ef.ProviderId,
                                ProviderName = ef.ProviderName,
                                ProviderAddress = new Address()
                                {
                                    AddressLine1 = ef.ProviderAddressLine1,
                                    AddressLine2 = ef.ProviderAddressLine2,
                                    City = ef.ProviderCity,
                                    StateName = ef.ProviderState,
                                    ZipCode = ef.ProviderZipcode
                                }
                            },
                            DistrictOfficeCode = ef.DistrictOfficeCode,
                            DistrictOfficeName = ef.DistrictOfficeName,
                            HasPotentialMatch = ef.HasPotentialMatch
                        });
                    }
                }
            }

            return expediteFacilities;
        }

        /// <summary>
        /// Gets the expedite facilities.
        /// </summary>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static ExpediteFacilityProvidersDto GetExpediteFacilitiesByString(string searchText, Guid tokenId)
        {
            ExpediteFacilityProvidersDto expediteFacilities = new ExpediteFacilityProvidersDto();

            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var dbExpediteFacilities = context.usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_STRING(searchText).ToList();

                if (dbExpediteFacilities != null)
                {
                    foreach (var ef in dbExpediteFacilities)
                    {
                        expediteFacilities.Facilities.Add(new ExpediteFacilityProviderDto()
                        {
                            Facility = new ExpediteFacilityDto()
                            {
                                ExpediteFacilityProviderId = ef.ExpediteFacilityProviderId,
                                FacilityCode = ef.FacilityCode,
                                FacilityName = ef.FacilityName,
                                FacilityAddress = new Address()
                                {
                                    AddressLine1 = ef.FacilityAddressLine1,
                                    AddressLine2 = ef.FacilityAddressLine2,
                                    City = ef.FacilityCity,
                                    StateName = ef.FacilityState,
                                    CountyName = ef.FacilityCounty,
                                    ZipCode = ef.FacilityZipcode
                                },
                            },
                            DistrictOfficeCode = ef.DistrictOfficeCode,
                        });
                    }
                }
            }

            return expediteFacilities;
        }

        /// <summary>
        /// Returns a single ExpediteFacility, with provider match data
        /// </summary>
        /// <param name="expediteFacilityId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public static ExpediteFacilityDto GetExpediteFacilityById(int expediteFacilityId, Guid tokenId)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var queryResult = context.usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_ID(expediteFacilityId);
                string facilityJson = queryResult.FirstOrDefault();
                if (!string.IsNullOrEmpty(facilityJson))
                {
                    ExpediteFacilityDto result = JsonConvert.DeserializeObject<ExpediteFacilityDto>(facilityJson);
                    return result;
                }
                else
                {
                    return new ExpediteFacilityDto()
                    {
                        CaresError = CaresError.NotFound,
                        IsSuccessful = false
                    };
                }
            }
        }

        /// <summary>
        /// Saves ExpediteFacilityDto data
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public static bool UpdateExpediteFacility(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            performDataIntegrityCheckExpediteFacility(expediteFacility);

            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                string asJson = JsonConvert.SerializeObject(expediteFacility);
                context.usp_UPDATE_EXPEDITE_FACILITY_PROVIDER(asJson);
                // If it got here, there wasn't an error:
                return true;
            }
        }

        /// <summary>
        /// ONLY sets the PROVIDER_ID for a given Expedite Facility
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public static BaseApiMessage SetExpediteFacilityProvider(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            BaseApiMessage result = new BaseApiMessage();

            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var facility = context.REF_EXPEDITE_FACILITY_PROVIDER.FirstOrDefault(f => f.EXPEDITE_FACILITY_PROVIDER_ID == expediteFacility.ExpediteFacilityProviderId);
                if (facility != null)
                {
                    facility.PROVIDER_ID = expediteFacility.ProviderId;
                    facility.UPDATED_BY = username;
                    facility.UPDATED_DATE = DateTime.Now;
                    context.SaveChanges();
                }
                else
                {
                    result.CaresError = CaresError.NotFound;
                    result.IsSuccessful = false;
                }
            }

            return result;
        }

        /// <summary>
        /// Saves the expedite facility.
        /// </summary>
        /// <param name="expediteFacilityInfo">The expedite facility information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool InsertExpediteFacility(ExpediteFacilityDto expediteFacilityInfo, string username, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_INSERT_REF_EXPEDITE_FACILITY_PROVIDER(expediteFacilityInfo.FacilityAddress.CountyId,
                    expediteFacilityInfo.FacilityAddress.StateId,
                    expediteFacilityInfo.DistrictOfficeId,
                    expediteFacilityInfo.FacilityName,
                    expediteFacilityInfo.FacilityAddress.AddressLine1,
                    expediteFacilityInfo.FacilityAddress.AddressLine2,
                    expediteFacilityInfo.FacilityAddress.City,
                    expediteFacilityInfo.FacilityAddress.ZipCode,
                    expediteFacilityInfo.ANHAHospital,
                    username);

                return true;
            }
        }

        /// <summary>
        /// Saves the financial institution.
        /// </summary>
        /// <param name="financialInstitutionInfo">The financial institution information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static int InsertFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, string username, Guid tokenId)
        {
            ObjectParameter returnId = new ObjectParameter("bankId", typeof(Int32));
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_INSERT_BANK(financialInstitutionInfo.BankAddress.CountyId,
                    financialInstitutionInfo.BankAddress.StateId,
                    financialInstitutionInfo.BankCode,
                    financialInstitutionInfo.BankName,
                    financialInstitutionInfo.BankAddress.AddressLine1,
                    financialInstitutionInfo.BankAddress.AddressLine2,
                    financialInstitutionInfo.BankAddress.City,
                    financialInstitutionInfo.BankAddress.ZipCode,
                    username,
                    returnId
                    );

                return (int)returnId.Value;
            }
        }

        /// <summary>
        /// Get the financial institution based on BankCode or BankName.
        /// </summary>
        /// <param name="searchText">Search info</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static FinancialInstitutionsDto GetFinancialInstitutionsByString(string searchText, Guid tokenId)
        {
            FinancialInstitutionsDto FinancialInstitutions = new FinancialInstitutionsDto();
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var dbFinancialInstitutions = context.usp_SELECT_BANK_BY_CODE_OR_BY_NAME(searchText);
                if (dbFinancialInstitutions != null)
                {
                    foreach (var eachFI in dbFinancialInstitutions)
                    {
                        var newFinancialInstitution = new FinancialInstitutionDto()
                        {
                            BankId = eachFI.BankId,
                            BankCode = eachFI.BankCode,
                            BankName = eachFI.BankName,
                            BankAddress = new Address()
                            {
                                AddressLine1 = eachFI.BankAddressLine1,
                                AddressLine2 = eachFI.BankAddressLine2,
                                City = eachFI.BankCity,
                                ZipCode = eachFI.BankZipcode,
                                StateId = eachFI.BankStateId,
                                CountyId = eachFI.BankCountyId,
                                StateName = eachFI.BankStateAbrev,
                                CountyName = eachFI.BankCounty
                            }
                        };
                        FinancialInstitutions.FinancialInstitutions.Add(newFinancialInstitution);
                    }
                }
            }
            return FinancialInstitutions;
        }

        /// <summary>
        /// Get the financial institution based on BankId or Get all if BankId is null.
        /// </summary>
        /// <param name="bankId">search by bankId</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static FinancialInstitutionsDto GetFinancialInstitutions(int? bankId, Guid tokenId)
        {
            FinancialInstitutionsDto FinancialInstitutions = new FinancialInstitutionsDto();
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var dbFinancialInstitutions = context.usp_GET_BANK_DATA(bankId);
                if (dbFinancialInstitutions != null)
                {
                    foreach (var eachFI in dbFinancialInstitutions)
                    {
                        var newFinancialInstitution = new FinancialInstitutionDto()
                        {
                            BankId = eachFI.BANK_ID,
                            BankCode = eachFI.BANK_CODE,
                            BankName = eachFI.BANK_NAME,
                            BankAddress = new Address()
                            {
                                AddressLine1 = eachFI.ADDRESS_LINE1,
                                AddressLine2 = eachFI.ADDRESS_LINE2,
                                City = eachFI.CITY,
                                ZipCode = eachFI.ZIPCODE,
                                StateId = eachFI.STATE_ID,
                                StateName = eachFI.STATE_ABREV,
                                CountyId = eachFI.COUNTY_ID,
                                CountyName = eachFI.COUNTY_NAME
                            },
                            BankClosedDate = eachFI.BANK_CLOSED_DATE,
                            BankMergeDate = eachFI.BANK_MERGE_DATE,
                            MergedToBankId = eachFI.MERGED_TO_BANK_ID,
                            MergedBankName = eachFI.MERGED_BANK_NAME,
                            BankIsActive = eachFI.IS_ACTIVE,
                            WasMerged = eachFI.WAS_MERGED,
                            DateLastSent = eachFI.DATE_LAST_SENT,
                            CreatedBy = eachFI.CREATED_BY,
                            CreatedDate = eachFI.CREATED_DATE,
                            UpdatedBy = eachFI.UPDATED_BY,
                            UpdatedDate = eachFI.UPDATED_DATE
                        };
                        FinancialInstitutions.FinancialInstitutions.Add(newFinancialInstitution);
                    }
                }
            }
            return FinancialInstitutions;
        }

        /// <summary>
        /// Get the last Exparte application info.
        /// </summary>
        /// <param name="personId"></param>
        /// <returns></returns>
        public static ExparteApplicationInfoDto GetExparteAppInfoByPersonId(int personId)
        {
            try
            {
                var exparteInfo = new ExparteApplicationInfoDto();
                using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
                {
                    var result = context.usp_SELECT_EXPARTE_APP_BY_PERSON_ID(personId).FirstOrDefault();
                    if (result != null)
                    {
                        exparteInfo.ApplicationId = result.APPLICATION_ID;
                        exparteInfo.ContactPersonId = result.CONTACT_PERSON_ID;
                        exparteInfo.CreatedDate = result.CREATED_DATE;
                        exparteInfo.ProgramSubCategoryId = result.PROGRAM_SUB_CATEGORY_ID;
                    }
                }
                return exparteInfo;
            }
            catch (Exception ex)
            {
                return new ExparteApplicationInfoDto()
                {
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }

        }

        /// <summary>
        /// Saves the financial institution.
        /// </summary>
        /// <param name="financialInstitutionInfo">The financial institution information.</param>
        /// <param name="MergeFlag">Merge or not merge</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static bool UpdateFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, bool MergeFlag, string username, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                context.usp_UPDATE_BANK_INFO(financialInstitutionInfo.BankId,
                    financialInstitutionInfo.BankAddress.StateId,
                    financialInstitutionInfo.BankAddress.CountyId,
                    financialInstitutionInfo.BankName,
                    financialInstitutionInfo.BankAddress.AddressLine1,
                    financialInstitutionInfo.BankAddress.AddressLine2,
                    financialInstitutionInfo.BankAddress.City,
                    financialInstitutionInfo.BankAddress.ZipCode,
                    financialInstitutionInfo.BankIsActive,
                    financialInstitutionInfo.BankClosedDate,
                    financialInstitutionInfo.DateLastSent,
                    username,
                    MergeFlag
                    );
                return true;
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Creates a new ElderlyDisabledPerson.  It will both set the ID on the incoming object, and return that id
        /// </summary>
        /// <param name="newPerson">The new person.</param>
        /// <returns></returns>
        private static long createNewElderlyDisabledPerson(ElderlyDisabledPerson newPerson)
        {
            using (CaresApplicationDBEntities context = new CaresApplicationDBEntities())
            {
                var newDbPerson = context.sysnl_donotmodify__PERSON.Create();
                SqlToDtoMapper.DtoToSql(newPerson, newDbPerson);
                newDbPerson.CREATED_BY = newPerson.UpdatedBy;
                newDbPerson.CREATED_DATE = newPerson.UpdatedDate;
                context.sysnl_donotmodify__PERSON.Add(newDbPerson);
                context.SaveChanges();
                newPerson.PersonId = newDbPerson.PERSON_ID;

                insertElderlyDisabledPersonDetail(context, newDbPerson.PERSON_ID, newPerson.UpdatedBy);

                return newDbPerson.PERSON_ID;
            }
        }

        /// <summary>
        /// Performs find on a person, by Patient Matching, and by strict SSN search.
        /// This will return Drastic Alert errors when necessary
        /// </summary>
        /// <param name="newPerson">The new person.</param>
        /// <param name="phoneList">The phone list.</param>
        /// <param name="addressList">The address list.</param>
        /// <param name="email">The email.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <param name="newPersonId">The new identifier.</param>
        /// <param name="notesList">The note list.</param>
        /// <returns></returns>
        public static CaresError FindElderlyDisabledPerson(ElderlyDisabledPerson newPerson, List<PhoneAndPersonPhone> phoneList,
            PersonAddresses addressList, string email, Guid tokenId,
            out long newPersonId, List<NotesDal.InsertNotesEntry> notesList = null)
        {
            CaresError returnValue = CaresError.None;
            newPersonId = 0;
            notesList = notesList != null ? notesList : new List<NotesDal.InsertNotesEntry>();

            // Search on the person, both by patient matching, and by strictly SSN
            // First, by Patient Matching:
            string phone1 = phoneList.Count() > 0 ? phoneList[0].PhoneNo : string.Empty;
            string phone2 = phoneList.Count() > 1 ? phoneList[1].PhoneNo : string.Empty;
            PersonAddress homeAddress = addressList.Addresses.FirstOrDefault(a => a.AddressTypeId == RefValues.AddressType.HOME_ADDRESS_TYPE);
            PersonAddress mailingAddress = addressList.Addresses.FirstOrDefault(a => a.AddressTypeId == RefValues.AddressType.MAIL_ADDRESS_TYPE);
            long patientMatchId = PersonDal.PersonPatientMatching(newPerson.FirstName, newPerson.LastName, newPerson.DateOfBirth, newPerson.Ssn, newPerson.MiddleName,
                email, phone1, phone2, mailingAddress?.Address?.AddressLine1, mailingAddress?.Address?.AddressLine2, mailingAddress?.Address?.City
                , (mailingAddress?.Address?.StateId ?? 0).ToString() // check on this one
                , mailingAddress?.Address?.ZipCode, homeAddress?.Address?.AddressLine1, homeAddress?.Address?.AddressLine2, homeAddress?.Address?.City
                , (homeAddress?.Address?.StateId ?? 0).ToString() // Check on this
                , homeAddress?.Address?.ZipCode);

            // Search strictly by SSN:
            sysnl_donotmodify__PERSON personBySsn = PersonDal.GetPersonBySsn(newPerson.Ssn);

            // Found matches, either way?
            if (patientMatchId != 0 || personBySsn != null)
            {
                // Found something.
                // Did Patient Match find someone?
                if (patientMatchId != 0)
                {
                    // No conflict with strict SSN search?
                    if (personBySsn == null || personBySsn.PERSON_ID == patientMatchId)
                    {
                        newPersonId = patientMatchId;
                        newPerson.PersonId = patientMatchId;
                        // Also update the located person with this data
                        bool dataChangeRequiringMmisUpdate = false;
                        UpdateElderlyDisabledPerson(newPerson, notesList, ref dataChangeRequiringMmisUpdate, tokenId);
                    }
                    else
                    {
                        // Search mismatch.
                        returnValue = CaresError.DrasticAlert;
                    }
                }
                else
                {
                    // Found an existing SSN, but did not fully match that person
                    returnValue = CaresError.DrasticAlert;
                }
            }

            return returnValue;
        }

        /// <summary>
        /// Call to either find, or create a new person.  Performs matching on the
        /// person prior to creating a new one.
        /// Will return DrasticAlert errors when necessary
        /// </summary>
        /// <param name="newPerson">The new person.</param>
        /// <param name="phoneList">The phone list.</param>
        /// <param name="addressList">The address list.</param>
        /// <param name="email">The email.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <param name="newPersonId">The new identifier.</param>
        /// <param name="notesList">The note list.</param>
        /// <returns></returns>
        public static CaresError FindOrCreateNewElderlyDisabledPerson(ElderlyDisabledPerson newPerson, List<PhoneAndPersonPhone> phoneList,
            PersonAddresses addressList, string email, string username, Guid tokenId, out long newPersonId, List<NotesDal.InsertNotesEntry> notesList = null)
        {
            notesList = notesList != null ? notesList : new List<NotesDal.InsertNotesEntry>();
            newPersonId = 0;
            CaresError returnValue = FindElderlyDisabledPerson(newPerson, phoneList, addressList, email, tokenId, out newPersonId, notesList);

            if (returnValue == CaresError.None)
            {
                if (newPersonId == 0)
                {
                    // No matches, safe to create a new person
                    newPersonId = createNewElderlyDisabledPerson(newPerson);
                }
                else
                {
                    // An existing person was found.  Ensure they have a t_person_detail
                    using (var context = new CaresApplicationDBEntities())
                    {
                        long tempPersonId = newPersonId;
                        t_PERSON_DETAIL existingPersonDetail = context.t_PERSON_DETAIL.FirstOrDefault(p => p.PERSON_ID == tempPersonId);

                        //  Does not exist, insert one
                        if (existingPersonDetail == null)
                        {
                            existingPersonDetail = insertElderlyDisabledPersonDetail(context, tempPersonId, username);
                        }
                    }
                }
            }

            return returnValue;
        }

        /// <summary>
        /// Get liability information to display on landing screen for E&D section.
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public static List<ElderlyDisabledLiabilityDetailDto> getElderlyDisabledPersonLiabilityInfo(long personId, Guid tokenId)
        {
            List<ElderlyDisabledLiabilityDetailDto> lstLiability = new List<ElderlyDisabledLiabilityDetailDto>();
            using (var context = new CaresApplicationDBEntities())
            {
                lstLiability = getPersonLiabilityInfo(personId, 0, tokenId, context);
            }

            return lstLiability;
        }

        /// <summary>
        /// Get applicant info for liability screen.
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <param name="context">The context.</param>
        /// <returns></returns>
        private static PersonInfoDto getApplicantInfo(long personId, Guid tokenId, CaresApplicationDBEntities context)
        {
            PersonInfoDto applicantInfo = new PersonInfoDto();

            // Get applicant info
            var results = GetEdPersonByPersonId(personId);
            if (results != null)
            {
                applicantInfo.FirstName = results.FirstName;
                applicantInfo.MiddleName = results.MiddleName;
                applicantInfo.LastName = results.LastName;
                applicantInfo.Suffix = ENUMS.SuffixType.GetSuffixTypeDescById(results.SuffixId)?.Description ?? string.Empty;
            }

            // Get applicant full mailing address
            var applicantMailingAddress = (from a in context.vw_ADDRESS
                                           where a.PERSON_ID == personId && a.ADDRESS_TYPE_ID == (int)ENUMS.enumAddressType.Mailing
                                           select a).FirstOrDefault();

            if (applicantMailingAddress != null)
            {
                applicantInfo.FullAddress = ApplicationDal.BuildFullAddress(applicantMailingAddress);
            }

            return applicantInfo;
        }

        /// <summary>
        /// Get the Primary Sponsor info for liability screen.
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        private static List<RepresentativeInfoDto> getPrimarySponsorInfo(long personId, Guid tokenId)
        {
            List<RepresentativeInfoDto> representativeInfo = new List<RepresentativeInfoDto>();
            var sponsorList = new Representatives.RepresentativeDAL().GetSponsorInformation(personId);
            representativeInfo = sponsorList.Where(s => (s.SponsorRole ?? 0) == (byte)ENUMS.enumSponsorRole.Primary).Select(s => new RepresentativeInfoDto()
            {
                FirstName = s.FirstName,
                MiddleName = s.MiddleName,
                LastName = s.LastName,
                Suffix = ENUMS.SuffixType.GetSuffixTypeDescById(s.SuffixId)?.Description ?? string.Empty,
                FullAddress = ApplicationDal.BuildFullAddress(new AddressInfo()
                {
                    AddressLine1 = s.AddressLine1,
                    AddressLine2 = s.AddressLine2,
                    City = s.City,
                    State = ENUMS.UsState.GetStateAbbreviationFromId((short)s.StateId),
                    Zipcode = s.Zipcode,
                }),
                SponsorRole = ENUMS.SponsorRole.GetSponsorRoleById(s.SponsorRole)?.Description ?? string.Empty
            }).ToList();

            return representativeInfo;
        }

        /// <summary>
        /// Gets Liability Test data
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="personLiabilityId"></param>
        /// <param name="tokenId"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        private static List<ElderlyDisabledLiabilityTestDto> getLiabilityTestData(long personId, Guid tokenId, CaresApplicationDBEntities context)
        {
            List<ElderlyDisabledLiabilityTestDto> returnValue = new List<ElderlyDisabledLiabilityTestDto>();

            var liabilityListEntries = context.PERSON_LIABILITY_TEST.Where(l => l.PERSON_ID == personId).OrderByDescending(l => l.EFFECTIVE_DATE);

            foreach (var dbEntry in liabilityListEntries)
            {
                ElderlyDisabledLiabilityTestDto newEntry = new ElderlyDisabledLiabilityTestDto();
                SqlToDtoMapper.SqlToDto(dbEntry, newEntry);
                returnValue.Add(newEntry);
            }

            return returnValue;
        }

        /// <summary>
        /// Gets liability info for a person
        /// </summary>
        /// <param name="personId">Set this to return all rows for a person</param>
        /// <param name="personLiabilityId">Set this to return a single row</param>
        /// <param name="tokenId"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        private static List<ElderlyDisabledLiabilityDetailDto> getPersonLiabilityInfo(long personId, long personLiabilityId, Guid tokenId, CaresApplicationDBEntities context)
        {
            List<ElderlyDisabledLiabilityDetailDto> lstLiability = new List<ElderlyDisabledLiabilityDetailDto>();
            ObjectParameter liabilityInfo = new ObjectParameter("LiabilityInfo", typeof(string));
            var result = context.usp_SELECT_LIABILITY_INFORMATION(personId, personLiabilityId, liabilityInfo);
            if (liabilityInfo.Value != System.DBNull.Value)
            {
                lstLiability = JsonConvert.DeserializeObject<List<ElderlyDisabledLiabilityDetailDto>>(liabilityInfo.Value.ToString());
                // These need to be set for automated tests:
                foreach (var l in lstLiability)
                {
                    l.PersonId = personId;
                    l.ContactPersonId = personId;
                }
            }
            return lstLiability;
        }

        /// <summary>
        /// Perform Data Integrity Check
        /// </summary>
        /// <param name="application">The application.</param>
        private static void performDataIntegrityCheckApplication(ElderlyDisabledApplicationDto application)
        {
            // Elderly Disabled Program Info
            switch (application.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId)
            {
                case (byte)ENUMS.enumElderlyDisabledMedicaidPrograms.Hospital:
                case (byte)ENUMS.enumOtherProgram.ChildrenUnder21YearsofAgeinaPsychiatricFacility:
                    // Reset Nursing Home Info
                    application.ApplicationElderlyDisabledDetail.NursingHomeId = null;
                    application.ApplicationElderlyDisabledDetail.NursingHomeAdmissionDate = DateTime.MinValue;
                    // Reset SSI Related Program Info
                    application.ApplicationElderlyDisabledDetail.SsiRelatedProgramId = null;
                    break;
                case (byte)ENUMS.enumElderlyDisabledMedicaidPrograms.NursingHome:
                case (byte)ENUMS.enumOtherProgram.GeriatricPsychiatric:
                case (byte)ENUMS.enumOtherProgram.GrandfatheredNursingHomeMedicaid:
                case (byte)ENUMS.enumOtherProgram.IntermediateCareFacilityfortheIntellectuallyDisabled:
                case (byte)ENUMS.enumOtherProgram.PostHospitalExtendedCarePEC:
                    // Reset Hospital Info
                    application.ApplicationElderlyDisabledDetail.HospitalId = null;
                    application.ApplicationElderlyDisabledDetail.HospitalAdmissionDate = DateTime.MinValue;
                    // Reset SSI Related Program Info
                    application.ApplicationElderlyDisabledDetail.SsiRelatedProgramId = null;
                    break;
                case (byte)ENUMS.enumElderlyDisabledMedicaidPrograms.HomeAndCommunityBasedWaiverProgram:
                    // Reset Hospital Info
                    application.ApplicationElderlyDisabledDetail.HospitalId = null;
                    application.ApplicationElderlyDisabledDetail.HospitalAdmissionDate = DateTime.MinValue;
                    // Reset Nursing Home Info
                    application.ApplicationElderlyDisabledDetail.NursingHomeId = null;
                    application.ApplicationElderlyDisabledDetail.NursingHomeAdmissionDate = DateTime.MinValue;
                    // Reset SSI Related Program Info
                    application.ApplicationElderlyDisabledDetail.SsiRelatedProgramId = null;
                    break;
                case (byte)ENUMS.enumElderlyDisabledMedicaidPrograms.SSIRelatedPrograms:
                    // Reset Hospital Info
                    application.ApplicationElderlyDisabledDetail.HospitalId = null;
                    application.ApplicationElderlyDisabledDetail.HospitalAdmissionDate = DateTime.MinValue;
                    // Reset Nursing Home Info
                    application.ApplicationElderlyDisabledDetail.NursingHomeId = null;
                    application.ApplicationElderlyDisabledDetail.NursingHomeAdmissionDate = DateTime.MinValue;
                    break;
                default:
                    // Reset Hospital Info
                    application.ApplicationElderlyDisabledDetail.HospitalId = null;
                    application.ApplicationElderlyDisabledDetail.HospitalAdmissionDate = DateTime.MinValue;
                    // Reset Nursing Home Info
                    application.ApplicationElderlyDisabledDetail.NursingHomeId = null;
                    application.ApplicationElderlyDisabledDetail.NursingHomeAdmissionDate = DateTime.MinValue;
                    // Reset SSI Related Program Info
                    application.ApplicationElderlyDisabledDetail.SsiRelatedProgramId = null;
                    break;
            }

            // Date of Death Verification Data
            if (!application.PersonDetail.DateOfDeath.HasValue)
            {
                application.PersonDetail.DeathVerificationDate = null;
                application.PersonDetail.DeathVerificationSource = null;
            }

            if (!application.PersonDetail.DeathVerificationDate.HasValue)
            {
                application.PersonDetail.DeathVerificationSource = null;
            }

            // Races
            if (!application.PersonRaces.IsOther)
            {
                application.PersonRaces.OtherRace = null;
            }

            // Phones
            if (application.PersonPhones != null)
            {
                foreach (var pp in application.PersonPhones)
                {
                    if (pp.PhoneTypeId != (byte)ENUMS.enumPhoneType.Other)
                    {
                        pp.OtherPhoneOwner = null;
                    }
                }
            }

            // Living Arrangements
            switch (application.ApplicationLivingArrangement.LivingArrangementId)
            {
                case (short)ENUMS.enumLivingArrangement.InARentedHouseApartmentOrRoom:
                    application.ApplicationLivingArrangement.LivingArrangementPayUtilitiesFood = null;
                    application.ApplicationLivingArrangement.LivingArrangementOtherArrangement = null;
                    break;
                case (short)ENUMS.enumLivingArrangement.WithSomeoneElseNotInYourOwnHome:
                    application.ApplicationLivingArrangement.LivingArrangementRentAmount = null;
                    application.ApplicationLivingArrangement.LivingArrangementOtherArrangement = null;
                    break;
                case (short)ENUMS.enumLivingArrangement.Other:
                    application.ApplicationLivingArrangement.LivingArrangementRentAmount = null;
                    application.ApplicationLivingArrangement.LivingArrangementPayUtilitiesFood = null;
                    break;
                default:
                    application.ApplicationLivingArrangement.LivingArrangementRentAmount = null;
                    application.ApplicationLivingArrangement.LivingArrangementPayUtilitiesFood = null;
                    application.ApplicationLivingArrangement.LivingArrangementOtherArrangement = null;
                    break;
            }

            // Residency Info
            if (application.Person.IsUsCitizen != null && application.Person.IsUsCitizen.Value)
            {
                application.ApplicationResidencyInformation.DateEnteredUs = null;
                application.ApplicationDetail.ManCitVerDocId = null;
            }

            // Spoken Language
            if (application.PersonContactPreference.SpokenLanguageId != (short)ENUMS.enumSpokenLanguage.Other)
            {
                application.PersonContactPreference.OtherSpokenLanguage = null;
            }

            // SSI Info
            if (application.ApplicationDetail.HasAppliedOrRecievedSsi != null && !application.ApplicationDetail.HasAppliedOrRecievedSsi.Value)
            {
                application.ApplicationDetail.SsiAppliedReceivedDate = null;
            }
        }

        /// <summary>
        /// Performs the data integrity check property.
        /// </summary>
        /// <param name="property">The property.</param>
        private static void performDataIntegrityCheckProperty(ElderlyDisabledPropertyDto property)
        {
            //  TODO (MAYBE): Streamline this by maybe using a base class?? This would cause a lot of shift in the supporting code.
            //  Ensure the county information aligns with the state information.
            foreach (var mobileHome in property.PropertyMobileHomes)
            {
                if (mobileHome.MobileHomeStateId == (short)ENUMS.enumState.Alabama)
                {
                    mobileHome.MobileHomeOutOfStateCountyName = string.Empty;
                }
                else
                {
                    mobileHome.MobileHomeInStateCountyId = null;
                }

                //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                mobileHome.LandownerName = mobileHome.LandownerName ?? string.Empty;
                mobileHome.MobileHomeValueAmount = mobileHome.MobileHomeValueAmount ?? 0;
                mobileHome.MobileHomeLandAddressLine1 = mobileHome.MobileHomeLandAddressLine1 ?? string.Empty;
                mobileHome.MobileHomeLandAddressLine2 = mobileHome.MobileHomeLandAddressLine2 ?? string.Empty;
                mobileHome.MobileHomeLandCity = mobileHome.MobileHomeLandCity ?? string.Empty;
                mobileHome.MobileHomeLandZipCode = mobileHome.MobileHomeLandZipCode ?? string.Empty;
                mobileHome.MobileHomeOutOfStateCountyName = mobileHome.MobileHomeOutOfStateCountyName ?? string.Empty;

                //  Clear reason if the no excluded resource indicator(no value or false).
                if (!mobileHome.MobileHomeHasExcludedResource.HasValue || !mobileHome.MobileHomeHasExcludedResource.Value)
                {
                    mobileHome.MobileHomeExcludedResourceId = null;
                    mobileHome.MobileHomeOtherExcludedResourceDesc = string.Empty;
                }
                // Clear reason description if the MobileHomeExcludedResourceId is not 'Other'
                else if (mobileHome.MobileHomeExcludedResourceId != (byte)ENUMS.enumExcludedResource.Other)
                {
                    mobileHome.MobileHomeOtherExcludedResourceDesc = string.Empty;
                }

                if (mobileHome.EDPropertyMobileHomeDetail != null)
                {
                    // Data integrity check for mobile home detail
                    foreach (var mobileHomeDetail in mobileHome.EDPropertyMobileHomeDetail)
                    {
                        mobileHomeDetail.MobileHomeValueAmount = mobileHomeDetail.MobileHomeValueAmount ?? 0;

                        //  Clear reason if the no excluded resource indicator(no value or false).
                        if (!mobileHomeDetail.MobileHomeHasExcludedResource.HasValue || !mobileHomeDetail.MobileHomeHasExcludedResource.Value)
                        {
                            mobileHomeDetail.MobileHomeExcludedResourceId = null;
                            mobileHomeDetail.MobileHomeOtherExcludedResourceDesc = string.Empty;
                        }
                        // Clear reason description if the MobileHomeDetailExcludedResourceId is not 'Other'
                        else if (mobileHomeDetail.MobileHomeExcludedResourceId != (byte)ENUMS.enumExcludedResource.Other)
                        {
                            mobileHomeDetail.MobileHomeOtherExcludedResourceDesc = string.Empty;
                        }
                    }
                }
            }

            foreach (var parcel in property.PropertyParcels)
            {
                if (parcel.ParcelStateId == (short)ENUMS.enumState.Alabama)
                {
                    parcel.ParcelOutOfStateCountyName = string.Empty;
                }
                else
                {
                    parcel.ParcelInStateCountyId = null;
                }

                //  Clear tenant information if the no one lives there now (no value or false)
                if (!parcel.DoesSomeoneLiveThereNow.HasValue || !parcel.DoesSomeoneLiveThereNow.Value)
                {
                    parcel.TenantRelationshipTypeId = null;
                    parcel.TenantName = string.Empty;
                    parcel.TenantOtherRelationshipDesc = string.Empty;
                }

                //  Clear reason if the no excluded resource indicator(no value or false).
                if (!parcel.HasExcludedResource.HasValue || !parcel.HasExcludedResource.Value)
                {
                    parcel.ParcelExcludedResourceId = null;
                    parcel.ParcelOtherExcludedResourceDesc = string.Empty;
                }
                // Clear reason description if the ParcelExcludedResourceId is not 'Other'
                else if (parcel.ParcelExcludedResourceId != (byte)ENUMS.enumExcludedResource.Other)
                {
                    parcel.ParcelOtherExcludedResourceDesc = string.Empty;
                }

                //  If the relationship is Other, but not provided (null), needs to be set to empty.
                parcel.TenantOtherRelationshipDesc = parcel.TenantOtherRelationshipDesc ?? string.Empty;

                //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                parcel.ParcelIdNumber = parcel.ParcelIdNumber ?? string.Empty;
                parcel.ParcelOwner = parcel.ParcelOwner ?? string.Empty;
                parcel.AppraisedValue = parcel.AppraisedValue ?? 0;
                parcel.Equity = parcel.Equity ?? 0;
                parcel.ParcelAddressLine1 = parcel.ParcelAddressLine1 ?? string.Empty;
                parcel.ParcelAddressLine2 = parcel.ParcelAddressLine2 ?? string.Empty;
                parcel.ParcelCity = parcel.ParcelCity ?? string.Empty;
                parcel.ParcelZipCode = parcel.ParcelZipCode ?? string.Empty;
                parcel.ParcelOutOfStateCountyName = parcel.ParcelOutOfStateCountyName ?? string.Empty;
                parcel.TenantName = parcel.TenantName ?? string.Empty;

                if (parcel.EDPropertyParcelDetail != null)
                {
                    // Data integrity check for parcel detail
                    foreach (var parcelDetail in parcel.EDPropertyParcelDetail)
                    {
                        parcelDetail.AppraisedValue = parcelDetail.AppraisedValue ?? 0;

                        //  Clear reason if the no excluded resource indicator(no value or false).
                        if (!parcelDetail.HasExcludedResource.HasValue || !parcelDetail.HasExcludedResource.Value)
                        {
                            parcelDetail.ParcelExcludedResourceId = null;
                            parcelDetail.ParcelOtherExcludedResourceDesc = string.Empty;
                        }
                        // Clear reason description if the ParcelExcludedResourceId is not 'Other'
                        else if (parcelDetail.ParcelExcludedResourceId != (byte)ENUMS.enumExcludedResource.Other)
                        {
                            parcelDetail.ParcelOtherExcludedResourceDesc = string.Empty;
                        }
                    }
                }
            }

            foreach (var previous in property.PreviousProperties)
            {
                if (previous.PreviousPropertyStateId == (short)ENUMS.enumState.Alabama)
                {
                    previous.PreviousPropertyOutOfStateCountyName = string.Empty;
                }
                else
                {
                    previous.PreviousPropertyInStateCountyId = null;
                }

                //  Since the following field is non-nullable in the database, must set to an empty string to prevent an issue.
                previous.PreviousPropertyAddressLine1 = previous.PreviousPropertyAddressLine1 ?? string.Empty;
                previous.PreviousPropertyAddressLine2 = previous.PreviousPropertyAddressLine2 ?? string.Empty;
                previous.PreviousPropertyCity = previous.PreviousPropertyCity ?? string.Empty;
                previous.PreviousPropertyZipCode = previous.PreviousPropertyZipCode ?? string.Empty;
                previous.PreviousPropertyOutOfStateCountyName = previous.PreviousPropertyOutOfStateCountyName ?? string.Empty;

                // Clear specific DB values based on IsPreviousPropertyTypeParcel selection
                if (previous.IsPreviousPropertyTypeParcel != null && previous.IsPreviousPropertyTypeParcel.HasValue)
                {
                    if (previous.IsPreviousPropertyTypeParcel == true)
                    {
                        previous.PreviousMobileHomeLandOwnerName = previous.PreviousMobileHomeLandOwnerName ?? string.Empty;
                        previous.PreviousMobileHomeValueAmount = previous.PreviousMobileHomeValueAmount ?? 0;
                    }
                    else
                    {
                        previous.PreviousParcelOwnerName = previous.PreviousParcelOwnerName ?? string.Empty;
                        previous.PreviousParcelIdNumber = previous.PreviousParcelIdNumber ?? string.Empty;
                        previous.PreviousParcelAppraisedValue = previous.PreviousParcelAppraisedValue ?? 0;
                    }
                }

                if (previous.EDPreviousPropertyDetail != null)
                {
                    // Data integrity check for previous property detail
                    foreach (var previousPropertyDetail in previous.EDPreviousPropertyDetail)
                    {
                        previousPropertyDetail.PreviousPropertyParcelAppraisedValue = previousPropertyDetail.PreviousPropertyParcelAppraisedValue ?? 0;
                        previousPropertyDetail.PreviousPropertyMobileHomeValueAmount = previousPropertyDetail.PreviousPropertyMobileHomeValueAmount ?? 0;
                    }
                }

            }
        }

        /// <summary>
        /// Performs the data integrity check resource.
        /// </summary>
        /// <param name="applicationResourceInfo">The application resource information.</param>
        private static void performDataIntegrityCheckResource(ElderlyDisabledResourceDto applicationResourceInfo)
        {
            foreach (var resourceDetail in applicationResourceInfo.ResourceDetails)
            {
                // Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                resourceDetail.Remarks = resourceDetail.Remarks ?? string.Empty;
                resourceDetail.ApplicantAmount = resourceDetail.ApplicantAmount ?? 0;
                resourceDetail.ApplicantExcludedAmount = resourceDetail.ApplicantExcludedAmount ?? 0;
                resourceDetail.SpouseAmount = resourceDetail.SpouseAmount ?? 0;
                resourceDetail.SpouseExcludedAmount = resourceDetail.SpouseExcludedAmount ?? 0;
                if (resourceDetail.EDResourceMonthDetail != null)
                {
                    // Data integrity check for resource month detail
                    foreach (var rmd in resourceDetail.EDResourceMonthDetail)
                    {
                        rmd.ResourceMonthDetailApplicantAmount = rmd.ResourceMonthDetailApplicantAmount ?? 0;
                    }
                }
            }

            //  Ensure the county information aligns with the state information.
            foreach (var resourceBankDetail in applicationResourceInfo.ResourceBankDetails)
            {
                //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                resourceBankDetail.AccountName = resourceBankDetail.AccountName ?? string.Empty;
                resourceBankDetail.AccountNumber = resourceBankDetail.AccountNumber ?? string.Empty;

                if (resourceBankDetail.BankAccountTypeId != (byte)ENUMS.enumBankAccountType.Other)
                {
                    resourceBankDetail.OtherAccountType = string.Empty;
                }
                resourceBankDetail.OtherAccountType = resourceBankDetail.OtherAccountType ?? string.Empty;
                resourceBankDetail.CurrentBalance = resourceBankDetail.CurrentBalance ?? 0;

                if (resourceBankDetail.BankDetails != null)
                {
                    // Data integrity check for bank detail
                    foreach (var BankDetail in resourceBankDetail.BankDetails)
                    {
                        BankDetail.CurrentBalance = BankDetail.CurrentBalance ?? 0;
                    }
                }
            }

            foreach (var resourceTransferDetail in applicationResourceInfo.ResourceTransferDetails)
            {
                if (resourceTransferDetail.SoldGivenDate == null)
                {
                    // Currently NULLs are not allowed in the DB.  Must insert SQLDefaultDate for now, and switched back during load above
                    resourceTransferDetail.SoldGivenDate = Api.Infrastructure.Constants.SQLDefaultDate;
                }

                //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                resourceTransferDetail.ItemDescription = resourceTransferDetail.ItemDescription ?? string.Empty;
                resourceTransferDetail.ReceiverName = resourceTransferDetail.ReceiverName ?? string.Empty;
                resourceTransferDetail.SoldGivenAmount = resourceTransferDetail.SoldGivenAmount ?? 0;

                //  Clear penalty reason if no to Is there a penalty? (no value or true).
                if (!resourceTransferDetail.HasPenalty.HasValue || resourceTransferDetail.HasPenalty.Value)
                {
                    resourceTransferDetail.PenaltyReason = string.Empty;
                }
            }
        }

        /// <summary>
        /// Updates Resource Header Values with latest Monthly Detail values
        /// </summary>
        /// <param name="applicationResourceInfo"></param>
        private static void performResourceHeaderUpdatesWithLatestMonthlyDetails(ElderlyDisabledResourceDto applicationResourceInfo)
        {
            // Update Resource Transfers
            foreach (var transfer in applicationResourceInfo.ResourceTransferDetails)
            {
                // Get the latest Monthly Detail
                ElderlyDisabledResourceTransferMonthDetailDto latestMonthly = transfer.EDTransferMonthDetail?.Where(md => !md.IsDeleted).OrderByDescending(md => md.ResourceMonth).FirstOrDefault();
                if (latestMonthly != null)
                {
                    transfer.DateDisposed = latestMonthly.DateDisposed;
                    transfer.TransferPenaltyAmount = (latestMonthly.AmountGiven ?? 0) - (latestMonthly.AmountReturned ?? 0);
                    transfer.HasPenalty = latestMonthly.HasPenalty;
                }
            }
        }

        /// <summary>
        /// Performs the data integrity check for E&D life insurance.
        /// </summary>
        /// <param name="applicationInsuranceInfo">The application insurance info.</param>
        private static void performDataIntegrityCheckLifeInsurance(ElderlyDisabledLifeInsuranceDto applicationInsuranceInfo)
        {
            if (applicationInsuranceInfo.LifeInsuranceDetails != null)
            {
                foreach (var insuranceDetail in applicationInsuranceInfo.LifeInsuranceDetails)
                {
                    //  Ensure the county information aligns with the state information.
                    if (insuranceDetail.StateId == (short)ENUMS.enumState.Alabama)
                    {
                        insuranceDetail.OutOfStateCountyName = string.Empty;
                    }
                    else
                    {
                        insuranceDetail.CountyId = null;
                    }

                    //  Clears the CountableAmountRemarks if HasCountableAmount flag is not 'false'
                    if (insuranceDetail.HasCountableAmount != false)
                    {
                        insuranceDetail.CountableAmountRemarks = null;
                    }

                    //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                    insuranceDetail.NameOfInsuranceCompany = insuranceDetail.NameOfInsuranceCompany ?? string.Empty;
                    insuranceDetail.PolicyNumber = insuranceDetail.PolicyNumber ?? string.Empty;
                    insuranceDetail.StreetName = insuranceDetail.StreetName ?? string.Empty;
                    insuranceDetail.AptOrSuiteNumber = insuranceDetail.AptOrSuiteNumber ?? string.Empty;
                    insuranceDetail.City = insuranceDetail.City ?? string.Empty;
                    insuranceDetail.Zipcode = insuranceDetail.Zipcode ?? string.Empty;
                    insuranceDetail.OutOfStateCountyName = insuranceDetail.OutOfStateCountyName ?? string.Empty;
                    // Default amount to $0.
                    insuranceDetail.CashSurrenderValue = insuranceDetail.CashSurrenderValue ?? 0;
                }
            }

            if (applicationInsuranceInfo.OtherBurialFundDetails != null)
            {
                foreach (var otherBurialFundDetail in applicationInsuranceInfo.OtherBurialFundDetails)
                {
                    if (otherBurialFundDetail.StateId == (short)ENUMS.enumState.Alabama)
                    {
                        otherBurialFundDetail.OutOfStateCountyName = string.Empty;
                    }
                    else
                    {
                        otherBurialFundDetail.CountyId = null;
                    }

                    //  Clears the CountableAmountRemarks if HasCountableAmount flag is not 'false'
                    if (otherBurialFundDetail.HasCountableAmount != false)
                    {
                        otherBurialFundDetail.CountableAmountRemarks = null;
                    }

                    //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                    otherBurialFundDetail.NameOfFuneralHome = otherBurialFundDetail.NameOfFuneralHome ?? string.Empty;
                    otherBurialFundDetail.StreetName = otherBurialFundDetail.StreetName ?? string.Empty;
                    otherBurialFundDetail.AptOrSuiteNumber = otherBurialFundDetail.AptOrSuiteNumber ?? string.Empty;
                    otherBurialFundDetail.City = otherBurialFundDetail.City ?? string.Empty;
                    otherBurialFundDetail.Zipcode = otherBurialFundDetail.Zipcode ?? string.Empty;
                    otherBurialFundDetail.OutOfStateCountyName = otherBurialFundDetail.OutOfStateCountyName ?? string.Empty;
                }
            }

            if (applicationInsuranceInfo.AdditionalBurialFundDetails != null)
            {
                foreach (var additionalBurialFundDetail in applicationInsuranceInfo.AdditionalBurialFundDetails)
                {
                    if (additionalBurialFundDetail.IsAccountActive == false)
                    {
                        additionalBurialFundDetail.AdditionalBurialFundsAmount = null;
                    }

                    // Reset the dynamic fields according to the drop-down selection
                    switch (additionalBurialFundDetail.AdditionalBurialFundsTypeId)
                    {
                        case (byte)ENUMS.enumElderlyDisabledAdditionalBurialFundsType.Savings:
                        case (byte)ENUMS.enumElderlyDisabledAdditionalBurialFundsType.Certificate_of_Deposit:
                        case (byte)ENUMS.enumElderlyDisabledAdditionalBurialFundsType.Checking:
                            additionalBurialFundDetail.AdditionalBurialFundsRemarks = additionalBurialFundDetail.AdditionalBurialFundsRemarks ?? string.Empty;

                            //  Clears the CountableAmountRemarks if HasCountableAmount flag is not 'false'
                            if (additionalBurialFundDetail.HasCountableAmount != false)
                            {
                                additionalBurialFundDetail.CountableAmountRemarks = null;
                            }
                            break;
                        case (byte)ENUMS.enumElderlyDisabledAdditionalBurialFundsType.Cash:
                            additionalBurialFundDetail.NameOfFinancialInstitution = additionalBurialFundDetail.NameOfFinancialInstitution ?? string.Empty;
                            additionalBurialFundDetail.AdditionalBurialFundsAccountNumber = additionalBurialFundDetail.AdditionalBurialFundsAccountNumber ?? string.Empty;
                            additionalBurialFundDetail.AdditionalBurialFundsRemarks = additionalBurialFundDetail.AdditionalBurialFundsRemarks ?? string.Empty;

                            //  Clears the CountableAmountRemarks if HasCountableAmount flag is not 'false'
                            if (additionalBurialFundDetail.HasCountableAmount != false)
                            {
                                additionalBurialFundDetail.CountableAmountRemarks = null;
                            }
                            break;
                        case (byte)ENUMS.enumElderlyDisabledAdditionalBurialFundsType.Other:
                            additionalBurialFundDetail.NameOfFinancialInstitution = additionalBurialFundDetail.NameOfFinancialInstitution ?? string.Empty;
                            additionalBurialFundDetail.AdditionalBurialFundsAccountNumber = additionalBurialFundDetail.AdditionalBurialFundsAccountNumber ?? string.Empty;
                            additionalBurialFundDetail.HasCountableAmount = null;
                            additionalBurialFundDetail.CountableAmountRemarks = null;
                            break;
                        default:
                            break;
                    }

                    //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                    additionalBurialFundDetail.NameOfFinancialInstitution = additionalBurialFundDetail.NameOfFinancialInstitution ?? string.Empty;
                    additionalBurialFundDetail.AdditionalBurialFundsAccountNumber = additionalBurialFundDetail.AdditionalBurialFundsAccountNumber ?? string.Empty;
                    additionalBurialFundDetail.AdditionalBurialFundsRemarks = additionalBurialFundDetail.AdditionalBurialFundsRemarks ?? string.Empty;
                    additionalBurialFundDetail.Budgets.ForEach(
                        b => {
                            b.EffectiveDate = b.EffectiveDate == null ? DateTime.MinValue : b.EffectiveDate;
                            if (b.IsAccountActive == false)
                            {
                                b.Amount = null;
                            }
                        });
                }
            }
        }

        /// <summary>
        /// Performs the data integrity check personal property.
        /// </summary>
        /// <param name="personalProperty">The personal property.</param>
        private static void performDataIntegrityCheckPersonalProperty(ElderlyDisabledPersonalPropertyDto personalProperty)
        {
            foreach (var autoDetail in personalProperty.AutoDetails)
            {
                //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                autoDetail.AutoMake = autoDetail.AutoMake ?? string.Empty;
                autoDetail.AutoModel = autoDetail.AutoModel ?? string.Empty;
                autoDetail.AutoUsage = autoDetail.AutoUsage ?? string.Empty;
                autoDetail.AutoAdditionalInformation = autoDetail.AutoAdditionalInformation ?? string.Empty;
                // TODO : Update/Drop the "AutoEquity", once final decision is made (whether to save the value to DB or not for summary UI purpose).
                // Default amount to $0.
                autoDetail.AutoEquity = autoDetail.AutoEquity ?? 0;
            }

            foreach (var machineDetail in personalProperty.MachineDetails)
            {
                //  Since the following fields are non-nullable in the database, must set to an empty string to prevent an issue.
                machineDetail.MachineType = machineDetail.MachineType ?? string.Empty;
                // Default amount to $0.
                machineDetail.MachineExcludedAmount = machineDetail.MachineExcludedAmount ?? 0;
            }

            foreach (var collectionDetail in personalProperty.CollectibleDetails)
            {
                //  Since the following field is non-nullable in the database, must set to an empty string to prevent an issue.
                collectionDetail.CollectibleDescription = collectionDetail.CollectibleDescription ?? string.Empty;
                // Default amount to $0.
                collectionDetail.CollectibleExcludedAmount = collectionDetail.CollectibleExcludedAmount ?? 0;
            }
        }

        /// <summary>
        /// Performs the data integrity check for E&D medical insurance.
        /// </summary>
        /// <param name="medicalInsuranceInfo">The medical insurance info.</param>
        private static void performDataIntegrityCheckMedicalInsurance(ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo)
        {
            //  Since the following field is non-nullable in the database, must set to an empty string to prevent an issue.
            medicalInsuranceInfo.MedicarePartDCompanyName = medicalInsuranceInfo.MedicarePartDCompanyName ?? string.Empty;
            medicalInsuranceInfo.MedicarePartDPolicyNumber = medicalInsuranceInfo.MedicarePartDPolicyNumber ?? string.Empty;
            medicalInsuranceInfo.LtcPlanName = medicalInsuranceInfo.LtcPlanName ?? string.Empty;
            medicalInsuranceInfo.LtcContractNumber = medicalInsuranceInfo.LtcContractNumber ?? string.Empty;

            foreach (var medicalInsuranceDetail in medicalInsuranceInfo.MedicalInsuranceDetails)
            {
                //  Since the following field is non-nullable in the database, must set to an empty string to prevent an issue.
                medicalInsuranceDetail.MedicalInsuranceCompanyName = medicalInsuranceDetail.MedicalInsuranceCompanyName ?? string.Empty;
                medicalInsuranceDetail.MedicalInsurancePolicyNumber = medicalInsuranceDetail.MedicalInsurancePolicyNumber ?? string.Empty;
                medicalInsuranceDetail.StreetName = medicalInsuranceDetail.StreetName ?? string.Empty;
                medicalInsuranceDetail.AptOrSuiteNumber = medicalInsuranceDetail.AptOrSuiteNumber ?? string.Empty;
                medicalInsuranceDetail.City = medicalInsuranceDetail.City ?? string.Empty;
                medicalInsuranceDetail.StateId = medicalInsuranceDetail.StateId == 0 ? null : medicalInsuranceDetail.StateId;  // Don't allow zero for stateId.
                medicalInsuranceDetail.Zipcode = medicalInsuranceDetail.Zipcode ?? string.Empty;
                medicalInsuranceDetail.PremiumFrequencyId = medicalInsuranceDetail.PremiumFrequencyId == 0 ? null : medicalInsuranceDetail.PremiumFrequencyId;
                medicalInsuranceDetail.MedicalInsuranceType = medicalInsuranceDetail.MedicalInsuranceType ?? string.Empty;
                medicalInsuranceDetail.OtherPayerName = medicalInsuranceDetail.OtherPayerName ?? string.Empty;

                foreach (var monthDetail in medicalInsuranceDetail.MonthDetails)
                {
                    monthDetail.ResourceMonth = monthDetail.ResourceMonth ?? DateTime.MinValue;
                }
            }

            foreach (var partDBudget in medicalInsuranceInfo.PartDBudgets)
            {
                partDBudget.EffectiveDate = partDBudget.EffectiveDate ?? DateTime.MinValue;
            }
        }

        /// <summary>
        /// Performs data integrity check on Expedite Facility objects
        /// </summary>
        /// <param name="facilityData"></param>
        private static void performDataIntegrityCheckExpediteFacility(ExpediteFacilityDto facilityData)
        {
            // Provider matching:  If one is selected (marked 'C'), reject all others:
            if (facilityData.ProviderMatches.Any(p => p.StatusIndicator == RefValues.ExpediteReconcileFacilityProviderStatusInd.Confirmed))
            {
                foreach (var match in facilityData.ProviderMatches.Where(p => p.StatusIndicator != RefValues.ExpediteReconcileFacilityProviderStatusInd.Confirmed))
                {
                    match.StatusIndicator = RefValues.ExpediteReconcileFacilityProviderStatusInd.Rejected;
                }
            }
        }

        /// <summary>
        /// Maps the denial reasons to the entity framework object.
        /// </summary>
        /// <param name="eligibility">The eligibility.</param>
        /// <param name="denialReasons">The denial reasons.</param>
        private static void mapDenialReasonsToEFObject(APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION eligibility, List<short> denialReasons)
        {
            if (denialReasons.Count > 0)
            {
                eligibility.DENIAL_REASONS_JSON = JsonConvert.SerializeObject(denialReasons.ToArray());
            }
            else
            {
                eligibility.DENIAL_REASONS_JSON = string.Empty;
            }
        }

        /// <summary>
        /// If there is no Medicare Part-D info found in the TBQ (Territorial Beneficiary Query), then set as 'N/A'
        /// </summary>
        /// <param name="tbqInfo">The Medicare Part-D info from TBQ.</param>
        private static void setNotApplicable(ElderlyDisabledTBQMedicarePartDInfoDto tbqInfo)
        {
            tbqInfo.TBQMedicarePartDContractNumber = string.IsNullOrEmpty(tbqInfo.TBQMedicarePartDContractNumber) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDContractNumber;

            tbqInfo.TBQMedicarePartDPlanId = string.IsNullOrEmpty(tbqInfo.TBQMedicarePartDPlanId) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDPlanId;

            tbqInfo.TBQMedicarePartDEnrollTypeIndicator = string.IsNullOrEmpty(tbqInfo.TBQMedicarePartDEnrollTypeIndicator) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDEnrollTypeIndicator;

            tbqInfo.TBQMedicarePartDBeginDate = string.IsNullOrEmpty(tbqInfo.TBQMedicarePartDBeginDate) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDBeginDate;

            tbqInfo.TBQMedicarePartDEndDate = string.IsNullOrEmpty(tbqInfo.TBQMedicarePartDEndDate) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDEndDate;

            tbqInfo.TBQMedicarePartDOrganizationName = string.IsNullOrEmpty(tbqInfo.TBQMedicarePartDOrganizationName) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDOrganizationName;

            tbqInfo.TBQMedicarePartDPlanName = string.IsNullOrEmpty(tbqInfo.TBQMedicarePartDPlanName) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDPlanName;

            // If the Last Run Date is either null or SQL default date '01/01/1900', then set 'N/A'
            tbqInfo.TBQMedicarePartDLastRunDate = (tbqInfo.TBQMedicarePartDLastRunDate == null ||
                tbqInfo.TBQMedicarePartDLastRunDate == Constants.EmptyDate.ToString("MM/dd/yyyy")) ?
                Api.Infrastructure.Constants.NOT_APPLICABLE : tbqInfo.TBQMedicarePartDLastRunDate;
        }

        /// <summary>
        /// Add the latest auto additional details to the auto details object.
        /// </summary>
        /// <param name="personalPropertydetails"></param>
        private static void AddLatestAutoDetailsToTheHeader(ElderlyDisabledPersonalPropertyDto personalPropertydetails)
        {
            if (personalPropertydetails.AutoDetails == null || personalPropertydetails.AutoDetails.Count == 0)
            {
                return;
            }

            foreach (var item in personalPropertydetails.AutoDetails)
            {
                if (item.AutoAdditionalDetails == null || item.AutoAdditionalDetails.Count == 0)
                {
                    continue;
                }
                var latestAutoAdditionalDetails = item.AutoAdditionalDetails.OrderByDescending(x => x.AutoDetailMonth).FirstOrDefault();
                item.DateDisposed = latestAutoAdditionalDetails.DateDisposed;
                item.AutoOwedAmount = latestAutoAdditionalDetails.AutoOwedAmount;
                item.AutoValueAmount = latestAutoAdditionalDetails.AutoValueAmount;
                item.HasAutoExcludedResource = latestAutoAdditionalDetails.HasAutoExcludedResource;
            }
        }

		/// <summary>
		/// Add the latest auto additional details to the auto details object.
		/// </summary>
		/// <param name="personalPropertydetails"></param>
		private static void AddLatestCollectibleAndMachineDetailsToTheHeader(ElderlyDisabledPersonalPropertyDto personalPropertydetails)
		{
			if (personalPropertydetails.CollectibleDetails != null && personalPropertydetails.CollectibleDetails.Count > 0)
			{
			    foreach (var item in personalPropertydetails.CollectibleDetails)
			    {
				    if (item.CollectibleMonthDetails == null || item.CollectibleMonthDetails.Count == 0)
				    {
					    continue;
				    }

				    var latestDetails = item.CollectibleMonthDetails.OrderByDescending(x => x.CollectibleDetailMonth).FirstOrDefault();

                    item.CollectibleValueAmount = latestDetails.CollectibleValueAmount;
                    item.DateDisposed = latestDetails.DateDisposed;
			    }
			}

            if (personalPropertydetails.MachineDetails != null && personalPropertydetails.MachineDetails.Count > 0)
            {
                foreach (var item in personalPropertydetails.MachineDetails)
                {
                    if (item.MachineMonthDetails == null || item.MachineMonthDetails.Count == 0)
                    {
                        continue;
                    }

                    var latestDetails = item.MachineMonthDetails.OrderByDescending(x => x.MachineDetailMonth).FirstOrDefault();

                    item.MachineValueAmount = latestDetails.MachineValueAmount;
                    item.DateDisposed = latestDetails.DateDisposed;
                }
            }

        }
		#endregion

		#region Expedite
		/// <summary>
		/// Gets data from the EXPEDITE_IMPORT table
		/// </summary>
		/// <param name="expediteImportId">The expedite import identifier.</param>
		/// <param name="callToken">The token identifier.</param>
		/// <returns></returns>
		public static StagingExpediteImportDto GetStagingExpediteImport(long expediteImportId, Guid callToken)
        {
            StagingExpediteImportDto result = null;

            try
            {
                using (CaresStagingDBEntities context = new CaresStagingDBEntities())
                {
                    EXPEDITE_IMPORT ei = context.EXPEDITE_IMPORT.FirstOrDefault(a => a.EXPEDITE_IMPORT_ID == expediteImportId);

                    if (ei != null)
                    {
                        result = new StagingExpediteImportDto()
                        {
                            ExpediteImportId = ei.EXPEDITE_IMPORT_ID,
                            NhWebId = ei.NH_WEB_ID,
                            FlatFileData = ei.FLAT_FILE_DATA,
                            StatusId = ei.STATUS_ID,
                            ApplicationId = ei.APPLICATION_ID,
                            PersonId = ei.PERSON_ID,
                            LastProcessedDate = ei.LAST_PROCESSED_DATE,
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, callToken);
            }

            return result;
        }

        /// <summary>
        /// Called by the front end, for dashboard display, or other (displaying error to user who tries to navigate to a partial import)
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="callToken">The token identifier.</param>
        /// <returns></returns>
        public static StagingExpediteImportDetailsDto GetStagingExpediteImportDetails(long expediteImportId, long applicationId, Guid callToken)
        {
            StagingExpediteImportDetailsDto result = null;

            try
            {
                using (CaresStagingDBEntities context = new CaresStagingDBEntities())
                {
                    EXPEDITE_IMPORT ei = context.EXPEDITE_IMPORT.FirstOrDefault(a =>
                                            (expediteImportId != 0 && a.EXPEDITE_IMPORT_ID == expediteImportId) ||
                                            (applicationId != 0 && a.APPLICATION_ID == applicationId));

                    if (ei != null)
                    {
                        result = new StagingExpediteImportDetailsDto()
                        {
                            ExpediteImportId = ei.EXPEDITE_IMPORT_ID,
                            NhWebId = ei.NH_WEB_ID,
                            DateSentToDoUser = ei.DATE_SENT_TO_DO_USER,
                            DateAcceptedByDoUser = ei.DATE_ACCEPTED_BY_DO_USER,
                            DateSentToCares = ei.DATE_SENT_TO_CARES,
                            StatusId = ei.STATUS_ID,
                            ApplicationId = ei.APPLICATION_ID ?? 0,
                            ContactPersonId = ei.PERSON_ID ?? 0,
                            LastProcessedDate = ei.LAST_PROCESSED_DATE,
                            ResponseSentDate = ei.RESPONSE_SENT_DATE
                        };
                    }
                    else
                    {
                        result = new StagingExpediteImportDetailsDto()
                        {
                            CaresError = CaresError.NotFound,
                            IsSuccessful = false
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, callToken);
            }

            return result;
        }

        /// <summary>
        /// Gets data from the EXPEDITE_IMPORT table, by Application ID
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="callToken">The token identifier.</param>
        /// <returns></returns>
        public static StagingExpediteImportDto GetStagingExpediteImportByAppId(long applicationId, Guid callToken)
        {
            StagingExpediteImportDto result = null;

            try
            {
                using (CaresStagingDBEntities context = new CaresStagingDBEntities())
                {
                    EXPEDITE_IMPORT ei = context.EXPEDITE_IMPORT.FirstOrDefault(a => a.APPLICATION_ID == applicationId);

                    if (ei != null)
                    {
                        result = new StagingExpediteImportDto()
                        {
                            ExpediteImportId = ei.EXPEDITE_IMPORT_ID,
                            NhWebId = ei.NH_WEB_ID,
                            FlatFileData = ei.FLAT_FILE_DATA,
                            StatusId = ei.STATUS_ID,
                            ApplicationId = ei.APPLICATION_ID,
                            PersonId = ei.PERSON_ID,
                            LastProcessedDate = ei.LAST_PROCESSED_DATE,
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, callToken);
            }

            return result;
        }


        /// <summary>
        ///  Updates the status on the EXPEDITE_IMPORT table
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="newStatus">The new status.</param>
        /// <param name="callToken">The token identifier.</param>
        public static void UpdateStagingExpediteImportStatus(int expediteImportId, byte newStatus, Guid callToken)
        {
            try
            {
                using (CaresStagingDBEntities context = new CaresStagingDBEntities())
                {
                    EXPEDITE_IMPORT ei = context.EXPEDITE_IMPORT.FirstOrDefault(a => a.EXPEDITE_IMPORT_ID == expediteImportId);

                    if (ei != null)
                    {
                        ei.STATUS_ID = newStatus;
                        ei.LAST_PROCESSED_DATE = DateTime.Now;
                        context.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, callToken);
            }
        }

        /// <summary>
        ///  Updates the app and person IDs on the EXPEDITE_IMPORT table
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="callToken">The token identifier.</param>
        public static void UpdateStagingExpediteImportIds(int expediteImportId, long? applicationId, long? personId, Guid callToken)
        {
            try
            {
                using (CaresStagingDBEntities context = new CaresStagingDBEntities())
                {
                    EXPEDITE_IMPORT ei = context.EXPEDITE_IMPORT.FirstOrDefault(a => a.EXPEDITE_IMPORT_ID == expediteImportId);

                    if (ei != null)
                    {
                        ei.APPLICATION_ID = applicationId;
                        ei.PERSON_ID = personId;
                        ei.LAST_PROCESSED_DATE = DateTime.Now;
                        context.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, callToken);
            }
        }

        /// <summary>
        /// Updates the ExpediteImport appId and status
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="statusId">The status identifier.</param>
        /// <param name="callToken">The token identifier.</param>
        public static void UpdateStagingExpediteImportAppIdAndStatus(int expediteImportId, long? applicationId, byte statusId, Guid callToken)
        {
            try
            {
                using (CaresStagingDBEntities context = new CaresStagingDBEntities())
                {
                    EXPEDITE_IMPORT ei = context.EXPEDITE_IMPORT.FirstOrDefault(a => a.EXPEDITE_IMPORT_ID == expediteImportId);

                    if (ei != null)
                    {
                        ei.APPLICATION_ID = applicationId;
                        ei.STATUS_ID = statusId;
                        ei.LAST_PROCESSED_DATE = DateTime.Now;
                        context.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, callToken);
            }
        }

        /// <summary>
        /// Adds a message to
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="message">The message.</param>
        /// <param name="messageType">"I"-Info, "W"-Warning, "E"-Error</param>
        /// <param name="callToken">The token identifier.</param>
        public static void AddStagingExpediteImportMessage(int expediteImportId, string message, string messageType, Guid callToken)
        {
            try
            {
                using (CaresStagingDBEntities context = new CaresStagingDBEntities())
                {
                    EXPEDITE_IMPORT_MESSAGE m = new EXPEDITE_IMPORT_MESSAGE()
                    {
                        EXPEDITE_IMPORT_ID = expediteImportId,
                        MESSAGE = message,
                        MESSAGE_TYPE = messageType,
                        CREATED_DATE = DateTime.Now
                    };

                    context.EXPEDITE_IMPORT_MESSAGE.Add(m);
                    context.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, callToken);
            }
        }
        #endregion Expedite
    }
}