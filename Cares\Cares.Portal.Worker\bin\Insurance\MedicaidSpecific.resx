﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="haveHealthCoverage" xml:space="preserve">
    <value>Some people may qualify to receive help even if they already have health coverage. &lt;br/&gt; Do any of these people have health coverage now?</value>
  </data>
  <data name="helpLast3Months" xml:space="preserve">
    <value>Do you want help paying for [NAME]'s, (DOB: [DOB]) medical bills from the last 3 months?</value>
  </data>
  <data name="hoursPerWeekRequired" xml:space="preserve">
    <value>Parent's work hours per week are required.</value>
  </data>
  <data name="medicaidSpecificQuestions" xml:space="preserve">
    <value>Medicaid Program Questions</value>
  </data>
  <data name="parent1" xml:space="preserve">
    <value>Parent 1:</value>
  </data>
  <data name="parent2" xml:space="preserve">
    <value>Parent 2:</value>
  </data>
  <data name="parentLivingOutsideHome" xml:space="preserve">
    <value>Does [NAME], (DOB: [DOB]) have a parent living outside the home?</value>
  </data>
  <data name="parentWorkHours" xml:space="preserve">
    <value>How many hours per week do [NAME]'s, (DOB: [DOB]) parents work?</value>
  </data>
  <data name="radioRequiredSelection" xml:space="preserve">
    <value>Please select either "Yes" or "No" for required options.</value>
  </data>
  <data name="workHistoryGiveSSN_1" xml:space="preserve">
    <value>[NAME], (DOB: [DOB]) could receive free or low-cost health coverage if [HE/SHE] has enough of a work history in the U.S. on [HIS/HER] own or through a family member. We checked [NAME]'s, (DOB: [DOB]) work history because you gave us [HIS/HER] Social Security number (SSN).</value>
  </data>
  <data name="workHistoryGiveSSN_2_Married" xml:space="preserve">
    <value>We can also check the work history for [NAME]'s, (DOB: [DOB]) family members if you give us the SSN of [HIS/HER] parent or spouse (and he or she agrees you may use their SSN for this purpose). Would you like to give an SSN now?</value>
  </data>
  <data name="workHistoryGiveSSN_2_NotMarried" xml:space="preserve">
    <value>We can also check the work history for [NAME]'s, (DOB: [DOB]) family members if you give us the SSN of [HIS/HER] father or mother (and he or she agrees you may use their SSN for this purpose). Would you like to give an SSN now?</value>
  </data>
</root>