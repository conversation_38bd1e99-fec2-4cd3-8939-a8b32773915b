﻿@model Cares.Portal.Worker.Models.Models.PresumptiveEligibility.ViewModel.PepLandingViewModel
@using Cares.Api.Infrastructure
@using Cares.Portal.Infrastructure
@using System.Web.Optimization
@using Cares.Portal.Infrastructure.VGSecurity
<main role="main" class="container-fluid">
    <div class="row ml-2">
        <h4 class="mt-1">Provider</h4>
        @* Only Medicaid Admin are allowed to add PEP Provider *@
        @if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
        {
            <a class="btn btn-info btn-gradient shadow ml-4" id="btnAddPEPProvider" href="@Url.Action("AddPEPProvider","PresumptiveEligibility")">Add Provider</a>
        }
    </div>
    <div id="divProviders" class="landingContent mt-2">
        <table id="tblProviders" class="table table-striped table-bordered compact">
            <thead>
                <tr>
                    <th>Provider Name</th>
                    <th>NPI</th>
                    <th>Medicaid Billing Number</th>
                    <th>Address</th>
                    <th>Status</th>
                    <th>Facility Ownership</th>
                </tr>
            </thead>
            <tbody style="display:none">
                @foreach (var provider in Model.Providers)
                {
                    <tr>
                        <td>
                            @if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN))
                            {
                                        @Html.ActionLink(provider.ProviderName, "AddPEPProvider", new { pepProviderId = provider.ProviderId })
                            }
                            else
                            { 
                                @(provider.ProviderName)
                            }
                        </td>
                        <td>
                            @provider.Npi
                        </td>
                        <td>
                            @provider.MedicaidBillingNumber
                        </td>
                        <td>
                            <p class="m-0">@provider.ProviderAddress.AddressLine1</p>
                            @if (!string.IsNullOrWhiteSpace(provider.ProviderAddress.AddressLine2))
                                {
                                <p class="m-0">@provider.ProviderAddress.AddressLine2</p>
                            }
                            <p class="m-0">@provider.ProviderAddress.City, @provider.ProviderAddress.StateName @provider.ProviderAddress.ZipCode</p>
                        </td>
                        <td>
                            @*Here for filtering on "Active"*@
                            <span style="display:none">@((provider.ProviderStatusId==1) ? "Active" : "Inactive")</span>

                            @if (provider.ProviderIsActive)
                            {
                                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconCheck, new string[] { "verifiedIcon" })
                            }
                            else
                            {
                                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconClose)
                            }
                        </td>
                        <td>
                            @provider.FacilityOwnership
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</main>
<script language="javascript">
    // Setup - add a text input to each header cell
    $('#tblProviders thead tr')
        .clone(true)
        .addClass('filters')
        .appendTo('#tblProviders thead').hide();

    $(document).ready(function () {
        $('#tblProviders').DataTable({
            lengthMenu: [25, 20, 15, 10],
            orderCellsTop: true,
            fixedHeader: true,
            initComplete: function () {
                $('#tblProviders tbody').show();
                // Hide the top "search" bar.  We have filtering on each column
                $('#tblProviders_filter').hide();

                let trFilter = $('<tr class="filters">');
                let dtResult = $('#tblProviders').DataTable();

                // For each column
                dtResult.columns().every(function () {
                    let column = this;
                    let colIdx = column.index();

                    if (colIdx == 4) { // Active Column
                        //  Create select box filter
                        let statuses = ['Active', 'Inactive'];
                        let thFilter = DataTablesAddFilter.AddSelectFilterUsingCustomData(column, 'Active', statuses, null, null, false);

                        $(trFilter).append(thFilter);
                    }
                    else { // All others:  Text box
                        let header = $(column.header()).html();
                        let thFilter = DataTablesAddFilter.AddTextBoxFilter(column, header);

                        $(trFilter).append($(thFilter));
                    }
                });

                //  Show the filter fields
                $('#tblProviders thead').append($(trFilter)).show();
            }
        });
    });
</script>