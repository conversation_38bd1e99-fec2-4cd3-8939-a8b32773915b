﻿@model Cares.Portal.Worker.Models.Models.PresumptiveEligibility.ViewModel.PepLandingViewModel
@using Cares.Api.Infrastructure
@using Cares.Portal.Infrastructure
@using System.Web.Optimization
@using Cares.Portal.Infrastructure.VGSecurity
@{
	Layout = "~/Views/Shared/_LandingMasterLayout.cshtml";
	ViewBag.Title = "PEP Admin Landing";
	string partial = (ViewBag.PartialV != null && ViewBag.PartialV != string.Empty) ? ViewBag.PartialV : "Provider";
}
@section viewStyles{
    @Styles.Render("~/bundles/landingstyles")
    <style>
        .radio-button-header {
            font-size: 1.0rem;
            margin-top: 0px;
            text-align: center;
        }

        .form-check-label {
            font-size: 1.1rem;
        }

        .form-check-inline {
            padding-right: 50px; /* Increase space between radio buttons */
        }

        .form-check-input {
            transform: scale(1.5); /* Make the radio button bigger */
        }
    </style>
}

<header>
    <!--    Sticky Toolbar  -->
    @Html.Partial("_LandingNavBar", Model.NavBarOptions)
</header>
<main role="main" class="container-fluid">
    <div class="row ml-2">
        <h4 class="mt-1">PEP Providers & Determiners</h4>
    </div>
    @* Only Medicaid Admin and PEP Elig workers are allowed to view PEP Providers & Determiners *@
    @if (CaresSecurity.IsInRole(CaresSecurity.UserRoles.MCD_ADMIN) || CaresSecurity.IsInRole(CaresSecurity.UserRoles.PEP_ELIG))
    {
        <div class="container">
            <form id="viewSelector" method="post">
                <div class="row radio-button-header">
                    <div class="col-auto">

						<div class="form-check form-check-inline">
							@if (partial == "Provider")
							{
								<input class="form-check-input" type="radio" name="selectedValue" id="rbProviders" value="Providers" checked="checked" onclick="submitForm('Provider')">
							}
							else
							{
								<input class="form-check-input" type="radio" name="selectedValue" id="rbProviders" value="Providers" onclick="submitForm('Provider')">
							}
							<label class="form-check-label" for="rbProviders">Provider</label>
						</div>
                    </div>
                    <div class="col-auto">
						<div class="form-check form-check-inline">
							@if (partial == "Determiner")
							{
								<input class="form-check-input" type="radio" name="selectedValue" id="rbDeterminers" checked="checked" value="Determiners" onclick="submitForm('Determiner')">
							}
                            else
							{
								<input class="form-check-input" type="radio" name="selectedValue" id="rbDeterminers" value="Determiners" onclick="submitForm('Determiner')">
							}
							<label class="form-check-label" for="rbDeterminers">Determiner</label>
						</div>
                    </div>
                </div>
            </form>
        </div>
		<div id="content">
            <b>Loading data...</b>
		</div>
    }
</main>
@Scripts.Render("~/bundles/datatablesscripts")
@section viewScripts{
    <script language="javascript">
        $(document).ready(function ()
        {
            submitForm("@(partial)");
        });
        function submitForm(value) {
            $.post('@Url.Action("pepAdminToggleView", "PresumptiveEligibility")', { selectedValue: value }, function (data) {
                $('#content').html(data);
            });
        }
    </script>
}