﻿namespace Cares.Api
{
    /// <summary>
    /// Holds all AppTier web service names
    /// </summary>
    public class AppTierWebServiceNames
    {
        // Infrastructure:
        public static string InfrastructureApi_LookupData = "InfrastructureApi/LookupData";

        // Elderly Disabled Letters:
        public static string ElderlyDisabledLettersApi_SaveDraftLetter = "ElderlyDisabledLettersApi/SaveDraftLetter";
        public static string ElderlyDisabledLettersApi_GetDraftSummary = "ElderlyDisabledLettersApi/GetDraftSummary";
        public static string ElderlyDisabledLettersApi_DropDraftLetter = "ElderlyDisabledLettersApi/DropDraftLetter";
        public static string ElderlyDisabledLettersApi_GetDraftFormDetails = "ElderlyDisabledLettersApi/GetDraftFormDetails";
        public static string ElderlyDisabledLettersApi_SaveFinalLetter = "ElderlyDisabledLettersApi/SaveFinalLetter";
        public static string ElderlyDisabledLettersApi_GetTerminationLetter = "ElderlyDisabledLettersApi/GetTerminationLetter";
        public static string ElderlyDisabledLettersApi_GetRmaTerminationLetter = "ElderlyDisabledLettersApi/GetRmaTerminationLetter";
        public static string ElderlyDisabledLettersApi_GetDenialLetter = "ElderlyDisabledLettersApi/GetDenialLetter";
        public static string ElderlyDisabledLettersApi_GetAwardLetterData = "ElderlyDisabledLettersApi/GetAwardLetterData";
        public static string ElderlyDisabledLettersApi_GetLiabilityUpdateLetter = "ElderlyDisabledLettersApi/GetElderlyDisabledLiabilityData";
        public static string ElderlyDisabledLettersApi_GetCommonLetterData = "ElderlyDisabledLettersApi/GetCommonLetterData";

        // Elderly Disabled:
        public static string ElderlyDisabledApi_GetApplication = "ElderlyDisabledApi/GetApplication";
        public static string ElderlyDisabledApi_SaveApplication = "ElderlyDisabledApi/SaveApplication";
        public static string ElderlyDisabledApi_GetSpouseInfo = "ElderlyDisabledApi/GetSpouseInfo";
        public static string ElderlyDisabledApi_SaveSpouseInfo = "ElderlyDisabledApi/UpsertElderlyDisabledSpouseInfo";
        public static string ElderlyDisabledApi_GetElderlyDisabledEligibilityEnrollment = "ElderlyDisabledApi/GetElderlyDisabledEligibilityEnrollment";
        public static string ElderlyDisabledApi_GetWorkerNumbers = "ElderlyDisabledApi/GetWorkerNumbers";
        public static string ElderlyDisabledApi_GetHouseholdMembers = "ElderlyDisabledApi/GetHouseholdMembers";
        public static string ElderlyDisabledApi_SaveHouseholdMembers = "ElderlyDisabledApi/SaveHouseholdMembers";
        public static string ElderlyDisabledApi_DeleteHouseholdMember = "ElderlyDisabledApi/DeleteHouseholdMember";
        public static string ElderlyDisabledApi_GetPropertyInformation = "ElderlyDisabledApi/GetPropertyInformation";
        public static string ElderlyDisabledApi_SavePropertyInformation = "ElderlyDisabledApi/SavePropertyInformation";
        public static string ElderlyDisabledApi_GetResourceInformation = "ElderlyDisabledApi/GetResourceInformation";
        public static string ElderlyDisabledApi_SaveResourceInformation = "ElderlyDisabledApi/SaveResourceInformation";
        public static string ElderlyDisabledApi_GetMedicalInsuranceInfo = "ElderlyDisabledApi/GetMedicalInsuranceInfo";
        public static string ElderlyDisabledApi_SaveMedicalInsuranceInfo = "ElderlyDisabledApi/SaveMedicalInsuranceInfo";
        public static string ElderlyDisabledApi_GetVeteranDetails = "ElderlyDisabledApi/GetVeteranDetails";
        public static string ElderlyDisabledApi_SaveElderlyDisabledVeteranInfo = "ElderlyDisabledApi/SaveElderlyDisabledVeteranInfo";
        public static string ElderlyDisabledApi_GetLifeInsuranceInfo = "ElderlyDisabledApi/GetLifeInsuranceInfo";
        public static string ElderlyDisabledApi_SaveLifeInsuranceInfo = "ElderlyDisabledApi/SaveLifeInsuranceInfo";
        public static string ElderlyDisabledApi_GetPersonalPropertyInfo = "ElderlyDisabledApi/GetPersonalPropertyInfo";
        public static string ElderlyDisabledApi_SavePersonalPropertyInfo = "ElderlyDisabledApi/SavePersonalPropertyInfo";
        public static string ElderlyDisabledUserApi_GetRepresentativeInfo = "ElderlyDisabledApi/GetRepresentativeInfo";
        public static string ElderlyDisabledUserApi_SaveRepresentativeInfo = "ElderlyDisabledApi/SaveRepresentativeInfo";
        public static string ElderlyDisabledUserApi_GetAppInfoBarInfo = "ElderlyDisabledApi/GetAppInfoBarInfo";
        public static string ElderlyDisabledApi_GetPersonLiabilityInfo = "ElderlyDisabledApi/GetPersonLiabilityInfo";
        public static string ElderlyDisabledApi_UpsertLiabilityTestData = "ElderlyDisabledApi/UpsertLiabilityTestData";
        public static string ElderlyDisabledApi_UpsertLiabilitySegment = "ElderlyDisabledApi/UpsertLiabilitySegment";
        public static string ElderlyDisabledApi_DeleteLiabilitySegment = "ElderlyDisabledApi/DeleteLiabilitySegment";
        public static string ElderlyDisabledApi_GetRenewApplication = "ElderlyDisabledApi/GetRenewApplication";
        public static string ElderlyDisabledApi_GetNonMagiIncomes = "ElderlyDisabledApi/GetNonMagiIncomes";
        public static string ElderlyDisabledApi_CalculateNetVAIncome = "ElderlyDisabledApi/CalculateVANetValues";
        public static string ElderlyDisabledApi_GetQitAndAllocation = "ElderlyDisabledApi/GetQitAndAllocation";
        public static string ElderlyDisabledApi_SaveAllocationInfo = "ElderlyDisabledApi/SaveAllocationInfo";
        public static string ElderlyDisabledApi_SaveApplicationNonMagiIncome = "ElderlyDisabledApi/SaveApplicationNonMagiIncome";
        public static string ElderlyDisabledApi_GetEligibilityDeterminations = "ElderlyDisabledApi/GetEligibilityDeterminations";
        public static string ElderlyDisabledApi_GetApplicationDetailQitLien = "ElderlyDisabledApi/GetApplicationDetailQitLien";
        public static string ElderlyDisabledApi_SaveEligibilityDeterminations = "ElderlyDisabledApi/SaveEligibilityDeterminations";
        public static string ElderlyDisabledApi_TruncateLiabilitySegments = "ElderlyDisabledApi/TruncateLiabilitySegments";
        public static string ElderlyDisabledApi_EnrollMsp = "ElderlyDisabledApi/EnrollMsp";
        public static string ElderlyDisabledApi_EnrollComplete = "ElderlyDisabledApi/EnrollComplete";
        public static string ElderlyDisabledApi_GetPrimarySponsorInfo = "ElderlyDisabledApi/GetPrimarySponsorInfo";
        public static string ElderlyDisabledApi_GetElderlyDisabledNonMagiIncomeInfo = "ElderlyDisabledApi/GetElderlyDisabledNonMagiIncomeInfo";
        public static string ElderlyDisabledApi_InsertExpediteFacility = "ElderlyDisabledApi/InsertExpediteFacility";
        public static string ElderlyDisabledApi_UpdateExpediteFacility = "ElderlyDisabledApi/UpdateExpediteFacility";
        public static string ElderlyDisabledApi_GetImportExpediteDetails = "ElderlyDisabledApi/GetImportExpediteDetails";
        public static string ElderlyDisabledApi_GetExpediteFacilityProviders = "ElderlyDisabledApi/GetExpediteFacilityProviders";
        public static string ElderlyDisabledApi_GetExpediteFacilityByString = "ElderlyDisabledApi/GetExpediteFacilitiesByString";
        public static string ElderlyDisabledApi_GetExpediteFacilityById = "ElderlyDisabledApi/GetExpediteFacilityById";
        public static string ElderlyDisabledApi_GetProviders = "ElderlyDisabledApi/GetProviders";
        public static string ElderlyDisabledApi_SetExpediteFacilityProvider = "ElderlyDisabledApi/SetExpediteFacilityProvider";
        public static string ElderlyDisabledApi_GetFinancialInstitutionsByString = "ElderlyDisabledApi/GetFinancialInstitutionsByString";
        public static string ElderlyDisabledApi_GetFinancialInstitutions = "ElderlyDisabledApi/GetFinancialInstitutionsByBankId";
        public static string ElderlyDisabledApi_GetExparteInfoByPersonId = "ElderlyDisabledApi/GetExparteInfoByPersonId";
        public static string ElderlyDisabledApi_InsertFinancialInstitution = "ElderlyDisabledApi/InsertFinancialInstitution";
        public static string ElderlyDisabledApi_UpdateFinancialInstitution = "ElderlyDisabledApi/UpdateFinancialInstitution";

        // Enrollment
        public static string EnrollmentApi_GetPersonEnrollments = "EnrollmentApi/GetPersonEnrollments";
        public static string EnrollmentApi_GetNonDeceasedUnborns = "PersonApi/GetNonDeceasedUnborns";
        public static string EnrollmentApi_ReviewReceived = "EnrollmentApi/ReviewReceived";


        //Facility
        public static string FacilityApi_GetDysFacilities = "FacilityApi/GetDysFacilities";
        public static string FacilityApi_InsertDYSFacility = "FacilityApi/InsertDYSFacility";
        public static string FacilityApi_UpdateDYSFacility = "FacilityApi/UpdateDYSFacility";


        //Presumptive Eligibility
        public static string PresumptiveApi_GetProviderById = "PresumptiveEligibilityApi/GetPEPProviderInfoById";
        public static string PresumptiveApi_GetDeterminerById = "PresumptiveEligibilityApi/GetPEPDeterminerById";
        public static string PresumptiveApi_SaveProvider = "PresumptiveEligibilityApi/SavePEPProviderInfo";
        public static string PresumptiveApi_SaveDeterminer = "PresumptiveEligibilityApi/SavePEPDeterminerInfo";
        public static string PresumptiveApi_GetProviderByString = "PresumptiveEligibilityApi/GetProviderByString";
        public static string PresumptiveApi_GetProviders = "PresumptiveEligibilityApi/GetPEPProvidersInfo";
        public static string PresumptiveApi_GetDeterminers = "PresumptiveEligibilityApi/GetPEPDeterminersInfo";


        // Landing:
        public static string LandingApi_GetApplicationInformation = "LandingApi/GetApplicationInformation";
        public static string LandingApi_GetStartingApplicationId = "LandingApi/GetStartingApplicationId";
        public static string LandingApi_GetFullPersonDetails = "LandingApi/GetFullPersonDetails";
        public static string LandingApi_GetBasicPersonDetails = "LandingApi/GetBasicPersonDetails";
        public static string LandingApi_GetEnDPersonDetails = "LandingApi/GetEnDPersonDetails";
        public static string LandingApi_GetSearchResults = "LandingApi/GetSearchResults";
        public static string LandingApi_GetPersonLatestAppDetails = "LandingApi/GetPersonLatestAppDetails";
        public static string LandingApi_GetApplicationSnapshot = "LandingApi/GetApplicationSnapshot";

        // Notes:
        public static string NotesApi_SearchNotes = "NotesApi/SearchNotes";

        // Person
        public static string PersonApi_GetLatestApplicationIdByPersonId = "PersonApi/GetLatestApplicationIdByPersonId";

        // Application:
        public static string ApplicationApi_ProcessHubCallVerification = "ApplicationApi/ProcessHubCallVerification";
        public static string ApplicationApi_GetNonMagiIncomes = "ApplicationApi/GetNonMagiIncomes";
        public static string ApplicationApi_SaveNonMagiIncomes = "ApplicationApi/SaveApplicationNonMagiIncome";
        public static string ApplicationApi_UpdateApplicationStatus = "ApplicationApi/UpdateApplicationStatus";
        public static string ApplicationApi_GetLettersParameterDetails = "ApplicationApi/GetLettersParameterDetails";
        public static string ApplicationApi_GetDenialReasons = "ApplicationApi/GetDenialReasons";
        public static string ApplicationApi_GetApplicationDto = "ApplicationApi/GetApplicationDto";
        public static string ApplicationApi_GetApplicationSnapshot = "ApplicationApi/GetApplicationSnapshot";
        public static string ApplicationApi_SaveApplicationSnapshot = "ApplicationApi/SaveApplicationSnapshot";

        // Authorized User
        public static string AuthorizedUserApi_GetRepresentativeInfo = "AuthorizedUserApi/GetRepresentativeInfo";
        public static string AuthorizedUserApi_GetAuthRepresentativeInfo = "AuthorizedUserApi/GetAuthRepresentative";
        public static string AuthorizedUserApi_SaveRepresentativeInfo = "AuthorizedUserApi/SaveRepresentativeInfo";
        public static string AuthorizedUserApi_DeleteRepresentativeInfo = "AuthorizedUserApi/DeleteRepresentativeInfo";

        //Refugee Medical Assistance
        public static string RefugeeMedicalAssistanceApi_GetApplicant = "RefugeeMedicalAssistanceApi/GetApplicant";
        public static string RefugeeMedicalAssistanceApi_SaveApplicant = "RefugeeMedicalAssistanceApi/SaveApplicant";
        public static string RefugeeMedicalAssistanceApi_GetSpouseInfo = "RefugeeMedicalAssistanceApi/GetSpouseInfo";
        public static string RefugeeMedicalAssistanceApi_SaveSpouseInfo = "RefugeeMedicalAssistanceApi/SaveSpouseInfo";
        public static string RefugeeMedicalAssistanceApi_GetIncomes = "RefugeeMedicalAssistanceApi/GetIncomes";
        public static string RefugeeMedicalAssistanceApi_SaveIncome = "RefugeeMedicalAssistanceApi/SaveIncome";
        public static string RefugeeMedicalAssistanceApi_GetMedicalInsurance = "RefugeeMedicalAssistanceApi/GetMedicalInsurance";
        public static string RefugeeMedicalAssistanceApi_SaveMedicalInsurance = "RefugeeMedicalAssistanceApi/SaveMedicalInsurance";
        public static string RefugeeMedicalAssistanceApi_GetEligibilityDetermination = "RefugeeMedicalAssistanceApi/GetEligibilityDetermination";
        public static string RefugeeMedicalAssistanceApi_SaveEligibilityDetermination = "RefugeeMedicalAssistanceApi/SaveEligibilityDetermination";
        public static string RefugeeMedicalAssistanceApi_EnrollComplete = "RefugeeMedicalAssistanceApi/EnrollComplete";

        // Worker Portal Account
        public static string WorkerPortalAccount_UpsertWorkerReminder = "WorkerPortalAccountApi/UpsertWorkerReminder";
        public static string WorkerPortalAccount_CompleteWorkerReminder = "WorkerPortalAccountApi/CompleteWorkerReminder";
        public static string WorkerPortalAccount_GetActiveWorkerReminderCount = "WorkerPortalAccountApi/GetActiveReminderCount";
        public static string WorkerPortalAccount_GetActiveReminderCountByWorkerNumber = "WorkerPortalAccountApi/GetActiveReminderCountByWorkerNumber";
        public static string WorkerPortalAccount_GetActiveReminder = "WorkerPortalAccountApi/GetActiveReminder";
        public static string WorkerPortalAccount_DeleteWorkerReminder = "WorkerPortalAccountApi/DeleteWorkerReminder";
        public static string WorkerPortalAccount_GetWorkerPortalAccountByUsername = "WorkerPortalAccountApi/GetWorkerPortalAccountByUsername";

        // Historical Data
        public static string HistoricalDataApi_GetSearchResults = "HistoricalDataApi/GetSearchResults";
        public static string HistoricalDataApi_FetchDetails = "HistoricalDataApi/FetchDetails";

        // Reference
        public static string ReferenceApi_GetAllColaFactSheetCategories = "ReferenceApi/GetAllColaFactSheetCategories";
        public static string ReferenceApi_GetAllColaFactSheetCategoriesForYear = "ReferenceApi/GetAllColaFactSheetCategoriesForYear";
        public static string ReferenceApi_GetColaFactSheetYears = "ReferenceApi/GetColaFactSheetYears";
        public static string ReferenceApi_ColaFactSheetAddNewYear = "ReferenceApi/ColaFactSheetAddNewYear";
        public static string ReferenceApi_GetLatestColaFactSheetCategory = "ReferenceApi/GetLatestColaFactSheetCategory";
        public static string ReferenceApi_UpdateColaFactSheetCategory = "ReferenceApi/UpdateColaFactSheetCategory";

    }
}