﻿using Cares.Api.Hub;
using Cares.Api.Infrastructure;
using Cares.Api.Infrastructure.Enums;
using Cares.Api.Infrastructure.Helper;
using Cares.Api.Infrastructure.WebAPI;
using Cares.Api.Messages.Applications;
using Cares.Api.Messages.ApplicationSnapshot;
using Cares.Api.Messages.ElderlyDisabled;
using Cares.Api.Messages.InRule;
using Cares.Api.Messages.Mapper;
using Cares.Api.Messages.Person;
using Cares.Data;
using Cares.Data.DataAbstractionLayer;
using Cares.Data.DataAbstractionLayer.NonMAGIIncome;
using Cares.Data.DataAbstractionLayer.Person;
using Cares.Infrastructure.Log;
using Cares.Models.Application;
using Cares.Models.DataExchange.SSI.ViewModel;
using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel;
using Cares.Services.AppTier.BLL.Application;
using Cares.Services.AppTier.BLL.AuthorizedRep;
using Cares.Services.AppTier.BLL.ElderlyDisabled.Expedite;
using Cares.Services.AppTier.Controllers;
using Cares.Services.AppTier.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ENUMS = Cares.Api.Infrastructure.Enums;

namespace Cares.Services.AppTier.BLL.ElderlyDisabled
{
    /// <summary>
    /// BLL for Application data
    /// </summary>
    public class ElderlyDisabledBll
    {
        protected ILog _log;

        public ElderlyDisabledBll()
        {
            _log = new Cares.Infrastructure.Log.Log(this.GetType());
        }
        /// <summary>
        /// Get Application Information
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledApplicationDto GetApplication(long applicationId, Guid tokenId)
        {
            ElderlyDisabledApplicationDto applicationResponse = ElderlyDisabledDal.GetApplication(applicationId, tokenId);
            if (applicationResponse != null)
            {
                return applicationResponse;
            }

            return new ElderlyDisabledApplicationDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Performs inserts/updates on an ElderlyDisabledApplicationResponse
        /// </summary>
        /// <param name="application">The application.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SaveApplication(ElderlyDisabledApplicationDto application, string username, Guid tokenId)
        {
            //Get previously created Application data for validating snapshot generation
            bool isNewApp = (application.Application.ApplicationId == 0);
            byte applicationStatusBeforeSave = 0;
            if (!isNewApp)
            {
                applicationStatusBeforeSave = ApplicationDal.GetApplicationStatusId(application.ApplicationId);
            }
            bool isTerminalStatus = ApplicationHelper.IsEnrollmentComplete(application.ApplicationStatusId);
            bool shouldTriggerMmisSend = false;

            // Save everything but address
            var result = ElderlyDisabledDal.SaveApplication(application, username, tokenId);

            if (result.IsSuccessful)
            {
                //Generated snapshot if the application is Admin closed
                if (!isNewApp)
                {
                    if (applicationStatusBeforeSave != (byte)enumApplicationStatus.Admin_Closed &&
                        application.Application.ApplicationStatusId == (byte)enumApplicationStatus.Admin_Closed)
                    {
                        BaseApiResponse snapshotapiresponse = GenerateAndSaveApplicationSnapshot(application.Application.ApplicationId, username, tokenId);
                        if (!snapshotapiresponse.IsSuccessful)
                        {
                            // Even though EnrollComplete succeeded (IsSuccessful = true), we do want to report back
                            // this failure, but leave IsSuccessful = true:
                            result.CaresError = CaresError.ApplicationSnapshotFailed;
                        }
                    }
                }

                // Now save address.  Test for changes in case we need to clear the Insurance Send indicator
                var before = PersonDal.GetPersonAddresses(application.Application.ContactPersonId);
                Api.AddressDAL addressDAL = new Api.AddressDAL(Api.Infrastructure.ProcessorKind.InHouse);
                string addressSaveResult = addressDAL.SaveAddressData(application.Application.ApplicationId,
                                            application.Application.ContactPersonId, application.PersonAddresses,
                                            true, null, application.Application.UpdatedBy, tokenId);
                if (isTerminalStatus)
                {
                    var after = PersonDal.GetPersonAddresses(application.Application.ContactPersonId);
                    shouldTriggerMmisSend = didAddressChange(before, after);
                }

                if (!string.IsNullOrEmpty(addressSaveResult))
                {
                    return new BaseApiResponse()
                    {
                        IsSuccessful = false,
                        ErrorMessage = addressSaveResult
                    };
                }

                // Perform hub calls
                // And Note: Only SSI Related should call equifax
                var applicationBll = new ApplicationBll();
                var hubcall = applicationBll.ProcessHubCallVerification(
                               application.Application.ContactPersonId, application.Application.ApplicationId,
                               application.ApplicationElderlyDisabledDetail.ProgramOrCategory == ENUMS.enumElderlyDisabledMedicaidPrograms.SSIRelatedPrograms,
                               application.Application.UserName, tokenId);
            }

            // Update MMIS?
            if (isTerminalStatus && shouldTriggerMmisSend)
            {
                PersonDal.ClearInsuranceSendIndicatorForPerson(application.Application.ContactPersonId, username);
            }

            return result;
        }

        /// <summary>
        /// Compares Address before and after.  Returns true if there was a change
        /// </summary>
        /// <param name="before"></param>
        /// <param name="after"></param>
        /// <returns></returns>
        private bool didAddressChange(PersonAddresses before, PersonAddresses after)
        {
            if (before.Addresses.Count != after.Addresses.Count)
            {
                return true;
            }
            // Match up the ordering in case it was off
            List<PersonAddress> beforeOrdered = before.Addresses.OrderBy(a => a.PersonAddressId).ToList();
            List<PersonAddress> afterOrdered = after.Addresses.OrderBy(a => a.PersonAddressId).ToList();
            for (int i = 0; i < before.Addresses.Count; i++)
            {
                if (beforeOrdered[i].Address.AddressLine1 != afterOrdered[i].Address.AddressLine1 ||
                    beforeOrdered[i].Address.AddressLine2 != afterOrdered[i].Address.AddressLine2 ||
                    beforeOrdered[i].Address.City != afterOrdered[i].Address.City ||
                    beforeOrdered[i].Address.StateId != afterOrdered[i].Address.StateId ||
                    beforeOrdered[i].Address.ZipCode != afterOrdered[i].Address.ZipCode)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Saves phone info
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <param name="phoneList">The phone list.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SavePersonPhones(long personId, List<PhoneAndPersonPhone> phoneList, Guid tokenId)
        {
            string result = PersonDal.UpsertPersonPhones(personId, phoneList, tokenId);
            if (string.IsNullOrEmpty(result))
            {
                return new BaseApiResponse();
            }

            return new BaseApiResponse()
            {
                IsSuccessful = false,
                ErrorMessage = result
            };
        }

        /// <summary>
        /// Returns spouse info
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledSpouseDto GetSpouseInfo(long applicationId, Guid tokenId)
        {
            return ElderlyDisabledDal.GetSpouseInfo(applicationId, tokenId);
        }

        /// <summary>
        /// Upsert spouse data
        /// </summary>
        /// <param name="spouseInfo">The spouse information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse UpsertElderlyDisabledSpouseInfo(ElderlyDisabledSpouseDto spouseInfo, string username, Guid tokenId)
        {
            bool isTerminalStatus = new ApplicationSnapshotDAL().CheckApplicationIsInTerminalStatus(spouseInfo.ApplicationId, tokenId);
            BaseApiResponse response = ElderlyDisabledDal.UpsertElderlyDisabledSpouseInfo(spouseInfo, isTerminalStatus, username, tokenId);
            ElderlyDisabledSpouseDto spouseDataBefore = new ElderlyDisabledSpouseDto();

            if (response.IsSuccessful)
            {
                // if this is Terminal, we need to compare the spouse's address before and after:
                if (isTerminalStatus)
                {
                    spouseDataBefore = GetSpouseInfo(spouseInfo.ApplicationId, tokenId);
                }

                // Save address info if they answered the AddressSameAsPrimary question
                if (spouseInfo.SpouseAddress.AddressSameAsPrimary.HasValue)
                {
                    //  Data integrity, ensure the county id is null if the state is not Alabama.
                    if ((byte)enumState.Alabama != spouseInfo.SpouseAddress.PersonAddress.Address.StateId)
                    {
                        spouseInfo.SpouseAddress.PersonAddress.Address.CountyId = null;
                    }

                    // Force this single address item to be of type Home
                    spouseInfo.SpouseAddress.PersonAddress.AddressTypeId = (int)enumAddressType.Home;
                    PersonAddresses addresses = new PersonAddresses();
                    addresses.Addresses.Add(spouseInfo.SpouseAddress.PersonAddress);
                    Cares.Api.AddressDAL addressDAL = new Cares.Api.AddressDAL(Api.Infrastructure.ProcessorKind.InHouse);
                    string addressSaveResult = addressDAL.SaveAddressData(spouseInfo.ApplicationId, spouseInfo.Spouse.PersonId, addresses,
                        false, spouseInfo.SpouseAddress.AddressSameAsPrimary, spouseInfo.SpouseAddress.PersonAddress.UpdatedBy, tokenId);

                    if (!string.IsNullOrEmpty(addressSaveResult))
                    {
                        return new BaseApiResponse()
                        {
                            IsSuccessful = false,
                            ErrorMessage = addressSaveResult
                        };
                    }
                }

                if (isTerminalStatus)
                {
                    // If spouse address changed, we need to send an update to MMIS
                    var spouseDataAfter = GetSpouseInfo(spouseInfo.ApplicationId, tokenId);
                    if (didAddressChange(new PersonAddresses() { Addresses = new List<PersonAddress>() { spouseDataBefore.SpouseAddress.PersonAddress } },
                        new PersonAddresses() { Addresses = new List<PersonAddress>() { spouseDataAfter.SpouseAddress.PersonAddress } }))
                    {
                        PersonDal.ClearInsuranceSendIndicatorForPerson(spouseInfo.ContactPersonId, username);
                    }
                }

                // Perform hub calls on spouse
                if (spouseInfo.Spouse != null && spouseInfo.Spouse.PersonId != 0)
                {
                    var appDetails = ElderlyDisabledDal.GetApplicationElderlyDisabledDetail(spouseInfo.ApplicationId, tokenId);
                    var applicationBll = new ApplicationBll();
                    var hubcall = applicationBll.ProcessHubCallVerification(
                                   spouseInfo.Spouse.PersonId, spouseInfo.ApplicationId,
                                   // Only SSI Related should make Equifax calls:
                                   appDetails.ProgramOrCategory == ENUMS.enumElderlyDisabledMedicaidPrograms.SSIRelatedPrograms,
                                   username, tokenId);
                }
            }

            return response;
        }

        /// <summary>
        /// Get Veteran Information
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// /// <returns></returns>
        public ElderlyDisabledVeteranDto SelectVeteranDetails(int applicationId, Guid tokenId)
        {
            using (var context = new CaresApplicationDBEntities())
            {
                // Get the app's primary contact ID
                var app = context.APPLICATIONs.FirstOrDefault(a => a.APPLICATION_ID == applicationId);
                if (app != null)
                {
                    MSPPersonalInformationDAL mspPersonalInformationDAL = new MSPPersonalInformationDAL();
                    ElderlyDisabledVeteranDto elderlyDisabledObj = new ElderlyDisabledVeteranDto();

                    // Set base values
                    ApplicationDal.SetBaseDtoValues(elderlyDisabledObj, app);

                    // Fill in the veteran detail
                    elderlyDisabledObj.VeteranDetails = mspPersonalInformationDAL.SelectVeteranDetails(context, applicationId, (int)app.CONTACT_PERSON_ID);

                    // Get primary contact info
                    elderlyDisabledObj.PrimaryContactInfo = ElderlyDisabledDal.GetEdPersonByPersonId(elderlyDisabledObj.ContactPersonId);

                    // Get the spouse info
                    var spouseInfo = GetSpouseInfo(applicationId, tokenId);
                    elderlyDisabledObj.SpouseInfo = spouseInfo?.Spouse;

                    return elderlyDisabledObj;
                }
                else
                {
                    return new ElderlyDisabledVeteranDto()
                    {
                        IsSuccessful = false,
                        CaresError = Api.Infrastructure.CaresError.NotFound
                    };
                }
            }
        }

        /// <summary>
        /// Upsert Elderly Disabled Veteran Info
        /// </summary>
        /// <param name="veteranInfo">The veteran information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse UpsertElderlyDisabledVeteranInfo(ElderlyDisabledVeteranDto veteranInfo, Guid tokenId)
        {
            performVeteranDataIntegrity(veteranInfo);

            long result;
            using (var context = new CaresApplicationDBEntities())
            {
                MSPPersonalInformationDAL mspPersonalInformationDAL = new MSPPersonalInformationDAL();
                MSPIntakeModel mspIntake = new MSPIntakeModel()
                {
                    ApplicationDetails = new MSPIntakeApplicationDetails
                    {
                        VeteranDetails = veteranInfo.VeteranDetails,
                        PersonId = (int)veteranInfo.ContactPersonId
                    },
                    UpdatedBy = veteranInfo.UpdatedBy,
                    ApplicationId = (int)veteranInfo.ApplicationId,
                };
                mspPersonalInformationDAL.SaveVeteranStatus(mspIntake, context);
                result = (int)mspIntake.ApplicationDetails.VeteranDetails.VeteranStatusId;
            }
            return new BaseApiResponse()
            {
                Id = result,
                IsSuccessful = result != 0
            };
        }

        /// <summary>
        /// Data integrity on Veteran Info
        /// </summary>
        /// <param name="veteranInfo">The veteran info.</param>
        private void performVeteranDataIntegrity(ElderlyDisabledVeteranDto veteranInfo)
        {
            // If relationship is self or spouse, clear out person info.  We want those
            // values always pulled from their original values:
            if (veteranInfo.VeteranDetails.RelationshipToVeteran == ((byte)enumRelationship.Self).ToString() ||
                veteranInfo.VeteranDetails.RelationshipToVeteran == ((byte)enumRelationship.Husband_or_Wife).ToString())
            {
                veteranInfo.VeteranDetails.VeteranFirstName = null;
                veteranInfo.VeteranDetails.VeteranMiddleName = null;
                veteranInfo.VeteranDetails.VeteranLastName = null;
                veteranInfo.VeteranDetails.SuffixId = null;
                veteranInfo.VeteranDetails.VeteranSsn = null;
            }
        }

        /// <summary>
        /// Gets household members
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledHouseholdMembersDto GetHouseholdMembers(long applicationId, Guid tokenId)
        {
            var membersDto = new ElderlyDisabledHouseholdMembersDto()
            {
                ApplicationId = (int)applicationId,
                HouseholdMembers = ElderlyDisabledDal.SelectHouseholdMembers(applicationId, tokenId)
            };
            ApplicationDal.SetBaseDtoValues(membersDto, applicationId);

            return membersDto;
        }

        /// <summary>
        /// Saves household members
        /// </summary>
        /// <param name="memberInfo">The member information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SaveHouseholdMembers(ElderlyDisabledHouseholdMembersDto memberInfo, Guid tokenId)
        {
            return new BaseApiResponse { IsSuccessful = ElderlyDisabledDal.UpdateHouseholdMembers(memberInfo, tokenId) };
        }

        /// <summary>
        /// Deletes household member
        /// </summary>
        /// <param name="applicationNonMagiNoSsnPersonId">The application non-magi no ssn person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse DeleteHouseholdMember(int applicationNonMagiNoSsnPersonId, Guid tokenId)
        {
            return new BaseApiResponse { IsSuccessful = ElderlyDisabledDal.DeleteHouseholdMember(applicationNonMagiNoSsnPersonId, tokenId) };
        }

        /// <summary>
        /// Gets the facility providers.
        /// </summary>
        /// <param name="county">The county.</param>
        /// <param name="facilityProvider">The facility provider.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledExpediteFacilityListDto GetExpediteFacilityProviders(enumCounty county, FacilityProviderType facilityProvider, Guid tokenId)
        {
            //TODO: Update by uncommenting function when SP is created.
            var facilityProviders = new List<ElderlyDisabledExpediteFacilityDto>(); //RefTablesDAL.GetExpediteFacilityProviders(county, facilityProvider);

            if (facilityProviders != null)
            {
                return new ElderlyDisabledExpediteFacilityListDto
                {
                    ElderlyDisabledExpediteFacilityList = facilityProviders,
                    IsSuccessful = true
                };
            }

            return new ElderlyDisabledExpediteFacilityListDto
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Gets the property information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledPropertyDto GetPropertyInformation(long applicationId, Guid tokenId)
        {
            var result = ElderlyDisabledDal.GetPropertyInformation(applicationId, tokenId);

            if (result != null)
            {
                result.IsSuccessful = true;

                return result;
            }

            return new ElderlyDisabledPropertyDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Get the resource info by applicationId
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledResourceDto GetResourceInformation(long applicationId, Guid tokenId)
        {
            ElderlyDisabledResourceDto appResourceInfo = ElderlyDisabledDal.SelectResourceInformation(applicationId, tokenId);

            if (appResourceInfo != null)
            {
                // Check for bank resource is not null
                if (appResourceInfo.ResourceBankDetails != null)
                {
                    foreach (var bankDetail in appResourceInfo.ResourceBankDetails)
                    {
                        if (bankDetail.BankId > 0)
                        {
                            var institutions = ElderlyDisabledDal.GetFinancialInstitutions(bankDetail.BankId, tokenId);
                            var details = institutions.FinancialInstitutions.FirstOrDefault();
                            bankDetail.Address = details.BankAddress;
                            bankDetail.BankName = details.BankName;
                        }
                    }
                }
                return appResourceInfo;
            }

            return new ElderlyDisabledResourceDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled property information.
        /// </summary>
        /// <param name="propertyInformation">The property information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <param name="username">The username.</param>
        /// <returns></returns>
        public BaseApiResponse SavePropertyInformation(ElderlyDisabledPropertyDto propertyInformation, string username, Guid tokenId)
        {
            return new BaseApiResponse { IsSuccessful = ElderlyDisabledDal.SavePropertyInformation(propertyInformation, username, tokenId) };
        }

        /// <summary>
        /// Get E & D Eligibility Enrollment for snapshot
        /// </summary>
        /// <param name="applicationId">The applicationId Identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledEligibilityDeterminationsDto GetElderlyDisabledEligibilityEnrollmentSnapshot(long applicationId, Guid tokenId)
        {
            ElderlyDisabledEligibilityDeterminationsDto response = new ElderlyDisabledEligibilityDeterminationsDto();
            var edEnrollmentDenials = Task.Run(() => ElderlyDisabledDal.GetElderlyDisabledEnrollmentSegmentsDenials((long)applicationId, tokenId));
            var edEnrollment = Task.Run(() => ElderlyDisabledDal.GetEnrollmentEligibilitySegments((long)applicationId, tokenId));
            Task.WaitAll(edEnrollment, edEnrollmentDenials);
            response.Determinations.AddRange(edEnrollment.Result);
            response.Determinations.AddRange(edEnrollmentDenials.Result);

            // Formatting response so empty denial segments doesn't get added
            ElderlyDisabledEligibilityDeterminationsDto formattedResponse = new ElderlyDisabledEligibilityDeterminationsDto();
            foreach (var det in response.Determinations)
            {
                if (det.IsAward && det.StartDate != null && det.CancelDate != null)
                {
                    formattedResponse.Determinations.Add(det);
                }
                else
                {
                    if (det.DenialReasons.Count > 0)
                    {
                        formattedResponse.Determinations.Add(det);
                    }
                }
            }

            return formattedResponse;
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled resource information.
        /// </summary>
        /// <param name="applicationResourceInfo">The application resource info.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SaveResourceInformation(ElderlyDisabledResourceDto applicationResourceInfo, string username, Guid tokenId)
        {
            return new BaseApiResponse { IsSuccessful = ElderlyDisabledDal.SaveResourceInformation(applicationResourceInfo, username, tokenId) };
        }

        /// <summary>
        /// Get E&D life insurance information by appId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledLifeInsuranceDto GetLifeInsuranceInfo(long applicationId, Guid tokenId)
        {
            ElderlyDisabledLifeInsuranceDto appInsuranceInfo = ElderlyDisabledDal.GetLifeInsuranceInfo(applicationId, tokenId);

            if (appInsuranceInfo != null)
            {
                return appInsuranceInfo;
            }

            return new ElderlyDisabledLifeInsuranceDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Get personal property information by applicationId.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledPersonalPropertyDto GetPersonalPropertyInfo(long applicationId, Guid tokenId)
        {
            var result = ElderlyDisabledDal.SelectPersonalPropertyInfo(applicationId, tokenId);

            if (result != null)
            {
                result.IsSuccessful = true;

                return result;
            }

            return new ElderlyDisabledPersonalPropertyDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the elderly disabled life insurance information.
        /// </summary>
        /// <param name="applicationInsuranceInfo">The insurance information.</param>
        /// <param name="tokenId">>The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SaveLifeInsuranceInfo(ElderlyDisabledLifeInsuranceDto applicationInsuranceInfo, Guid tokenId)
        {
            return new BaseApiResponse { IsSuccessful = ElderlyDisabledDal.SaveLifeInsuranceInfo(applicationInsuranceInfo, tokenId) };
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the personal property information.
        /// </summary>
        /// <param name="personalPropertyInfo">The personal property information.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SavePersonalPropertyInfo(ElderlyDisabledPersonalPropertyDto personalPropertyInfo, Guid tokenId)
        {
            return new BaseApiResponse { IsSuccessful = ElderlyDisabledDal.UpsertPersonalPropertyInfo(personalPropertyInfo, tokenId) };
        }

        /// <summary>
        /// Get E&D medical insurance information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledMedicalInsuranceDto GetMedicalInsuranceInfo(long applicationId, Guid tokenId)
        {
            ElderlyDisabledMedicalInsuranceDto appMedicalInsuranceInfo = ElderlyDisabledDal.SelectMedicalInsuranceInfo(applicationId, tokenId);

            if (appMedicalInsuranceInfo != null)
            {
                return appMedicalInsuranceInfo;
            }

            return new ElderlyDisabledMedicalInsuranceDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Saves (update(s)/insert(s)/delete(s)) the medical insurance information.
        /// </summary>
        /// <param name="medicalInsuranceInfo">The medical insurance info.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SaveMedicalInsuranceInfo(ElderlyDisabledMedicalInsuranceDto medicalInsuranceInfo, Guid tokenId)
        {
            return new BaseApiResponse { IsSuccessful = ElderlyDisabledDal.UpsertMedicalInsuranceInfo(medicalInsuranceInfo, tokenId) };
        }

        /// <summary>
        /// Upsert Liability Test data
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public BaseApiResponse UpsertLiabilityTestData(ElderlyDisabledLiabilityDto personLiabilityInfo, string username, Guid tokenId)
        {
            return ElderlyDisabledDal.UpsertLiabilityTestData(personLiabilityInfo, username, tokenId);
        }

        /// <summary>
        /// Gets liability info a person
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="personId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public ElderlyDisabledLiabilityDto GetPersonLiabilityInfo(long applicationId, long personId, Guid tokenId)
        {
            ElderlyDisabledLiabilityDto liabilityInfo = ElderlyDisabledDal.GetPersonLiabilityInfo(applicationId, personId, tokenId);

            if (liabilityInfo != null)
            {
                return liabilityInfo;
            }

            return new ElderlyDisabledLiabilityDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Upsert on a single liability segment
        /// </summary>
        /// <param name="personLiabilityInfo"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public ElderlyDisabledLiabilityDetailDto UpsertLiabilitySegment(ElderlyDisabledLiabilityDetailDto personLiabilityInfo, string username, Guid tokenId)
        {
            var segment = ElderlyDisabledDal.UpsertLiabilitySegment(personLiabilityInfo, username, tokenId);

            if (segment != null)
            {
                return segment;
            }

            return new ElderlyDisabledLiabilityDetailDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Delete liability segment
        /// </summary>
        /// <param name="personLiabilityId">The person liability identifier.</param>
        /// <param name="username">The user name.</param>
        /// <returns></returns>
        public BaseApiMessage DeleteLiabilitySegment(long personLiabilityId, string username)
        {
            ElderlyDisabledDal.DeleteLiabilitySegment(personLiabilityId, username);
            return new BaseApiMessage();
        }

        /// <summary>
        /// Truncate the liability segments
        /// </summary>
        /// <param name="enrollmentInfo">The list of elderly disabled eligibility determination dto.</param>
        /// <param name="username">The user name.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiMessage TruncateLiabilitySegments(ElderlyDisabledEligibilityDeterminationsDto enrollmentInfo, string username, Guid tokenId)
        {
            var snapshotDal = new ApplicationSnapshotDAL();
            bool isTerminalStatus = snapshotDal.CheckApplicationIsInTerminalStatus(enrollmentInfo.ApplicationId, tokenId);

            if (isTerminalStatus)
            {
                ElderlyDisabledDal.TruncateLiabilitySegments(enrollmentInfo.Determinations, enrollmentInfo.ContactPersonId, username, tokenId);
            }

            return new BaseApiMessage();
        }

        /// <summary>
        /// Gets the application information bar information.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledApplicationInfoBarDto GetAppInfoBarInfo(long applicationId, Guid tokenId)
        {
            var result = ElderlyDisabledDal.GetApplicationInformationBarInfo(applicationId, tokenId);

            return result;
        }

        /// <summary>
        /// Renew application.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse RenewApplication(long applicationId, string username, Guid tokenId)
        {
            var result = ElderlyDisabledDal.RenewApplication(applicationId, username, tokenId);
            return new BaseApiResponse { IsSuccessful = true, ApplicationId = result };
        }

        /// <summary>
        ///  Returns non Magi income
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledNonMagiIncomesDto GetNonMagiIncome(long applicationId, Guid tokenId)
        {
            var income = ElderlyDisabledDal.GetNonMagiIncome(applicationId, tokenId);

            // Get the TitleII Monthly income from the SSA composite result:
            var nonMAGIIncomeDAL = new NonMAGIIncomeDAL();
            income.HubVerifiedSsaIncomes = nonMAGIIncomeDAL.GetHubVerifiedSsaIncome((int)applicationId);

            return income;
        }

        /// <summary>
        ///  Save non Magi income
        /// </summary>
        /// <param name="nonMagiIncome">The non-magi income info.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiMessage SaveApplicationNonMagiIncome(ElderlyDisabledNonMagiIncomesDto nonMagiIncome, string username, Guid tokenId)
        {
            return ElderlyDisabledDal.UpsertNonMagiIncome(nonMagiIncome, username, tokenId);
        }

        /// <summary>
        ///  Save allocation info
        /// </summary>
        /// <param name="allocationDto">The spouse and family allocation info.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiMessage SaveAllocationInfo(ElderlyDisabledQitAndAllocationDto allocationDto, string username, Guid tokenId)
        {
            return ElderlyDisabledDal.UpsertAllocationInfo(allocationDto, username, tokenId);
        }

        /// <summary>
        ///  Calculates the VA net value
        /// </summary>
        /// <param name="appId">The app Id.</param>
        /// <param name="appNonMagiIncomeId">The app non magi income Id.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiMessage CalculateVANetValues(long appId, long appNonMagiIncomeId, string username, Guid tokenId)
        {
            try
            {
                var nonMAGIIncomeDAL = new NonMAGIIncomeDAL();

                return new BaseApiMessage() { IsSuccessful = nonMAGIIncomeDAL.Calculate_VA_NET_Values(appId, appNonMagiIncomeId, username, tokenId) };
            }
            catch (Exception ex)
            {
                _log.Error(ex, tokenId, username);
                return new BaseApiMessage() { IsSuccessful = false };
            }

        }

        /// <summary>
        /// Gets the eligibility determinations.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledEligibilityDeterminationsDto GetEligibilityDeterminations(long applicationId, string username, Guid tokenId)
        {
            // Multiple calls require the person ID, get that first:
            long personId = 0;
            APPLICATION app = ApplicationDal.GetApplication(applicationId);
            if (app != null)
            {
                personId = app.CONTACT_PERSON_ID;
            }

            var determinations = ElderlyDisabledDal.GetEligibilityDeterminations(applicationId, personId, tokenId);
            fillInPreEligibilityCheckWarnings(applicationId, personId, determinations, username, tokenId);
            return determinations;
        }

        /// <summary>
        /// Gets the Details from an app with the QIT or Lien Indicators.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledApplicationDetailQitLienDto GetElderlyDisabledDetailQitDto(long applicationId, string username, Guid tokenId)
        {
            var details = new ElderlyDisabledApplicationDetailQitLienDto();

            var response = ApplicationDal.GetApplicationDetailsQitOrLien(applicationId);

            if (response?.PERSON_ID != null)
            {
                details.IsSuccessful = true;
                details.PersonId = (long)response.PERSON_ID;
                details.ApplicationId = response.APPLICATION_ID;
                details.DateOfDeath = (DateTime)response.DATE_OF_DEATH;
                details.IsQIT = response.QIT_IND;
                details.IsLien = response.LIEN_IND;
            }
            else
            {
                details.IsSuccessful = false;
                details.CaresError = CaresError.NotFound;
            }
            return details;
        }

        /// <summary>
        /// Creates any warnings the user needs to view when navigating to the Elig/Enroll screen for an application
        /// </summary>
        /// <param name="determinations"></param>
        private void fillInPreEligibilityCheckWarnings(long applicationId, long personId, ElderlyDisabledEligibilityDeterminationsDto determinations, string username, Guid tokenId)
        {
            sysnl_donotmodify__PERSON personData = Cares.Data.DataAbstractionLayer.Person.PersonDal.GetPersonByPersonId(personId);
            var enrollmentDal = new EnrollmentDal();
            var enrollments = enrollmentDal.GetPersonEnrollments(personData?.PERSON_ID ?? 0, username);
            ApplicationElderlyDisabledDetail edDetail = ElderlyDisabledDal.GetApplicationElderlyDisabledDetail(applicationId, tokenId);

            // SDX Income check:
            // Get SDX info from staging table
            SDXInfo sdxInfo = new SdxDal().GetSdxInfo(personData?.SSN);
            if (sdxInfo != null)
            {
                // Logic according to Product Backlog Item 226370 & 229846
                // Payment Status is "C01"?
                if (sdxInfo.PersonInfoSection?.PaymentStatus == SdxDal.PaymentStatus_C01)
                {
                    var firstFieldUIIncome = sdxInfo.UnearnedIncomeSection?.UnearnedIncomeList?.FirstOrDefault(ui => ui.Index == 1);
                    if (firstFieldUIIncome != null)
                    {
                        DateTime? startMonth = Cares.Portal.Infrastructure.DateHelper.ConvertMM_YYYYToNullableDate(firstFieldUIIncome.Start);
                        DateTime? throughMonth = Cares.Portal.Infrastructure.DateHelper.ConvertMM_YYYYToNullableDate(firstFieldUIIncome.Stop);
                        DateTime? stopMonth = throughMonth?.AddMonths(1);

                        // Take action if they have SSI Income and DO NOT have unearned income (SSI Income Only).
                        // If column PSTAT_01 = C01 and column IUETYP_TC_1 = Null or empty (Display the message prompt).
                        // If column PSTAT_01 = C01 and column IUETYP_TC_1 = Value and in column IUESTART_RD_1, IUESTOP_RD_1 the pay period is inactive(Display the message prompt).
                        if (string.IsNullOrWhiteSpace(firstFieldUIIncome.Type) ||
                            !(startMonth <= DateTime.Today && (!stopMonth.HasValue || DateTime.Today < stopMonth)))
                        {
                            determinations.PreEligibilityCheckWarnings.Add(SdxDal.RecipientReceivesSSIIncomeOnly);

                            // If user did NOT select SSI Retroactive on the current E&D app, do further hard-stop checks
                            if (edDetail.ElderlyDisabledProgramId != (byte)ENUMS.enumSSIProgram.Retroactive)
                            {
                                // If this person is also on an active SSI app, we need to notify the UI (to disable Award buttons)
                                bool onSsiApp = false;
                                foreach (var enrollment in enrollments.PersonEnrollments)
                                {
                                    // Active?
                                    DateTime? startDate = Portal.Infrastructure.DateHelper.ConvertStringToNullableDate(enrollment.CED);
                                    DateTime? endDate = Portal.Infrastructure.DateHelper.ConvertStringToNullableDate(enrollment.CancelDate);
                                    if (DateTime.Today >= startDate && DateTime.Today <= endDate)
                                    {
                                        // Is an SSI app?
                                        int subProgCatId = 0;
                                        int.TryParse(enrollment.ProgramCategoryId, out subProgCatId);
                                        ProgramSubCategory cat = ProgramSubCategory.GetProgramSubCategoryById(subProgCatId);
                                        if (cat?.Sub_Program_Category_Id == (byte)Cares.Api.Infrastructure.Constants.SubProgramCategories.SSI)
                                        {
                                            onSsiApp = true;
                                            break;
                                        }
                                    }
                                }
                                if (onSsiApp)
                                {
                                    determinations.EnDPreEligibilityProcessingCodes.Add(Cares.Api.Infrastructure.Constants.EnDPreEligibilityProcessingCode.RecipientReceivesSSIIncomeOnlyWhileOnSsiApp);
                                }
                            }
                        }
                    }
                }
            }

            using (var context = new CaresApplicationDBEntities())
            {
                // Check if person is suspended
                var suspendData = context.t_PERSON_SUSPENSION.FirstOrDefault(t => t.PERSON_ID == personId);
                if (suspendData != null)
                {
                    determinations.PersonIsSuspended =
                        suspendData.SUSPENSION_SOURCE_ID == Cares.Api.Infrastructure.Constants.AMAES_SuspensionSource_ProgramIntegrity &&
                        suspendData.SUSPENSION_REASON_ID == Cares.Api.Infrastructure.Constants.AMAES_SuspensionReason_FraudAndAbuse &&
                        suspendData.SUSPENSION_BEGIN_DATE <= DateTime.Today && suspendData.SUSPENSION_END_DATE >= DateTime.Today;

                    if (determinations.PersonIsSuspended)
                    {
                        // If so, add a warning message
                        determinations.PreEligibilityCheckWarnings.Add(Cares.Api.Infrastructure.Constants.AMAES_SuspensionReason_FraudAndAbuse_Message);
                    }
                }

            }
        }

        /// <summary>
        /// Saves the eligibility determinations.
        /// </summary>
        /// <param name="eligibility">The eligibility.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse SaveEligibilityDeterminations(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            return ElderlyDisabledDal.UpsertEligibilityDeterminations(eligibility, tokenId);
        }

        /// <summary>
        /// Creates an MSP enrollment
        /// </summary>
        /// <param name="eligibility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public BaseApiResponse EnrollMsp(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            // First, save the user's edits:
            var saveResult = ElderlyDisabledDal.UpsertEligibilityDeterminations(eligibility, tokenId);
            if (!saveResult.IsSuccessful)
            {
                // Something went wrong, stop here
                return saveResult;
            }

            var enrollmentSegments = eligibility.Determinations.Select(d => new EnrollmentSegment
            {
                StartDate = d.StartDate,
                CancelDate = d.CancelDate,
                ProgramSubCategoryId = d.ProgramSubCategoryId,
                OverrideReasonId = d.OverrideReasonId
            });

            return EnrollMspAndSaveResult(eligibility, username, tokenId, enrollmentSegments, false);
        }

        /// <summary>
        /// Gets the primary sponsor info.
        /// </summary>
        /// <param name="personId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public RepresentativeInfoDto GetPrimarySponsorInfo(long personId, Guid tokenId)
        {
            RepresentativeInfoDto primarySponsorInfo = ElderlyDisabledDal.GetPrimarySponsorInfo(personId, tokenId);

            if (primarySponsorInfo != null)
            {
                return primarySponsorInfo;
            }

            return new RepresentativeInfoDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Gets the elderly disabled non-magi income info for Elig/Enroll screen.
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="personId">The person identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ElderlyDisabledNonMagiIncomesDto GetElderlyDisabledNonMagiIncomeInfo(long applicationId, long personId, Guid tokenId)
        {
            List<NonMagiIncome> nonMagiIncomeInfo = ElderlyDisabledDal.GetElderlyDisabledNonMagiIncomeInfo(applicationId, tokenId);

            if (nonMagiIncomeInfo != null)
            {
                return new ElderlyDisabledNonMagiIncomesDto()
                {
                    ApplicationId = applicationId,
                    ContactPersonId = personId,
                    NonMagiIncomes = nonMagiIncomeInfo
                };
            }

            return new ElderlyDisabledNonMagiIncomesDto()
            {
                IsSuccessful = false,
                CaresError = Api.Infrastructure.CaresError.NotFound
            };
        }

        /// <summary>
        /// Imports an Expedite flat file line loaded into the staging EXPEDITE_IMPORT table.
        /// </summary>
        /// <param name="caller">The caller.</param>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse ImportExpedite(ElderlyDisabledApiController caller, int expediteImportId, string username, Guid tokenId)
        {
            ExpediteAppCreator creator = new ExpediteAppCreator(caller, null, username, tokenId);
            return creator.CreateEnDAppFromExpediteImportId(expediteImportId);
        }

        /// <summary>
        ///  Returns Qit and Allocation info
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public ElderlyDisabledQitAndAllocationDto GetQitAndAllocationInfo(long applicationId, Guid tokenId)
        {
            Tuple<long, byte?> familySizeResult = ElderlyDisabledDal.GetFamilyAllocationTotalFamilyMembers(applicationId, tokenId);

            var nmi = new ElderlyDisabledQitAndAllocationDto()
            {
                EnDApplicationDetailId = familySizeResult.Item1,
                FamilySize = familySizeResult.Item2,
                QitData = ElderlyDisabledDal.GetNonMagiDetailForQitByAppId(applicationId),
                SpouseAllocationList = ElderlyDisabledDal.GetSpouseAllocationDetailsByAppId(applicationId),
                FamilyAllocationList = ElderlyDisabledDal.GetFamilyAllocationDetailsByAppId(applicationId)
            };

            ApplicationDal.SetBaseDtoValues(nmi, applicationId);

            return nmi;
        }

        /// <summary>
        /// Called by the front end, for dashboard display, or other (displaying error to user who tries to navigate to a partial import)
        /// </summary>
        /// <param name="expediteImportId">The expedite import identifier.</param>
        /// <param name="applicationId">The application identifier.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public StagingExpediteImportDetailsDto GetImportExpediteDetails(int expediteImportId, long applicationId, string username, Guid tokenId)
        {
            return ElderlyDisabledDal.GetStagingExpediteImportDetails(expediteImportId, applicationId, tokenId);
        }

        /// <summary>
        /// Completes the eligibility process.
        /// </summary>
        /// <param name="eligibility">The eligibility.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse EnrollComplete(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId)
        {
            // Populating ProgramSubCategory/StateAidCategory per segment inside eligibility object.
            var resp = populateProgramSubCategoryIntoEligibility(eligibility, tokenId);

            if (!resp.IsSuccessful)
            {
                return resp;
            }

            var enrollCompleteResponse = ElderlyDisabledDal.EnrollComplete(eligibility, username, tokenId);
            if (enrollCompleteResponse.IsSuccessful)
            {
                // Check if any ex parte segments are present
                var exparteSegments = eligibility.Determinations.Where(d => d.ExparteProgramSubCategoryId.HasValue && d.ExparteProgramSubCategoryId > 0);
                if (exparteSegments.Count() > 0)
                {
                    //Get the start and cancel dates for this new MSP app that we will be creating
                    var mspStartDate = exparteSegments.First().CancelDate;
                    var mspCancelDate = mspStartDate?.AddMonths(12);

                    var enrollmentSegments = exparteSegments.Select(d => new EnrollmentSegment
                    {
                        StartDate = mspStartDate,
                        CancelDate = mspCancelDate,
                        ProgramSubCategoryId = d.ExparteProgramSubCategoryId,
                        OverrideReasonId = d.CancelReasonId
                    });
                    // We have to keep returning the "AwardOrDeny" identifier in the Object.Id
                    var exparteResponse = EnrollMspAndSaveResult(eligibility, username, tokenId, enrollmentSegments, true);
                    if (!exparteResponse.IsSuccessful)
                    {
                        return exparteResponse;
                    }
                    enrollCompleteResponse.ApplicationId = exparteResponse.Id;
                }
            }

            return enrollCompleteResponse;
        }

        private BaseApiResponse EnrollMspAndSaveResult(ElderlyDisabledEligibilityDeterminationsDto eligibility, string username, Guid tokenId, IEnumerable<EnrollmentSegment> enrollmentSegments, bool isExparte)
        {
            var request = new CopyEDAppToMspRequest()
            {
                EAndDApplicationId = eligibility.ApplicationId,
                PersonId = eligibility.ContactPersonId,
                CreatedBy = username,
                IsExparte = isExparte,
                EnrollmentSegments = enrollmentSegments.OrderByDescending(d => d.CancelDate).ToArray()
            };

            // Make a call to the copyApp service in WebMethods (aka EnrollMSP)
            var eligDAL = new Api.EligibilityEnrollmentDAL(Api.Infrastructure.ProcessorKind.InHouse);
            var mspEnrollResponse = eligDAL.EnrollMsp(request, tokenId);
            var callSucceeded = mspEnrollResponse?.responseStatus == "Success";

            // If webMethods returned an ExCustomMsg value, there was an error, and that value needs to be displayed to the user
            if (!string.IsNullOrEmpty(mspEnrollResponse.ExCustomMsg))
            {
                BaseApiResponse response = new BaseApiResponse()
                {
                    ErrorMessage = mspEnrollResponse.ExCustomMsg,
                    CaresError = CaresError.ValidationError,
                    IsSuccessful = false,
                };
                return response;
            }

            // Fetch the new MSP app Id and update the determinations
            var mspSaveResult = ElderlyDisabledDal.EnrollMsp(eligibility, callSucceeded, tokenId);
            if (mspSaveResult.IsSuccessful)
            {
                // Store this MSP app id
                ElderlyDisabledDal.StoreMspAppId(eligibility.ApplicationId, mspSaveResult.Id, tokenId);

                // Clear out MSP determinations. At this point, these are the only ones in this list:
                eligibility.Determinations.Clear();
                ElderlyDisabledDal.UpsertEligibilityDeterminations(eligibility, tokenId);
            }

            return mspSaveResult;
        }


        /// <summary>
        /// Obtaining correct ProgramSubCategoryId by using InRule
        /// </summary>
        /// <param name="eligibility"></param>
        /// <param name="tokenId"></param>
        private BaseApiResponse populateProgramSubCategoryIntoEligibility(ElderlyDisabledEligibilityDeterminationsDto eligibility, Guid tokenId)
        {
            // filtering only the award segments
            var awardDeterminations = eligibility.Determinations.Where(d => d.DenialReasons.Count() == 0);

            if (awardDeterminations.Count() > 0)
            {
                BaseApiResponse resp = new BaseApiResponse();
                // Obtaining the app information to get the Blind indicator, EDProgram, and DOB.
                var info = this.GetApplication(eligibility.ApplicationId, tokenId);
                resp = info;

                if (info?.ApplicationElderlyDisabledDetail == null)
                {
                    resp.ErrorMessage += $".  In populateProgramSubCategoryIntoEligibility, could not find ApplicationElderlyDisabledDetail for app id {eligibility.ApplicationId}";
                    resp.IsSuccessful = false;
                    return resp;
                }

                var programCode = ElderlyDisabledProgram.ElderlyDisabledPrograms.FirstOrDefault(prog => prog.Id == ((byte)info.ApplicationElderlyDisabledDetail.ElderlyDisabledProgramId));
                if (info == null || info.Person.DateOfBirth == null || info.Application.CreatedDate == null || programCode == null)
                {
                    resp.ErrorMessage += "At least one of the following fields are missing: DateOfBirth, CreatedDate, ElderlyDisabledProgramId.";
                    resp.IsSuccessful = false;
                    return resp;
                }

                // Calling the RuleSet to determine the StateAidCategory for each Award segment in the enroll determinations.
                foreach (var det in awardDeterminations)
                {
                    var mspType = ElderlyDisabledAwardOption.GetElderlyDisabledAwardOptionById((byte?)det.AwardOption);

                    if (mspType == null)
                    {
                        resp.IsSuccessful = false;
                        resp.ErrorMessage += "AwardOption is missing.";
                        break;
                    }

                    var req = new EnDStateAidCategoryDeterminationRequest()
                    {
                        MspType = mspType?.Description,
                        DOB = ((DateTime)info.Person.DateOfBirth).ToString("yyyy-MM-dd"),
                        EDProgramCode = programCode.Code,
                        Blind = info.ApplicationElderlyDisabledDetail.BlindInd ?? false,
                        CreatedDate = ((DateTime)info.Application.CreatedDate).ToString("yyyy-MM-dd")
                    };

                    var inRuleStateAidResp = new InRuleBll().GetEnDStateAidDetermination(req, tokenId);

                    if (!inRuleStateAidResp.IsSuccessful)
                    {
                        _log.Error("InRule call failed. " + inRuleStateAidResp.ErrorMessage, tokenId);
                        resp.IsSuccessful = false;
                        resp.ErrorMessage += "InRule call failed.";
                        break;
                    }

                    // Get/filter only E&D program sub categories.
                    det.ProgramSubCategoryId = (byte)ProgramSubCategory.ProgramSubCategories.FirstOrDefault(p => p.State_Aid_Category_Id == inRuleStateAidResp.StateAidCategoryCode.ToString() && p.Certifying_Agency_Code == "D")?.Id;
                }

                if (!resp.IsSuccessful)
                {
                    return resp;
                }
            }

            return new BaseApiResponse() { IsSuccessful = true, ApplicationId = eligibility.ApplicationId };
        }

        /// <summary>
        /// Returns providers from the REF_NH_PROVIDER table
        /// </summary>
        /// <returns></returns>
        public ProvidersDto GetProviders()
        {
            return ElderlyDisabledDal.GetProviders();
        }

        /// <summary>
        /// Gets the expedite facilities and providers
        /// </summary>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ExpediteFacilityProvidersDto GetExpediteFacilityProviders(string username, Guid tokenId)
        {
            return ElderlyDisabledDal.GetExpediteFacilityProviders(tokenId);
        }

        /// <summary>
        /// Gets the expedite facilities.
        /// </summary>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public ExpediteFacilityProvidersDto GetExpediteFacilitiesByString(string searchText, string username, Guid tokenId)
        {
            return ElderlyDisabledDal.GetExpediteFacilitiesByString(searchText, tokenId);
        }

        /// <summary>
        /// Returns a single ExpediteFacility, with provider match data
        /// </summary>
        /// <param name="expediteFacilityId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public ExpediteFacilityDto GetExpediteFacilityById(int expediteFacilityId, Guid tokenId)
        {
            return ElderlyDisabledDal.GetExpediteFacilityById(expediteFacilityId, tokenId);
        }

        /// <summary>
        /// Saves ExpediteFacilityDto data
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public BaseApiResponse UpdateExpediteFacility(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            BaseApiResponse result = new BaseApiResponse();

            result.IsSuccessful = ElderlyDisabledDal.UpdateExpediteFacility(expediteFacility, username, tokenId);
            return result;
        }

        /// <summary>
        /// ONLY sets the PROVIDER_ID for a given Expedite Facility
        /// </summary>
        /// <param name="expediteFacility"></param>
        /// <param name="username"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public BaseApiMessage SetExpediteFacilityProvider(ExpediteFacilityDto expediteFacility, string username, Guid tokenId)
        {
            return ElderlyDisabledDal.SetExpediteFacilityProvider(expediteFacility, username, tokenId);
        }

        /// <summary>
        /// Save the expedite facility information.
        /// </summary>
        /// <param name="expediteFacilityInfo">The expedite facility information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">>The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse InsertExpediteFacility(ExpediteFacilityDto expediteFacilityInfo, string username, Guid tokenId)
        {
            BaseApiResponse result = new BaseApiResponse();

            // Save the expedite facility info.
            result.IsSuccessful = ElderlyDisabledDal.InsertExpediteFacility(expediteFacilityInfo, username, tokenId);
            return result;
        }

        /// <summary>
        /// Save the financial institution information.
        /// </summary>
        /// <param name="financialInstitutionInfo">The financial institution information.</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">>The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse InsertFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, string username, Guid tokenId)
        {
            BaseApiResponse result = new BaseApiResponse();
            result.Id = ElderlyDisabledDal.InsertFinancialInstitution(financialInstitutionInfo, username, tokenId);
            return result;
        }

        /// <summary>
        /// Get the financial institution based on BankCode or BankName.
        /// </summary>
        /// <param name="searchText">Search info</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public FinancialInstitutionsDto GetFinancialInstitutionsByString(string searchText, Guid tokenId)
        {
            return ElderlyDisabledDal.GetFinancialInstitutionsByString(searchText, tokenId);
        }

        /// <summary>
        /// Get the financial institution based on BankId or Get all if BankId is null.
        /// </summary>
        /// <param name="bankId">search by bankId</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public FinancialInstitutionsDto GetFinancialInstitutions(int? bankId, Guid tokenId)
        {
            return ElderlyDisabledDal.GetFinancialInstitutions(bankId, tokenId);
        }

        /// <summary>
        /// Saves the financial institution.
        /// </summary>
        /// <param name="financialInstitutionInfo">The financial institution information.</param>
        /// <param name="MergeFlag">Merge or not merge</param>
        /// <param name="username">The username.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <param name="tokenId">The token identifier.</param>
        /// <returns></returns>
        public BaseApiResponse UpdateFinancialInstitution(FinancialInstitutionDto financialInstitutionInfo, bool MergeFlag, string username, Guid tokenId)
        {
            BaseApiResponse result = new BaseApiResponse();
            result.IsSuccessful = ElderlyDisabledDal.UpdateFinancialInstitution(financialInstitutionInfo, MergeFlag, username, tokenId);
            return result;
        }

        /// <summary>
        /// Gets all data for ElderlyDisbaled Snapshot
        /// </summary>
        public ElderlyDisabledSnapshotViewModel GetSnapshotData(long applicationId, string username, Guid tokenId)
        {
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            ElderlyDisabledSnapshotViewModel dc = new ElderlyDisabledSnapshotViewModel(applicationId);

            //this needs to be synchronous to pass personid as paramenter for Liability
            var applicationTask = Task.Run(() => this.GetApplication(applicationId, tokenId));
            Task.WaitAll(applicationTask);
            dc.Application = mapper.Map<ElderlyDisabledApplicationViewModel>(applicationTask.Result);

            var applicationInfoDetailsTask = Task.Run(() => (new ApplicationSnapshotDAL()).GetApplicationInformation(applicationId, tokenId));
            var representativesTask = Task.Run(() => new AuthorizedRepBll().GetRepresentativeInfo(applicationId, username, tokenId));
            var spouseTask = Task.Run(() => GetSpouseInfo(applicationId, tokenId));
            var veteransTask = Task.Run(() => (this.SelectVeteranDetails((int)applicationId, tokenId)));
            var householdTask = Task.Run(() => (this.GetHouseholdMembers(applicationId, tokenId)));
            var personalpropertyTask = Task.Run(() => (this.GetPersonalPropertyInfo(applicationId, tokenId)));
            var medicalinsuranceTask = Task.Run(() => (this.GetMedicalInsuranceInfo(applicationId, tokenId)));
            var nonmagiIncomeTask = Task.Run(() => (this.GetNonMagiIncome(applicationId, tokenId)));
            var propertyTask = Task.Run(() => (this.GetPropertyInformation(applicationId, tokenId)));
            var resourceTask = Task.Run(() => (this.GetResourceInformation(applicationId, tokenId)));
            var lifeinsuranceTask = Task.Run(() => (this.GetLifeInsuranceInfo(applicationId, tokenId)));
            var liabilityTask = Task.Run(() => this.GetPersonLiabilityInfo(applicationId, dc.Application.ContactPersonId, tokenId));
            var eligibilityenrollmentTask = Task.Run(() => this.GetElderlyDisabledEligibilityEnrollmentWithHub((int)applicationId, tokenId));

            Task.WaitAll(applicationInfoDetailsTask, representativesTask, spouseTask,
                          veteransTask, householdTask, personalpropertyTask, medicalinsuranceTask, nonmagiIncomeTask,
                          propertyTask, resourceTask, lifeinsuranceTask, liabilityTask, eligibilityenrollmentTask);

            // Put these results into the view model
            dc.ReceivedDate = applicationInfoDetailsTask.Result.ReceivedDate;
            dc.CompleteDate = applicationInfoDetailsTask.Result.CompleteDate;
            dc.DeterminedDate = applicationInfoDetailsTask.Result.DeterminedDate;
            dc.DataEntryDate = applicationInfoDetailsTask.Result.DataEntryDate;
            dc.Representatives = ElderlyDisabledMapper.MapToElderlyDisableRepresentativesViewModel(applicationId, representativesTask.Result);
            dc.Representatives.ApplicationStatusId = applicationTask.Result.ApplicationStatusId;
            dc.Spouse = mapper.Map<ElderlyDisabledSpouseViewModel>(spouseTask.Result);
            dc.Veterans = mapper.Map<ElderlyDisabledVeteranViewModel>(veteransTask.Result);
            dc.Household = mapper.Map<ElderlyDisabledHouseholdMembersViewModel>(householdTask.Result);
            dc.PersonalProperty = mapper.Map<ElderlyDisabledPersonalPropertyViewModel>(personalpropertyTask.Result);
            dc.MedicalInsurance = mapper.Map<ElderlyDisabledMedicalInsuranceViewModel>(medicalinsuranceTask.Result);
            dc.NonMagiIncome = mapper.Map<ElderlyDisabledNonMagiIncomeViewModel>(nonmagiIncomeTask.Result);
            dc.Property = mapper.Map<ElderlyDisabledPropertyViewModel>(propertyTask.Result);
            dc.Resource = mapper.Map<ElderlyDisabledResourceViewModel>(resourceTask.Result);
            dc.LifeInsurance = mapper.Map<ElderlyDisabledLifeInsuranceViewModel>(lifeinsuranceTask.Result);
            dc.Liability = mapper.Map<ElderlyDisabledLiabilityViewModel>(liabilityTask.Result);
            dc.EligibilityEnrollment = eligibilityenrollmentTask.Result;
            dc.IsSuccessful = (dc.Application.IsSuccessful && dc.Representatives.IsSuccessful && dc.Spouse.IsSuccessful
                               && dc.Veterans.IsSuccessful && dc.Household.IsSuccessful && dc.PersonalProperty.IsSuccessful
                               && dc.MedicalInsurance.IsSuccessful && dc.NonMagiIncome.IsSuccessful && dc.Property.IsSuccessful
                               && dc.Resource.IsSuccessful && dc.LifeInsurance.IsSuccessful && dc.Liability.IsSuccessful
                               && dc.EligibilityEnrollment.IsSuccessful);
            return dc;
        }

        public ElderlyDisabledEligibilityEnrollmentViewModel GetElderlyDisabledEligibilityEnrollmentWithHub(long applicationId, Guid tokenId)
        {
            ElderlyDisabledEligibilityEnrollmentViewModel vm = new ElderlyDisabledEligibilityEnrollmentViewModel();
            var mapper = ElderlyDisabledMapper.GetElderlyDisabledMapper();
            var eligDto = Task.Run(() => GetElderlyDisabledEligibilityEnrollmentSnapshot(applicationId, tokenId));
            var eligHub = Task.Run(() => new Hub(ProcessorKind.Worker).GetHubResponseInfo(applicationId.ToString(), tokenId));

            Task.WaitAll(eligHub, eligDto);

            vm.EnDDeterminations = this.CombineDenailReasonsForEnrollment(
                                   mapper.Map<List<ElderlyDisabledEligEnrollAwardDenyViewModel>>(eligDto.Result.Determinations))
                                   .OrderBy(d => d.StartDate).ToList();

            vm.HubInfo = eligHub.Result;

            return vm;
        }

        public List<ElderlyDisabledEligEnrollAwardDenyViewModel> CombineDenailReasonsForEnrollment(List<ElderlyDisabledEligEnrollAwardDenyViewModel> elderlyDisabledElig)
        {
            List<ElderlyDisabledEligEnrollAwardDenyViewModel> elderlyDisbaledList = new List<ElderlyDisabledEligEnrollAwardDenyViewModel>();

            Dictionary<(DateTime? start, DateTime? end, byte? stateAidCategoryId, short? OverrideReasonId, string OverrideBy, DateTime? OverrideDate), List<short>> elderly = new Dictionary<(DateTime? start, DateTime? end, byte? stateAidCategoryId, short? OverrideReasonId, string OverrideBy, DateTime? OverrideDate), List<short>>();
            foreach (var edEnroll in elderlyDisabledElig)
            {
                (DateTime? st, DateTime? ed, byte? scId, short? OverrideReasonId, string OverrideBy, DateTime? OverrideDate) objCheck = (edEnroll.StartDate, edEnroll.CancelDate, edEnroll.StateAidCategoryId, edEnroll.OverrideReasonId, edEnroll.OverrideBy, edEnroll.OverrideDate);
                if (elderly.ContainsKey(objCheck))
                {
                    elderly[objCheck].AddRange(edEnroll.DenialReasons);
                }
                else
                {
                    elderly.Add(objCheck, edEnroll.DenialReasons);
                }
            }
            foreach (var row in elderly)
            {
                ElderlyDisabledEligEnrollAwardDenyViewModel enrollObj = new ElderlyDisabledEligEnrollAwardDenyViewModel()
                {
                    StartDate = row.Key.start,
                    CancelDate = row.Key.end,
                    StateAidCategoryId = row.Key.stateAidCategoryId,
                    DenialReasons = row.Value,
                    OverrideBy = row.Key.OverrideBy,
                    OverrideDate = row.Key.OverrideDate,
                    OverrideReasonId = row.Key.OverrideReasonId
                };
                elderlyDisbaledList.Add(enrollObj);
            }
            return elderlyDisbaledList;
        }

        public BaseApiResponse GenerateAndSaveApplicationSnapshot(long applicationId, string username, Guid tokenId)
        {
            try
            {
                ElderlyDisabledSnapshotViewModel edsnapshot = GetSnapshotData(applicationId, username, tokenId);
                string html = MvcViewRenderer.RenderView("~/Views/Snapshot/ElderlyDisabledSnapshot.cshtml", edsnapshot);
                ApplicationSnapshotDto snapshot = new ApplicationSnapshotDto
                {
                    ApplicationId = applicationId,
                    ApplicationSnapshot = StringCompressor.Compress(html),
                    IsCurrent = true,
                    CreatedBy = username
                };
                return new ApplicationBll().SaveApplicationSnapshot(snapshot, tokenId);
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                return new BaseApiResponse()
                {
                    IsSuccessful = false,
                    CaresError = CaresError.ApplicationSnapshotFailed,
                    ErrorMessage = $"GenerateAndSaveApplicationSnapshot failed for applicationId {applicationId}"
                };
            }
        }

        public ExparteApplicationInfoDto GetExparteAppInfoByPersonId(int personId, Guid tokenId)
        {
            return ElderlyDisabledDal.GetExparteAppInfoByPersonId(personId);
        }
    }
}