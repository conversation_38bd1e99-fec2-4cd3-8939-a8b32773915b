﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class OtherDocumentType {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal OtherDocumentType() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.OtherDocumentType", typeof(OtherDocumentType).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select either Alien Number or I-94..
        /// </summary>
        public static string eitherAlienorI94Required {
            get {
                return ResourceManager.GetString("eitherAlienorI94Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resident of American Samoa.
        /// </summary>
        public static string EligibleImmigrationOtherDocsAmericanSamoa {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsAmericanSamoa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certification from U.S. Department of Health and Human Services (HHS) Office of Refugee Resettlement (ORR).
        /// </summary>
        public static string EligibleImmigrationOtherDocsCertRefugeeResettlement {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsCertRefugeeResettlement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cuban/Haitian Entrant.
        /// </summary>
        public static string EligibleImmigrationOtherDocsCubanHaitianEntrant {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsCubanHaitianEntrant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document indicating a member of a federally recognized Indian tribe or American Indian born in Canada.
        /// </summary>
        public static string EligibleImmigrationOtherDocsIndianTribeCanada {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsIndianTribeCanada", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None of the above.
        /// </summary>
        public static string EligibleImmigrationOtherDocsNone {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsNone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Office of Refugee Resettlement (ORR) eligibility letter (if under 18).
        /// </summary>
        public static string EligibleImmigrationOtherDocsOfficeRefugeeResettlement {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsOfficeRefugeeResettlement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string EligibleImmigrationOtherDocsOther {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alien Number.
        /// </summary>
        public static string EligibleImmigrationOtherDocsOtherAlienNumber {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsOtherAlienNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Description.
        /// </summary>
        public static string EligibleImmigrationOtherDocsOtherDesc {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsOtherDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I-94 number.
        /// </summary>
        public static string EligibleImmigrationOtherDocsOtherI94Number {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsOtherI94Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Administrative order staying removal issued by the Department of Homeland Security.
        /// </summary>
        public static string EligibleImmigrationOtherDocsStayingRemoval {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsStayingRemoval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document indicating withholding of removal.
        /// </summary>
        public static string EligibleImmigrationOtherDocsWithholdingRemoval {
            get {
                return ResourceManager.GetString("EligibleImmigrationOtherDocsWithholdingRemoval", resourceCulture);
            }
        }
    }
}
