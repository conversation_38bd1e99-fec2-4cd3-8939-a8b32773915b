﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Account {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ForgotPassword {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ForgotPassword() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Account.ForgotPassword", typeof(ForgotPassword).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check your email and follow the provided instructions to reset your password. .
        /// </summary>
        public static string CheckEmail {
            get {
                return ResourceManager.GetString("CheckEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm New Password.
        /// </summary>
        public static string confirmPassword {
            get {
                return ResourceManager.GetString("confirmPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password Reset Failed.  Please try again later..
        /// </summary>
        public static string Failure {
            get {
                return ResourceManager.GetString("Failure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html xmlns=&quot;http://www.w3.org/1999/xhtml&quot;&gt;
        ///&lt;head&gt;
        ///    &lt;title&gt;&lt;/title&gt;
        ///&lt;/head&gt;
        ///&lt;body&gt;
        ///    &lt;p&gt;Dear [FullName],&lt;/p&gt;
        ///
        ///    &lt;p&gt;A password reset request has been received for your login.&lt;/p&gt;
        ///
        ///    &lt;p&gt;To reset your password, please follow this link below :  &lt;br/&gt;&lt;br/&gt;[url]&lt;/p&gt;   
        ///&lt;p&gt; &lt;b&gt; Note: - The above password reset link would expire in 24 hours&lt;/b&gt;&lt;/p&gt;
        ///    &lt;p&gt;If you are not [FullName] or didn&apos;t request a password reset on the &lt;a href=&quot;[SiteHome]&quot;&gt;Alabamacares.Alabama.gov &lt;/a&gt; websit [rest of string was truncated]&quot;;.
        /// </summary>
        public static string ForgotPasswordUsernameStepEmailhtml {
            get {
                return ResourceManager.GetString("ForgotPasswordUsernameStepEmailhtml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter your username and click Next..
        /// </summary>
        public static string Instruction {
            get {
                return ResourceManager.GetString("Instruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Password.
        /// </summary>
        public static string newPassword {
            get {
                return ResourceManager.GetString("newPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Answer does not match. Please try again with correct answer..
        /// </summary>
        public static string PasswordAnswerDoesNotMatch {
            get {
                return ResourceManager.GetString("PasswordAnswerDoesNotMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html xmlns=&quot;http://www.w3.org/1999/xhtml&quot;&gt;
        ///&lt;head&gt;
        ///    &lt;title&gt;&lt;/title&gt;
        ///&lt;/head&gt;
        ///&lt;body&gt;
        ///    &lt;p&gt;Dear [FullName],&lt;/p&gt;
        ///
        ///    &lt;p&gt;Your password reset was successfull.&lt;/p&gt;
        ///
        ///    &lt;p&gt;Please follow this link to Login to &lt;a href=&quot;[SiteHome]&quot;&gt;Alabamacares.Alabama.gov &lt;/a&gt; :- &lt;br/&gt;&lt;br/&gt;&quot;[url]&quot;&lt;/p&gt;   
        ///
        ///    &lt;p&gt;If you are not [FullName] or didn&apos;t request a password reset on the &lt;a href=&quot;[SiteHome]&quot;&gt;Alabamacares.Alabama.gov &lt;/a&gt; website, please call us at 1-888-373-5437.&lt;/p&gt;
        ///
        ///    &lt;p&gt;Regards,&lt;/p&gt;
        /// [rest of string was truncated]&quot;;.
        /// </summary>
        public static string PasswordResetSuccessfullhtml {
            get {
                return ResourceManager.GetString("PasswordResetSuccessfullhtml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password reset link expired !!!  Please click on the link below and go through the password reset process again..
        /// </summary>
        public static string resetLinkExpired {
            get {
                return ResourceManager.GetString("resetLinkExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your password reset was successfull! .
        /// </summary>
        public static string resetSuccessfull {
            get {
                return ResourceManager.GetString("resetSuccessfull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        public static string SideMenuHeader {
            get {
                return ResourceManager.GetString("SideMenuHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thanks,.
        /// </summary>
        public static string Thanks {
            get {
                return ResourceManager.GetString("Thanks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not found..
        /// </summary>
        public static string UserNotFound {
            get {
                return ResourceManager.GetString("UserNotFound", resourceCulture);
            }
        }
    }
}
