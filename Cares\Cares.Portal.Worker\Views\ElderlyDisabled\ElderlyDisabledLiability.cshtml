@model Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.ElderlyDisabledLiabilityViewModel
@using Cares.Api.Infrastructure
@using Cares.Data.DataAbstractionLayer
@using ENUMS = Cares.Api.Infrastructure.Enums
@using Cares.Portal.Infrastructure
@using AlabamaConnectExpress.Controllers.ElderlyAndDisabled
@using Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel
@using DD = Cares.Classes.DropDownValues
@using Cares.Portal.Infrastructure.VGSecurity
@{
    Layout = "~/Views/Shared/_ElderlyDisabledMasterLayout.cshtml";
    bool showButton = ViewBag.IsAnyChange != null ? (bool)ViewBag.IsAnyChange : false;
    bool isActiveApp = ViewBag.IsActiveEnroll != null ? (bool)ViewBag.IsActiveEnroll : false;
    bool hasEnrollComplete = ViewBag.IsEnrollComplete != null ? (bool)ViewBag.IsEnrollComplete : false;
    ViewBag.Title = "E&D - Liability";
    var addIcon = @CommonHtmlExtensions.IconHelper(Cares.Api.Infrastructure.Constants.Icons.IconAdd);
    // Here PACE should also be treated like NH (see PBI 232158)
    bool isNhRelated = ENUMS.ElderlyDisabledProgram.IsNursingHomeRelatedProgram(Model.ElderlyDisabledProgramId)
        || Model.ElderlyDisabledProgramId == ENUMS.ElderlyDisabledProgram.PACE_Services.Id;
    bool isDenailApp = (Model.ApplicationStatusId == (byte)ENUMS.enumApplicationStatus.Denied);
    bool canAddLiability = false;
    var strDeleteLiabilityTests = "strDeleteLiabilityTests";
}

@section viewStyles
{
    <style>
        /*  This will hide the day picker  */
        .pickerMonthYearClass .ui-datepicker-calendar,
        .liabilityDatepickerMonthYear .ui-datepicker-calendar,
        /*  This will hide the default datepicker button (using a custom one)    */
        .datepickerMonthYear + button.ui-datepicker-trigger,
        .liabilityDatepickerMonthYear + button.ui-datepicker-trigger,
        /*  This will hide the Today button in the datepicker    */
        .liabilityDatepickerMonthYear button.ui-datepicker-current,
        .pickerMonthYearClass button.ui-datepicker-current {
            display: none;
        }

        /*  Highlights the row being edited. Set important because of bootstrap class overwriting.    */
        .edit-row {
            background-color: var(--ed-editing) !important;
        }

        /*  Highlights the row that was created or edited. Set important because of bootstrap class overwriting.    */
        .edited-row {
            background-color: var(--ed-editedRow) !important;
        }

        /*  Highlights the row being deleted. Set important because of bootstrap class overwriting.    */
        .delete-row {
            background-color: var(--ed-deleteRow) !important;
        }
    </style>
}

<div class="elderly-disabled-liability">
    @if (isNhRelated)
    {
        <div class="divLiabilityTestRoot">
            @using (Html.BeginForm("SaveElderlyDisabledLiabilityTests", "ElderlyDisabled", FormMethod.Post, new { id = ElderlyDisabledController.ElderlyDisabledLiabilityTestFormName }))
            {
                @Html.HiddenFor(m => m.ApplicationId)
                @Html.HiddenFor(m => m.ContactPersonId)

                @* Liability Test Information *@
                <div class="elderly-disabled-liability-test-info">
                    <div class="card shadow mb-2 borderColorNavyBlue">
                        <div class="card-header text-white">
                            <label class="m-auto">
                                Post Eligibility (Liability) Test
                            </label>
                        </div>
                        <div class="m-3">
                            @Html.Partial("Liability/_LiabilityTests", Model, new ViewDataDictionary { { PortalConstants.ViewDataConstants.LiabilityTestType, ENUMS.LiabilityTestTypeType.Infrequent_Irregular.Id } })
                        </div>
                        <div class="m-3">
                            @Html.Partial("Liability/_LiabilityTests", Model, new ViewDataDictionary { { PortalConstants.ViewDataConstants.LiabilityTestType, ENUMS.LiabilityTestTypeType.Other.Id } })
                        </div>
                        <div class="m-3">
                            @Html.Partial("Liability/_LiabilityTests", Model, new ViewDataDictionary { { PortalConstants.ViewDataConstants.LiabilityTestType, ENUMS.LiabilityTestTypeType.VA_Aid_Attendance.Id } })
                        </div>
                    </div>
                </div>
                <div class="mb-2">
                    @*Currently Liability Tests are the only thing being saved. Placing the validatino summary here:*@
                    @Html.Partial("_ValidationSummary")
                </div>
            }
        </div>
    }

    @* Sponsor Information *@
    <div class="elderly-disabled-sponsor-info">
        <div class="card shadow mb-2 borderColorNavyBlue">
            <div class="card-header text-white">
                <label class="m-auto">
                    Sponsor Information
                </label>
            </div>
            <div class="card-body p-3">
                @if (Model != null)
                {
                    RepresentativeInfoViewModel sponsor = Model.RepresentativeInfo.FirstOrDefault(r => r.SponsorRole == "Primary");
                    if (sponsor != null)
                    {
                        string fullName = Cares.Api.Infrastructure.Helper.PersonHelper.GetCombinedName(
                            sponsor.FirstName, sponsor.MiddleName, sponsor.LastName, sponsor.Suffix);
                        <div>
                            @fullName<br />
                            @sponsor.FullAddress
                        </div>
                    }
                    else
                    {
                        <div>No primary sponsor.</div>
                    }
                }
            </div>
        </div>
    </div>
    @* Liability Segments *@
    <div class="elderly-disabled-liability-history-info">
        <div class="card shadow mb-2 borderColorNavyBlue">
            <div class="card-header text-white d-flex justify-content-between align-items-center py-1">
                Liability Segments
                @* Add Liability segment is accessible only when program is 'Nursing Home' *@
                @* TODO: Add check for being actively enrolled in Nursing Home Aid Cat   *@
                @* Only Authorized users can add liability *@
                @{

                    if (CaresSecurity.IsInRole(CaresSecurity.EandDRoles))
                    {
                        ElderlyDisabledApplicationInfoBarViewModel appInfoBarVM = (ElderlyDisabledApplicationInfoBarViewModel)ViewData[PortalConstants.ViewDataConstants.AppInfoBarVM];
                        var isTerminalStatus = Cares.Api.Infrastructure.Helper.ApplicationHelper.IsEnrollmentComplete(appInfoBarVM.ApplicationStatusId.ToString());
                        var EandDuserRoles = isTerminalStatus ? ElderlyDisabledViewAccessByRole.ElderlyDisabledTerminalStatus["ElderlyDisabledLiability"]
                                             : ElderlyDisabledViewAccessByRole.ElderlyDisabledNonTerminalStatus["ElderlyDisabledLiability"];
                        var userRoleId = CaresSecurity.GetEandDRoleId();

                        canAddLiability = !ElderlyDisabledViewAccessByRole.GetUserAccessOnView(EandDuserRoles, userRoleId); //  This returns false if the user isn't a read-only role
                    }
                }

                @if (canAddLiability && !isDenailApp)
                {
                    // Disable the add(+) icon on the liability screen, if Date of Death(DOD) is provided.
                    <button type="button" class="btn-ed-circle btn-add-record pt-1" data-toggle="tooltip" data-original-title="Add Liability Segment" id="btnAddLiabilityInfo" onclick="OpenLiabilityEditor()">@addIcon</button>
                }
            </div>
            <div class="card-body p-3">
                @if (Model != null)
                {
                    <table id="tblLiabilitySegments" class="table table-striped table-bordered m-0">
                        <thead class="alert-warning">
                            <tr>
                                <th scope="col" class="d-none">Hidden Fields</th>
                                <th scope="col">Updated Date</th>
                                <th scope="col">Updated By</th>
                                <th scope="col" style="width: 18%;">Change Code/Description</th>
                                <th scope="col">Amount</th>
                                <th scope="col">From Date</th>
                                <th scope="col">Through Date</th>
                                <th scope="col">Has Transfer Penalty</th>
                                <th scope="col">Remarks</th>
                                <th scope="col">Created By (Created Date)</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.LiabilitySegments != null)
                            {
                                for (var i = 0; i < Model.LiabilitySegments.Count; i++)
                                {
                                    var lh = Model.LiabilitySegments[i];

                                    @Html.Partial("Liability/_LiabilitySegment", lh, new ViewDataDictionary { { PortalConstants.ViewDataConstants.EandDProgramId, Model.ElderlyDisabledProgramId } })
                                }
                            }
                        </tbody>
                    </table>
                }
                <div class="mb-4 medicaid-nursing" style="display:@(showButton ? "block" : "none")">
                    <div class="row m-auto">
                        <div class="col-12 mb-4">
                            <br /><br />
                            <span class="my-1 pb-1 address-heading">Generate Letter Draft</span>
                        </div>
                    </div>
                    <div class="row m-auto">
                        <div class="col">
                            <button class="btn btn-info shadow btn-gradient" id="btnCreateLiabilityLttrDrft" type="button" onclick="CreateLetterDraft()">Generate</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@section modals
{
    @*  Add/Edit a liability segment *@
    <div class="modal hide" id="divUpsertLiabilitySegmentDialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f5f5f5">
                    <h4 class="modal-title"><span id="spnEditorTitle"></span> Liability Segment</h4>
                </div>
                <div class="modal-body">
                    @using (Html.BeginForm("SaveElderlyDisabledLiability", "ElderlyDisabled", FormMethod.Post, new { id = ElderlyDisabledController.ElderlyDisabledLiabilityFormName }))
                    {
                        // Note this ApplicationId hidden field:  This Id is coming from the root view model, which
                        // is in the context of the application the user is viewing.
                        // This HiddenFor creates an element with @Name = ApplicationId.  When the form gets serialized,
                        // it places that value in the SEGMENT's ApplicationId.
                        @Html.HiddenFor(model => model.ApplicationId)
                        @Html.HiddenFor(model => model.ContactPersonId, new { @Name = "PersonId" })
                        @Html.HiddenFor(model => model.ElderlyDisabledProgramId, new { id = "hdnEDProgramId" })
                        @Html.HiddenFor(model => model.ApplicationStatusId)
                        @Html.HiddenFor(model => model.SubProgramCatogory)
                        @Html.HiddenFor(model => model.AddOrEditSegment.PersonLiabilityId, new { id = "hdnEditorPersonLiaibilityId", @Name = "PersonLiabilityId" })
                        @Html.HiddenFor(model => model.AddOrEditSegment.LiabilityAmount, new { id = "hdnAmount", @Name = "hdnAmount" })
                        @Html.HiddenFor(model => model.AddOrEditSegment.HasTransferPenalty, new { id = "hdnTransferPenalty", @Name = "hdnTransferPenalty" })
                        @Html.HiddenFor(model => model.AddOrEditSegment.LiabilityStartDate, new { id = "hdnStartDate", @Name = "hdnStartDate" })
                        @Html.HiddenFor(model => model.AddOrEditSegment.LiabilityEndDate, new { id = "hdnEndDate", @Name = "hdnEndDate" })
                        @Html.HiddenFor(model => model.AddOrEditSegment.Remarks, new { id = "hdnRemarks", @Name = "hdnRemarks" })
                        @Html.HiddenFor(model => model.AddOrEditSegment.LiabilityChangeCodeIds, new { id = "hdnChangeCodeIds", @Name = "hdnChangeCodeIds" })
                        @Html.Hidden("IsNhRelated", isNhRelated, new { id = "hdnIsNhRelated" })

                        <div class="card-body mt-2 p-0 d-flex flex-column">
                            <div class="row justify-content-center pb-3">
                                <div class="col-3 justify-content-center align-self-center text-right">
                                    <label for="ddlLiabilityChangeCodeId" class="required">Change Code/Description</label>
                                </div>
                                <div class="col-6">
                                    @Html.CustomDropdownFor(model => model.AddOrEditSegment.LiabilityChangeCodeIds,
                                        DD.GetLiabilityChangeCodes(Model.AddOrEditSegment.LiabilityChangeCodeIds),
                                        null,
                                        new
                                        {
                                                 id = "ddlLiabilityChangeCodeId",
                                                 @class = "selectpicker form-control validate-required",
                                                 multiple = "",
                                                 data_live_search = "true",
                                            data_actions_box = true,
                                                 data_selected_text_format = "count > 3",
                                                 data_none_selected_text = "Select one or more...",
                                            data_initial_value_id = "#hdnChangeCodeIds",
                                                 @Name = "LiabilityChangeCodeIds",
                                            data_validation_errors_container = "#divLiabilityChangeCodeValidationErrors",
                                        })
                                    @*Container for client-side validations for layout purposes. NOTE: Use of the class is optional based on the design.*@
                                    <div id="divLiabilityChangeCodeValidationErrors" class="error-js-container"></div>
                                </div>
                            </div>
                            <div class="row justify-content-center pb-3">
                                <div class="col-3 justify-content-center align-self-center text-right">
                                    <label for="txtbxLiabilityAmount" class="required">Amount</label>
                                </div>
                                <div class="col-6">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text border-0">
                                                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconDollarSign, new string[] { "colorNavyBlue", "align-middle" })
                                            </span>
                                        </div>
                                        @Html.TextBoxFor(model => model.AddOrEditSegment.LiabilityAmount,
                                                    new
                                                    {
                                                        id = "txtbxLiabilityAmount",
                                                        @Name = "LiabilityAmount",
                                                        placeholder = "0.00",
                                                        @class = "form-control liability-amount validate-required",
                                                        maxlength = 12,
                                                        data_validation_errors_container = "#divLiabilityAmountValidationErrors",
                                                        data_initial_value_id = "#hdnAmount"
                                                    })
                                    </div>
                                    @*Container for client-side validations for layout purposes. NOTE: Use of the class is optional based on the design.*@
                                    <div id="divLiabilityAmountValidationErrors" class="error-js-container"></div>
                                </div>
                            </div>
                            <div class="row justify-content-center pb-3">
                                <div class="col-3 justify-content-center align-self-center text-right">
                                    <label for="chkbxHasTransferPenalty" class="@(!isNhRelated?"required":string.Empty)">Has Transfer Penalty?</label>
                                </div>
                                <div class="col-6 justify-content-center align-self-center ">
                                    <div class="form-inline">
                                        @Html.CheckBoxFor(model => model.AddOrEditSegment.HasTransferPenalty,
                                                        new
                                                        {
                                                            id = "chkbxHasTransferPenalty",
                                                            @Name = "HasTransferPenalty",
                                                            @class = "form-check" + (!isNhRelated ? " validate-required" : string.Empty),
                                                            data_initial_value_id = "#hdnTransferPenalty",
                                                            data_validation_errors_container = "#divTransferPenaltyValidationErrors"
                                                        })
                                    </div>
                                    @if (!isNhRelated)
                                    {
                                        @*Container for client-side validations for layout purposes. NOTE: Use of the class is optional based on the design.*@
                                        <div id="divTransferPenaltyValidationErrors" class="error-js-container"></div>
                                    }
                                </div>
                            </div>
                            <div class="row justify-content-center pb-3">
                                <div class="col-3 justify-content-center align-self-center text-right">
                                    <label for="txtbxLiabilityStartDate" class="required">From Date</label>
                                </div>
                                <div class="col-6">
                                    <div class="input-group">
                                        @Html.TextBoxFor(model => model.AddOrEditSegment.LiabilityStartDate,
                                                    "{0:MM/yyyy}",
                                                    new
                                                    {
                                                        id = "txtbxLiabilityStartDate",
                                                        @Name = "LiabilityStartDate",
                                                        placeholder = "MM/YYYY",
                                                        @class = "form-control datepickerMonthYear monthYearMask validate-requiredDate validate-liabilityStartDateRange validate-overlap",
                                                        oncopy = "return false",
                                                        onpaste = "return false",
                                                        data_validation_errors_container = "#divLiabilityStartDateValidationErrors",
                                                        data_initial_value_id = "#hdnStartDate",
                                                        use_custom_position = "true" // Need to position the datepicker correctly for this implementation
                                                    })
                                        <div class="input-group-append">
                                            <button type="button" data-date-input-id="txtbxLiabilityStartDate" data-toggle="tooltip" data-original-title="Clear Date"
                                                    class="btn btn-clear-input btn-outline-secondary py-0">
                                                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconCross, new string[] { "align-middle" })
                                            </button>
                                            <button type="button" id="btnFromDateDatePicker" tabindex="-1" data-toggle="tooltip" data-original-title="Select Date" data-datepicker-id="txtbxLiabilityStartDate"
                                                    class="btn btn-outline-primary custom-datepicker-trigger py-0">
                                                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconCalendar, new string[] { "align-middle" })
                                            </button>
                                        </div>
                                        @*Container for client-side validations for layout purposes. NOTE: Use of the class is optional based on the design.*@
                                        <div id="divLiabilityStartDateValidationErrors" class="error-js-container"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center pb-3">
                                <div class="col-3 justify-content-center align-self-center text-right">
                                    <label for="txtbxLiabilityEndDate">Through Date</label>
                                </div>
                                <div class="col-6">
                                    <div class="input-group">
                                        @Html.TextBoxFor(model => model.AddOrEditSegment.LiabilityEndDate,
                                                    "{0:MM/yyyy}",
                                                    new
                                                    {
                                                        id = "txtbxLiabilityEndDate",
                                                        @Name = "LiabilityEndDate",
                                                        placeholder = "MM/YYYY",
                                                        @class = "form-control datepickerMonthYear monthYearMask validate-dateFormat validate-liabilityToDateMustBeGreaterThanFromDate validate-liabilityEndDateRange validate-overlap",
                                                        oncopy = "return false",
                                                        onpaste = "return false",
                                                        data_validation_dependson = "#txtbxLiabilityStartDate",
                                                        data_validation_errors_container = "#divLiabilityEndDateValidationErrors",
                                                        data_initial_value_id = "#hdnEndDate",
                                                        use_custom_position = "true" // Need to position the datepicker correctly for this implementation
                                                    })
                                        <div class="input-group-append">
                                            <button type="button" data-date-input-id="txtbxLiabilityEndDate" data-toggle="tooltip" data-original-title="Clear Date"
                                                    class="btn btn-clear-input btn-outline-secondary py-0">
                                                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconCross, new string[] { "align-middle" })
                                            </button>
                                            <button type="button" id="btnToDateDatePicker" tabindex="-1" data-toggle="tooltip" data-original-title="Select Date" data-datepicker-id="txtbxLiabilityEndDate"
                                                    class="btn btn-outline-primary custom-datepicker-trigger py-0">
                                                @CommonHtmlExtensions.IconHelper(Constants.Icons.IconCalendar, new string[] { "align-middle" })
                                            </button>
                                        </div>
                                        @*Container for client-side validations for layout purposes. NOTE: Use of the class is optional based on the design.*@
                                        <div id="divLiabilityEndDateValidationErrors" class="error-js-container"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center pb-3">
                                <div class="col-3 justify-content-center align-self-center text-right">
                                    <label for="txtbxRemarks">Remarks</label>
                                </div>
                                <div class="col-6">
                                    @Html.TextAreaFor(model => model.AddOrEditSegment.Remarks,
                                                    new
                                                    {
                                                        id = "txtbxRemarks",
                                                        @Name = "Remarks",
                                                        placeholder = "Enter Remarks",
                                                        @class = "form-control JS_EDLiabilityRemark validate-liabilityRemarkFormat",
                                                        maxlength = 500,
                                                        data_initial_value_id = "#hdnRemarks"
                                                    })
                                </div>
                            </div>
                        </div>
                        <div>
                            @Html.Partial("_ValidationSummary")
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnLiabilityEditorCancel" class="btn btn-sm btn-primary btn-gradient" style="float: right;" onclick="CancelLiabilityConfirmation()">Cancel</button>
                    <button type="button" id="btnLiabilityEditorSave" class="btn btn-sm btn-success btn-gradient" style="float: right; margin-right: 1%;" onclick="SaveEDLiabilityInfo()">Save</button>
                </div>
            </div>
        </div>
    </div>

    @*  Cancel Liability Editor Confirmation Modal *@
    <div class="modal hide cancelConfirmationModal" id="divCancelEditorConfirmationDialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f5f5f5">
                    <h4 class="modal-title">Cancel Editor</h4>
                </div>
                <div class="modal-body">
                    Are you sure you want to close without saving? The data will be lost.
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnCancelLiabilityEditor" class="btn btn-sm btn-primary btn-gradient" data-dismiss="modal" style="float: right;">No</button>
                    <button type="button" id="btnConfirmLiabilityEditor" class="btn btn-sm btn-success btn-gradient" data-dismiss="modal" style="float: right; margin-right: 1%;" onclick="CancelEditor()">Yes</button>
                </div>
            </div>
        </div>
    </div>

    @if (canAddLiability)
    {
        @* Delete Liability Info Confirmation Modal *@
        <div class="modal hide" id="divDeleteLiabilityInfoConfirmationDialog">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: #f5f5f5">
                        <h4 class="modal-title">Delete Liability Segment</h4>
                    </div>
                    <div class="modal-body">
                        Deleting the segment will be permanent. Do you want to continue?
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="btnCancelDeleteLiabilityInfo" class="btn btn-sm btn-primary btn-gradient" data-dismiss="modal" style="float: right;" onclick="ResetDeleteLiabilitySegmentId()">Cancel</button>
                        <button type="button" id="btnConfirmDeleteLiabilityInfo" class="btn btn-sm btn-success btn-gradient" data-dismiss="modal" style="float: right; margin-right: 1%;" value="Yes" onclick="DeleteLiabilityInfoConfirmed()">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    }

    <div id="divCustomMsgModalPopup" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="modalCustomMessageHeading" />
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <p align="justify" id="modalCustomMessageBody" />
                </div>
                <div class="modal-footer">
                    <button id="btnCustomMsgSuccess-close" name="btnCustomMsgSuccess-close" class="btn btn-sm btn-light btn-secondary btn-gradient W3 round" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    @Html.Partial("_DeleteConfirmationModal", new ViewDataDictionary {
                                                                    { PortalConstants.ViewDataConstants.DeleteModalSectionNameId, strDeleteLiabilityTests },
                                                                    { PortalConstants.ViewDataConstants.DeleteModalTitle, "Liability Test Item" },
                                                                    { PortalConstants.ViewDataConstants.DeleteModalMessage, "liability test item" }
                                                                })
}

@section scripts{
    <script>
        $(document).ready(function () {
            const currentDate = new Date();

            //Disable all fields if user is not authorized
            if ('@Model.ApplicationStatusId' == '@((int)ENUMS.enumApplicationStatus.Denied)') {
                $('form').find(':input:not(:disabled)').prop('disabled', true);
            }
            else {
                $('form').find(':input:not(:disabled)').prop('disabled', @(ViewBag.IsReadOnly));
            }

            //  Since the screen will show up to 3 years worth of data, need to set the min date.
            const minDate = new Date(currentDate.getFullYear() - 3, currentDate.getMonth(), 1);

            //  This needs to be a var as this will be set and accessed in different functions.
            var deleteLiabilitySegmentId = null;

            //  Clear and reset fields when the editor modal is closed (mostly for pressing escape or outside the modal)
            $('#divUpsertLiabilitySegmentDialog').on('hidden.bs.modal', function () {
                ResetEditorFields();
            });

            //  Reset when the delete confirmation modal is closed (mostly for pressing escape or outside the modal)
            $('#divDeleteLiabilityInfoConfirmationDialog').on('hidden.bs.modal', function () {
                ResetDeleteLiabilitySegmentId();
            });

            //  Initialize a month & year datepicker
            $('input.datepickerMonthYear').datepicker({
                changeMonth: true,
                changeYear: true,
                minDate: minDate,
                showOn: 'button',
                showButtonPanel: true,
                dateFormat: 'mm/yy',
                beforeShow: function (input, inst) {
                    $(inst.dpDiv).addClass('pickerMonthYearClass'); //  inst = the instance of the datepicker object. dpDiv = the datepicker div for display

                    // We need to fix the position of these pop-ups.
                    // This problem occurs when the Liability screen gets large enough to cause a scrolling to occur.  Scrolling amount gets added to the pop-up location.
                    // However, this date picker is located in a pop-up modal, and the scrolling is mis-placing the pop-up.
                    // This code fixes the issue:
                    var rect = input.getBoundingClientRect();
                    setTimeout(function () { // use 'setTimeout' to prevent effect overridden by other scripts
                        var scrollTop = $("body").scrollTop();
                        inst.dpDiv.css({ top: rect.top + input.offsetHeight + scrollTop });
                    }, 0);
                },
                onClose: function (dateText, inst) {
                    $(this).datepicker('setDate', new Date(inst.selectedYear, inst.selectedMonth, 1));
                    $(inst.dpDiv).removeClass('pickerMonthYearClass'); //  inst = the instance of the datepicker object. dpDiv = the datepicker div for display

                    // Remove validation error from error container
                    if ($(this).hasClass('error')) {
                        $(this).removeClass('error');

                        let errorContainerId = $(this).data('validation-errors-container');
                        $(errorContainerId).children('em.error').remove();
                    }
                }
            });

            //  From and through dates masking
            $('.monthYearMask').inputmask({
                alias: 'monthYear'
            });

            //  Open the custom datepicker
            $('.custom-datepicker-trigger').click(function () {
                let datepickerId = `#${$(this).data('datepicker-id')}`;

                //  Show the datepicker
                $(datepickerId).datepicker('show');
            });

            //  Clear functionality for input fields
            $(document.body).on('click', '.btn-clear-input', function () {
                let dateInputId = `#${$(this).data('date-input-id')}`;

                //  Clear the value
                $(dateInputId).val(null);

                // Remove validation error from error container
                if ($(dateInputId).hasClass('error')) {
                    $(dateInputId).removeClass('error');

                    let errorContainerId = $(dateInputId).data('validation-errors-container');
                    $(errorContainerId).children('em.error').remove();
                }

                $(this).blur();
            });

            // If HasTransferPenalty is checked, set liability amount to '88888.88' and mark liability end date as required.
            $('#chkbxHasTransferPenalty').change(function () {
                let liabilityEndDate = $('#txtbxLiabilityEndDate');

                if ($(this).prop('checked')) {
                    $('#txtbxLiabilityAmount').val('88888.88');

                    // Add validation markup for liability end date
                    if (!liabilityEndDate.hasClass('validate-requiredDate')) {
                        $('label[for=txtbxLiabilityEndDate]').addClass('required');
                        liabilityEndDate.removeClass('validate-dateFormat').addClass('validate-requiredDate');
                    }
                }
                else {
                    $('#txtbxLiabilityAmount').val(null);

                    // Remove validation markup for liability end date
                    if (liabilityEndDate.hasClass('validate-requiredDate')) {
                        $('label[for=txtbxLiabilityEndDate]').removeClass('required');
                        liabilityEndDate.removeClass('validate-requiredDate error').addClass('validate-dateFormat');
                        $('#divLiabilityEndDateValidationErrors').children('em.error').remove(); // Remove validation error from error container
                    }
                }

                // Check if the Save button needs to be disabled/enabled.
                EnableSaveButton(false);
            });

            $('#ddlLiabilityChangeCodeId').change(function () {
                // Check if the Save button needs to be disabled/enabled.
                EnableSaveButton(false);
            });

            $.validator.addClassRules({
                'validate-liabilityRemarkFormat': {
                    'validate-regex': [ValidationRegex.ResourceRemark, 'remarks']
                },
                'validate-liabilityStartDateRange': {
                    'validate-inDateRange': [{ baseDate: new Date(), modifier: 3, modifierType: DateParts.Year, modifierAdd: false, isEqual: true, message: 'From date cannot be beyond 36 months in the past.' },
                        { baseDate: new Date(), modifier: 1, modifierType: DateParts.Year, modifierAdd: true, isEqual: true, message: 'From date cannot be beyond 12 months in the future from the current date.' }]
                },
                'validate-liabilityEndDateRange': {
                    'validate-inDateRange': [{ baseDate: new Date(), modifier: 3, modifierType: DateParts.Year, modifierAdd: false, isEqual: true, message: 'Through date cannot be beyond 36 months in the past.' },
                    { baseDate: new Date(), modifier: 1, modifierType: DateParts.Year, modifierAdd: true, isEqual: true, message: 'Through date cannot be beyond 12 months in the future from the current date.' }]
                },
                'validate-liabilityToDateMustBeGreaterThanFromDate': {
                    'validate-toDateGreaterThanFromDate': [{ isEqual: true, message: 'Liability To Date must be greater than or equal to the From Date.' }]
                }
            });

            InitializeAllCurrencyMask();
            InitializeInsuranceAdditionalDetailDatePickers();

            // Reset fields
            ResetEditorFields = function () {
                //  Clear the selections for the multi-select dropdown element.
                $('.selectpicker').selectpicker('deselectAll');
                $('.selectpicker').parent('div').removeClass('error');
                $($('.selectpicker').data('validation-errors-container')).children('em.error').remove(); // Remove validation error from error container


                $('#divUpsertLiabilitySegmentDialog').find('textarea, select, input[type="text"]').val(null).removeClass('error').next('em.error').remove();
                $('#chkbxHasTransferPenalty').prop('checked', false);

                // Remove validation markup for liability end date
                $('label[for=txtbxLiabilityEndDate]').removeClass('required');
                $('#txtbxLiabilityEndDate').removeClass('validate-requiredDate').removeClass('error');
                $($('#txtbxLiabilityEndDate').data('validation-errors-container')).children('em.error').remove(); // Remove validation error from error container

                $('tr.edit-row').removeClass('edit-row');

                // Clear validation errors
                $('#error').empty();
                $('#errorSummary').css('display', 'none');
                $('#F_EDLiability :input').addClass('valid');
                $('#F_EDLiability :input').attr('style', 'border-color: none');
                $('#hdnEditorPersonLiaibilityId').val(0);

                // Remove validation errors from error containers
                $('#divLiabilityAmountValidationErrors').children('em.error').remove();
                $('#divLiabilityStartDateValidationErrors').children('em.error').remove();
                $('#divTransferPenaltyValidationErrors').children('em.error').remove();

                //  Reset the disabled status of the Save button.
                EnableSaveButton(false)
            };

            // Save
            SaveEDLiabilityTestData = function () {
                let isValid = validator.form();
                if (isValid) {
                    GenericFunctions.WaitMessageOpen('Please wait....');
                    $('#F_EDLiabilityTest').submit();
                }
            };

            hideTheNoDataToSaveMessage = function () {
                $('#divNoDataToSaveMsg').slideUp('slow', function () {
                    $('#divNoDataToSaveMsg').hide();
                });
            };

            // Begin delete:  Prompt user
            DisplayDeleteConfirmation = function (personLiabilityId) {
                deleteLiabilitySegmentId = personLiabilityId;
                //  Highlight from the row.
                $('tr[data-person-liability-id="' + personLiabilityId + '"]').addClass('delete-row');
                $('#divDeleteLiabilityInfoConfirmationDialog').modal('show');
            }

            // Make the delete call
            DeleteLiabilityInfoConfirmed = function () {
                var url = '@Url.Action("DeleteElderlyDisabledLiability", "ElderlyDisabled")';
                $.ajax({
                    url: url,
                    contentType: 'application/html; charset=utf-8',
                    type: 'GET',
                    data: { personLiabilityId: deleteLiabilitySegmentId },
                    beforeSend: function () {
                        GenericFunctions.WaitMessageOpen('Please wait....');
                        $('.blockUI').css('z-index', '2000');
                    },
                    success: DeleteSucceeded,
                    complete: function () {
                        GenericFunctions.WaitMessageClose();
                    }
                });
            };

            // Delete call return handler
            DeleteSucceeded = function (data) {
                if (data.success == true) {
                    // Delete that row
                    $('tr[data-person-liability-id="' + data.deletedId + '"]').remove();

                    //  Reset the "global" variable for delete liability id
                    ResetDeleteLiabilitySegmentId();
                    // Show Letter button
                    ShowLetterButton();
                }
                else {
                    alert('An error occurred while attempting the delete.  Try again later.');
                }
            };

            ShowLetterButton = function () {
                @if (hasEnrollComplete && isNhRelated)
	            {
                    @:$('.medicaid-nursing').show();
	            }
            };

            ResetDeleteLiabilitySegmentId = function () {
                //  Remove the highlight from the row.
                $('tr[data-person-liability-id="' + deleteLiabilitySegmentId + '"]').removeClass('delete-row');
                //  Reset the liability segment id after the record is deleted.
                deleteLiabilitySegmentId = null;
            };

            // Used for segment overlap validation
            function NoOverlap (value, element) {
                if (value == '') return true;
                let MAX_DATE = new Date('9999-12-31T23:59:59');
                let currentPersonLiablityId = $('#hdnEditorPersonLiaibilityId').val();

                // Get the user's Start and End dates (for EndDate, use MAX_DATE if blank)
                let newStartDateText = $('#txtbxLiabilityStartDate').val();
                newStartDateText = newStartDateText.slice(0, 3) + '01/' + newStartDateText.slice(3);
                let newStartDate = new Date(newStartDateText);
                let newEndDateText = $('#txtbxLiabilityEndDate').val();
                let newEndDate = MAX_DATE;
                if (newEndDateText != undefined && newEndDateText != '') {
                    newEndDateText = newEndDateText.slice(0, 3) + '01/' + newEndDateText.slice(3);
                    newEndDate = new Date(newEndDateText);
                }

                //  Store all the dates and personLiabilityIds
                let table = $('#tblLiabilitySegments tbody');
                let trs = table.children();
                let startDates = [];
                let endDates = [];
                let personLiabilityIds = [];

                //  Get the start/end date for each row in the table
                trs.each(function () {
                    let tr = $(this);
                    let pLId = tr.data('person-liability-id');
                    personLiabilityIds.push(pLId);

                    //  In format MM/YYYY
                    let startDateText = tr.children('td[id^=tdStartDate]').text();
                    //  Inject the DD (01/) into the string
                    startDateText = startDateText.slice(0, 3) + '01/' + startDateText.slice(3);
                    startDates.push(new Date(startDateText));

                    let endDate = MAX_DATE;
                    let endDateText = tr.children('td[id^=tdEndDate]').text();
                    if (endDateText != undefined && endDateText != '') {
                        //  Inject the DD (01/) into the string
                        endDateText = endDateText.slice(0, 3) + '01/' + endDateText.slice(3);
                        endDate = new Date(endDateText);
                    }
                    endDates.push(endDate);
                });

                // Loop through them
                for (var i = 0; i < startDates.length; i++) {
                    // Skip it's own segment:
                    if (currentPersonLiablityId == personLiabilityIds[i]) {
                        continue;
                    }

                    //  New segment is NOT entirely before or after this segment:
                    if (!(newStartDate < startDates[i] && newEndDate < startDates[i] ||
                        newStartDate > endDates[i] && newEndDate > endDates[i])) {
                        return false;
                    }
                }

                return true;
            };

            //  Validates there are no overlaps on date segments.
            $.validator.addMethod('validate-overlap', NoOverlap,
                'This segment\'s dates create an overlap with existing segment(s).');

            // Add focus out events on the date fields.  Editing one may clear the validation on both:
            $('#txtbxLiabilityEndDate, #txtbxLiabilityStartDate, #txtbxLiabilityAmount, #txtbxRemarks').focusout(function () {
                let thisId = $(this).attr('id');
                let aryDateIds = ['txtbxLiabilityStartDate', 'txtbxLiabilityEndDate'];

                //  If it is a date textbox, execute the validator.
                if(aryDateIds.includes(thisId)) {
                    validator.form();
                }

                //  Check if the Save button needs to be disabled.
                EnableSaveButton(false);
            });

            // Performs a form post to save the E&D liability info
            SaveEDLiabilityInfo = function () {
                let isValid = validator.form();

                if (isValid) {
                    GenericFunctions.WaitMessageOpen('Please wait....');
                    // The call to add readonly to txtbxLiabilityAmount makes the save call hang.  This fixes that problem:
                    $('#txtbxLiabilityAmount').prop('readonly', false);

                    // Since there are two forms on the page, we can't use formSelector here:
                    let formdata = $('#F_EDLiability').serialize();

                    JS_SaveAppInfo.SaveApplicationData(
                        '/ElderlyDisabled/SaveElderlyDisabledLiability',
                        formdata,
                        SusSaveEDLiability,
                        ErrorAlert,
                        ''
                    );
                }
            };

            SusSaveEDLiability = function (data) {
                if (data.success === true) {
                    let segment = data.liabilitySegment;
                    let segmentId = data.liabilitySegmentId;

                    //  Remove the previous version of the row if it was updated
                    $(`tr[data-person-liability-id="${segmentId}"]`).remove();

                    //  Insert the segment that was returned and sort appropriately
                    //  Based on a jsFiddle by Emmanuel Lara: https://jsfiddle.net/atduksgf/2/
                    //  Get all the existing rows in the table
                    let table = $('#tblLiabilitySegments tbody');
                    let trs = table.children();
                    //  Store all the start dates
                    let startDates = [];

                    //  Get the start date for each row in the table
                    trs.each(function () {
                        let tr = $(this);
                        //  In format MM/YYYY
                        let dateText = tr.children('td[id^=tdStartDate]').text();

                        //  Inject the DD (01/) into the string
                        dateText = dateText.slice(0, 3) + '01/' + dateText.slice(3);

                        startDates.push(new Date(dateText));
                    });

                    let newRow = $(segment);
                    //  In format MM/YYYY
                    let newStartDateText = newRow.children('td[id^=tdStartDate]').text();

                    //  Inject the DD (01/) into the string
                    newStartDateText = newStartDateText.slice(0, 3) + '01/' + newStartDateText.slice(3);

                    let newStartDate = new Date(newStartDateText);

                    //  Default (needs to be the last row)
                    let insertIndex = startDates.length;

                    //  Find the insertion spot
                    for (var i = 0; i < startDates.length; i++) {
                        if (newStartDate > startDates[i]) {
                            insertIndex = i;
                            break;
                        }
                    }

                    //  Insert into the DOM
                    if (insertIndex == startDates.length) {
                        table.append(newRow);
                    }
                    else if (insertIndex == 0) {
                        table.prepend(newRow);
                    }
                    else {
                        newRow.insertBefore(trs[insertIndex]);
                    }

                    $('#divUpsertLiabilitySegmentDialog').modal('hide');
                    newRow.addClass('edited-row');
                    setTimeout(function () { newRow.removeClass('edited-row'); }, 1200);

                    ShowLetterButton();
                }
            };

            OpenLiabilityEditor = function (personLiabilityId) {
                //  Clear the fields as a precaution
                ResetEditorFields();

                if (personLiabilityId != null && personLiabilityId != '' && personLiabilityId > 0) {
                    $('#spnEditorTitle').html('Edit');

                    //  Map the data from the table to the editor form.
                    $('#hdnEditorPersonLiaibilityId').val(personLiabilityId);

                    //  Map change code
                    let liabilityIds = $(`#hdnChangeCodeIds${personLiabilityId}`).val();
                    let aryChangeCodes = JSON.parse(liabilityIds);

                    $('#ddlLiabilityChangeCodeId').val(aryChangeCodes);
                    $('#hdnChangeCodeIds').val(liabilityIds);

                    //  Disable options that need to be disabled.
                    if (aryChangeCodes.includes('@((byte)ENUMS.enumLiabilityChangeCode.Award)')) {
                        //  Disable all the other options
                        $('.selectpicker').find('option:not(:first-of-type').attr('disabled', 'disabled');
                    }
                    else {
                        //  Disable the award option
                        $('.selectpicker').find('option:first-of-type').attr('disabled', 'disabled');
                    }

                    $('.selectpicker').selectpicker('refresh');

                    //  Map amount
                    $('#txtbxLiabilityAmount').val($(`#hdnLiabilityAmount${personLiabilityId}`).val());
                    $('#hdnAmount').val($(`#hdnLiabilityAmount${personLiabilityId}`).val());

                    //  Map transfer penalty
                    $('#hdnTransferPenalty').val($(`#hdnLiabilityTransfer${personLiabilityId}`).val() == 'True');
                    if ($(`#hdnLiabilityTransfer${personLiabilityId}`).val() == 'True') {
                        $('#chkbxHasTransferPenalty').prop('checked', true);
                        // If HasTransferPenalty is checked, mark liability end date as required
                        $('#txtbxLiabilityEndDate').addClass('validate-requiredDate');
                        $('label[for=txtbxLiabilityEndDate]').addClass('required');
                    }

                    //  Map start date
                    $('#txtbxLiabilityStartDate').val($(`#tdStartDate${personLiabilityId}`).html());
                    $('#hdnStartDate').val($(`#tdStartDate${personLiabilityId}`).html());

                    //  Map end date
                    $('#txtbxLiabilityEndDate').val($(`#tdEndDate${personLiabilityId}`).html());
                    $('#hdnEndDate').val($(`#tdEndDate${personLiabilityId}`).html());

                    //  Map remarks
                    $('#txtbxRemarks').val($(`#hdnLiabilityRemarks${personLiabilityId}`).val());
                    $('#hdnRemarks').val($(`#hdnLiabilityRemarks${personLiabilityId}`).val());

                    //  Highlight the row in the table with a background to indicate it is being edited.
                    $(`tr[data-person-liability-id="${personLiabilityId}"]`).addClass('edit-row');

                    //  Disable the Save button on the editor.
                    EnableSaveButton(true);
                }
                else {
                    $('#hdnEditorPersonLiaibilityId').val(0);
                    $('#spnEditorTitle').html('Add');
                }

                // If non-nursing home, disable liability amount text field and remove required validations.
                $('#txtbxLiabilityAmount').prop('readonly', '@isNhRelated' != 'True');
                if ($('#txtbxLiabilityAmount').prop('readonly')) {
                    $('#txtbxLiabilityAmount').removeClass('validate-required');
                    $('label[for=txtbxLiabilityAmount]').removeClass('required');
                }

                $('#divUpsertLiabilitySegmentDialog').modal({
                    backdrop: 'static',
                    keyboard: true
                });
            };

            CancelLiabilityConfirmation = function () {
                $('#divCancelEditorConfirmationDialog').modal('show');
            };

            CancelEditor = function () {
                $('#divUpsertLiabilitySegmentDialog').modal('hide');
            };

            //  Determines when to enable and disable the button.
            EnableSaveButton = function (isInitialEdit) {
                //  If this is for a new liability segment, the button should always be enabled.
                if ($('#hdnEditorPersonLiaibilityId').val() == 0) {
                    $('#btnLiabilityEditorSave').removeAttr('disabled');
                    return;
                }

                //  If the method is being called from when the editor is opened for editing a segment, disable the button.
                if (isInitialEdit) {
                    $('#btnLiabilityEditorSave').attr('disabled', 'disabled');
                    return;
                }

                let hasChangeCodeChanged = AreValuesDifferent('ddlLiabilityChangeCodeId');
                let hasAmountChanged = AreValuesDifferent('txtbxLiabilityAmount');
                let hasTransferPenaltyChanged = AreValuesDifferent('chkbxHasTransferPenalty');
                let hasStartDateChanged = AreValuesDifferent('txtbxLiabilityStartDate');
                let hasEndDateChanged = AreValuesDifferent('txtbxLiabilityEndDate');
                let hasRemarksChanged = AreValuesDifferent('txtbxRemarks');

                let anyValuesAreChanged = hasChangeCodeChanged || hasAmountChanged || hasTransferPenaltyChanged || hasStartDateChanged
                    || hasEndDateChanged || hasRemarksChanged;

                if (anyValuesAreChanged) {
                    $('#btnLiabilityEditorSave').removeAttr('disabled');
                }
                else {
                    $('#btnLiabilityEditorSave').attr('disabled', 'disabled');
                }
            };
        });

        AreValuesDifferent = function (changedElement) {
            let changedElementId = `#${changedElement}`;
            let currentVal = $(changedElementId).val();
            let existingValId = $(changedElementId).data('initial-value-id');
            let existingVal = $(existingValId).val();

            //  Special case for checkboxes
            if (changedElement == 'chkbxHasTransferPenalty') {
                return $(changedElementId).is(':checked').toString() != existingVal;
            }
            //  Special case for mulit-select checkbox
            else if (changedElement == 'ddlLiabilityChangeCodeId') {
                let currentVals = currentVal;
                let existingVals = JSON.parse(existingVal);

                return currentVals.sort().toString() != existingVals.sort().toString();
            }

            currentVal = currentVal.trim();

            return currentVal != existingVal;
        };

        InitializeAllCurrencyMask = function () {
            $(':text.liability-amount').inputmask({ alias: 'nonNegativeCurrency', rightAlign: false, removeMaskOnSubmit: true, autoUnmask: true });
        };

        //  Plug-in: bootstrap-select URL: https://developer.snapappointments.com/bootstrap-select/
        //  The addClass adds styling to the container around the drop-down and the setStyle adds styling to the drop-down
        $('.selectpicker').addClass('col-lg-12 px-0 border rounded').selectpicker('setStyle', 'btn-light', 'remove');  //  Initialize the multi-select drop-down

        $('.selectpicker').on('loaded.bs.select', function () {
            //  Disable the select all button.
            $('button.bs-select-all').attr('disabled', 'disabled');
        });

        $('.selectpicker').on('hide.bs.select', function () {
            //  Validate when the drop-down is closed.
            // NOTE: Once two forms were added to the page, this line started breaking:
            //validator.element(this);
        });

        $('.selectpicker').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
            //  Award should be the first option in the drop-down, which is index 0
            if (clickedIndex == 0) {
                if (isSelected) {
                    //  Disable other options if Award is selected.
                    $(this).find('option:not(:first-of-type').attr('disabled', 'disabled');
                }
                else {
                    //  Re-enable other options if Award is deselected.
                    $(this).find('option:not(:first-of-type').removeAttr('disabled');
                }
            }
            else if (clickedIndex > 0) {
                if (isSelected) {
                    //  Disable Award if another option is selected
                    $(this).find('option:first-of-type').attr('disabled', 'disabled');
                }
                else if ($(this).find('option:selected').length == 0) {
                    //  Re-enable Award if Award all other options are deselected.
                    $(this).find('option:first-of-type').removeAttr('disabled');
                }
            }
            else {
                //  Deselect all was clicked, so re-enable and disabled options
                $(this).find('option').removeAttr('disabled');
            }

            //  Refreshes the select picker to show the disabled/enabled updates
            $(this).selectpicker('refresh');
        });

        function CreateLetterDraft() {
            let applicationId= @Model.ApplicationId;
            if (applicationId == '0')
            {
                $('#modalCustomMessageHeading').text('Warning!');
                $('#modalCustomMessageBody').text('You must have an Application Id to create letter.');
                $('#divCustomMsgModalPopup').modal('show');
            }
            else
            {
                let formdata = 'ApplicationId=' + applicationId + '&ContactPersonId=' + @Model.ContactPersonId
                    + '&selectedFormId=56'; // 56 = Liability Update Letter
                let url = '/ElderlyDisabledLetters/AutoGenerateLetterDraft';
                JS_SaveAppInfo.SaveApplicationData(
                    url,
                    formdata,
                    susCreateLetterDraft,
                    ErrorAlert,
                    '');
            }
        };
        function susCreateLetterDraft() {
            $('#modalCustomMessageHeading').text('Generate Draft Letter');
            $('#modalCustomMessageBody').text('Draft letter generated for Liability Updates.');
            $('#divCustomMsgModalPopup').modal('show').appendTo("body");
        };

        // Delete Liability Test items
        $(document.body).on('click', '.btn-delete-liability-test', function () {
            let button = this;

            $('#divDelete' + '@strDeleteLiabilityTests' + 'ConfirmationDialog').modal({
                backdrop: 'static',
                keyboard: true
            });

            // Confirm delete handler
            $('#btnConfirmDelete' + '@strDeleteLiabilityTests').off('click').click(function () {
                var selection = $(button).closest('.divRow');
                selection.hide();
                selection.children('.hdnLiabilityTestIsDeleted').val('True');
                $('.tooltip').hide();

                var personLiabilityTestId = selection.find('input[type="hidden"][name*="PersonLiabilityTestId"]').val();
                if (personLiabilityTestId > 0) {
                    $('#divSavingDeleteRequiredMsg').show();
                }
            });
        });

        // Add Liability Test row
        $(document.body).on('click', '.btn-add-liability-test', function () {
            let typeShortName = `${$(this).data('short-name')}`;

            // Assign the id based on the hidden counter field
            let count = parseInt($('#hdnLiabilityTests_' + typeShortName + '_Counter').val());
           $('#divLiabilityTest_' + typeShortName + '_Records').
               append($('#divNewLiabilityTest_' + typeShortName).html()
                    .replace(/{index}/g, count)
                    .replace(/JS_date/g, 'JS_date datepicker')
                    .replace(/datePickReplace/g , 'liabilityDatepickerMonthYear'));

            $('#hdnLiabilityTests_' + typeShortName + '_Counter').val(count + 1);

            //Initialize Fields with mask
            InitializeCurrencyMask($(`#divLTRow_${typeShortName}_${count} input.liability-amount`), false);

            //  Initialize a month & year datepicker
            initMonthYearDatepicker($(`#txtbxLT_${typeShortName}_EffectiveDate_${count}`));

            InitializeDatimePicker(`#divLTRow_${typeShortName}_${count}`);
        });

        // Initializes all month/year datepickers
        function InitializeInsuranceAdditionalDetailDatePickers() {
            $('.liabilityDatepickerMonthYear').each(function (index) {
                initMonthYearDatepicker(this);
            });
        }
        //  Open the custom datepicker
        $(document.body).on('click', '.liability-datepicker-trigger', function () {
            let datepId = '#' + $(this).data('datepicker-id');
            //  Show the datepicker
            $(datepId).datepicker('show');
        });

        function initMonthYearDatepicker(selector) {
            $(selector).datepicker({
                changeMonth: true,
                changeYear: true,
                showOn: 'button',
                showButtonPanel: true,
                dateFormat: 'mm/yy',
                beforeShow: function (input, inst) {
                    $(inst.dpDiv).addClass('pickerMonthYearClass');
                },
                onClose: function (dateText, inst) {
                    $(this).datepicker('setDate', new Date(inst.selectedYear, inst.selectedMonth, 1));
                    $(inst.dpDiv).removeClass('pickerMonthYearClass');

                    if ($(this).hasClass('error')) {
                        $(this).removeClass('error');

                        const errorContainerId = $(this).data('validation-errors-container');
                        $(errorContainerId).children('em.error').remove();
                    }
                }
            });

            // Mask for Month Year
            $(selector).inputmask({
                alias: 'monthYear'
            });
        }

    </script>
}