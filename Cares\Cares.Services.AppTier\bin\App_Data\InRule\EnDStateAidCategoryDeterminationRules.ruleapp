﻿<?xml version="1.0" encoding="utf-8"?>
<RuleApplicationDef xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  Revision="1"
  PublicRevision="1"
  Guid="154ab8c3-314c-4f26-ac8a-4a4178f73237"
  Id="-1"
  Name="NewRuleApplication"
  RepositoryAssemblyFileVersion="5.7.2.240"
  LastValidateContentCode="96756036"
  IsolatedTestDomain="false"
  SchemaGuid="c181bd73-2315-4516-9f83-c40816781a58"
  SchemaRevision="1"
  SchemaPublicRevision="1"
  LastValidateDateTimeUtc="2024-08-05T16:15:02.5258509Z">
  <UpgraderMessageList xmlns="http://www.inrule.com/XmlSchema/Schema" />
  <RuntimeEngine xmlns="http://www.inrule.com/XmlSchema/Schema">InRule</RuntimeEngine>
  <FeatureVersion xmlns="http://www.inrule.com/XmlSchema/Schema">106</FeatureVersion>
  <CompatibilityVersion xmlns="http://www.inrule.com/XmlSchema/Schema">1</CompatibilityVersion>
  <AuthoringSettings xmlns="http://www.inrule.com/XmlSchema/Schema">
    <SelectedAuthoringItemGuid>986e9b73-e07d-46b4-ab92-10f5f17540b6</SelectedAuthoringItemGuid>
    <UseAdvancedXmlOptions>false</UseAdvancedXmlOptions>
    <AllowAccessToParentsInLanguageRules>true</AllowAccessToParentsInLanguageRules>
    <TreeRuleNameVisibility>HideReserved</TreeRuleNameVisibility>
    <CreatedDateTime>2023-02-23T09:15:48.6287641-06:00</CreatedDateTime>
    <ModifiedDateTime>2024-08-05T11:15:02.5475364-05:00</ModifiedDateTime>
    <TargetPlatform>InRule</TargetPlatform>
  </AuthoringSettings>
  <IndentUnboundCollectionXml xmlns="http://www.inrule.com/XmlSchema/Schema">true</IndentUnboundCollectionXml>
  <AllowRuleInactivation xmlns="http://www.inrule.com/XmlSchema/Schema">true</AllowRuleInactivation>
  <UseRuleVersions xmlns="http://www.inrule.com/XmlSchema/Schema">false</UseRuleVersions>
  <UseVersionCreationDates xmlns="http://www.inrule.com/XmlSchema/Schema">false</UseVersionCreationDates>
  <HasContextVersionSettings xmlns="http://www.inrule.com/XmlSchema/Schema">false</HasContextVersionSettings>
  <RunawayCycleCount xmlns="http://www.inrule.com/XmlSchema/Schema">100000</RunawayCycleCount>
  <Timeout xmlns="http://www.inrule.com/XmlSchema/Schema">30000</Timeout>
  <RuntimeErrorHandlingPolicy xmlns="http://www.inrule.com/XmlSchema/Schema">ContinueWithNextRuleSet</RuntimeErrorHandlingPolicy>
  <Entities xmlns="http://www.inrule.com/XmlSchema/Schema">
    <EntityDef
      Revision="1"
      PublicRevision="1"
      Guid="92073504-670a-4d4f-836d-627d5a176fff"
      Name="StateAidCategoryDeterminationDTO"
      DisplayName="State Aid Category Determination DTO">
      <RuleElements>
        <RuleElementDef
          xsi:type="RuleSetDef"
          Revision="1"
          PublicRevision="1"
          Guid="03b06c65-b085-4e7d-9d4c-f50e6629fdf4"
          Name="DeterminingStateAidCat">
          <FireMode>Auto</FireMode>
          <RunMode>SequentialRunOnce</RunMode>
          <Rules>
            <RuleElementDef
              xsi:type="LanguageRuleDef"
              Revision="1"
              PublicRevision="1"
              Guid="cfef1798-267b-4448-811c-9e4c1f9b1f4e"
              Name="FirstLetterDetermination">
              <Attributes>
                <anyType
                  xsi:type="CollectionItem">
                  <Key>
                    <Name>ReservedAttributeTokenKey</Name>
                    <Guid>60fae55a-9a45-41a5-828a-bc71a3b65013</Guid>
                  </Key>
                  <Collection>
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0"
                      Value="ActionSetDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.0"
                      Value="ActionSetDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.FirstLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.FirstLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Blind.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.1.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Blind.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.FirstLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.2.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.1.0"
                      Value="CompareNumber" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Age.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Age.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.1.0.1"
                      Value="Comparison_gte" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.1.0.2"
                      Value="IntegerDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.FirstLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.3.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.2"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.FirstLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.3"
                      Value="StringDesigner" />
                  </Collection>
                </anyType>
              </Attributes>
              <RuleElement
                xsi:type="SimpleRuleDef"
                Revision="1"
                PublicRevision="1"
                Guid="a0e8889e-f34d-4054-ba98-ba9d109aad71"
                Name="FirstLetterDetermination">
                <Condition>
                  <ReturnType>Boolean</ReturnType>
                  <FormulaText>true</FormulaText>
                </Condition>
                <SubRules>
                  <RuleElementDef
                    xsi:type="ExclusiveRuleDef"
                    Revision="1"
                    PublicRevision="1"
                    Guid="eade01ae-3b8d-4c6d-96d6-cd1bf528de8a"
                    Name="Rule3">
                    <DefaultSubRulesRoot
                      Guid="3de9fab2-cafd-49fd-a9a5-7f0a23cfcc70"
                      Name="Otherwise" />
                    <Conditions>
                      <SimpleRuleDef
                        Revision="1"
                        PublicRevision="1"
                        Guid="95a42ba4-2c41-4477-9b0b-bdddfb82a620"
                        Name="If_Then1">
                        <Condition>
                          <ReturnType>Boolean</ReturnType>
                          <FormulaText>Request.EDProgramCode = "8"</FormulaText>
                        </Condition>
                        <SubRules>
                          <RuleElementDef
                            xsi:type="SetValueActionDef"
                            Revision="1"
                            PublicRevision="1"
                            Guid="861d66a8-4152-40c2-939f-34e4eeffe0ec"
                            Name="SetValue1">
                            <Element>
                              <FormulaText>FirstLetter</FormulaText>
                            </Element>
                            <Value>
                              <FormulaText>"4"</FormulaText>
                            </Value>
                          </RuleElementDef>
                        </SubRules>
                        <HasContextVersionSettings>false</HasContextVersionSettings>
                      </SimpleRuleDef>
                      <SimpleRuleDef
                        Revision="1"
                        PublicRevision="1"
                        Guid="29984de5-d21c-4fff-9b44-26c5654b1b70"
                        Name="If_Then2">
                        <Condition>
                          <ReturnType>Boolean</ReturnType>
                          <FormulaText>Request.EDProgramCode = "T"</FormulaText>
                        </Condition>
                        <SubRules>
                          <RuleElementDef
                            xsi:type="SetValueActionDef"
                            Revision="1"
                            PublicRevision="1"
                            Guid="ba7f2d04-e06b-4fef-be34-836ddb90d1d5"
                            Name="Set_Value1">
                            <Element>
                              <FormulaText>FirstLetter</FormulaText>
                            </Element>
                            <Value>
                              <FormulaText>"T"</FormulaText>
                            </Value>
                          </RuleElementDef>
                        </SubRules>
                        <HasContextVersionSettings>false</HasContextVersionSettings>
                      </SimpleRuleDef>
                      <SimpleRuleDef
                        Revision="1"
                        PublicRevision="1"
                        Guid="dba088a9-532e-4e1d-86a5-4f4ce0927aa6"
                        Name="Rule4">
                        <Condition>
                          <ReturnType>Boolean</ReturnType>
                          <FormulaText>Request.Blind</FormulaText>
                        </Condition>
                        <SubRules>
                          <RuleElementDef
                            xsi:type="SetValueActionDef"
                            Revision="1"
                            PublicRevision="1"
                            Guid="352e7d9e-ae92-4378-a912-325969524b4c"
                            Name="Set_Value3">
                            <Element>
                              <FormulaText>FirstLetter</FormulaText>
                            </Element>
                            <Value>
                              <FormulaText>"2"</FormulaText>
                            </Value>
                          </RuleElementDef>
                        </SubRules>
                        <HasContextVersionSettings>false</HasContextVersionSettings>
                      </SimpleRuleDef>
                      <SimpleRuleDef
                        Revision="1"
                        PublicRevision="1"
                        Guid="0686f272-0952-4152-8462-383f5040dbe5"
                        Name="If_Then3">
                        <Condition>
                          <ReturnType>Boolean</ReturnType>
                          <FormulaText>Request.Age &gt;= 65</FormulaText>
                        </Condition>
                        <SubRules>
                          <RuleElementDef
                            xsi:type="SetValueActionDef"
                            Revision="1"
                            PublicRevision="1"
                            Guid="95778a1a-55a2-4d52-bf9d-8d58ad7008cd"
                            Name="Set_Value2">
                            <Element>
                              <FormulaText>FirstLetter</FormulaText>
                            </Element>
                            <Value>
                              <FormulaText>"1"</FormulaText>
                            </Value>
                          </RuleElementDef>
                        </SubRules>
                        <HasContextVersionSettings>false</HasContextVersionSettings>
                      </SimpleRuleDef>
                    </Conditions>
                    <DefaultSubRules>
                      <RuleElementDef
                        xsi:type="SetValueActionDef"
                        Revision="1"
                        PublicRevision="1"
                        Guid="2efe48cf-f691-4e9f-92f8-404cd6a745ee"
                        Name="Set_Value5">
                        <Element>
                          <FormulaText>FirstLetter</FormulaText>
                        </Element>
                        <Value>
                          <FormulaText>"4"</FormulaText>
                        </Value>
                      </RuleElementDef>
                    </DefaultSubRules>
                    <HasContextVersionSettings>false</HasContextVersionSettings>
                  </RuleElementDef>
                </SubRules>
                <HasContextVersionSettings>false</HasContextVersionSettings>
              </RuleElement>
              <HasContextVersionSettings>false</HasContextVersionSettings>
            </RuleElementDef>
            <RuleElementDef
              xsi:type="DeclareVariableActionDef"
              Revision="1"
              PublicRevision="1"
              Guid="487a0b81-d3bf-4cd9-ae3b-123931aed9c6"
              Name="FirstLetter"
              DataType="String">
              <DefaultValue />
            </RuleElementDef>
            <RuleElementDef
              xsi:type="LanguageRuleDef"
              Revision="1"
              PublicRevision="1"
              Guid="4f92a18d-2aaa-431c-86f4-7c2a317d2981"
              Name="SecondLetterDetermination">
              <Attributes>
                <anyType
                  xsi:type="CollectionItem">
                  <Key>
                    <Name>ReservedAttributeTokenKey</Name>
                    <Guid>60fae55a-9a45-41a5-828a-bc71a3b65013</Guid>
                  </Key>
                  <Collection>
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0"
                      Value="ActionSetDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.0"
                      Value="ActionSetDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.1.1.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.2"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.0.3.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.1.1.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.2"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1.1.3.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.2"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.0"
                      Value="SelectCaseElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.0"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.2"
                      Value="SelectCaseItemDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.1.1.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.2"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.RuleSets.DeterminingStateAidCat._nullVocabSurrogate.SecondLetter.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.3.0.3.0.3"
                      Value="StringDesigner" />
                  </Collection>
                </anyType>
              </Attributes>
              <RuleElement
                xsi:type="SimpleRuleDef"
                Revision="1"
                PublicRevision="1"
                Guid="faf8d2d8-97f5-4e10-99cf-29b009fbd49f"
                Name="SecondLetterDetermination">
                <Condition>
                  <ReturnType>Boolean</ReturnType>
                  <FormulaText>true</FormulaText>
                </Condition>
                <SubRules>
                  <RuleElementDef
                    xsi:type="ExclusiveRuleDef"
                    Revision="1"
                    PublicRevision="1"
                    Guid="8a36436a-66d8-472e-8cee-da1ac34a129e"
                    Name="Rule1">
                    <DefaultSubRulesRoot
                      Guid="e300e56c-15d6-4be1-b67e-760fadcee0ce"
                      Name="Otherwise" />
                    <Conditions>
                      <SimpleRuleDef
                        Revision="1"
                        PublicRevision="1"
                        Guid="3866ec0c-2c47-4fd6-b5e3-00acfc6ea857"
                        Name="If_Then4">
                        <Condition>
                          <ReturnType>Boolean</ReturnType>
                          <FormulaText>Request.EDProgramCode = "8"</FormulaText>
                        </Condition>
                        <SubRules>
                          <RuleElementDef
                            xsi:type="ExclusiveRuleDef"
                            Revision="1"
                            PublicRevision="1"
                            Guid="a82aa19c-a255-450c-a64c-368bc1b46d23"
                            Name="If_Then_Else1">
                            <DefaultSubRulesRoot
                              Guid="c83ba50d-c816-4ce7-bdca-ae610ee8a092"
                              Name="Otherwise" />
                            <Conditions>
                              <SimpleRuleDef
                                Revision="1"
                                PublicRevision="1"
                                Guid="488bfd0e-3f58-4b0c-b6fc-b60e2559df53"
                                Name="If_Then5">
                                <Condition>
                                  <ReturnType>Boolean</ReturnType>
                                  <FormulaText>Request.MspType = "None"</FormulaText>
                                </Condition>
                                <SubRules>
                                  <RuleElementDef
                                    xsi:type="SetValueActionDef"
                                    Revision="1"
                                    PublicRevision="1"
                                    Guid="1d8d6414-2264-4099-873f-f7ed5f22d4b3"
                                    Name="SetValue1">
                                    <Element>
                                      <FormulaText>SecondLetter</FormulaText>
                                    </Element>
                                    <Value>
                                      <FormulaText>"W"</FormulaText>
                                    </Value>
                                  </RuleElementDef>
                                </SubRules>
                                <HasContextVersionSettings>false</HasContextVersionSettings>
                              </SimpleRuleDef>
                              <SimpleRuleDef
                                Revision="1"
                                PublicRevision="1"
                                Guid="0f0a89d1-5250-48c5-bcd6-f86afeb213a2"
                                Name="Rule2">
                                <Condition>
                                  <ReturnType>Boolean</ReturnType>
                                  <FormulaText>Request.MspType = "QMB"</FormulaText>
                                </Condition>
                                <SubRules>
                                  <RuleElementDef
                                    xsi:type="SetValueActionDef"
                                    Revision="1"
                                    PublicRevision="1"
                                    Guid="684c426f-35dd-42ee-8d8e-a74a2f1d3b41"
                                    Name="Set_Value4">
                                    <Element>
                                      <FormulaText>SecondLetter</FormulaText>
                                    </Element>
                                    <Value>
                                      <FormulaText>"Q"</FormulaText>
                                    </Value>
                                  </RuleElementDef>
                                </SubRules>
                                <HasContextVersionSettings>false</HasContextVersionSettings>
                              </SimpleRuleDef>
                            </Conditions>
                            <DefaultSubRules>
                              <RuleElementDef
                                xsi:type="SetValueActionDef"
                                Revision="1"
                                PublicRevision="1"
                                Guid="973e46a1-feb2-40ac-aee9-f6ed31b7aeee"
                                Name="Set_Value6">
                                <Element>
                                  <FormulaText>SecondLetter</FormulaText>
                                </Element>
                                <Value>
                                  <FormulaText>"L"</FormulaText>
                                </Value>
                              </RuleElementDef>
                            </DefaultSubRules>
                            <HasContextVersionSettings>false</HasContextVersionSettings>
                          </RuleElementDef>
                        </SubRules>
                        <HasContextVersionSettings>false</HasContextVersionSettings>
                      </SimpleRuleDef>
                      <SimpleRuleDef
                        Revision="1"
                        PublicRevision="1"
                        Guid="6612fd19-dfb8-4926-be18-6b6996f1753f"
                        Name="If_Then6">
                        <Condition>
                          <ReturnType>Boolean</ReturnType>
                          <FormulaText>Request.EDProgramCode = "T"</FormulaText>
                        </Condition>
                        <SubRules>
                          <RuleElementDef
                            xsi:type="ExclusiveRuleDef"
                            Revision="1"
                            PublicRevision="1"
                            Guid="1a57d302-d399-4dd6-beb5-2afd9cac7f18"
                            Name="If_Then_Else2">
                            <DefaultSubRulesRoot
                              Guid="35b4b126-92aa-49d2-992c-115c269d42ba"
                              Name="Otherwise" />
                            <Conditions>
                              <SimpleRuleDef
                                Revision="1"
                                PublicRevision="1"
                                Guid="8f2f8fa6-3a56-4624-b477-997ca508a225"
                                Name="If_Then7">
                                <Condition>
                                  <ReturnType>Boolean</ReturnType>
                                  <FormulaText>Request.MspType = "None"</FormulaText>
                                </Condition>
                                <SubRules>
                                  <RuleElementDef
                                    xsi:type="SetValueActionDef"
                                    Revision="1"
                                    PublicRevision="1"
                                    Guid="ff430f9e-302d-4e52-92eb-4ed43c89b2a1"
                                    Name="Set_Value7">
                                    <Element>
                                      <FormulaText>SecondLetter</FormulaText>
                                    </Element>
                                    <Value>
                                      <FormulaText>"T"</FormulaText>
                                    </Value>
                                  </RuleElementDef>
                                </SubRules>
                                <HasContextVersionSettings>false</HasContextVersionSettings>
                              </SimpleRuleDef>
                              <SimpleRuleDef
                                Revision="1"
                                PublicRevision="1"
                                Guid="6db926aa-4d1e-485d-a1c4-6cd1cbbfdfdd"
                                Name="If_Then8">
                                <Condition>
                                  <ReturnType>Boolean</ReturnType>
                                  <FormulaText>Request.MspType = "QMB"</FormulaText>
                                </Condition>
                                <SubRules>
                                  <RuleElementDef
                                    xsi:type="SetValueActionDef"
                                    Revision="1"
                                    PublicRevision="1"
                                    Guid="4f34dc92-0cd7-4dac-9972-6fc3d95a9f9b"
                                    Name="Set_Value8">
                                    <Element>
                                      <FormulaText>SecondLetter</FormulaText>
                                    </Element>
                                    <Value>
                                      <FormulaText>"Q"</FormulaText>
                                    </Value>
                                  </RuleElementDef>
                                </SubRules>
                                <HasContextVersionSettings>false</HasContextVersionSettings>
                              </SimpleRuleDef>
                            </Conditions>
                            <DefaultSubRules>
                              <RuleElementDef
                                xsi:type="SetValueActionDef"
                                Revision="1"
                                PublicRevision="1"
                                Guid="f21a1f9a-2354-48b6-845e-a6421f6964f0"
                                Name="Set_Value9">
                                <Element>
                                  <FormulaText>SecondLetter</FormulaText>
                                </Element>
                                <Value>
                                  <FormulaText>"L"</FormulaText>
                                </Value>
                              </RuleElementDef>
                            </DefaultSubRules>
                            <HasContextVersionSettings>false</HasContextVersionSettings>
                          </RuleElementDef>
                        </SubRules>
                        <HasContextVersionSettings>false</HasContextVersionSettings>
                      </SimpleRuleDef>
                    </Conditions>
                    <DefaultSubRules>
                      <RuleElementDef
                        xsi:type="ExclusiveRuleDef"
                        Revision="1"
                        PublicRevision="1"
                        Guid="48f2e052-8300-4596-bd0a-d853d2c0643c"
                        Name="If_Then_Else3">
                        <DefaultSubRulesRoot
                          Guid="97dc9dce-7e85-4396-9468-a082d3677ed7"
                          Name="Otherwise" />
                        <Conditions>
                          <SimpleRuleDef
                            Revision="1"
                            PublicRevision="1"
                            Guid="ae8cbfad-453d-4e06-8f94-df2dde250803"
                            Name="If_Then9">
                            <Condition>
                              <ReturnType>Boolean</ReturnType>
                              <FormulaText>Request.MspType = "None"</FormulaText>
                            </Condition>
                            <SubRules>
                              <RuleElementDef
                                xsi:type="SetValueActionDef"
                                Revision="1"
                                PublicRevision="1"
                                Guid="828a250b-0abd-4d61-9393-54338b96f37e"
                                Name="Set_Value10">
                                <Element>
                                  <FormulaText>SecondLetter</FormulaText>
                                </Element>
                                <Value>
                                  <FormulaText>"6"</FormulaText>
                                </Value>
                              </RuleElementDef>
                            </SubRules>
                            <HasContextVersionSettings>false</HasContextVersionSettings>
                          </SimpleRuleDef>
                          <SimpleRuleDef
                            Revision="1"
                            PublicRevision="1"
                            Guid="c2fc26ca-25d7-457a-b71a-d767cf72b63d"
                            Name="Rule3">
                            <Condition>
                              <ReturnType>Boolean</ReturnType>
                              <FormulaText>Request.MspType = "QMB"</FormulaText>
                            </Condition>
                            <SubRules>
                              <RuleElementDef
                                xsi:type="SetValueActionDef"
                                Revision="1"
                                PublicRevision="1"
                                Guid="652b897d-0d16-4ad6-82f9-0cd61c485289"
                                Name="Set_Value11">
                                <Element>
                                  <FormulaText>SecondLetter</FormulaText>
                                </Element>
                                <Value>
                                  <FormulaText>"7"</FormulaText>
                                </Value>
                              </RuleElementDef>
                            </SubRules>
                            <HasContextVersionSettings>false</HasContextVersionSettings>
                          </SimpleRuleDef>
                        </Conditions>
                        <DefaultSubRules>
                          <RuleElementDef
                            xsi:type="SetValueActionDef"
                            Revision="1"
                            PublicRevision="1"
                            Guid="3251797a-76aa-4b16-8f5b-2e02afb9cbc4"
                            Name="Set_Value12">
                            <Element>
                              <FormulaText>SecondLetter</FormulaText>
                            </Element>
                            <Value>
                              <FormulaText>"8"</FormulaText>
                            </Value>
                          </RuleElementDef>
                        </DefaultSubRules>
                        <HasContextVersionSettings>false</HasContextVersionSettings>
                      </RuleElementDef>
                    </DefaultSubRules>
                    <HasContextVersionSettings>false</HasContextVersionSettings>
                  </RuleElementDef>
                </SubRules>
                <HasContextVersionSettings>false</HasContextVersionSettings>
              </RuleElement>
              <HasContextVersionSettings>false</HasContextVersionSettings>
            </RuleElementDef>
            <RuleElementDef
              xsi:type="DeclareVariableActionDef"
              Revision="1"
              PublicRevision="1"
              Guid="288ed45f-8aa4-48f1-bb53-c9ad3f64b363"
              Name="SecondLetter"
              DataType="String" />
            <RuleElementDef
              xsi:type="SetValueActionDef"
              Revision="1"
              PublicRevision="1"
              Guid="8812d5a4-aed9-4221-b7e0-89c6fd231acf"
              Name="SetStateAidCat"
              AuthoringUseNameForElementTreeName="true">
              <Element>
                <FormulaText>Response.StateAidCategoryCode</FormulaText>
              </Element>
              <Value>
                <FormulaText>Concat(FirstLetter, SecondLetter)</FormulaText>
              </Value>
            </RuleElementDef>
          </Rules>
          <HasContextVersionSettings>false</HasContextVersionSettings>
        </RuleElementDef>
        <RuleElementDef
          xsi:type="RuleSetDef"
          Revision="1"
          PublicRevision="1"
          Guid="be908001-1bae-48ad-be69-e04dc0c527fd"
          Name="StateAidDeterminationRule">
          <FireMode>Explicit</FireMode>
          <RunMode>SequentialRunOnce</RunMode>
          <Rules>
            <RuleElementDef
              xsi:type="ExclusiveRuleDef"
              Revision="1"
              PublicRevision="1"
              Guid="3e16b4ed-f655-4d85-a575-307551c99049"
              Name="IfThenElse1">
              <Attributes>
                <anyType
                  xsi:type="CollectionItem">
                  <Key>
                    <Name>ReservedAttributeTokenKey</Name>
                    <Guid>60fae55a-9a45-41a5-828a-bc71a3b65013</Guid>
                  </Key>
                  <Collection>
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.5.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.5.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.5.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.5.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.5.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.5.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.3.0.5.0.5.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.EDProgramCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.5.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.5.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.5.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.5.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.5.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.5.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.3.0.5.0.5.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Blind.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.1.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Blind.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.5.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.5.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.5.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.5.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.5.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.5.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.3.0.5.0.5.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.1.0"
                      Value="CompareNumber" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Age.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.Age.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.1.0.1"
                      Value="Comparison_gte" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.1.0.2"
                      Value="IntegerDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.5.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.5.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.5.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.5.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.5.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.5.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.3.0.5.0.5.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.0"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.1"
                      Value="ConditionGroupTemplateSingleCondition" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.1.0"
                      Value="CompareString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.1.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.1.0.0.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Request.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.1.0.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationRequest.MspType.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.1.0.1"
                      Value="Comparison_eq" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.1.0.2"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.2"
                      Value="IfThenDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.3.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.3.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.3.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.3.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.3.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.3.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.3.0.3"
                      Value="StringDesigner" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.4"
                      Value="IfThenElseDefault" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.5.0"
                      Value="SetValueStringAssignableString" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.5.0.0"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.5.0.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.5.0.1.0"
                      Value="NewRuleApplication.StateAidCategoryDeterminationDTO.Response.Implicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.5.0.1.1"
                      Value="NewRuleApplication.StateAidCategoryDeterminationResponse.StateAidCategoryCode.Explicit" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.5.0.2"
                      Value="SetValueBooleanAssignableBoolean" />
                    <anyType
                      xsi:type="XmlSerializableStringDictionaryItem"
                      Key="0.5.0.5.0.5.0.5.0.5.0.5.0.3"
                      Value="StringDesigner" />
                  </Collection>
                </anyType>
              </Attributes>
              <DefaultSubRulesRoot
                Guid="4d8d90f7-82cb-4aa7-9b2b-9d03fabfe4df"
                Name="Not_Waiver"
                AuthoringUseNameForElementTreeName="true" />
              <Conditions>
                <SimpleRuleDef
                  Revision="1"
                  PublicRevision="1"
                  Guid="541b913f-aa8b-4ac0-9ea7-9a0bcbd8272f"
                  Name="IsWaiver"
                  AuthoringUseNameForElementTreeName="true">
                  <Condition
                    LastAuthoringView="BusinessLangauge">
                    <ReturnType>Boolean</ReturnType>
                    <FormulaText>Request.EDProgramCode = "8"</FormulaText>
                  </Condition>
                  <SubRules>
                    <RuleElementDef
                      xsi:type="ExclusiveRuleDef"
                      Revision="1"
                      PublicRevision="1"
                      Guid="b433918f-a6a3-4158-b234-8de3ad6873cb"
                      Name="SecondDigitWaiver"
                      AuthoringUseNameForElementTreeName="true">
                      <DefaultSubRulesRoot
                        Guid="61f5aac8-f561-4493-83e7-9b4c66d06f35"
                        Name="Otherwise" />
                      <Conditions>
                        <SimpleRuleDef
                          Revision="1"
                          PublicRevision="1"
                          Guid="a972d2f8-8a2c-4cd1-991c-b420ec79fb6d"
                          Name="IsNone"
                          AuthoringUseNameForElementTreeName="true">
                          <Condition
                            LastAuthoringView="Syntax">
                            <ReturnType>Boolean</ReturnType>
                            <FormulaText>Request.MspType = "None"</FormulaText>
                          </Condition>
                          <SubRules>
                            <RuleElementDef
                              xsi:type="SetValueActionDef"
                              Revision="1"
                              PublicRevision="1"
                              Guid="b9ecd966-0552-4000-b142-a3429a99e46f"
                              Name="SetValue5">
                              <Element>
                                <FormulaText>Response.StateAidCategoryCode</FormulaText>
                              </Element>
                              <Value>
                                <FormulaText>"4W"</FormulaText>
                              </Value>
                            </RuleElementDef>
                          </SubRules>
                          <HasContextVersionSettings>false</HasContextVersionSettings>
                        </SimpleRuleDef>
                      </Conditions>
                      <DefaultSubRules>
                        <RuleElementDef
                          xsi:type="ExclusiveRuleDef"
                          Revision="1"
                          PublicRevision="1"
                          Guid="2b697216-1c7e-4fc7-8de1-74bfd173ad51"
                          Name="QMB"
                          AuthoringUseNameForElementTreeName="true">
                          <DefaultSubRulesRoot
                            Guid="be439b7d-e9f7-4849-854e-530ab6b469b8"
                            Name="SLIMB"
                            AuthoringUseNameForElementTreeName="true" />
                          <Conditions>
                            <SimpleRuleDef
                              Revision="1"
                              PublicRevision="1"
                              Guid="000fea7b-9e5e-49d0-9b3a-acf73c6c7417"
                              Name="IsQMB"
                              AuthoringUseNameForElementTreeName="true">
                              <Condition
                                LastAuthoringView="Syntax">
                                <ReturnType>Boolean</ReturnType>
                                <FormulaText>Request.MspType = "QMB"</FormulaText>
                              </Condition>
                              <SubRules>
                                <RuleElementDef
                                  xsi:type="SetValueActionDef"
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="1d46030a-eef8-4216-b510-035365bb4e48"
                                  Name="SetValue5">
                                  <Element>
                                    <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                  </Element>
                                  <Value>
                                    <FormulaText>"4Q"</FormulaText>
                                  </Value>
                                </RuleElementDef>
                              </SubRules>
                              <HasContextVersionSettings>false</HasContextVersionSettings>
                            </SimpleRuleDef>
                          </Conditions>
                          <DefaultSubRules>
                            <RuleElementDef
                              xsi:type="SetValueActionDef"
                              Revision="1"
                              PublicRevision="1"
                              Guid="9da3c319-1c59-4f6f-a781-1513f00bd5f1"
                              Name="SetValue6">
                              <Element>
                                <FormulaText>Response.StateAidCategoryCode</FormulaText>
                              </Element>
                              <Value>
                                <FormulaText>"4L"</FormulaText>
                              </Value>
                            </RuleElementDef>
                          </DefaultSubRules>
                          <HasContextVersionSettings>false</HasContextVersionSettings>
                        </RuleElementDef>
                      </DefaultSubRules>
                      <HasContextVersionSettings>false</HasContextVersionSettings>
                    </RuleElementDef>
                  </SubRules>
                  <HasContextVersionSettings>false</HasContextVersionSettings>
                </SimpleRuleDef>
              </Conditions>
              <DefaultSubRules>
                <RuleElementDef
                  xsi:type="ExclusiveRuleDef"
                  Revision="1"
                  PublicRevision="1"
                  Guid="29eeeab9-c566-4df3-93ad-327db87a7139"
                  Name="Not_Waiver_Cond"
                  AuthoringUseNameForElementTreeName="true">
                  <DefaultSubRulesRoot
                    Guid="541a480e-fe95-4be0-bf76-dede2f5ae28e"
                    Name="Otherwise" />
                  <Conditions>
                    <SimpleRuleDef
                      Revision="1"
                      PublicRevision="1"
                      Guid="f65e82c8-ffed-48fa-ace3-21df83de7384"
                      Name="IsTech"
                      AuthoringUseNameForElementTreeName="true">
                      <Condition
                        LastAuthoringView="Syntax">
                        <ReturnType>Boolean</ReturnType>
                        <FormulaText>Request.EDProgramCode = "T"</FormulaText>
                      </Condition>
                      <SubRules>
                        <RuleElementDef
                          xsi:type="ExclusiveRuleDef"
                          Revision="1"
                          PublicRevision="1"
                          Guid="e48c4113-54ba-4af4-bb8d-992a23de4569"
                          Name="SecondDigitTech"
                          AuthoringUseNameForElementTreeName="true">
                          <DefaultSubRulesRoot
                            Guid="e37ea61b-c247-444b-a3f9-964831e5f40c"
                            Name="Otherwise" />
                          <Conditions>
                            <SimpleRuleDef
                              Revision="1"
                              PublicRevision="1"
                              Guid="255da5ab-587e-412f-84bd-6a42ffd029d2"
                              Name="IsNone"
                              AuthoringUseNameForElementTreeName="true">
                              <Condition
                                LastAuthoringView="Syntax">
                                <ReturnType>Boolean</ReturnType>
                                <FormulaText>Request.MspType = "None"</FormulaText>
                              </Condition>
                              <SubRules>
                                <RuleElementDef
                                  xsi:type="SetValueActionDef"
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="6d8bee9c-d951-488d-9ba4-d3cf8f432cf5"
                                  Name="SetValue5">
                                  <Element>
                                    <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                  </Element>
                                  <Value>
                                    <FormulaText>"TT"</FormulaText>
                                  </Value>
                                </RuleElementDef>
                              </SubRules>
                              <HasContextVersionSettings>false</HasContextVersionSettings>
                            </SimpleRuleDef>
                          </Conditions>
                          <DefaultSubRules>
                            <RuleElementDef
                              xsi:type="ExclusiveRuleDef"
                              Revision="1"
                              PublicRevision="1"
                              Guid="d071e60b-3138-4d23-8a66-594f097fc3cf"
                              Name="QMB"
                              AuthoringUseNameForElementTreeName="true">
                              <DefaultSubRulesRoot
                                Guid="74991073-13e3-44ec-b45d-a91bf9bbdf04"
                                Name="SLIMB"
                                AuthoringUseNameForElementTreeName="true" />
                              <Conditions>
                                <SimpleRuleDef
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="44823884-98b4-46ed-a64e-2953c0ebd32c"
                                  Name="IsQMB"
                                  AuthoringUseNameForElementTreeName="true">
                                  <Condition
                                    LastAuthoringView="Syntax">
                                    <ReturnType>Boolean</ReturnType>
                                    <FormulaText>Request.MspType = "QMB"</FormulaText>
                                  </Condition>
                                  <SubRules>
                                    <RuleElementDef
                                      xsi:type="SetValueActionDef"
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="dc4db642-79ab-415f-baea-2663cf820918"
                                      Name="SetValue5">
                                      <Element>
                                        <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                      </Element>
                                      <Value>
                                        <FormulaText>"TQ"</FormulaText>
                                      </Value>
                                    </RuleElementDef>
                                  </SubRules>
                                  <HasContextVersionSettings>false</HasContextVersionSettings>
                                </SimpleRuleDef>
                              </Conditions>
                              <DefaultSubRules>
                                <RuleElementDef
                                  xsi:type="SetValueActionDef"
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="12d653e0-a8e6-4b6c-826b-e8bca814f77f"
                                  Name="SetValue6">
                                  <Element>
                                    <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                  </Element>
                                  <Value>
                                    <FormulaText>"TL"</FormulaText>
                                  </Value>
                                </RuleElementDef>
                              </DefaultSubRules>
                              <HasContextVersionSettings>false</HasContextVersionSettings>
                            </RuleElementDef>
                          </DefaultSubRules>
                          <HasContextVersionSettings>false</HasContextVersionSettings>
                        </RuleElementDef>
                      </SubRules>
                      <HasContextVersionSettings>false</HasContextVersionSettings>
                    </SimpleRuleDef>
                  </Conditions>
                  <DefaultSubRules>
                    <RuleElementDef
                      xsi:type="ExclusiveRuleDef"
                      Revision="1"
                      PublicRevision="1"
                      Guid="c87ef8f2-bdc1-40d4-9244-************"
                      Name="IfThenElse3">
                      <DefaultSubRulesRoot
                        Guid="d0785011-0718-4983-888d-afb769aa69e4"
                        Name="Otherwise" />
                      <Conditions>
                        <SimpleRuleDef
                          Revision="1"
                          PublicRevision="1"
                          Guid="38548684-bf99-4b10-a471-8924c118b942"
                          Name="IsBlind"
                          AuthoringUseNameForElementTreeName="true">
                          <Condition>
                            <ReturnType>Boolean</ReturnType>
                            <FormulaText>Request.Blind</FormulaText>
                          </Condition>
                          <SubRules>
                            <RuleElementDef
                              xsi:type="ExclusiveRuleDef"
                              Revision="1"
                              PublicRevision="1"
                              Guid="3e4b3efc-fd4b-4b72-b61a-070974ec7721"
                              Name="Blind_MSP"
                              AuthoringUseNameForElementTreeName="true">
                              <DefaultSubRulesRoot
                                Guid="b94f1ee8-9eb0-4b05-be06-3284a26c0f46"
                                Name="Otherwise" />
                              <Conditions>
                                <SimpleRuleDef
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="ea2b92a4-b8f3-4b5e-af15-5a4eec179233"
                                  Name="IsNone"
                                  AuthoringUseNameForElementTreeName="true">
                                  <Condition
                                    LastAuthoringView="Syntax">
                                    <ReturnType>Boolean</ReturnType>
                                    <FormulaText>Request.MspType = "None"</FormulaText>
                                  </Condition>
                                  <SubRules>
                                    <RuleElementDef
                                      xsi:type="SetValueActionDef"
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="e359d070-cd16-4164-99c1-d12fe6b133a2"
                                      Name="Blind_None">
                                      <Element>
                                        <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                      </Element>
                                      <Value>
                                        <FormulaText>"26"</FormulaText>
                                      </Value>
                                    </RuleElementDef>
                                  </SubRules>
                                  <HasContextVersionSettings>false</HasContextVersionSettings>
                                </SimpleRuleDef>
                              </Conditions>
                              <DefaultSubRules>
                                <RuleElementDef
                                  xsi:type="ExclusiveRuleDef"
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="1c7a089f-ab00-437d-bf8f-779e9ed9cf6e"
                                  Name="QMB"
                                  AuthoringUseNameForElementTreeName="true">
                                  <DefaultSubRulesRoot
                                    Guid="d10113a1-5215-4967-929c-5fbab1ac9633"
                                    Name="SLIMB"
                                    AuthoringUseNameForElementTreeName="true" />
                                  <Conditions>
                                    <SimpleRuleDef
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="e9696de9-5ea7-4e3b-ab32-da89d47578ae"
                                      Name="IsQMB"
                                      AuthoringUseNameForElementTreeName="true">
                                      <Condition>
                                        <ReturnType>Boolean</ReturnType>
                                        <FormulaText>Request.MspType = "QMB"</FormulaText>
                                      </Condition>
                                      <SubRules>
                                        <RuleElementDef
                                          xsi:type="SetValueActionDef"
                                          Revision="1"
                                          PublicRevision="1"
                                          Guid="3be469f7-8a9b-47fd-ad95-0a705a47bc6e"
                                          Name="Blind_QMB">
                                          <Element>
                                            <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                          </Element>
                                          <Value>
                                            <FormulaText>"27"</FormulaText>
                                          </Value>
                                        </RuleElementDef>
                                      </SubRules>
                                      <HasContextVersionSettings>false</HasContextVersionSettings>
                                    </SimpleRuleDef>
                                  </Conditions>
                                  <DefaultSubRules>
                                    <RuleElementDef
                                      xsi:type="SetValueActionDef"
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="eb1667ce-b259-40e2-8b52-1f872c18e000"
                                      Name="Blind_SLIMB">
                                      <Element>
                                        <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                      </Element>
                                      <Value>
                                        <FormulaText>"28"</FormulaText>
                                      </Value>
                                    </RuleElementDef>
                                  </DefaultSubRules>
                                  <HasContextVersionSettings>false</HasContextVersionSettings>
                                </RuleElementDef>
                              </DefaultSubRules>
                              <HasContextVersionSettings>false</HasContextVersionSettings>
                            </RuleElementDef>
                          </SubRules>
                          <HasContextVersionSettings>false</HasContextVersionSettings>
                        </SimpleRuleDef>
                      </Conditions>
                      <DefaultSubRules>
                        <RuleElementDef
                          xsi:type="ExclusiveRuleDef"
                          Revision="1"
                          PublicRevision="1"
                          Guid="cb561c6f-1470-4882-bc45-ebc36ae81350"
                          Name="IfThenElse2">
                          <DefaultSubRulesRoot
                            Guid="c803d5c8-6216-4f4b-94f8-7df38784a325"
                            Name="Otherwise" />
                          <Conditions>
                            <SimpleRuleDef
                              Revision="1"
                              PublicRevision="1"
                              Guid="a1d7a5d7-ffae-43e9-8fd6-28900c727efe"
                              Name="IsAged"
                              AuthoringUseNameForElementTreeName="true">
                              <Condition
                                LastAuthoringView="Syntax">
                                <ReturnType>Boolean</ReturnType>
                                <FormulaText>Request.Age &gt;= 66</FormulaText>
                              </Condition>
                              <SubRules>
                                <RuleElementDef
                                  xsi:type="ExclusiveRuleDef"
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="ea8b7385-2f81-4bae-a414-5310bc8a1803"
                                  Name="SecondDigit2"
                                  AuthoringUseNameForElementTreeName="true">
                                  <DefaultSubRulesRoot
                                    Guid="e236d2a1-627c-4127-916b-647d4bda2d75"
                                    Name="Otherwise" />
                                  <Conditions>
                                    <SimpleRuleDef
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="82d85db7-9412-497c-83cb-b8d3dd7f46b5"
                                      Name="IsNone"
                                      AuthoringUseNameForElementTreeName="true">
                                      <Condition
                                        LastAuthoringView="Syntax">
                                        <ReturnType>Boolean</ReturnType>
                                        <FormulaText>Request.MspType = "None"</FormulaText>
                                      </Condition>
                                      <SubRules>
                                        <RuleElementDef
                                          xsi:type="SetValueActionDef"
                                          Revision="1"
                                          PublicRevision="1"
                                          Guid="d1996248-cf5a-4d49-a24b-3da2bba25c5b"
                                          Name="Aged_None">
                                          <Element>
                                            <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                          </Element>
                                          <Value>
                                            <FormulaText>"16"</FormulaText>
                                          </Value>
                                        </RuleElementDef>
                                      </SubRules>
                                      <HasContextVersionSettings>false</HasContextVersionSettings>
                                    </SimpleRuleDef>
                                  </Conditions>
                                  <DefaultSubRules>
                                    <RuleElementDef
                                      xsi:type="ExclusiveRuleDef"
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="b747095e-4113-4d1f-a9fb-503361bf0a74"
                                      Name="QMB"
                                      AuthoringUseNameForElementTreeName="true">
                                      <DefaultSubRulesRoot
                                        Guid="c732dc0a-d005-45a8-b213-e5a4bb3e6286"
                                        Name="SLIMB"
                                        AuthoringUseNameForElementTreeName="true" />
                                      <Conditions>
                                        <SimpleRuleDef
                                          Revision="1"
                                          PublicRevision="1"
                                          Guid="f475b6c8-e356-4625-a9b9-5583b10ee106"
                                          Name="IsQMB"
                                          AuthoringUseNameForElementTreeName="true">
                                          <Condition>
                                            <ReturnType>Boolean</ReturnType>
                                            <FormulaText>Request.MspType = "QMB"</FormulaText>
                                          </Condition>
                                          <SubRules>
                                            <RuleElementDef
                                              xsi:type="SetValueActionDef"
                                              Revision="1"
                                              PublicRevision="1"
                                              Guid="f321f8ec-c673-4d36-a9c4-87649ce166c9"
                                              Name="Aged_QMB">
                                              <Element>
                                                <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                              </Element>
                                              <Value>
                                                <FormulaText>"17"</FormulaText>
                                              </Value>
                                            </RuleElementDef>
                                          </SubRules>
                                          <HasContextVersionSettings>false</HasContextVersionSettings>
                                        </SimpleRuleDef>
                                      </Conditions>
                                      <DefaultSubRules>
                                        <RuleElementDef
                                          xsi:type="SetValueActionDef"
                                          Revision="1"
                                          PublicRevision="1"
                                          Guid="67c4654e-4bfb-431c-999e-b1a01e96ca37"
                                          Name="Aged_SLIMB">
                                          <Element>
                                            <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                          </Element>
                                          <Value>
                                            <FormulaText>"18"</FormulaText>
                                          </Value>
                                        </RuleElementDef>
                                      </DefaultSubRules>
                                      <HasContextVersionSettings>false</HasContextVersionSettings>
                                    </RuleElementDef>
                                  </DefaultSubRules>
                                  <HasContextVersionSettings>false</HasContextVersionSettings>
                                </RuleElementDef>
                              </SubRules>
                              <HasContextVersionSettings>false</HasContextVersionSettings>
                            </SimpleRuleDef>
                          </Conditions>
                          <DefaultSubRules>
                            <RuleElementDef
                              xsi:type="ExclusiveRuleDef"
                              Revision="1"
                              PublicRevision="1"
                              Guid="e0408039-c6dc-4fc4-a08c-3d13eb24fbb4"
                              Name="SecondDigit3"
                              AuthoringUseNameForElementTreeName="true">
                              <DefaultSubRulesRoot
                                Guid="9734d051-7198-40b8-ac7b-8ddf8801e91c"
                                Name="Otherwise" />
                              <Conditions>
                                <SimpleRuleDef
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="31eebc27-18cc-4999-8397-7ff1df79520a"
                                  Name="IsNone"
                                  AuthoringUseNameForElementTreeName="true">
                                  <Condition
                                    LastAuthoringView="Syntax">
                                    <ReturnType>Boolean</ReturnType>
                                    <FormulaText>Request.MspType = "None"</FormulaText>
                                  </Condition>
                                  <SubRules>
                                    <RuleElementDef
                                      xsi:type="SetValueActionDef"
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="368b2716-0134-48e1-93b0-256ea5f49d16"
                                      Name="Blind_None">
                                      <Element>
                                        <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                      </Element>
                                      <Value>
                                        <FormulaText>"46"</FormulaText>
                                      </Value>
                                    </RuleElementDef>
                                  </SubRules>
                                  <HasContextVersionSettings>false</HasContextVersionSettings>
                                </SimpleRuleDef>
                              </Conditions>
                              <DefaultSubRules>
                                <RuleElementDef
                                  xsi:type="ExclusiveRuleDef"
                                  Revision="1"
                                  PublicRevision="1"
                                  Guid="f081de15-6272-413a-8089-2c1c06ae9135"
                                  Name="QMB"
                                  AuthoringUseNameForElementTreeName="true">
                                  <DefaultSubRulesRoot
                                    Guid="7eb6f546-e00a-40b5-9a50-60288decc582"
                                    Name="SLIMB"
                                    AuthoringUseNameForElementTreeName="true" />
                                  <Conditions>
                                    <SimpleRuleDef
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="51585bda-6ccb-4d65-8870-2b0e0f14db75"
                                      Name="IsQMB"
                                      AuthoringUseNameForElementTreeName="true">
                                      <Condition>
                                        <ReturnType>Boolean</ReturnType>
                                        <FormulaText>Request.MspType = "QMB"</FormulaText>
                                      </Condition>
                                      <SubRules>
                                        <RuleElementDef
                                          xsi:type="SetValueActionDef"
                                          Revision="1"
                                          PublicRevision="1"
                                          Guid="d6a20bd4-5681-4753-96c2-aa5bd24b6e7a"
                                          Name="Blind_QMB">
                                          <Element>
                                            <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                          </Element>
                                          <Value>
                                            <FormulaText>"47"</FormulaText>
                                          </Value>
                                        </RuleElementDef>
                                      </SubRules>
                                      <HasContextVersionSettings>false</HasContextVersionSettings>
                                    </SimpleRuleDef>
                                  </Conditions>
                                  <DefaultSubRules>
                                    <RuleElementDef
                                      xsi:type="SetValueActionDef"
                                      Revision="1"
                                      PublicRevision="1"
                                      Guid="817eb490-5a45-46be-ae7c-0136ea1625a4"
                                      Name="Blind_SLIMB">
                                      <Element>
                                        <FormulaText>Response.StateAidCategoryCode</FormulaText>
                                      </Element>
                                      <Value>
                                        <FormulaText>"48"</FormulaText>
                                      </Value>
                                    </RuleElementDef>
                                  </DefaultSubRules>
                                  <HasContextVersionSettings>false</HasContextVersionSettings>
                                </RuleElementDef>
                              </DefaultSubRules>
                              <HasContextVersionSettings>false</HasContextVersionSettings>
                            </RuleElementDef>
                          </DefaultSubRules>
                          <HasContextVersionSettings>false</HasContextVersionSettings>
                        </RuleElementDef>
                      </DefaultSubRules>
                      <HasContextVersionSettings>false</HasContextVersionSettings>
                    </RuleElementDef>
                  </DefaultSubRules>
                  <HasContextVersionSettings>false</HasContextVersionSettings>
                </RuleElementDef>
              </DefaultSubRules>
              <HasContextVersionSettings>false</HasContextVersionSettings>
            </RuleElementDef>
          </Rules>
          <HasContextVersionSettings>false</HasContextVersionSettings>
        </RuleElementDef>
      </RuleElements>
      <Fields>
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="6e8bec5e-350e-48b8-9358-ecf815477ae3"
          Name="Request"
          DataType="Entity"
          DataTypeEntityName="StateAidCategoryDeterminationRequest" />
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="4cc7c0c8-e43a-4cb0-9b14-0e4d816a3d64"
          Name="Response"
          DataType="Entity"
          DataTypeEntityName="StateAidCategoryDeterminationResponse" />
      </Fields>
      <HasContextVersionSettings>false</HasContextVersionSettings>
    </EntityDef>
    <EntityDef
      Revision="1"
      PublicRevision="1"
      Guid="a8b0705e-b664-4fe9-a53b-9e79dc5f711c"
      Name="StateAidCategoryDeterminationRequest"
      DisplayName="State Aid Category Determination Request">
      <Fields>
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="9d7815df-4d32-4d21-9bda-3acfb770247e"
          Name="EDProgramCode"
          DataType="String">
          <ValueList
            Guid="6eb1909d-8431-43d7-9649-a52604847860">
            <Parameters />
            <SourceValueListName>ProgramCode</SourceValueListName>
          </ValueList>
        </FieldDef>
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="f153dfa6-30c5-4ce8-a992-b89dfe2967da"
          Name="DOB"
          DataType="Date" />
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="81d63e66-4246-4284-a9fa-687df7c303ff"
          Name="CreatedDate"
          DataType="Date"
          DisplayName="Created Date" />
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="8932d9e2-7ee5-4e2e-b225-d51d29a9b39b"
          Name="Blind"
          DataType="Boolean" />
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="12ce7ba2-59e7-44a9-83c4-4e44471cbcd5"
          Name="Disabled"
          DataType="Boolean" />
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="986e9b73-e07d-46b4-ab92-10f5f17540b6"
          Name="Age"
          DataType="Number"
          IsCalculated="true">
          <Calc
            LastAuthoringView="Syntax">
            <FormulaText>Year(CreatedDate - AddDays(DOB,1))</FormulaText>
          </Calc>
        </FieldDef>
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="edc8a7d0-6ccd-4769-91e4-364e626e3d73"
          Name="MspType"
          DataType="String"
          DisplayName="Msp Type">
          <ValueList
            Guid="682d01ae-7357-4eeb-8824-0085754a982b">
            <Parameters />
            <SourceValueListName>MSPType</SourceValueListName>
          </ValueList>
        </FieldDef>
      </Fields>
      <HasContextVersionSettings>false</HasContextVersionSettings>
    </EntityDef>
    <EntityDef
      Revision="1"
      PublicRevision="1"
      Guid="5c6a9486-a842-4a69-908b-7bf6e8924dbf"
      Name="StateAidCategoryDeterminationResponse"
      DisplayName="State Aid Category Determination Response">
      <Fields>
        <FieldDef
          Revision="1"
          PublicRevision="1"
          Guid="fe8c711a-9055-4734-9ffb-937752e4a5d6"
          Name="StateAidCategoryCode"
          DataType="String"
          DisplayName="State Aid Category Code" />
      </Fields>
      <HasContextVersionSettings>false</HasContextVersionSettings>
    </EntityDef>
  </Entities>
  <DataElements xmlns="http://www.inrule.com/XmlSchema/Schema">
    <DataElementDef
      xsi:type="InlineValueListDef"
      Revision="1"
      PublicRevision="1"
      Guid="ee866d82-9e8f-459d-88f4-bea6e1ecb3ef"
      Name="MSPType">
      <SortDirection>Ascending</SortDirection>
      <SortMode>None</SortMode>
      <Items>
        <ValueListItemDef
          Guid="0ea6e8a5-e0e9-4f47-991f-fddcccb6e46f">
          <Value>None</Value>
          <DisplayText>None</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="3c277aef-a3ef-4e36-a764-4707edd68e1d">
          <Value>QMB</Value>
          <DisplayText>QMB</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="b86c6288-9022-47a8-a837-8f1304eab199">
          <Value>SLIMB</Value>
          <DisplayText>SLIMB</DisplayText>
        </ValueListItemDef>
      </Items>
    </DataElementDef>
    <DataElementDef
      xsi:type="InlineValueListDef"
      Revision="1"
      PublicRevision="1"
      Guid="e8800436-70b4-499c-b97a-06c59187f9da"
      Name="ProgramCode">
      <SortDirection>Ascending</SortDirection>
      <SortMode>None</SortMode>
      <Items>
        <ValueListItemDef
          Guid="a2798fb0-2358-4257-961d-15836b20a814">
          <Value>0</Value>
          <DisplayText>Nursing Home</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="0322ff98-3101-4092-af26-c7a5eed730b8">
          <Value>1</Value>
          <DisplayText>Continous Medicaid (Pickle)</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="64e3cd7f-5033-4a82-9dba-11ee973119a2">
          <Value>3</Value>
          <DisplayText>Hospital Program</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="dc555652-b46a-4f57-b3e8-38e9f5991b66">
          <Value>4</Value>
          <DisplayText>Retro SSI Medicaid</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="4782ba3a-a5ce-43ec-8faa-6b3cf33c2b5c">
          <Value>5</Value>
          <DisplayText>Medicaid ICF-MR Facility</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="45403513-cf75-4baf-8519-6ca48f7ea91d">
          <Value>6</Value>
          <DisplayText>Disabled Widow/Widower</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="8e5908f1-e5cb-49e2-835d-64ef17b46ecf">
          <Value>7</Value>
          <DisplayText>Disabled Adult Child (DAC)</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="3df0b172-e7cc-4d4f-a525-d97d194ca7d3">
          <Value>8</Value>
          <DisplayText>Comunity Waiver</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="af50993a-86d1-403c-be86-1e7257db99d9">
          <Value>10</Value>
          <DisplayText>ACT Waiver</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="0c472f0d-15c7-4f44-adbd-a6349929d7b5">
          <Value>11</Value>
          <DisplayText>PACE Services</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="1df2e52a-923c-4d9f-994f-6bb1918ed342">
          <Value>12</Value>
          <DisplayText>Living at Home Waiver (LAH)</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="4158764a-d4f3-40f7-ae1e-5bb6c60cdb2a">
          <Value>A</Value>
          <DisplayText>Early Widow/Widower</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="56dafbc1-e93e-47b9-85e4-4dc9a686d5a8">
          <Value>B</Value>
          <DisplayText>Elderly and Disabled Waiver</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="78ec2753-fb6f-49b0-9462-423e7b1b510e">
          <Value>G</Value>
          <DisplayText>Geriatric Psychiatric</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="a84abd41-b422-44c6-8ca4-c3e0a25b1811">
          <Value>H</Value>
          <DisplayText>SAIL Waiver</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="9f81e32b-184e-4d3f-b79b-a3b8c853f2ea">
          <Value>P</Value>
          <DisplayText>Children under 21 in Psychiatric Facility</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="6ff0edea-d406-45e9-be92-e0de9145d656">
          <Value>R</Value>
          <DisplayText>Person w/Intellectual Disabilities Waiver</DisplayText>
        </ValueListItemDef>
        <ValueListItemDef
          Guid="486a873e-34a1-405c-8392-febeacb9a90a">
          <Value>T</Value>
          <DisplayText>Tech Assistance</DisplayText>
        </ValueListItemDef>
      </Items>
    </DataElementDef>
  </DataElements>
</RuleApplicationDef>