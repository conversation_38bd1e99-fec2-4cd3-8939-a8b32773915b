﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class CareTaker {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CareTaker() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.CareTaker", typeof(CareTaker).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age must be less than 19..
        /// </summary>
        public static string ageMustbeLessThan19 {
            get {
                return ResourceManager.GetString("ageMustbeLessThan19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Another child.
        /// </summary>
        public static string anotherChild {
            get {
                return ResourceManager.GetString("anotherChild", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note: To be the main person taking care of a child, you must live with the child, pay for necessary items (like the child’s food and clothing), and help the child with daily activities (like school and transportation)..
        /// </summary>
        public static string careTakerNote {
            get {
                return ResourceManager.GetString("careTakerNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child Information.
        /// </summary>
        public static string childInformation {
            get {
                return ResourceManager.GetString("childInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Caretaker Information.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do any of these children live with more than one parent, through birth or adoption?.
        /// </summary>
        public static string liveWithMoreThanOneParent {
            get {
                return ResourceManager.GetString("liveWithMoreThanOneParent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME], (DOB:[DOB]) live with one or more children under age 19, and is the main person taking care of that child or children?.
        /// </summary>
        public static string livingWithAndCaretakerOfChildUnder19 {
            get {
                return ResourceManager.GetString("livingWithAndCaretakerOfChildUnder19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid value selected. You cannot select &quot;Self&quot;..
        /// </summary>
        public static string noSelfDependent {
            get {
                return ResourceManager.GetString("noSelfDependent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who does [NAME], (DOB:[DOB]) live with and take care of?.
        /// </summary>
        public static string whoDoesPersonLiveWith {
            get {
                return ResourceManager.GetString("whoDoesPersonLiveWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please let us know who does you live with and take care of?.
        /// </summary>
        public static string whoLivesWithTakeCareOf {
            get {
                return ResourceManager.GetString("whoLivesWithTakeCareOf", resourceCulture);
            }
        }
    }
}
