﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\EntityFramework.6.3.0\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.3.0\build\EntityFramework.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{123BC641-5065-4DCD-B044-47E93936F79A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Cares.Data</RootNamespace>
    <AssemblyName>Cares.Data</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Dev|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Dev\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Test\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging|AnyCPU'">
    <OutputPath>bin\Staging\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=10.0.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.3.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.3.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Linq.Dynamic, Version=1.0.6132.35681, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Dynamic.1.0.7\lib\net40\System.Linq.Dynamic.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime" />
    <Reference Include="System.Runtime.InteropServices" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ACCOUNT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="ACCOUNT_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="ACCOUNT_PREREQUISITE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="ACCOUNT_SECURITY_QUESTION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="AC_BALANCE_ADJUSTMENT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="AC_BALANCE_ADJUSTMENT_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="AC_PAYMENT_BATCH.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="AC_PAYMENT_REFUND.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="ADDRESS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="AMAES_CORE_DATA_STAGING.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ADDITIONAL_BURIAL_FUNDS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ADDITIONAL_BURIAL_FUNDS_BUDGET.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_AIAN_INCOME_SOURCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_BANNER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_CHIP_SPECIFIC.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_DEDUCTIONS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_DEPENDENTS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_DETAIL_JIY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_DETAIL_PEP.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_EHC.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_EHC_EMPLOYEE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_EHC_EMPLOYER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ELDERLY_DISABLED_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ELDERLY_DISABLED_INCOME_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ELIGIBILITY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ELIGIBILITY_DENIAL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ELIGIBILITY_ELDERLY_DISABLED_DETERMINATION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ENROLLMENT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_ENROLLMENT_HISTORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_FAMILY_ALLOCATION_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_FORMER_SPOUSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_FUTURE_INCOME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_HEALTH_COVERAGE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_HOUSEHOLD.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_INCOME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_INCOME_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_LIFE_INSURANCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_LIFE_INSURANCE_ADDITIONAL_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_LIFE_INSURANCE_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_LIVES_WITH.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_LIVING_ARRANGEMENT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Application_Log.cs">
      <DependentUpon>CaresLogDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MEDICAID_SPECIFIC.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MEDICAL_INSURANCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MEDICAL_INSURANCE_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MEDICAL_INSURANCE_LTC_BUDGET.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MEDICAL_INSURANCE_MONTH_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MEDICAL_INSURANCE_PART_D_BUDGET.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MED_CHIP_SPECIFIC.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_MED_CHIP_SPECIFIC_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_NON_MAGI_INCOME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_NON_MAGI_INCOME_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_NON_MAGI_NO_SSN_PERSON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_NOTES.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_NOTES_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_OTHER_BURIAL_FUNDS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_OTHER_BURIAL_FUNDS_BUDGET.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_NEEDS_ALLOWANCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_PROPERTY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_PROPERTY_AUTO.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_PROPERTY_AUTO_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_PROPERTY_COLLECTIBLE_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_PROPERTY_MACHINE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PERSONAL_PROPERTY_MACHINE_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PREPAID_BURIAL_SPACE_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PREVIOUS_ENROLLMENT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PROPERTY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PROPERTY_MOBILE_HOME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PROPERTY_MOBILE_HOME_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PROPERTY_PARCEL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PROPERTY_PARCEL_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PROPERTY_PREVIOUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_PROPERTY_PREVIOUS_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RENEWAL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_REPRESENTATIVE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESIDENCY_INFORMATION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESOURCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESOURCE_BANK.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESOURCE_BANK_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESOURCE_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESOURCE_MONTH_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESOURCE_TRANSFER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_RESOURCE_TRANSFER_MONTH_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_SIGN_SUBMIT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_SIGN_SUBMIT_INCARCERATED.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_SNAPSHOT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_SPOUSE_ALLOCATION_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_STATUS_HISTORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="APPLICATION_VETERAN_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="ARCHIVED_ACCOUNT.cs">
      <DependentUpon>CaresArchiveDB.tt</DependentUpon>
    </Compile>
    <Compile Include="ARCHIVED_PERSON.cs">
      <DependentUpon>CaresArchiveDB.tt</DependentUpon>
    </Compile>
    <Compile Include="CaresApplicationDB.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresApplicationDB.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="CaresApplicationDB.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="CaresApplicationDB.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresApplicationDB.edmx</DependentUpon>
    </Compile>
    <Compile Include="CaresArchiveDB.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresArchiveDB.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="CaresArchiveDB.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresArchiveDB.tt</DependentUpon>
    </Compile>
    <Compile Include="CaresArchiveDB.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresArchiveDB.edmx</DependentUpon>
    </Compile>
    <Compile Include="CaresLogDB.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresLogDB.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="CaresLogDB.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresLogDB.tt</DependentUpon>
    </Compile>
    <Compile Include="CaresLogDB.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CaresLogDB.edmx</DependentUpon>
    </Compile>
    <Compile Include="CaresStagingDB.Context.cs">
      <DependentUpon>CaresStagingDB.Context.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="CaresStagingDB.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="CaresStagingDB.Designer.cs">
      <DependentUpon>CaresStagingDB.edmx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="CHI_PAY_PERIOD.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="COLA_FACTSHEET_YEAR.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="COLA_FACTSHEET_YEAR_VALUE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="CONTACT_PREFERENCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="CURRENT_HOUSEHOLD_INCOME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DataAbstractionLayer\Accounting\AccountingDal.cs" />
    <Compile Include="DataAbstractionLayer\Accounting\IAccountingDal.cs" />
    <Compile Include="DataAbstractionLayer\AddressDataDal.cs" />
    <Compile Include="DataAbstractionLayer\AMAESCoreInfoDAL.cs" />
    <Compile Include="DataAbstractionLayer\ApplicationDal.cs" />
    <Compile Include="DataAbstractionLayer\ApplicationSnapshotDAL.cs" />
    <Compile Include="DataAbstractionLayer\BaseDal.cs" />
    <Compile Include="DataAbstractionLayer\BENDEX\BENDEXDal.cs" />
    <Compile Include="DataAbstractionLayer\BENDEX\BENDEXDALResponse.cs" />
    <Compile Include="DataAbstractionLayer\Constants.cs" />
    <Compile Include="DataAbstractionLayer\Dashboard.cs" />
    <Compile Include="DataAbstractionLayer\ElderlyDisabledDal.cs" />
    <Compile Include="DataAbstractionLayer\ELE.cs" />
    <Compile Include="DataAbstractionLayer\ELETestDAL.cs" />
    <Compile Include="DataAbstractionLayer\EnrollmentDal.cs" />
    <Compile Include="DataAbstractionLayer\enumLogType.cs" />
    <Compile Include="DataAbstractionLayer\enumMergeStatus.cs" />
    <Compile Include="DataAbstractionLayer\ExpressLaneEligibility.cs" />
    <Compile Include="DataAbstractionLayer\FacilityDal.cs" />
    <Compile Include="DataAbstractionLayer\HistoricalData\DTO\DetailsRecord.cs" />
    <Compile Include="DataAbstractionLayer\HistoricalData\DTO\DetailsRecordDto.cs" />
    <Compile Include="DataAbstractionLayer\HistoricalData\DTO\SearchResultRecord.cs" />
    <Compile Include="DataAbstractionLayer\HistoricalData\DTO\SearchResultRecordDto.cs" />
    <Compile Include="DataAbstractionLayer\HistoricalData\SearchDal.cs" />
    <Compile Include="DataAbstractionLayer\HpeEventArgs.cs" />
    <Compile Include="DataAbstractionLayer\IApplicationSnapshotDAL.cs" />
    <Compile Include="DataAbstractionLayer\IDataAccess.cs" />
    <Compile Include="DataAbstractionLayer\IMergeDAL.cs" />
    <Compile Include="DataAbstractionLayer\Landing\DTO\EligibilityInformation.cs" />
    <Compile Include="DataAbstractionLayer\Landing\DTO\SearchRecord.cs" />
    <Compile Include="DataAbstractionLayer\Landing\DTO\SearchRecordDto.cs" />
    <Compile Include="DataAbstractionLayer\Landing\PersonDetailDal.cs" />
    <Compile Include="DataAbstractionLayer\Landing\SearchDal.cs" />
    <Compile Include="DataAbstractionLayer\Letters\ElderlyDisabledLettersDal.cs" />
    <Compile Include="DataAbstractionLayer\Letters\ILettersDAL.cs" />
    <Compile Include="DataAbstractionLayer\Letters\LettersDAL.cs" />
    <Compile Include="DataAbstractionLayer\LIS\DTO\LISRecipient.cs" />
    <Compile Include="DataAbstractionLayer\LIS\LISDAL.cs" />
    <Compile Include="DataAbstractionLayer\MergeDAL.cs" />
    <Compile Include="DataAbstractionLayer\NonMAGIIncome\NonMAGIIncomeDAL.cs" />
    <Compile Include="DataAbstractionLayer\NotesDal.cs" />
    <Compile Include="DataAbstractionLayer\Person\DHRPersonalInformationDAL.cs" />
    <Compile Include="DataAbstractionLayer\Person\IDHRPersonalInformationDAL.cs" />
    <Compile Include="DataAbstractionLayer\Person\IMSPPersonalInformationDAL.cs" />
    <Compile Include="DataAbstractionLayer\Person\IPersonDal.cs" />
    <Compile Include="DataAbstractionLayer\Person\MSPPersonalInformationDAL.cs" />
    <Compile Include="DataAbstractionLayer\Person\PersonDal.cs" />
    <Compile Include="DataAbstractionLayer\PresumptiveEligibilityDAL.cs" />
    <Compile Include="DataAbstractionLayer\RefTablesDAL.cs" />
    <Compile Include="DataAbstractionLayer\RefugeeMedicalAssistanceDal.cs" />
    <Compile Include="DataAbstractionLayer\Reporting.cs" />
    <Compile Include="DataAbstractionLayer\ReportingTest.cs" />
    <Compile Include="DataAbstractionLayer\Representatives\RepresentativeDAL.cs" />
    <Compile Include="DataAbstractionLayer\ResponseContainer.cs" />
    <Compile Include="DataAbstractionLayer\RetirementIncome\RetirementIncomeDal.cs" />
    <Compile Include="DataAbstractionLayer\RetirementIncome\RIIRecipient.cs" />
    <Compile Include="DataAbstractionLayer\RIDPDal.cs" />
    <Compile Include="DataAbstractionLayer\RIDPTestDAL.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\FraudRequestData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\FraudResponseData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\IFraudRequestData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\IFraudResponseData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\IPersonRIDPData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\IPrimaryRequestData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\IPrimaryResponseData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\ISecondaryRequestData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\ISecondaryResponseData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\PersonRIDPData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\PrimaryRequestData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\PrimaryResponseData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\SecondaryRequestData.cs" />
    <Compile Include="DataAbstractionLayer\RIDP\SecondaryResponseData.cs" />
    <Compile Include="DataAbstractionLayer\RRV.cs" />
    <Compile Include="DataAbstractionLayer\SdxDal.cs" />
    <Compile Include="DataAbstractionLayer\SearchPerAppDAL.cs" />
    <Compile Include="DataAbstractionLayer\SuspensionDAL.cs" />
    <Compile Include="DataAbstractionLayer\SVES\SVESDal.cs" />
    <Compile Include="DataAbstractionLayer\SVES\SVESDALResponse.cs" />
    <Compile Include="DataAbstractionLayer\TBQ\TBQDal.cs" />
    <Compile Include="DataAbstractionLayer\WorkerPortalAccountDal.cs" />
    <Compile Include="DataAbstractionLayer\WebMethodsDataAccess.cs" />
    <Compile Include="EMAIL_ADDRESS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EMPLOYER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EMPLOYER_ADDRESS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EMPLOYER_CONTACT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EMPLOYER_EMAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EMPLOYER_PHONE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="ENROLLEE_UPDATE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EXPEDITE_IMPORT.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EXPEDITE_IMPORT_MESSAGE.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="EXPRESS_LANE_ELIGIBILITY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_MEDICARE_INSURANCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_MEDICARE_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_MONTHLY_INCOME_INFORMATION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_NON_ESI_MEC_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_RIDP_FIRST_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_RIDP_FIRST_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_RIDP_FIRST_RESPONSE_QUESTION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_RIDP_FRAUD_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_RIDP_FRAUD_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_RIDP_SECOND_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_RIDP_SECOND_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_SPONSORSHIP_DATA.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_SSA_COMPOSITE_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_SSA_COMPOSITE_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_SSA_INCARCERATION_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_VLPREVERIFY_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_VLPSUBMIT_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_VLP_GETCASEDETAILS_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_VLP_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="FH_VLP_RESPONSE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Helpers\NotesDescriptions.cs" />
    <Compile Include="Helpers\XmlSchemaValidator.cs" />
    <Compile Include="LETTER_HISTORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="LETTER_HISTORY_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="MEC_CHECK.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="MEDICAID_RECIPIENTS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="MEDICAID_SUSPENDED.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="MEDICARE_INFORMATION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="NON_MAGI_APPLICATION_INCOME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_ADDRESS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_ASSISTANCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_DOC.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_ETHNICITY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_LIABILITY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_LIABILITY_CHANGE_CODE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_LIABILITY_TEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_PHONE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_RACE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_RIDP.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_SANITIZED_NAME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_SEARCH.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PHONE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PLASTIC_CARD.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PRESUMPTIVE_DETERMINER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PRESUMPTIVE_PROVIDER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PROVIDER_REPRESENTATIVE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="RECONCILE_FACILITY_PROVIDER.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Helpers\SqlToDtoHints.cs" />
    <Compile Include="Helpers\SqlToDtoMapper.cs" />
    <Compile Include="HST_MedicaidMapping.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="IMP_HP_MEDICAID_TRANSITION_OUT.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="IMP_MEDICAID_TRANSITION_ISSUE_MASTER.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Log.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="MedicaidMapping.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Merge_GetAllReportNames_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Merge_GetApplicationDetails_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Merge_GetNextRecord_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Merge_GetPersonDetails_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Merge_GetPersonRace_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="MERGE_MEDICAID_NUMBERS.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Merge_SearchWithFNLNSSNMnbr_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_DONOTMERGE.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_MERGE.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="PERSON_MERGE_HISTORY.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="REF_AC_ADJUSTMENT_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_AC_ADJUSTMENT_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_AC_BATCH_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_AC_PAYMENT_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_AC_REFUND_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_ADDITIONAL_BURIAL_FUNDS_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_AIAN_INCOME_SOURCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_APPLICATION_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_APPLICATION_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_BANK.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_BANK_ACCOUNT_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_CANCEL_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_COLA_FACTSHEET_CATEGORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_COLA_FACTSHEET_SUB_CATEGORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_COUNTY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_DHR_COVERAGE_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_DISTRICT_OFFICE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_DYSLOCATIONS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_ELDERLY_DISABLED_FORM_NAME.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_ELDERLY_DISABLED_FORM_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_ELDERLY_DISABLED_PROGRAM.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_EMPLOYER_COVERAGE_CHANGE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_EXCLUDED_RESOURCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_EXPEDITE_FACILITY_PROVIDER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_FAMILY_NEEDS_ALLOWANCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_FED_REC_TRIBES.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_GENDER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_HEALTH_COVERAGE_MC_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_HEALTH_COVERAGE_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_IH_ROLE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_IH_SITE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_IMMIGRATION_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_INCOME_FREQUENCY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_INCOME_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_INSURANCE_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_LETTER_CATEGORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_LETTER_PARAGRAPH.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_LETTER_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_LETTER_XREF.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_LIABILITY_CHANGE_CODE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_LIABILITY_TEST_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_LIVING_ARRANGEMENT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_MANUAL_CITIZENSHIP_VER_DOC.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_NH_PROVIDER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_NH_PROVIDER_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_OVERRIDE_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_PHONE_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_PREPAID_BURIAL_CONTRACT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_PROCESS_PARAM.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_PROCESS_PARAM_VALUE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_PROGRAM.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_PROGRAM_SUB_CATEGORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_PROVIDER_DETERMINER_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_RACE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_RELATIONSHIP_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_RESOURCE_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_RIDP_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_RMA_APPLICANT_CATEGORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SPONSOR_LEGAL_AUTHORITY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SPONSOR_ROLE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SPONSOR_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_STATE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_STATE_REASON_CODE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SUB_PROGRAM_CATEGORY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SUFFIX.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SUSPENSION_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SUSPENSION_REASON_SOURCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SVES_UNEARNED_INCOME_FREQUENCY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SVES_UNEARNED_INCOME_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_SVES_XREF_CODE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TAX_ASSESSOR_OFFICE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_BENEFICIARY_PART_A_ENROLLMENT_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_BENEFICIARY_PART_B_ENROLLMENT_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_BENEFICIARY_PART_B_THIRD_PARTY_PREMIUM_PAYER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_BENE_ESRD_TERMINATION_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_ENROLLMENT_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_MBI_EFFECTIVE_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_MBI_END_REASON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_PART_A_NON_ENTITLEMENT_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_PART_B_NON_ENTITLEMENT_STATUS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_PLAN_BENEFITS_PACKAGE_COVERAGE_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_TBQ_THIRD_PARTY_BUY_IN_ELIGIBILITY.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="REF_WORKER_REMINDER_TYPE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="RENEWAL_REDETERMINATION_VERIFICATION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="RETIREMENT_INCOME_INCREASE_POOL.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="SDX_DATA_STAGING_HISTORY.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="SDX_DATA_TEMPORAL_STAGING.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="SPONSOR.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="SPONSOR_PHONE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="SVES_REQUEST.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="sysnl_donotmodify__FTI_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="sysnl_donotmodify__PERSON.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="t_CONTACT_PREFERENCE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="t_PERSON_ADDRESS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="t_PERSON_DETAIL.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="t_PERSON_PHONE.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="T_PERSON_RIDP.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="t_PERSON_SUSPENSION.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_LetterPanel_ContactAddress_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_LetterPanel_LetterHistorySearch_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_Letter_InvoiceStatement_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_Letter_PaymentHistory_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_Letter_PaymentReceipt_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_Letter_RefundVoucher_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_Letter_Type_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accounting_SearchPayment_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accouting_LetterPanel_LetterHistorySearch_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accouting_LetterPanel_PaymentList_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accouting_LetterPanel_SearchPersonList_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accouting_LetterPanel_Search_FNLN_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Accouting_LetterPanel_Search_SSN_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_ADDITIONAL_CONTACT_INFORMATION_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_APP_DETAIL_BY_APP_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_APP_HOUSEHOLD_BY_APP_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_APP_INFORMATION_BY_APP_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_ELIG_ENROLL_INCOME_INFORMATION_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_INCOME_BY_APP_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_PERSON_SNAPSHOT_BY_APP_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_APPLICATION_SNAPSHOT_TAX_FILER_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_ELE_LETTER_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_GET_BANK_DATA_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_ClaimNumbers_By_PersonId_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_GET_ELDERLY_DISABLED_LIABILITY_LETTER_DATA_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_GET_ELDERLY_DISABLED_TERMINATION_LETTER_DATA_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_GET_RMA_APPLICATION_INCOME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_GET_RMA_TERMINATION_LETTER_DATA_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_GET_WORKERS_BY_DO_CODE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_LANDING_ADDRESS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_LANDING_NAME_BASED_SEARCH_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_LANDING_PAYEE_ID_SEARCH_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_LANDING_SELECT_ELDERLY_DISABLED_DETAILS_BY_PERSON_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_LANDING_SELECT_LTC_DETAILS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_LANDING_SELECT_PERSON_DETAILS_BY_PERSON_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_LANDING_SELECT_PLASTIC_CARD_INFO_BY_PERSON_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SDX_Search_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_selectHistoricalMedicaidIdInfo_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ADDRESS_SUMMARY_BY_PERSON_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_AIAN_Income_Detail_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_CHIP_Specific_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Deductions_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Dependents_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Details_For_Ele_Copy_App_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Detail_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_EHC_Employee_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_EHC_Employer_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_EHC_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Eligibility_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Enrollment_History_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Enrollment_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Future_Income_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Health_Coverage_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_APPLICATION_HISTORY_SEARCH_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Household_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Income_Detail_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Income_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Lives_With_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Medicaid_CHIP_Specific_Detail_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Medicaid_CHIP_Specific_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Medicaid_Specific_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_APPLICATION_NON_MAGI_NO_SSN_PERSON_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_APPLICATION_REFUGEE_MEDICAL_ASSISTANCE_DETAIL_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Representative_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_APPLICATION_SIGN_SUBMIT_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Application_Status_History_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_APPLOCK_POPUP_ELIGIBILTY_DETAILS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_APPLOCK_POPUP_HOUSEHOLD_DETAILS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_BANK_BY_CODE_OR_BY_NAME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Batch_And_Payment_Number_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_BENDEX_RESPONSE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_CHI_EMPLOYER_NAME_ADDRESS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Current_Pregnancy_Enrollments_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_DEEMED_INCOME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_DHR_APPLICATION_INSURANCE_DETAIL_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_DHR_ELIGIBILITY_DETAIL_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ELDERLY_DISABLED_LETTER_DRAFT_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_DRAFT_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ELDERLY_DISABLED_LETTER_SUMMARY_FINAL_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ELDERLY_DISABLED_NON_MAGI_INCOME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ELE_ERRORS1_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_ELE_Errors_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ELE_STAGING_MEMBERS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ELIGIBILITY_REVIEW_PARAMETERS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_EMAIL_INFO_PEP_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Employer_Address_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Employer_Contact_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Employer_Email_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Employer_Phone_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Employer_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_ENROLLMENT_HISTORY_FOR_ED_BY_PERSON_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_EQUIFAX_INCOME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_EXPARTE_APP_BY_PERSON_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_EXPEDITE_FACILITY_PROVIDER_BY_STRING_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_EXPEDITE_FACILITY_PROVIDER_INFO_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_FH_RIDP_FIRST_REQUEST_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_FH_RIDP_FIRST_RESPONSE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_FH_RIDP_FRAUD_REQUEST_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_FH_RIDP_FRAUD_RESPONSE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_FH_RIDP_SECOND_REQUEST_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_FH_RIDP_SECOND_RESPONSE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_From_Application_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_GROSS_AMOUNT_OF_NON_MAGI_APPLICATION_INCOME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_HOUSEHOLD_MEMBERS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_HUB_RESPONSES_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_JIY_RELEASE_DATE_HISTORY_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_LATEST_ENROLLMENT_BY_PERSON_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_LIS_RECIPIENTS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_MEC_CHECK_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_MEDICAID_RENEWAL_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_MEDICAL_INSURANCE_INFORMATION_FOR_SNAPSHOT_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_MEDICARE_HUB_RESPONSE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_MEDICARE_INFORMATION_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_MODIFY_PERSON_ENROLLMENT_ENROLLMENTS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_NONDECEASED_UNBORNS_BY_MOTHER_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_NON_MAGI_APPLICATION_INCOME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_NON_MAGI_FAMILY_MEMBERS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_NON_MAGI_PERSON_RACES_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_NON_US_ADDRESS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_Overlap_Enrollments_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_BY_PERSONID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_DETAIL_BY_PERSONID1_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_INFO_BY_SSN_OR_MEDICAID_ID_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_LATEST_APP_INFO_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_RACE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_RECENT_APPLICATIONS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_RIDP_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_SPONSOR_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PERSON_WITH_APPLICATION_ID_FROM_PERSON_SSN_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_PRESUMPTIVE_DETERMINERS_BY_INPUT_TEXT_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_RESIDENCY_INFORMATION_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_RETIREMENT_INCOME_INCREASE_APPS_Result.cs">
      <DependentUpon>CaresStagingDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_SPONSOR_PHONE_INFO_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_SSA_INCOME_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Select_SSI_Application_Representative_With_ApplicationId_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_SSI_PERSON_SUSPENSION_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_STATE_SUPP_CATEGORIES_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_SVES_STANDARD_RESPONSE_INFO_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_SVES_TITLE_II_RESPONSE_INFO_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_SVES_TITLE_XVI_RESPONSE_INFO_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_TAX_FILER_SUMMARY_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_TBQ_RESPONSE_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_TERMINAL_APPLOCK_INFO_GRID_DETAILS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_VETERAN_DETAILS_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_WORKER_PORTAL_ACCOUNT_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_SELECT_WORKER_REMINDER_Result.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="vw_ADDRESS.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="vw_APPLICATION_DETAILS_QIT_AND_LIEN.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="vw_APPLICATION_HISTORY_AMAES.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="vw_ELIGIBILITY_ENROLLMENT.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="vw_WORKER_REMINDER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="WorkerPortal_Account.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
    <Compile Include="WORKER_REMINDER.cs">
      <DependentUpon>CaresApplicationDB.tt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="CaresApplicationDB.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresApplicationDB.edmx</DependentUpon>
      <LastGenOutput>CaresApplicationDB.Context.cs</LastGenOutput>
    </Content>
    <Content Include="CaresApplicationDB.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresApplicationDB.edmx</DependentUpon>
      <LastGenOutput>CaresApplicationDB.cs</LastGenOutput>
    </Content>
    <Content Include="CaresArchiveDB.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresArchiveDB.edmx</DependentUpon>
      <LastGenOutput>CaresArchiveDB.Context.cs</LastGenOutput>
    </Content>
    <Content Include="CaresArchiveDB.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresArchiveDB.edmx</DependentUpon>
      <LastGenOutput>CaresArchiveDB.cs</LastGenOutput>
    </Content>
    <Content Include="CaresLogDB.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresLogDB.edmx</DependentUpon>
      <LastGenOutput>CaresLogDB.Context.cs</LastGenOutput>
    </Content>
    <Content Include="CaresLogDB.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresLogDB.edmx</DependentUpon>
      <LastGenOutput>CaresLogDB.cs</LastGenOutput>
    </Content>
    <Content Include="CaresStagingDB.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresStagingDB.edmx</DependentUpon>
      <LastGenOutput>CaresStagingDB.Context.cs</LastGenOutput>
    </Content>
    <Content Include="CaresStagingDB.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>CaresStagingDB.edmx</DependentUpon>
      <LastGenOutput>CaresStagingDB.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
      <TransformOnBuild>true</TransformOnBuild>
    </None>
    <EntityDeploy Include="CaresStagingDB.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>CaresStagingDB.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <EntityDeploy Include="CaresApplicationDB.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>CaresApplicationDB.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="App.Debug.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>true</IsTransformFile>
    </None>
    <None Include="App.Dev.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>true</IsTransformFile>
    </None>
    <None Include="App.Release.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>true</IsTransformFile>
    </None>
    <None Include="App.Staging.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>true</IsTransformFile>
    </None>
    <None Include="App.Test.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>true</IsTransformFile>
    </None>
    <None Include="CaresApplicationDB.edmx.diagram">
      <DependentUpon>CaresApplicationDB.edmx</DependentUpon>
      <SubType>Designer</SubType>
    </None>
    <EntityDeploy Include="CaresLogDB.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>CaresLogDB.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <EntityDeploy Include="CaresArchiveDB.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>CaresArchiveDB.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="CaresArchiveDB.edmx.diagram">
      <DependentUpon>CaresArchiveDB.edmx</DependentUpon>
    </None>
    <None Include="CaresLogDB.edmx.diagram">
      <DependentUpon>CaresLogDB.edmx</DependentUpon>
    </None>
    <None Include="CaresStagingDB.edmx.diagram">
      <DependentUpon>CaresStagingDB.edmx</DependentUpon>
    </None>
    <None Include="Helpers\XmlSchemas\ApplicationPersonSnapshot.xsd">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Cares.Api.Infrastructure\Cares.Api.Infrastructure.csproj">
      <Project>{d8f7ab2c-bb31-4fe1-b470-9ae360f5abe5}</Project>
      <Name>Cares.Api.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Api.Messages\Cares.Api.Messages.csproj">
      <Project>{505845fb-0862-467b-92dc-09f27aea9002}</Project>
      <Name>Cares.Api.Messages</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Infrastructure.Log\Cares.Infrastructure.Log.csproj">
      <Project>{708cf02b-dfdd-4e88-ad78-1f63a0331256}</Project>
      <Name>Cares.Infrastructure.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Citizen.Models\Cares.Portal.Citizen.Models.csproj">
      <Project>{BDC9717F-EA1A-471F-9C30-A41D7F19CEDF}</Project>
      <Name>Cares.Portal.Citizen.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Infrastructure\Cares.Portal.Infrastructure.csproj">
      <Project>{EE96E415-E65C-4069-858A-3897238DCF11}</Project>
      <Name>Cares.Portal.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Worker.Models\Cares.Portal.Worker.Models.csproj">
      <Project>{94792cd6-a08c-4e5e-85a0-171cb5acb89f}</Project>
      <Name>Cares.Portal.Worker.Models</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.3.0\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.3.0\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.3.0\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.3.0\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.VisualStudio.SlowCheetah.4.0.8\build\Microsoft.VisualStudio.SlowCheetah.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.VisualStudio.SlowCheetah.4.0.8\build\Microsoft.VisualStudio.SlowCheetah.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.3.0\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.3.0\build\EntityFramework.targets')" />
  <Import Project="..\packages\Microsoft.VisualStudio.SlowCheetah.4.0.8\build\Microsoft.VisualStudio.SlowCheetah.targets" Condition="Exists('..\packages\Microsoft.VisualStudio.SlowCheetah.4.0.8\build\Microsoft.VisualStudio.SlowCheetah.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>