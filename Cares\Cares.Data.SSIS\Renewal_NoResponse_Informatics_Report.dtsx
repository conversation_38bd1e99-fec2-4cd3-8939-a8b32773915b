﻿<?xml version="1.0"?>
<DTS:Executable xmlns:DTS="www.microsoft.com/SqlServer/Dts"
  DTS:refId="Package"
  DTS:CreationDate="8/8/2023 1:14:25 PM"
  DTS:CreationName="SSIS.Package.3"
  DTS:CreatorComputerName="MS-LT-32106"
  DTS:CreatorName="MEDICAID\MS001182"
  DTS:DTSID="{2A39A84C-5E57-4DFF-9481-168A1F23EF08}"
  DTS:EnableConfig="True"
  DTS:ExecutableType="SSIS.Package.3"
  DTS:LastModifiedProductVersion="15.0.2000.180"
  DTS:LocaleID="1033"
  DTS:ObjectName="Renewal_NoResponse_Informatics_Report"
  DTS:PackageType="5"
  DTS:VersionBuild="308"
  DTS:VersionGUID="{DDA629C1-FA00-4EE3-8A60-F559CC202BC8}">
  <DTS:Property
    DTS:Name="PackageFormatVersion">6</DTS:Property>
  <DTS:Configurations>
    <DTS:Configuration
      DTS:ConfigurationString="&quot;Staging&quot;;&quot;[dbo].[SSIS_PackageConfigurations]&quot;;&quot;Environment&quot;;"
      DTS:ConfigurationType="7"
      DTS:CreationName=""
      DTS:DTSID="{9B2B4FEB-0A2C-4F43-9B2E-D546BCD4CE26}"
      DTS:ObjectName="Environment" />
    <DTS:Configuration
      DTS:ConfigurationString="&quot;Staging&quot;;&quot;[dbo].[SSIS_PackageConfigurations]&quot;;&quot;Renewal_NoReponse_Informatics_FailureEmailCC&quot;;"
      DTS:ConfigurationType="7"
      DTS:CreationName=""
      DTS:DTSID="{849373C8-B6CE-4558-9C56-6D70C1562AE2}"
      DTS:ObjectName="Renewal_NoReponse_Informatics_FailureEmailCC" />
    <DTS:Configuration
      DTS:ConfigurationString="&quot;Staging&quot;;&quot;[dbo].[SSIS_PackageConfigurations]&quot;;&quot;Renewal_NoReponse_Informatics_FailureEmailTo&quot;;"
      DTS:ConfigurationType="7"
      DTS:CreationName=""
      DTS:DTSID="{EBD5E43D-9A83-48BF-8B45-0F5762D3F7D9}"
      DTS:ObjectName="Renewal_NoReponse_Informatics_FailureEmailTo" />
    <DTS:Configuration
      DTS:ConfigurationString="&quot;Staging&quot;;&quot;[dbo].[SSIS_PackageConfigurations]&quot;;&quot;Renewal_NoReponse_Informatics_SuccessEmailCC&quot;;"
      DTS:ConfigurationType="7"
      DTS:CreationName=""
      DTS:DTSID="{98A9158B-B22D-4256-9CE4-22DEF8A996C0}"
      DTS:ObjectName="Renewal_NoReponse_Informatics_SuccessEmailCC" />
    <DTS:Configuration
      DTS:ConfigurationString="&quot;Staging&quot;;&quot;[dbo].[SSIS_PackageConfigurations]&quot;;&quot;Renewalanalytics_SuccessEmailTo&quot;;"
      DTS:ConfigurationType="7"
      DTS:CreationName=""
      DTS:DTSID="{20CC7EE3-8B43-44DD-B7EF-154BF04BC948}"
      DTS:ObjectName="Renewal_NoReponse_Informatics_SuccessEmailTo" />
  </DTS:Configurations>
  <DTS:PackageParameters>
    <DTS:PackageParameter
      DTS:CreationName=""
      DTS:DataType="8"
      DTS:Description="The Reporting Date"
      DTS:DTSID="{C6A4FC72-B866-4DE7-944C-3148A4E09F10}"
      DTS:ObjectName="ReportingDate">
      <DTS:Property
        DTS:DataType="8"
        DTS:Name="ParameterValue">2025-08-26</DTS:Property>
    </DTS:PackageParameter>
  </DTS:PackageParameters>
  <DTS:Variables>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{2911338B-EC0F-4E2E-9435-0E153AD24019}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="Environment">
      <DTS:VariableValue
        DTS:DataType="8">DEV</DTS:VariableValue>
    </DTS:Variable>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{B71F73DE-6335-4BFD-A7BE-B0EA533F091F}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="Renewal_No_Response_Informatics_FailureEmailCC">
      <DTS:VariableValue
        DTS:DataType="8"><EMAIL></DTS:VariableValue>
    </DTS:Variable>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{AF4D1DD2-1452-480B-A53B-1FF274EE4FA9}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="Renewal_No_Response_Informatics_FailureEmailTo">
      <DTS:VariableValue
        DTS:DataType="8"><EMAIL></DTS:VariableValue>
    </DTS:Variable>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{7A29E632-49D9-4829-879B-D926C2B48681}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="Renewal_No_Response_Informatics_SuccessEmailCC">
      <DTS:VariableValue
        DTS:DataType="8"><EMAIL></DTS:VariableValue>
    </DTS:Variable>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{910502D2-BE73-4F85-AEE8-2526075A14EF}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="Renewal_No_Response_Informatics_SuccessEmailTo">
      <DTS:VariableValue
        DTS:DataType="8"><EMAIL></DTS:VariableValue>
    </DTS:Variable>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{62CAAFF9-27A1-44DE-992F-6196D48F001B}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="ReportingDate">
      <DTS:VariableValue
        DTS:DataType="8">2025-08-26</DTS:VariableValue>
    </DTS:Variable>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{65A35E2A-45FE-44A5-857D-1FF61695D1BA}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="SuccessBody">
      <DTS:VariableValue
        DTS:DataType="8">Renewal No Reponse data for Informatics Report</DTS:VariableValue>
    </DTS:Variable>
    <DTS:Variable
      DTS:CreationName=""
      DTS:DTSID="{68B91593-66A1-43F0-86A2-40631822E29B}"
      DTS:IncludeInDebugDump="2345"
      DTS:Namespace="User"
      DTS:ObjectName="SuccessSubjectLine">
      <DTS:VariableValue
        DTS:DataType="8">DEV - Renewal No Reponse data for Informatics Report</DTS:VariableValue>
    </DTS:Variable>
  </DTS:Variables>
  <DTS:Executables>
    <DTS:Executable
      DTS:refId="Package\Determine Reporting Date"
      DTS:CreationName="Microsoft.SqlServer.Dts.Tasks.ScriptTask.ScriptTask, Microsoft.SqlServer.ScriptTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
      DTS:Description="Script Task"
      DTS:DTSID="{3A4BC68F-0296-4805-BE51-8CC41DD8EA7C}"
      DTS:ExecutableType="Microsoft.SqlServer.Dts.Tasks.ScriptTask.ScriptTask, Microsoft.SqlServer.ScriptTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
      DTS:LocaleID="-1"
      DTS:ObjectName="Determine Reporting Date"
      DTS:ThreadHint="0">
      <DTS:Variables />
      <DTS:ObjectData>
        <ScriptProject
          Name="ST_66c92738e926420299891acfbad73138"
          VSTAMajorVersion="3"
          VSTAMinorVersion="0"
          Language="CSharp"
          ReadOnlyVariables="$Package::ReportingDate"
          ReadWriteVariables="User::ReportingDate">
          <ProjectItem
            Name="Properties\Resources.resx"
            Encoding="UTF8"><![CDATA[<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
</root>]]></ProjectItem>
          <ProjectItem
            Name="ST_66c92738e926420299891acfbad73138.csproj"
            Encoding="UTF8"><![CDATA[<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectTypeGuids>{30D016F9-3734-4E33-A861-5E7D899E18F3};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A09652F0-0A31-416D-9A53-1C344DE4F1F0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ST_90ffed5b23fc45eba7332ab7d5420748</RootNamespace>
    <AssemblyName>ST_66c92738e926420299891acfbad73138</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ResolveAssemblyReferenceIgnoreTargetFrameworkAttributeVersionMismatch>true</ResolveAssemblyReferenceIgnoreTargetFrameworkAttributeVersionMismatch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>.\bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
    <OutputPath>.\bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.SqlServer.ManagedDTS, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
    <Reference Include="Microsoft.SqlServer.ScriptTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="ScriptMain.cs">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <!-- Include the build rules for a C# project.-->
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{30D016F9-3734-4E33-A861-5E7D899E18F3}">
        <ProjectProperties HostName="VSTAHostName" HostPackage="{B3A685AA-7EAF-4BC6-9940-57959FA5AC07}" ApplicationType="usd" Language="cs" TemplatesPath="" />
        <Host Name="ScriptTask" />
        <ProjectClient>
          <HostIdentifier>SSIS_ST110</HostIdentifier>
        </ProjectClient>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>]]></ProjectItem>
          <ProjectItem
            Name="Properties\Settings.Designer.cs"
            Encoding="UTF8"><![CDATA[//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode", Scope="member", Target="ST_90ffed5b23fc45eba7332ab7d5420748.Properties.Settings.get_Default():ST_90ffed5b23fc45eba7332ab7d5420748.Properties.Sett" +
    "ings")]

namespace ST_90ffed5b23fc45eba7332ab7d5420748.Properties {
    
    
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        private static Settings defaultInstance = new Settings();
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
    }
}]]></ProjectItem>
          <ProjectItem
            Name="ScriptMain.cs"
            Encoding="UTF8"><![CDATA[


#region Namespaces
using System;
using System.Data;
using Microsoft.SqlServer.Dts.Runtime;
using System.Windows.Forms;
#endregion

namespace ST_90ffed5b23fc45eba7332ab7d5420748
{
    /// <summary>
    /// ScriptMain is the entry point class of the script.  Do not change the name, attributes,
    /// or parent of this class.
    /// </summary>
	[Microsoft.SqlServer.Dts.Tasks.ScriptTask.SSISScriptTaskEntryPointAttribute]
	public partial class ScriptMain : Microsoft.SqlServer.Dts.Tasks.ScriptTask.VSTARTScriptObjectModelBase
	{
        #region Help:  Using Integration Services variables and parameters in a script
        /* To use a variable in this script, first ensure that the variable has been added to 
         * either the list contained in the ReadOnlyVariables property or the list contained in 
         * the ReadWriteVariables property of this script task, according to whether or not your
         * code needs to write to the variable.  To add the variable, save this script, close this instance of
         * Visual Studio, and update the ReadOnlyVariables and 
         * ReadWriteVariables properties in the Script Transformation Editor window.
         * To use a parameter in this script, follow the same steps. Parameters are always read-only.
         * 
         * Example of reading from a variable:
         *  DateTime startTime = (DateTime) Dts.Variables["System::StartTime"].Value;
         * 
         * Example of writing to a variable:
         *  Dts.Variables["User::myStringVariable"].Value = "new value";
         * 
         * Example of reading from a package parameter:
         *  int batchId = (int) Dts.Variables["$Package::batchId"].Value;
         *  
         * Example of reading from a project parameter:
         *  int batchId = (int) Dts.Variables["$Project::batchId"].Value;
         * 
         * Example of reading from a sensitive project parameter:
         *  int batchId = (int) Dts.Variables["$Project::batchId"].GetSensitiveValue();
         * */

        #endregion

        #region Help:  Firing Integration Services events from a script
        /* This script task can fire events for logging purposes.
         * 
         * Example of firing an error event:
         *  Dts.Events.FireError(18, "Process Values", "Bad value", "", 0);
         * 
         * Example of firing an information event:
         *  Dts.Events.FireInformation(3, "Process Values", "Processing has started", "", 0, ref fireAgain)
         * 
         * Example of firing a warning event:
         *  Dts.Events.FireWarning(14, "Process Values", "No values received for input", "", 0);
         * */
        #endregion

        #region Help:  Using Integration Services connection managers in a script
        /* Some types of connection managers can be used in this script task.  See the topic 
         * "Working with Connection Managers Programatically" for details.
         * 
         * Example of using an ADO.Net connection manager:
         *  object rawConnection = Dts.Connections["Sales DB"].AcquireConnection(Dts.Transaction);
         *  SqlConnection myADONETConnection = (SqlConnection)rawConnection;
         *  //Use the connection in some code here, then release the connection
         *  Dts.Connections["Sales DB"].ReleaseConnection(rawConnection);
         *
         * Example of using a File connection manager
         *  object rawConnection = Dts.Connections["Prices.zip"].AcquireConnection(Dts.Transaction);
         *  string filePath = (string)rawConnection;
         *  //Use the connection in some code here, then release the connection
         *  Dts.Connections["Prices.zip"].ReleaseConnection(rawConnection);
         * */
        #endregion


		/// <summary>
        /// This method is called when this script task executes in the control flow.
        /// Before returning from this method, set the value of Dts.TaskResult to indicate success or failure.
        /// To open Help, press F1.
        /// </summary>
		public void Main()
		{
            var paramFileDate = (string)Dts.Variables["$Package::ReportingDate"].Value;
            DateTime dateTime;
            var success = DateTime.TryParse(paramFileDate, out dateTime);
            if (!success)
            {
                dateTime = DateTime.Today;
            }
            Dts.Variables["User::ReportingDate"].Value = dateTime.ToString("yyyy-MM-dd");
            Dts.TaskResult = (int)ScriptResults.Success;
        }

        #region ScriptResults declaration
        /// <summary>
        /// This enum provides a convenient shorthand within the scope of this class for setting the
        /// result of the script.
        /// 
        /// This code was generated automatically.
        /// </summary>
        enum ScriptResults
        {
            Success = Microsoft.SqlServer.Dts.Runtime.DTSExecResult.Success,
            Failure = Microsoft.SqlServer.Dts.Runtime.DTSExecResult.Failure
        };
        #endregion

	}
}]]></ProjectItem>
          <ProjectItem
            Name="Project"
            Encoding="UTF16LE"><![CDATA[<?xml version="1.0" encoding="UTF-16" standalone="yes"?>
<c:Project xmlns:c="http://schemas.microsoft.com/codeprojectml/2010/08/main" xmlns:msb="http://schemas.microsoft.com/developer/msbuild/2003" runtimeVersion="3.0" schemaVersion="1.0">
	<msb:PropertyGroup>
		<msb:CodeName>ST_90ffed5b23fc45eba7332ab7d5420748</msb:CodeName>
		<msb:Language>msBuild</msb:Language>
		<msb:DisplayName>ST_90ffed5b23fc45eba7332ab7d5420748</msb:DisplayName>
		<msb:ProjectId>{2711F0D1-8FAE-4FE7-AB25-B90DAD4A4FB5}</msb:ProjectId>
	</msb:PropertyGroup>
	<msb:ItemGroup>
		<msb:Project Include="ST_66c92738e926420299891acfbad73138.csproj"/>
		<msb:File Include="Properties\AssemblyInfo.cs"/>
		<msb:File Include="Properties\Settings.settings"/>
		<msb:File Include="Properties\Resources.resx"/>
		<msb:File Include="Properties\Resources.Designer.cs"/>
		<msb:File Include="Properties\Settings.Designer.cs"/>
		<msb:File Include="ScriptMain.cs"/>
	</msb:ItemGroup>
</c:Project>]]></ProjectItem>
          <ProjectItem
            Name="Properties\AssemblyInfo.cs"
            Encoding="UTF8"><![CDATA[using System.Reflection;
using System.Runtime.CompilerServices;

//
// General Information about an assembly is controlled through the following 
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.
//
[assembly: AssemblyTitle("ST_90ffed5b23fc45eba7332ab7d5420748")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("ST_90ffed5b23fc45eba7332ab7d5420748")]
[assembly: AssemblyCopyright("Copyright @  2017")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
//
// Version information for an assembly consists of the following four values:
//
//      Major Version
//      Minor Version 
//      Build Number
//      Revision
//
// You can specify all the values or you can default the Revision and Build Numbers 
// by using the '*' as shown below:

[assembly: AssemblyVersion("1.0.*")]]]></ProjectItem>
          <ProjectItem
            Name="Properties\Settings.settings"
            Encoding="UTF8"><![CDATA[<?xml version='1.0' encoding='iso-8859-1'?>
<SettingsFile xmlns="uri:settings" CurrentProfile="(Default)">
  <Profiles>
    <Profile Name="(Default)" />
  </Profiles>
  <Settings />
</SettingsFile>]]></ProjectItem>
          <ProjectItem
            Name="Properties\Resources.Designer.cs"
            Encoding="UTF8"><![CDATA[//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode", Scope="member", Target="ST_90ffed5b23fc45eba7332ab7d5420748.Properties.Resources.get_ResourceManager():System.Resources.Resou" +
    "rceManager")]
[assembly: global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode", Scope="member", Target="ST_90ffed5b23fc45eba7332ab7d5420748.Properties.Resources.get_Culture():System.Globalization.CultureIn" +
    "fo")]
[assembly: global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode", Scope="member", Target="ST_90ffed5b23fc45eba7332ab7d5420748.Properties.Resources.set_Culture(System.Globalization.CultureInfo" +
    "):Void")]

namespace ST_90ffed5b23fc45eba7332ab7d5420748.Properties {
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if ((resourceMan == null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ST_90ffed5b23fc45eba7332ab7d5420748.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
    }
}]]></ProjectItem>
          <BinaryItem
            Name="ST_66c92738e926420299891acfbad73138.dll">TVqQAAMAAAAEAAAA//8AALgAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA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****************************************************************************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</BinaryItem>
        </ScriptProject>
      </DTS:ObjectData>
    </DTS:Executable>
    <DTS:Executable
      DTS:refId="Package\Execute SQL Task"
      DTS:CreationName="Microsoft.SqlServer.Dts.Tasks.ExecuteSQLTask.ExecuteSQLTask, Microsoft.SqlServer.SQLTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
      DTS:Description="Execute SQL Task"
      DTS:DTSID="{02004F6D-2DB4-42A9-A7DE-D587C8F0CDE2}"
      DTS:ExecutableType="Microsoft.SqlServer.Dts.Tasks.ExecuteSQLTask.ExecuteSQLTask, Microsoft.SqlServer.SQLTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
      DTS:LocaleID="-1"
      DTS:ObjectName="Execute SQL Task"
      DTS:ThreadHint="0">
      <DTS:Variables />
      <DTS:ObjectData>
        <SQLTask:SqlTaskData
          SQLTask:Connection="{C5642316-88DD-4441-9088-4623BEB0ADE1}"
          SQLTask:SqlStatementSource="EXEC dbo.usp_POPULATE_RENEWAL_NORESPONSE_INFORMATICA_REPORT @ReportingDate = ?" xmlns:SQLTask="www.microsoft.com/sqlserver/dts/tasks/sqltask">
          <SQLTask:ParameterBinding
            SQLTask:ParameterName="@ReportingDate"
            SQLTask:DtsVariableName="User::ReportingDate"
            SQLTask:ParameterDirection="Input"
            SQLTask:DataType="7"
            SQLTask:ParameterSize="-1" />
        </SQLTask:SqlTaskData>
      </DTS:ObjectData>
    </DTS:Executable>
    <DTS:Executable
      DTS:refId="Package\Success Email"
      DTS:CreationName="Microsoft.SqlServer.Dts.Tasks.SendMailTask.SendMailTask, Microsoft.SqlServer.SendMailTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
      DTS:DelayValidation="True"
      DTS:Description="Send Mail Task"
      DTS:DTSID="{2C102B23-0B3F-4480-827E-7FD4A708D3A2}"
      DTS:ExecutableType="Microsoft.SqlServer.Dts.Tasks.SendMailTask.SendMailTask, Microsoft.SqlServer.SendMailTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
      DTS:LocaleID="-1"
      DTS:ObjectName="Success Email"
      DTS:ThreadHint="0">
      <DTS:Variables />
      <DTS:PropertyExpression
        DTS:Name="CCLine">@[User::Renewal_No_Response_Informatics_SuccessEmailCC]</DTS:PropertyExpression>
      <DTS:PropertyExpression
        DTS:Name="Subject">@[User::SuccessSubjectLine]</DTS:PropertyExpression>
      <DTS:PropertyExpression
        DTS:Name="ToLine">@[User::Renewal_No_Response_Informatics_SuccessEmailTo]</DTS:PropertyExpression>
      <DTS:ObjectData>
        <SendMailTask:SendMailTaskData
          SendMailTask:SMTPServer="{C0FC5A22-C009-489D-A2A7-EDC1DEE13B34}"
          SendMailTask:From="<EMAIL>"
          SendMailTask:To="<EMAIL>"
          SendMailTask:CC="<EMAIL>"
          SendMailTask:Subject="DEV - Renewal No Reponse data for Informatics Report"
          SendMailTask:MessageSourceType="Variable"
          SendMailTask:MessageSource="User::SuccessBody" xmlns:SendMailTask="www.microsoft.com/sqlserver/dts/tasks/sendmailtask" />
      </DTS:ObjectData>
    </DTS:Executable>
  </DTS:Executables>
  <DTS:PrecedenceConstraints>
    <DTS:PrecedenceConstraint
      DTS:refId="Package.PrecedenceConstraints[Constraint]"
      DTS:CreationName=""
      DTS:DTSID="{78FBD1BC-1F02-4E0E-AA71-6C6075E5A437}"
      DTS:From="Package\Determine Reporting Date"
      DTS:LogicalAnd="True"
      DTS:ObjectName="Constraint"
      DTS:To="Package\Execute SQL Task" />
    <DTS:PrecedenceConstraint
      DTS:refId="Package.PrecedenceConstraints[Constraint 1]"
      DTS:CreationName=""
      DTS:DTSID="{C8DBCA1E-571C-46B1-ADA8-0EFCC48F8EF0}"
      DTS:From="Package\Execute SQL Task"
      DTS:LogicalAnd="True"
      DTS:ObjectName="Constraint 1"
      DTS:To="Package\Success Email" />
  </DTS:PrecedenceConstraints>
  <DTS:EventHandlers>
    <DTS:EventHandler
      DTS:refId="Package.EventHandlers[OnError]"
      DTS:CreationName="OnError"
      DTS:DTSID="{FEDF4191-EAB8-46FC-B136-42DA68A2A422}"
      DTS:EventID="0"
      DTS:EventName="OnError"
      DTS:LocaleID="-1">
      <DTS:Variables>
        <DTS:Variable
          DTS:CreationName=""
          DTS:Description="The propagate property of the event"
          DTS:DTSID="{F611A447-9B80-435F-A795-4EA85359FE01}"
          DTS:IncludeInDebugDump="6789"
          DTS:Namespace="System"
          DTS:ObjectName="Propagate">
          <DTS:VariableValue
            DTS:DataType="11">-1</DTS:VariableValue>
        </DTS:Variable>
      </DTS:Variables>
      <DTS:Executables>
        <DTS:Executable
          DTS:refId="Package.EventHandlers[OnError]\Failure Email"
          DTS:CreationName="Microsoft.SqlServer.Dts.Tasks.SendMailTask.SendMailTask, Microsoft.SqlServer.SendMailTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
          DTS:Description="Send Mail Task"
          DTS:DTSID="{7B0E770F-1845-486C-9876-680721122BA4}"
          DTS:ExecutableType="Microsoft.SqlServer.Dts.Tasks.SendMailTask.SendMailTask, Microsoft.SqlServer.SendMailTask, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"
          DTS:LocaleID="-1"
          DTS:ObjectName="Failure Email">
          <DTS:Variables />
          <DTS:PropertyExpression
            DTS:Name="CCLine">@[User::Renewal_No_Response_Informatics_FailureEmailCC]</DTS:PropertyExpression>
          <DTS:PropertyExpression
            DTS:Name="MessageSource">"The SSIS Package: " +@[System::PackageName]+".dtsx" +"  FAILED while running on the Server/Machine: "+ @[System::MachineName]+" on " + (DT_WSTR,4)YEAR(GETDATE()) + "-"  + RIGHT("0" + (DT_WSTR,2)MONTH(GETDATE()), 2) + "-"  + RIGHT("0" + (DT_WSTR,2)DAY( GETDATE()), 2) + " " + "at" + " "+ RIGHT("0" + (DT_WSTR,2)DATEPART("hh", GETDATE()), 2) + ":" + RIGHT("0" + (DT_WSTR,2)DATEPART("mi", GETDATE()), 2) + ":" + RIGHT("0" + (DT_WSTR,2)DATEPART("ss", GETDATE()), 2)
 + "\n\n"+"Task Name(Task where job failed ) – "+ @[System::SourceName]+"\n\n"+"Task Type(SSIS Component) – "+@[System::SourceDescription]+"\n\n"+ "Error Description -"+@[System::ErrorDescription]+"\n\n"+ "Error Code – "+(DT_WSTR, 12) @[System::ErrorCode]+"\n\n"</DTS:PropertyExpression>
          <DTS:PropertyExpression
            DTS:Name="Subject">(((@[User::Environment] == "TEST") || (@[User::Environment]  == "DEV")) ? ("[" + @[User::Environment] + "] " ) : (""))  + "Renewal No Response Informatics file failed on " +  LEFT( (DT_WSTR,29)   GETDATE(), 20 )</DTS:PropertyExpression>
          <DTS:PropertyExpression
            DTS:Name="ToLine">@[User::Renewal_No_Response_Informatics_FailureEmailTo]</DTS:PropertyExpression>
          <DTS:ObjectData>
            <SendMailTask:SendMailTaskData
              SendMailTask:SMTPServer="{C0FC5A22-C009-489D-A2A7-EDC1DEE13B34}"
              SendMailTask:From="<EMAIL>"
              SendMailTask:To="<EMAIL>"
              SendMailTask:CC="<EMAIL>"
              SendMailTask:Subject="[DEV] Renewal No Response Informatics file failed on 2025-09-19 09:40:03."
              SendMailTask:MessageSource="The SSIS Package: Renewal_NoResponse_Informatics_Report.dtsx  FAILED while running on the Server/Machine: MS-LT-31939 on 2025-09-19 at 09:40:03&#xA;&#xA;Task Name(Task where job failed ) – &#xA;&#xA;Task Type(SSIS Component) – &#xA;&#xA;Error Description -&#xA;&#xA;Error Code – 0&#xA;&#xA;" xmlns:SendMailTask="www.microsoft.com/sqlserver/dts/tasks/sendmailtask" />
          </DTS:ObjectData>
        </DTS:Executable>
      </DTS:Executables>
    </DTS:EventHandler>
  </DTS:EventHandlers>
  <DTS:DesignTimeProperties><![CDATA[<?xml version="1.0"?>
<!--This CDATA section contains the layout information of the package. The section includes information such as (x,y) coordinates, width, and height.-->
<!--If you manually edit this section and make a mistake, you can delete it. -->
<!--The package will still be able to load normally but the previous layout information will be lost and the designer will automatically re-arrange the elements on the design surface.-->
<Objects
  Version="sql11">
  <!--Each node below will contain properties that do not affect runtime behavior.-->
  <Package
    design-time-name="Package">
    <LayoutInfo>
      <GraphLayout
        Capacity="8" xmlns="clr-namespace:Microsoft.SqlServer.IntegrationServices.Designer.Model.Serialization;assembly=Microsoft.SqlServer.IntegrationServices.Graph" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mssgle="clr-namespace:Microsoft.SqlServer.Graph.LayoutEngine;assembly=Microsoft.SqlServer.Graph">
        <AnnotationLayout
          Text="Author: Balaram Kasula&#xA;Date: 08/20/2025&#xA;Description: This package runs to populate the date for the Informatica report"
          ParentId="Package"
          FontInfo="{x:Null}"
          Size="421,88"
          Id="90c3a04f-438d-4633-baaa-bef3a7c49467"
          TopLeft="19,46" />
        <NodeLayout
          Size="145,42"
          Id="Package\Success Email"
          TopLeft="577,292" />
        <NodeLayout
          Size="207,42"
          Id="Package\Determine Reporting Date"
          TopLeft="548,72" />
        <NodeLayout
          Size="170,42"
          Id="Package\Execute SQL Task"
          TopLeft="569,179" />
        <EdgeLayout
          Id="Package.PrecedenceConstraints[Constraint]"
          TopLeft="652.75,114">
          <EdgeLayout.Curve>
            <mssgle:Curve
              StartConnector="{x:Null}"
              EndConnector="0,65"
              Start="0,0"
              End="0,57.5">
              <mssgle:Curve.Segments>
                <mssgle:SegmentCollection
                  Capacity="5">
                  <mssgle:LineSegment
                    End="0,57.5" />
                </mssgle:SegmentCollection>
              </mssgle:Curve.Segments>
            </mssgle:Curve>
          </EdgeLayout.Curve>
          <EdgeLayout.Labels>
            <EdgeLabelCollection />
          </EdgeLayout.Labels>
        </EdgeLayout>
        <EdgeLayout
          Id="Package.PrecedenceConstraints[Constraint 1]"
          TopLeft="651.75,221">
          <EdgeLayout.Curve>
            <mssgle:Curve
              StartConnector="{x:Null}"
              EndConnector="0,71"
              Start="0,0"
              End="0,63.5">
              <mssgle:Curve.Segments>
                <mssgle:SegmentCollection
                  Capacity="5">
                  <mssgle:LineSegment
                    End="0,63.5" />
                </mssgle:SegmentCollection>
              </mssgle:Curve.Segments>
            </mssgle:Curve>
          </EdgeLayout.Curve>
          <EdgeLayout.Labels>
            <EdgeLabelCollection />
          </EdgeLayout.Labels>
        </EdgeLayout>
      </GraphLayout>
    </LayoutInfo>
  </Package>
  <DtsEventHandler
    design-time-name="Package.EventHandlers[OnError]">
    <LayoutInfo>
      <GraphLayout
        Capacity="4" xmlns="clr-namespace:Microsoft.SqlServer.IntegrationServices.Designer.Model.Serialization;assembly=Microsoft.SqlServer.IntegrationServices.Graph">
        <NodeLayout
          Size="139,42"
          Id="Package.EventHandlers[OnError]\Failure Email"
          TopLeft="340,81" />
      </GraphLayout>
    </LayoutInfo>
  </DtsEventHandler>
</Objects>]]></DTS:DesignTimeProperties>
</DTS:Executable>