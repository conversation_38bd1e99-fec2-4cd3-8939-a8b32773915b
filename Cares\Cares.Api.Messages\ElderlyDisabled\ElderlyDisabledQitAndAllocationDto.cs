﻿using Cares.Api.Infrastructure.WebAPI;
using Cares.Api.Messages.Applications;
using Cares.Models.Application;
using System.Collections.Generic;

namespace Cares.Api.Messages.ElderlyDisabled
{
    public class ElderlyDisabledQitAndAllocationDto : BaseApiResponse
    {
        public long EnDApplicationDetailId { get; set; }
        public byte? FamilySize { get; set; }
        public List<ElderlyDisabledNonMagiIncomeDetailDto> QitData { get; set; } = new List<ElderlyDisabledNonMagiIncomeDetailDto>();
        public List<ElderlyDisabledFamilyAllocationDetail> FamilyAllocationList { get; set; } = new List<ElderlyDisabledFamilyAllocationDetail>();
        public List<ElderlyDisabledSpouseAllocationDetail> SpouseAllocationList { get; set; } = new List<ElderlyDisabledSpouseAllocationDetail>();
    }
}
