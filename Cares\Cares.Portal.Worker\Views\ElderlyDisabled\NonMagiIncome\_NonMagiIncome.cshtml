﻿@model Cares.Portal.Worker.Models.Models.ElderlyDisabled.ViewModel.NonMagiIncomeViewModel
@using System;
@using DD = Cares.Classes.DropDownValues
@using Cares.Portal.Infrastructure
@using Constants = Cares.Api.Infrastructure.Constants;
@using Enums = Cares.Api.Infrastructure.Enums;

@{
	var delete = @CommonHtmlExtensions.IconHelper(Cares.Api.Infrastructure.Constants.Icons.IconDelete);
	string nonMagiIncomeIndex = ViewData[PortalConstants.ViewDataConstants.nonMagiIncomeIndex].ToString();
	string namePrefix = ViewData[PortalConstants.ViewDataConstants.NamePrefix].ToString();
	string appId = ViewData.ContainsKey(PortalConstants.ViewDataConstants.ApplicationId) ? ViewData[PortalConstants.ViewDataConstants.ApplicationId].ToString() : "";

	string nameDetailPrefix = namePrefix + @Html.NameFor(model => model.EDNonMagiIncomeDetail) + "[{detailIndex}].";
	string nameDetailPrefixText = namePrefix + "EDNonMagiIncomeDetail" + "[{detailIndex}].";
	string hasDetailsClass = Model.EDNonMagiIncomeDetail.Any() ? "readonly" : "";

	bool showClaimNumberField =    Model.IncomeTypeId == Constants.ReferenceValues.IncomeType.BlackLung
								|| Model.IncomeTypeId == Constants.ReferenceValues.IncomeType.Veteran
								|| Model.IncomeTypeId == Constants.ReferenceValues.IncomeType.Railroad
								|| Model.IncomeTypeId == Constants.ReferenceValues.IncomeType.SOCIAL_SECURITY_BENEFITS;
}

<div class="card non-magi-income-template shadow mb-2" id="@("divNonMagiIncome" + nonMagiIncomeIndex)">
    @Html.HiddenFor(model => model.ApplicationNonMagiIncomeId, new { Name = namePrefix + "ApplicationNonMagiIncomeId", id = "hdn" + nonMagiIncomeIndex + "ApplicationNonMagiIncomeId", @class = "IncomeApplicationNonMagiIncomeId" })
    @Html.HiddenFor(model => model.IsDeleted, new { Name = namePrefix + "IsDeleted", @class = "IncomeIsDeleted", id = "hdn" + nonMagiIncomeIndex + "IsDeleted" })

    <div class="card-header text-white d-flex justify-content-between align-items-center py-1">
        <label>Income</label>
        <button type="button" class="btn-ed-circle btn-elderly-disabled-delete btn-delete-non-magi-income pt-1" data-toggle="tooltip" data-original-title="Delete" id=@("btnDeleteNonMagiIncome" + nonMagiIncomeIndex)>@delete</button>
    </div>
    <div class="card-body p-2">
		<div class="row m-auto" id="divDetailsOnIncome">
			<div class="form-group col-xs-6 col-md-3 col-xl-2">
				<label for="ddl@(nonMagiIncomeIndex)IncomeType" class="required">Type of Income</label>
				@Html.DropDownListFor(model => model.IncomeTypeId, DD.GetNonMagiIncomeTypeList(Model.IncomeTypeId), "Select One...", new { id = "ddl" + nonMagiIncomeIndex + "IncomeTypeId", Name = namePrefix + "IncomeTypeId", @class = "form-control select-income-type validate-required " + hasDetailsClass })
			</div>
			<div class="form-group col-xs-6 col-md-3 col-xl-2 va-indicator" style="@((Model.IncomeTypeId ?? 0) == Constants.ReferenceValues.IncomeType.Veteran ? string.Empty : "display:none;")">
				<label for="ddl@(nonMagiIncomeIndex)VAIndicatorId">VA</label>
				@Html.DropDownListFor(model => model.VAIndicatorId, DD.GetVaIndicators(Model.VAIndicatorId.ToString()), "Select One...", new { id = "ddl" + nonMagiIncomeIndex + "VAIndicatorId", Name = namePrefix + "VAIndicatorId", @class = "form-control select-va-indicator" })
			</div>
			<div class="form-group col-xs-6 col-md-3 col-xl-2 other-income-type" style="@(Model.IncomeTypeId == Convert.ToInt32(Constants.ReferenceValues.IncomeType.OTHER) ? string.Empty : "display:none;")">
				<label for="txtbx@(nonMagiIncomeIndex)OtherIncomeDescription">Description</label>
				@Html.TextBoxFor(model => model.OtherIncomeDescription, new { id = "txtbx" + nonMagiIncomeIndex + "OtherIncomeDescription", placeholder = "Other Income Type", Name = namePrefix + "OtherIncomeDescription", @class = "form-control JS_EDIncomeOtherDescriptionFormat other-income-type validate-otherDescriptionFormat", maxlength = 50 })
			</div>
			<div class="form-group col-xs-6 col-md-3 col-xl-2 claim-number" style="@(showClaimNumberField ? string.Empty : "display:none;")">
				<label for="txtbx@(nonMagiIncomeIndex)ClaimNumber">Claim Number</label>
				@Html.TextBoxFor(model => model.ClaimNumber, new { id = "txtbx" + nonMagiIncomeIndex + "ClaimNumber", placeholder = "Claim Number", Name = namePrefix + "ClaimNumber", @class = "form-control claim-number JS_numAllowed validate-claimNumberFormat", maxlength = 12 })
			</div>
			<div class="form-group col-xs-6 col-md-3 col-xl-2 member" style="@(Model.IncomeTypeId > 0 ? string.Empty : "display:none;")">
				<label for="ddl@(nonMagiIncomeIndex)Member" class="required">Member</label>
				@Html.DropDownListFor(model => model.IncomeRelationshipTypeId, DD.GetRelationshipTypesNonMgiIncome(Model.IncomeRelationshipTypeId), "Select One...", new { id = "ddl" + nonMagiIncomeIndex + "IncomeRelationshipTypeId", Name = namePrefix + "IncomeRelationshipTypeId", @class = "form-control select-member validate-required" })
			</div>
			<div class="form-group col-xs-6 col-md-3 col-xl-2 attested-amount" style="@(Model.IncomeTypeId > 0 ? string.Empty : "display:none;")">
				<label for="txtbx@(nonMagiIncomeIndex)AttestedIncomeAmount" class="required">Attested</label>
				<div class="input-group">
					<div class="input-group-prepend">
						<span class="input-group-text border-0">
							@CommonHtmlExtensions.IconHelper(Constants.Icons.IconDollarSign, new string[] { "colorNavyBlue", "align-middle" })
						</span>
					</div>
					@Html.TextBoxFor(model => model.AttestedIncomeAmount, new { id = "txtbx" + nonMagiIncomeIndex + "AttestedIncomeAmount", data_toggle = "tooltip", title = "Attested Amount", Name = namePrefix + "AttestedIncomeAmount", @class = "form-control attested-amount validate-required validate-dollarAmountNonNegative", maxlength = 12 })
				</div>
			</div>
			<div class="form-group col-xs-6 col-md-3 col-xl-2 verify-amount" style="@(Model.IncomeTypeId > 0 ? string.Empty : "display:none;")">
				<label for="txtbx@(nonMagiIncomeIndex)GrossIncomeAmount">@(Model.IncomeTypeId == (int)Constants.ReferenceValues.IncomeType.Veteran && Model.VAIndicatorId == 2 ? "VA Check (Gross)" : "Gross")</label>
				<div class="input-group">
					<div class="input-group-prepend">
						<span class="input-group-text border-0">
							@CommonHtmlExtensions.IconHelper(Constants.Icons.IconDollarSign, new string[] { "colorNavyBlue", "align-middle" })
						</span>
					</div>
					@Html.TextBoxFor(model => model.GrossIncomeAmount, new { id = "txtbx" + nonMagiIncomeIndex + "GrossIncomeAmount", data_toggle = "tooltip", title = "Gross Amount", Name = namePrefix + "GrossIncomeAmount", @class = "form-control verify-amount validate-dollarAmountNonNegative", maxlength = 12 })
				</div>
			</div>
			<div class="form-group col-xs-6 col-md-3 col-xl-2 verify-amount" style="@(Model.IncomeTypeId > 0 ? string.Empty : "display:none;")">
				<label for="txtbx@(nonMagiIncomeIndex)NetIncomeAmount">Net</label>
				<div class="input-group">
					<div class="input-group-prepend">
						<span class="input-group-text border-0">
							@CommonHtmlExtensions.IconHelper(Constants.Icons.IconDollarSign, new string[] { "colorNavyBlue", "align-middle" })
						</span>
					</div>
					@Html.TextBoxFor(model => model.NetIncomeAmount, new { id = "txtbx" + nonMagiIncomeIndex + "NetIncomeAmount", data_toggle = "tooltip", title = "Net Amount", Name = namePrefix + "NetIncomeAmount", @class = "form-control " + (Model.IncomeTypeId == 2 ? "mask-negative-dollar validate-dollarAmountIncludeNegative" : "verify-amount validate-dollarAmountNonNegative"), maxlength = 12 })
				</div>
			</div>
			@*Showing Button to Calculate Net Income only on Veteran Income Type*@
			@if (Model.IncomeTypeId == (int)Enums.IncomeType.Veteran_Income.Id)
			{
				<div class="form-group col-xs-6 col-md-3 col-xl-2">
					@Html.ActionLink("Calculate", "CalculateVANetValues", new { applicationId = appId, applicationNonMagiIncomeId = Model.ApplicationNonMagiIncomeId }, new { @class = "btn btn-info btn-gradient mt-4 shadow", data_index = nonMagiIncomeIndex, data_nonmagi_id = Model.ApplicationNonMagiIncomeId })
				</div>
			}
			<div class="form-group col-xs-6 col-md-3 col-xl-2 frequency" style="@(Model.IncomeTypeId > 0 ? string.Empty : "display:none;")">
				<label for="ddl@(nonMagiIncomeIndex)IncomeFrequency" class="required">Frequency</label>
				@Html.DropDownListFor(model => model.IncomeFrequencyId, DD.GetEDIncomeFrequency(Model.IncomeFrequencyId.ToString(), "W,E,T,M,Q,Y,N"), "Select One...", new { id = "ddl" + nonMagiIncomeIndex + "IncomeFrequencyId", Name = namePrefix + "IncomeFrequencyId", @class = "form-control select-frequency validate-required" })
			</div>
			@if (Model.DateStop != null && Model.DateStop != (new DateTime(1, 1, 1)))
			{
				<div class="form-group col-xs-6 col-md-3 col-lg-2">
					<label for="txtbx@(nonMagiIncomeIndex)DateStop" class="required">Date Stopped</label>
					@Html.TextBox("DateStop", Model.DateStop.ToString("MM/dd/yyyy"), new { id = "txtbx" + nonMagiIncomeIndex + "DateStop", Name = namePrefix + "DateStoppedId", @class = "form-control dateOnlyMask", @readOnly = "read-only" })
				</div>
			}
		</div>
        <div>
            <fieldset class="ml-3 mb-2">
                <legend>Budget Calculation fields</legend>
                <div class="col-12 ml-1 w-100">

                    <div class="mt-1" id="divNonMagi-detail">
                        <div id="@("divNonMagi-records-" + nonMagiIncomeIndex)" class="col-12">
                            @for (int ii = 0; ii < Model.EDNonMagiIncomeDetail.Count; ii++)
                            {
                                string indexPrefix = nameDetailPrefix.Replace("{detailIndex}", ii.ToString());
                                string netIncomeAmountClass = Model.IncomeTypeId == 2 ? "mask-negative-dollar" : "verify-amount";
                                @Html.Partial("NonMagiIncome/_NonMagiIncomeDetail", Model.EDNonMagiIncomeDetail[ii], new ViewDataDictionary() { { PortalConstants.ViewDataConstants.NetIncomeAmountClass, netIncomeAmountClass }, { PortalConstants.ViewDataConstants.nonMagiIncomeDetailIndex, ii }, { PortalConstants.ViewDataConstants.nonMagiIncomeIndex, nonMagiIncomeIndex }, { PortalConstants.ViewDataConstants.NameDetailPrefix, indexPrefix }, { PortalConstants.ViewDataConstants.HeaderIncomeTypeId, Model.IncomeTypeId.ToString() ?? String.Empty }, { PortalConstants.ViewDataConstants.HeaderVaIndicator, Model.VAIndicatorId.ToString() ?? String.Empty } })
                            }
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <input type="hidden" id="@("hdnNonMagiIncomeDetailCounter_" + nonMagiIncomeIndex)" value="@(Model.EDNonMagiIncomeDetail.Count)" />
                        <button class="btn btn-info btn-gradient btnAddDetailIncome shadow" data-index="@nonMagiIncomeIndex" name="@("btnAddRecord" + nonMagiIncomeIndex)" type="button">Add Row</button>
                    </div>
                </div>
            </fieldset>
        </div>
        <div class="row m-auto">
            <div class="form-group col-6 col-md-3 va-other-information" style="@(Model.VAIndicatorId == (byte)Enums.enumVAIndicator.Other ? string.Empty : "display:none;")">
                <label for="txtbx@(nonMagiIncomeIndex)VaOtherInformation">Other VA Information</label>
                @Html.TextBoxFor(model => model.VaOtherInformation, new { id = "txtbx" + nonMagiIncomeIndex + "VaOtherInformation", placeholder = "VA Other Information", Name = namePrefix + "VaOtherInformation", @class = "form-control JS_EDVaOtherInformation va-other-information validate-vaOtherInfoFormat", maxlength = 50 })
            </div>
        </div>
    </div>
</div>
