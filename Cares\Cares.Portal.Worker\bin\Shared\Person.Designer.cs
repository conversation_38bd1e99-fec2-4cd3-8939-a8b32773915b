﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.Shared {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Person {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Person() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.Shared.Person", typeof(Person).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Citizenship.
        /// </summary>
        public static string citizenship {
            get {
                return ResourceManager.GetString("citizenship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Social Securtiy Number.
        /// </summary>
        public static string ConfirmSSN {
            get {
                return ResourceManager.GetString("ConfirmSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Birth.
        /// </summary>
        public static string dob {
            get {
                return ResourceManager.GetString("dob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB call failed. Please try again..
        /// </summary>
        public static string ErrorMessage {
            get {
                return ResourceManager.GetString("ErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ethnicity.
        /// </summary>
        public static string ethnicity {
            get {
                return ResourceManager.GetString("ethnicity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string firstName {
            get {
                return ResourceManager.GetString("firstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GENERATE.
        /// </summary>
        public static string GeneratePseudoSSN {
            get {
                return ResourceManager.GetString("GeneratePseudoSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string lastName {
            get {
                return ResourceManager.GetString("lastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marital Status.
        /// </summary>
        public static string maritalStatus {
            get {
                return ResourceManager.GetString("maritalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle Name.
        /// </summary>
        public static string middleName {
            get {
                return ResourceManager.GetString("middleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string name {
            get {
                return ResourceManager.GetString("name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (First/Middle/Last/Suffix).
        /// </summary>
        public static string nameParts {
            get {
                return ResourceManager.GetString("nameParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pseudo SSN Generator.
        /// </summary>
        public static string PseudoGenerator {
            get {
                return ResourceManager.GetString("PseudoGenerator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PSEUDO SSN.
        /// </summary>
        public static string PseudoSsn {
            get {
                return ResourceManager.GetString("PseudoSsn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Race.
        /// </summary>
        public static string race {
            get {
                return ResourceManager.GetString("race", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relationship.
        /// </summary>
        public static string relationship {
            get {
                return ResourceManager.GetString("relationship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sex.
        /// </summary>
        public static string sex {
            get {
                return ResourceManager.GetString("sex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security number.
        /// </summary>
        public static string ssn {
            get {
                return ResourceManager.GetString("ssn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB call succeeded..
        /// </summary>
        public static string SuccessMessage {
            get {
                return ResourceManager.GetString("SuccessMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suffix.
        /// </summary>
        public static string suffix {
            get {
                return ResourceManager.GetString("suffix", resourceCulture);
            }
        }
    }
}
