﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="addAnotherDependent" xml:space="preserve">
    <value>Add another dependent</value>
  </data>
  <data name="claimDependents" xml:space="preserve">
    <value>Will&amp;nbsp;[TAXFILER]&amp;nbsp;claim any dependents on&amp;nbsp;[HIS/HER/THEIR]&amp;nbsp;federal income tax return for&amp;nbsp;[COVERAGEYEAR]?</value>
    <comment>NAMES should be 'John Doe' if not married and filing jointly, or 'John Doe and Jane Doe' if married and filing jointly</comment>
  </data>
  <data name="claimDifferentDependentsNextYear" xml:space="preserve">
    <value>Will&amp;nbsp;[TAXFILER]&amp;nbsp;claim different dependents on the&amp;nbsp;[COVERAGEYEARPLUS]&amp;nbsp;tax return?</value>
    <comment>NAMES should be 'John Doe' if not married, or 'John Doe and Jane Doe' if married          COVERAGEYEARPLUS = plus 1 to coverage year</comment>
  </data>
  <data name="claimedAsDependentOnAnotherReturn" xml:space="preserve">
    <value>Will&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) be claimed as a dependent on someone else's federal income tax return for&amp;nbsp;[COVERAGEYEAR]?</value>
  </data>
  <data name="claimOtherDependents" xml:space="preserve">
    <value>Will&amp;nbsp;[TAXFILER]&amp;nbsp;claim any other dependents on his/her/their federal income tax return for&amp;nbsp;[COVERAGEYEAR]?</value>
    <comment>NAMES should be 'John Doe' if not married, or 'John Doe and Jane Doe' if married</comment>
  </data>
  <data name="isMarried" xml:space="preserve">
    <value>Is&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) married?</value>
  </data>
  <data name="liveWithBrotherSister" xml:space="preserve">
    <value>Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with brothers or sisters?</value>
  </data>
  <data name="liveWithClaimingParent" xml:space="preserve">
    <value>Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with the parent or stepparent(s) that claim&amp;nbsp;[NAME]&amp;nbsp;on the tax return?</value>
  </data>
  <data name="liveWithParent" xml:space="preserve">
    <value>Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with a parent and/or stepparent?</value>
  </data>
  <data name="liveWithParentNotOnApp" xml:space="preserve">
    <value>Does&amp;nbsp;[NAME] , (DOB:&amp;nbsp;[DOB]) live with a parent or stepparent other than&amp;nbsp;[TAXFILER]?</value>
    <comment>NAMES should be 'John Doe'  or 'John Doe and Jane Doe'</comment>
  </data>
  <data name="liveWithSonDaughter" xml:space="preserve">
    <value>Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) live with a son, daughter, stepson, or stepdaughter?</value>
  </data>
  <data name="liveWithSpouse" xml:space="preserve">
    <value>Does&amp;nbsp;[NAME]&amp;nbsp;live with&amp;nbsp;[HIS/HER]&amp;nbsp;spouse?</value>
  </data>
  <data name="mayBeEligibleForMediChip" xml:space="preserve">
    <value>[NAME]&amp;nbsp;may be eligible for Medicaid or ALL Kids through the parent they live with.  That parent can also file an application.  To do so, the parent can create an account or fill out a paper application to mail in.  You can also continue with this application now to see if&amp;nbsp;[NAMES]&amp;nbsp;can get tax credit to pay for health coverage for&amp;nbsp;[NAME]&amp;nbsp;instead.</value>
    <comment>NAMES should be tax filers</comment>
  </data>
  <data name="mustProvideInfo" xml:space="preserve">
    <value>You must provide information about&amp;nbsp;[NAMES]'s, (DOB:&amp;nbsp;[DOB]) income and about who else is on the tax return to get a tax credit to help pay for health coverage for&amp;nbsp;[NAME].  However, you can continue with this application without telling us more about&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) to see if you can get covered by&amp;nbsp;[AMACHIP].</value>
    <comment>NAMES should be 'John Doe' if not married, or 'John Doe and Jane Doe' if married</comment>
  </data>
  <data name="nameOfDependent" xml:space="preserve">
    <value>Name of dependents</value>
  </data>
  <data name="nameOfParent" xml:space="preserve">
    <value>Name of parent or stepparent that claims&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB])</value>
  </data>
  <data name="nameOfSonDaughter" xml:space="preserve">
    <value>Select any son, daughter, stepson, or stepdaughter that lives with&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB])</value>
  </data>
  <data name="nameOfSpouse" xml:space="preserve">
    <value>Name of spouse</value>
  </data>
  <data name="planToFileTaxes" xml:space="preserve">
    <value>Does&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) plan to file a federal income tax return for&amp;nbsp;[COVERAGEYEAR]?</value>
  </data>
  <data name="planToFileTaxesWithSpouse" xml:space="preserve">
    <value>Does&amp;nbsp;[TAXFILER]&amp;nbsp;plan to file a joint federal income tax return with a spouse for&amp;nbsp;[COVERAGEYEAR]?</value>
  </data>
  <data name="selectParentThatLiveHere" xml:space="preserve">
    <value>Select&amp;nbsp;[NAME]'s, (DOB:&amp;nbsp;[DOB]) parents and stepparent(s) that live with&amp;nbsp;[HIM/HER]</value>
  </data>
  <data name="someoneElseNotApplying" xml:space="preserve">
    <value>Someone else who isn't applying for health insurance</value>
  </data>
  <data name="taxFilerWhoWillClaimPerson" xml:space="preserve">
    <value>Who is the tax filer that will claim&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB]) on their income tax return?</value>
  </data>
  <data name="wantToProvideInfo" xml:space="preserve">
    <value>Do you want to provide the claiming tax filer's information, so they can apply for a tax credit?</value>
  </data>
  <data name="whoIsBrotherSister" xml:space="preserve">
    <value>Who is a brother or sister living with&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB])?</value>
  </data>
  <data name="whoIsSpouse" xml:space="preserve">
    <value>Who is&amp;nbsp;[NAME]'s, (DOB:&amp;nbsp;[DOB]) spouse?</value>
  </data>
  <data name="whoWillPersonClaim" xml:space="preserve">
    <value>Who will&amp;nbsp;[TAXFILER]&amp;nbsp;claim on&amp;nbsp;[HIS/HER/THEIR]&amp;nbsp;tax return for&amp;nbsp;[COVERAGEYEARPLUS]?</value>
    <comment>NAMES should be 'John Doe' if not married, or 'John Doe and Jane Doe' if married          COVERAGEYEARPLUS = plus 1 to coverage year</comment>
  </data>
  <data name="personsSpouse" xml:space="preserve">
    <value>[NAME]'s, (DOB:&amp;nbsp;[DOB]) Spouse</value>
    <comment>using as legend for fieldset</comment>
  </data>
  <data name="dlgNonApplicantParentHeader" xml:space="preserve">
    <value>Coverage Eligibility Tip</value>
    <comment>dialog message header</comment>
  </data>
  <data name="header" xml:space="preserve">
    <value>Tax Filer Information</value>
  </data>
  <data name="noNeedFileTaxesforCoverage" xml:space="preserve">
    <value>You don't have to file taxes to apply for coverage.</value>
  </data>
  <data name="howIsPersonRelated" xml:space="preserve">
    <value>How is&amp;nbsp;[NAME]&amp;nbsp;related to&amp;nbsp;[TAXFILER]?</value>
  </data>
  <data name="isTaxFilerMarried" xml:space="preserve">
    <value>Is&amp;nbsp;[TAXFILER]&amp;nbsp;married?</value>
  </data>
  <data name="nameOfBroSis" xml:space="preserve">
    <value>Select any brother or sister that lives with&amp;nbsp;[NAME], (DOB:&amp;nbsp;[DOB])</value>
  </data>
  <data name="addSpouseDetails" xml:space="preserve">
    <value>ADD SPOUSE DETAILS</value>
  </data>
  <data name="addTaxDependent" xml:space="preserve">
    <value>Add another tax dependent</value>
  </data>
  <data name="spousePersonListSelectionRequired" xml:space="preserve">
    <value>Selection of someone with whom you are filing jointly is required.</value>
  </data>
  <data name="dependentListSelectionRequired" xml:space="preserve">
    <value>Selection of a dependent is required.</value>
  </data>
  <data name="taxFilerUpdateWarning" xml:space="preserve">
    <value>Once a change is made to this screen, all tax filer data will be removed and you must enter it again.  If updating this information is not needed, you may press the Cancel button now.</value>
  </data>
  <data name="dependentHelpLink" xml:space="preserve">
    <value>To find out more about dependents,&amp;nbsp;[LINK]</value>
  </data>
  <data name="dependentNote" xml:space="preserve">
    <value>A dependent is someone who gets most of his or her financial support from someone else. Children, other family members, or other people who live with the tax filer can be dependents.</value>
  </data>
  <data name="diffDependentNote" xml:space="preserve">
    <value>If you and/or your family want health coverage for next year, tell us about any expected differences between who you claim as dependents now, and why you’ll claim on the tax return for next Year.</value>
  </data>
</root>