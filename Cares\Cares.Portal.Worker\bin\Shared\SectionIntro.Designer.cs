﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Shared {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class SectionIntro {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SectionIntro() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Shared.SectionIntro", typeof(SectionIntro).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Before you get started....
        /// </summary>
        public static string beforeYouGetStarted {
            get {
                return ResourceManager.GetString("beforeYouGetStarted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In order to view this site properly, it is recommended that you use one of the following browsers:.
        /// </summary>
        public static string browsers {
            get {
                return ResourceManager.GetString("browsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mozilla Firefox 22 or greater.
        /// </summary>
        public static string browsersFireFox {
            get {
                return ResourceManager.GetString("browsersFireFox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Google Chrome 29 or greater.
        /// </summary>
        public static string browsersGoogle {
            get {
                return ResourceManager.GetString("browsersGoogle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internet Explorer 10 (non-compatibility view) or greater.
        /// </summary>
        public static string browsersIE {
            get {
                return ResourceManager.GetString("browsersIE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coming Up In This Section.
        /// </summary>
        public static string comingUp {
            get {
                return ResourceManager.GetString("comingUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In this section, you will be asked for identifying information about who is applying for health coverage and who lives in your household..
        /// </summary>
        public static string comingUpInHouseholdSection {
            get {
                return ResourceManager.GetString("comingUpInHouseholdSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In this section, you will be asked about your household income.  In some cases, we may already have some information about your household income from your prior tax filings, regular payroll reports made by an employer, or other sources.  We will show you that information and you will be able to update it or confirm it.  You may also need to enter new information to complete your income calculation..
        /// </summary>
        public static string comingUpInIncomeSection {
            get {
                return ResourceManager.GetString("comingUpInIncomeSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In this section, you will be asked for information concerning any current or future insurance coverage for those applying for health coverage..
        /// </summary>
        public static string comingUpInInsuranceSection {
            get {
                return ResourceManager.GetString("comingUpInInsuranceSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In this section, you will review your application for completeness and accuracy.  Once you finish your review, you can give your e-signature and file your application.  In most cases, you will receive a final eligibility determination within minutes..
        /// </summary>
        public static string comingUpInReviewSection {
            get {
                return ResourceManager.GetString("comingUpInReviewSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You will need to have cookies enabled for this web site to function properly..
        /// </summary>
        public static string enablingCookieHeaderInfo {
            get {
                return ResourceManager.GetString("enablingCookieHeaderInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn more about managing your cookies settings in &lt;a href=&quot;https://support.google.com/accounts/answer/61416?hl=en&quot;&gt;Google Chrome&lt;/a&gt;, &lt;a href=&quot;http://windows.microsoft.com/en-us/internet-explorer/delete-manage-cookies#ie=ie-10&quot;&gt; Internet Explorer&lt;/a&gt; and &lt;a href=&quot;http://support.mozilla.org/en-US/kb/enable-and-disable-cookies-website-preferences&quot;&gt;Mozilla Firefox&lt;/a&gt;. .
        /// </summary>
        public static string enablingCookieInfo {
            get {
                return ResourceManager.GetString("enablingCookieInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You will be asked a series of questions to determine your eligibility for Medicaid and ALL Kids..
        /// </summary>
        public static string enterInformationStatement {
            get {
                return ResourceManager.GetString("enterInformationStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Your Information.
        /// </summary>
        public static string enterYourInformation {
            get {
                return ResourceManager.GetString("enterYourInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimated time needed to complete this section.
        /// </summary>
        public static string estimatedTime {
            get {
                return ResourceManager.GetString("estimatedTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find Health Care Plans.
        /// </summary>
        public static string findHealthCarePlans {
            get {
                return ResourceManager.GetString("findHealthCarePlans", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once you are determined eligible, you can see what health plans are available, compare them, and enroll in the health plan you choose..
        /// </summary>
        public static string findPlansStatement {
            get {
                return ResourceManager.GetString("findPlansStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Build Your Household.
        /// </summary>
        public static string headerHousehold {
            get {
                return ResourceManager.GetString("headerHousehold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Information.
        /// </summary>
        public static string headerIncome {
            get {
                return ResourceManager.GetString("headerIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance Information.
        /// </summary>
        public static string headerInsurance {
            get {
                return ResourceManager.GetString("headerInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review and File.
        /// </summary>
        public static string headerReview {
            get {
                return ResourceManager.GetString("headerReview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Your Application.
        /// </summary>
        public static string headerStart {
            get {
                return ResourceManager.GetString("headerStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It will be helpful to have the following information with you to complete this application:.
        /// </summary>
        public static string infoToCompleteApp {
            get {
                return ResourceManager.GetString("infoToCompleteApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security numbers and birth dates of household members for whom you are applying.
        /// </summary>
        public static string infoToCompleteApp1 {
            get {
                return ResourceManager.GetString("infoToCompleteApp1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Citizenship and immigration documentation of household members for whom you are applying.
        /// </summary>
        public static string infoToCompleteApp2 {
            get {
                return ResourceManager.GetString("infoToCompleteApp2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Household income from jobs, self-employment, and any other income sources.
        /// </summary>
        public static string infoToCompleteApp3 {
            get {
                return ResourceManager.GetString("infoToCompleteApp3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information about any current health insurance coverage for all household members.
        /// </summary>
        public static string infoToCompleteApp4 {
            get {
                return ResourceManager.GetString("infoToCompleteApp4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You will need to have cookies, popups, and scripting enabled for the web site to function properly..
        /// </summary>
        public static string itemsForProperFunction {
            get {
                return ResourceManager.GetString("itemsForProperFunction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to After you enter your information and file your application, you will see your results.  In most cases, you will get a final eligibility determination within minutes..
        /// </summary>
        public static string seeResultsStatement {
            get {
                return ResourceManager.GetString("seeResultsStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to See Your Results.
        /// </summary>
        public static string seeYourResults {
            get {
                return ResourceManager.GetString("seeYourResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string start {
            get {
                return ResourceManager.GetString("start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This application should take 30 - 45 minutes to complete. Once finished, you should have a good idea if you and/or your children may be eligible for any of the programs..
        /// </summary>
        public static string timeToCompleteApp {
            get {
                return ResourceManager.GetString("timeToCompleteApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10 minutes.
        /// </summary>
        public static string timeToCompleteSection10 {
            get {
                return ResourceManager.GetString("timeToCompleteSection10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Alabamacares.Alabama.gov.
        /// </summary>
        public static string welcomeHeader {
            get {
                return ResourceManager.GetString("welcomeHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You May Need.
        /// </summary>
        public static string youMayNeed {
            get {
                return ResourceManager.GetString("youMayNeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All previously required documents.
        /// </summary>
        public static string youMayNeedAllPrevDocuments {
            get {
                return ResourceManager.GetString("youMayNeedAllPrevDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birth dates.
        /// </summary>
        public static string youMayNeedBirthDates {
            get {
                return ResourceManager.GetString("youMayNeedBirthDates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group numbers.
        /// </summary>
        public static string youMayNeedGroupNumber {
            get {
                return ResourceManager.GetString("youMayNeedGroupNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance carrier.
        /// </summary>
        public static string youMayNeedInsCarrier {
            get {
                return ResourceManager.GetString("youMayNeedInsCarrier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay stubs.
        /// </summary>
        public static string youMayNeedPayStubs {
            get {
                return ResourceManager.GetString("youMayNeedPayStubs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy numbers.
        /// </summary>
        public static string youMayNeedPolicyNumber {
            get {
                return ResourceManager.GetString("youMayNeedPolicyNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Most recent tax filing.
        /// </summary>
        public static string youMayNeedRecentTaxFiling {
            get {
                return ResourceManager.GetString("youMayNeedRecentTaxFiling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security numbers.
        /// </summary>
        public static string youMayNeedSocialSecurityNumbers {
            get {
                return ResourceManager.GetString("youMayNeedSocialSecurityNumbers", resourceCulture);
            }
        }
    }
}
