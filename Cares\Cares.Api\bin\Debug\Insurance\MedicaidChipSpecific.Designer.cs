﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Insurance {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class MedicaidChipSpecific {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MedicaidChipSpecific() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Insurance.MedicaidChipSpecific", typeof(MedicaidChipSpecific).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some people qualify to get help even if they already have health coverage. &lt;br/&gt; Does [NAME], (DOB: [DOB]) have health coverage now?.
        /// </summary>
        public static string alreadyHaveCoverage {
            get {
                return ResourceManager.GetString("alreadyHaveCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME], (DOB: [DOB]) eligible to receive a health services from the Indian Health Service, a tribal health program, or urban Indian health program or through a referral from one of these programs?.
        /// </summary>
        public static string eligibleForIndianHealthCoverage {
            get {
                return ResourceManager.GetString("eligibleForIndianHealthCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Number:.
        /// </summary>
        public static string groupNbr {
            get {
                return ResourceManager.GetString("groupNbr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the current Health Coverage.
        /// </summary>
        public static string healthCoverageName {
            get {
                return ResourceManager.GetString("healthCoverageName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Health coverage is required..
        /// </summary>
        public static string healthCoveragesSelectionRequired {
            get {
                return ResourceManager.GetString("healthCoveragesSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of health plan is required..
        /// </summary>
        public static string healthPlanNameRequired {
            get {
                return ResourceManager.GetString("healthPlanNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Health Plan Name is required..
        /// </summary>
        public static string hlthPlanNameRequired {
            get {
                return ResourceManager.GetString("hlthPlanNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medicaid &amp; ALL Kids Program Questions.
        /// </summary>
        public static string medicaidAndChipSpecificQuestions {
            get {
                return ResourceManager.GetString("medicaidAndChipSpecificQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s the name of [NAME]&apos;s, (DOB: [DOB]) health plan?.
        /// </summary>
        public static string nameOfCoverage {
            get {
                return ResourceManager.GetString("nameOfCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Number/Member ID is required..
        /// </summary>
        public static string policyNumberMemberIDRequired {
            get {
                return ResourceManager.GetString("policyNumberMemberIDRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What&apos;s the policy number/memberID of the health plan?.
        /// </summary>
        public static string policyNumberOfCoverage {
            get {
                return ResourceManager.GetString("policyNumberOfCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy number/Member ID is required..
        /// </summary>
        public static string policyNumberRequired {
            get {
                return ResourceManager.GetString("policyNumberRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of Health Insurance:.
        /// </summary>
        public static string providerName {
            get {
                return ResourceManager.GetString("providerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Has [NAME], (DOB: [DOB]) ever received a health service from the Indian Health Service, a tribal health program, or urban Indian health program or through a referral from one of these programs?.
        /// </summary>
        public static string receivedIndianHealthCoverage {
            get {
                return ResourceManager.GetString("receivedIndianHealthCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What health coverage does [NAME], (DOB: [DOB]) have now?.
        /// </summary>
        public static string typeOfCoverage {
            get {
                return ResourceManager.GetString("typeOfCoverage", resourceCulture);
            }
        }
    }
}
