﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Main {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Privacy {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Privacy() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Main.Privacy", typeof(Privacy).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Privacy Policy.
        /// </summary>
        public static string headerPrivacyPolicy {
            get {
                return ResourceManager.GetString("headerPrivacyPolicy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Before you continue, please read our Privacy Policy..
        /// </summary>
        public static string headerPrivacyStatement {
            get {
                return ResourceManager.GetString("headerPrivacyStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This notice tells you about your privacy rights and what the Exchange may do with your personally identifiable information by law.  Please review it carefully.  Please note that this Privacy Policy is subject to change without notice and that it reflects the State&apos;s current business practices.  This Privacy Policy is dated October 1, 2013..
        /// </summary>
        public static string privacyPolicy1 {
            get {
                return ResourceManager.GetString("privacyPolicy1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We&apos;ll keep your information private as required by law.  Your answers on this form will only be used to determine eligibility for health coverage or help paying for coverage.  We&apos;ll check your answers using the information in our electronic databases and the databases of other federal agencies.  If the information doesn&apos;t match, we may ask you to send us proof..
        /// </summary>
        public static string privacyStatement1 {
            get {
                return ResourceManager.GetString("privacyStatement1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We won&apos;t ask any questions about your medical history.  Household members who don&apos;t want coverage won&apos;t be asked questions about citizenship or immigration status..
        /// </summary>
        public static string privacyStatement2 {
            get {
                return ResourceManager.GetString("privacyStatement2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As part of the application process, we may need to retrieve your information from the Internal Revenue Service(IRS), Social Security Administration(SSA), the Department of Homeland Security(DHS), and/or a consumer reporting agency.  We need this information to check your eligibility for coverage and help paying for coverage if you want it and to give you the best service possible.  We may also check your information at a later time to make sure your information is up to date.  We&apos;ll notify you if we find so [rest of string was truncated]&quot;;.
        /// </summary>
        public static string privacyStatement3 {
            get {
                return ResourceManager.GetString("privacyStatement3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I agree to have my information used and retrieved from data sources for this application.  I have consent for all people I&apos;ll list on the application for their information to be retrieved and used from data sources..
        /// </summary>
        public static string readAndUnderstand {
            get {
                return ResourceManager.GetString("readAndUnderstand", resourceCulture);
            }
        }
    }
}
