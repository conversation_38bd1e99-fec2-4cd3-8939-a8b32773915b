﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Worker.Resources.Application {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Income {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Income() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Worker.Resources.Application.Income", typeof(Income).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add another job.
        /// </summary>
        public static string addAnotherJob {
            get {
                return ResourceManager.GetString("addAnotherJob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add another Social Security benefit amount.
        /// </summary>
        public static string addAnotherSocialSecurity {
            get {
                return ResourceManager.GetString("addAnotherSocialSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add another unemployment source.
        /// </summary>
        public static string addAnotherUnemployment {
            get {
                return ResourceManager.GetString("addAnotherUnemployment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] receive from alimony?.
        /// </summary>
        public static string alimonyQuestion {
            get {
                return ResourceManager.GetString("alimonyQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        public static string amount {
            get {
                return ResourceManager.GetString("amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter amount you expect to change for selected income type..
        /// </summary>
        public static string amountIncomeTypeChangeRequired {
            get {
                return ResourceManager.GetString("amountIncomeTypeChangeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capital Gains - Current Month.
        /// </summary>
        public static string capitalGainsCurrentMonth {
            get {
                return ResourceManager.GetString("capitalGainsCurrentMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capital Gains - Current Year.
        /// </summary>
        public static string capitalGainsCurrentYear {
            get {
                return ResourceManager.GetString("capitalGainsCurrentYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] expect to receive from net capital gains (the profit after subtracting capital losses) this month?.
        /// </summary>
        public static string capitalGainsMonthQuestion {
            get {
                return ResourceManager.GetString("capitalGainsMonthQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] expect to receive from net capital gains (the profit after subtracting capital losses) this year?.
        /// </summary>
        public static string capitalGainsYearQuestion {
            get {
                return ResourceManager.GetString("capitalGainsYearQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days per week.
        /// </summary>
        public static string daysWeek {
            get {
                return ResourceManager.GetString("daysWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter deduction amount..
        /// </summary>
        public static string deductionAmountRequired {
            get {
                return ResourceManager.GetString("deductionAmountRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deduction payment frequency is required.
        /// </summary>
        public static string deductionFrequencyRequired {
            get {
                return ResourceManager.GetString("deductionFrequencyRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deduction type is required.
        /// </summary>
        public static string deductionTypeRequired {
            get {
                return ResourceManager.GetString("deductionTypeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer name is required.
        /// </summary>
        public static string employerNameRequired {
            get {
                return ResourceManager.GetString("employerNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telephone number must contain 10 digits..
        /// </summary>
        public static string employerPhone {
            get {
                return ResourceManager.GetString("employerPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiry Date.
        /// </summary>
        public static string expiryDate {
            get {
                return ResourceManager.GetString("expiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] receive from net farming or fishing income (the profit after subtracting costs)?.
        /// </summary>
        public static string farmingFishingQuestion {
            get {
                return ResourceManager.GetString("farmingFishingQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much will this income be in [COVERAGEYEAR]?.
        /// </summary>
        public static string futureIncomeAmount {
            get {
                return ResourceManager.GetString("futureIncomeAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Future income type amount is required.
        /// </summary>
        public static string futureIncomeAmountRequired {
            get {
                return ResourceManager.GetString("futureIncomeAmountRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You&apos;ve told us that [NAME]&apos;s current monthly income doesn&apos;t include any of the following.  Is there another type of income that may start in a future month?.
        /// </summary>
        public static string futureIncomeType {
            get {
                return ResourceManager.GetString("futureIncomeType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Future income type is required.
        /// </summary>
        public static string futureIncomeTypeSelectionRequired {
            get {
                return ResourceManager.GetString("futureIncomeTypeSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME] have any type of income?.
        /// </summary>
        public static string haveAnyIncome {
            get {
                return ResourceManager.GetString("haveAnyIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours per week.
        /// </summary>
        public static string hoursWeek {
            get {
                return ResourceManager.GetString("hoursWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] get paid (before taxes or anything is taken out)?  Tell us about the regular pay from this job that [NAME] receives as well as any one-time amounts this month, like a bonus or a severage payment..
        /// </summary>
        public static string howMuchDoYouGetPaid {
            get {
                return ResourceManager.GetString("howMuchDoYouGetPaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How many days does [NAME] usually work per week at this job?.
        /// </summary>
        public static string howMuchWorkPerWeekAtJobD {
            get {
                return ResourceManager.GetString("howMuchWorkPerWeekAtJobD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How many hours does [NAME] usually work per week at this job?.
        /// </summary>
        public static string howMuchWorkPerWeekAtJobH {
            get {
                return ResourceManager.GetString("howMuchWorkPerWeekAtJobH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How often does [NAME] pay this amount?.
        /// </summary>
        public static string howOftenDoYouPayThisAmt {
            get {
                return ResourceManager.GetString("howOftenDoYouPayThisAmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How often does [NAME] receive this amount?.
        /// </summary>
        public static string howOftenDoYouReceiveThisAmt {
            get {
                return ResourceManager.GetString("howOftenDoYouReceiveThisAmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount is required.
        /// </summary>
        public static string incomeAmountRequired {
            get {
                return ResourceManager.GetString("incomeAmountRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount received frequency is required.
        /// </summary>
        public static string incomeFrequencyRequired {
            get {
                return ResourceManager.GetString("incomeFrequencyRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Information.
        /// </summary>
        public static string incomeInfoHeader {
            get {
                return ResourceManager.GetString("incomeInfoHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income source is required.
        /// </summary>
        public static string incomeSourceRequired {
            get {
                return ResourceManager.GetString("incomeSourceRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME]&apos;s Income Summary.
        /// </summary>
        public static string incomeSummaryHeader {
            get {
                return ResourceManager.GetString("incomeSummaryHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How Often.
        /// </summary>
        public static string incomeSummaryHowOften {
            get {
                return ResourceManager.GetString("incomeSummaryHowOften", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monthly Amount.
        /// </summary>
        public static string incomeSummaryMonthlyAmt {
            get {
                return ResourceManager.GetString("incomeSummaryMonthlyAmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Source.
        /// </summary>
        public static string incomeSummarySource {
            get {
                return ResourceManager.GetString("incomeSummarySource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yearly Amount.
        /// </summary>
        public static string incomeSummaryYearlyAmt {
            get {
                return ResourceManager.GetString("incomeSummaryYearlyAmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income type expected to change is required.
        /// </summary>
        public static string IncomeTypeExpectedToChangeRequired {
            get {
                return ResourceManager.GetString("IncomeTypeExpectedToChangeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income type of selected sources is required.
        /// </summary>
        public static string incomeTypeOfSelectedSourceRequired {
            get {
                return ResourceManager.GetString("incomeTypeOfSelectedSourceRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please include income from these sources: Job, Self-employment, Social Security benefits, Unemployment, Retirement, Pension, Capital gains, Investment income, Rental or Royalty income, Farming or Fishing income, Alimony received and Other income..
        /// </summary>
        public static string incomeTypes {
            get {
                return ResourceManager.GetString("incomeTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Type Selection.
        /// </summary>
        public static string incomeTypeSelection {
            get {
                return ResourceManager.GetString("incomeTypeSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selection of a type of income is required.
        /// </summary>
        public static string incomeTypeSelectionRequired {
            get {
                return ResourceManager.GetString("incomeTypeSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of hours worked is required.
        /// </summary>
        public static string incomeWorkHoursRequired {
            get {
                return ResourceManager.GetString("incomeWorkHoursRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is any of the above income from these sources?.
        /// </summary>
        public static string indianAlaskanQuestion {
            get {
                return ResourceManager.GetString("indianAlaskanQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a value between 1 and 7..
        /// </summary>
        public static string invalidWeeklyLoadforDaily {
            get {
                return ResourceManager.GetString("invalidWeeklyLoadforDaily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a value between 1 and 168..
        /// </summary>
        public static string invalidWeeklyLoadforWeekly {
            get {
                return ResourceManager.GetString("invalidWeeklyLoadforWeekly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] receive from investment income, like interest and dividends?.
        /// </summary>
        public static string investmentQuestion {
            get {
                return ResourceManager.GetString("investmentQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In [COVERAGEYEAR] it will be about this much.
        /// </summary>
        public static string itWillBeAboutThisMuch {
            get {
                return ResourceManager.GetString("itWillBeAboutThisMuch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name of employer.
        /// </summary>
        public static string nameOfEmployer {
            get {
                return ResourceManager.GetString("nameOfEmployer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Amount must be greater than 0..
        /// </summary>
        public static string noZeroIncomeAllowed {
            get {
                return ResourceManager.GetString("noZeroIncomeAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You don’t need to tell us about child support, veteran’s payments, or Supplemental Security Income (SSI). Click here to learn more about what not to list..
        /// </summary>
        public static string otherIncomeDetail {
            get {
                return ResourceManager.GetString("otherIncomeDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A selection from Other Income is required.
        /// </summary>
        public static string otherIncomeTypeRequired {
            get {
                return ResourceManager.GetString("otherIncomeTypeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other deduction [NAME] could take on [HIS/HER] [COVERAGEYEAR] tax return.
        /// </summary>
        public static string otherTaxDeduction {
            get {
                return ResourceManager.GetString("otherTaxDeduction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] receive from this pension account?.
        /// </summary>
        public static string pensionQuestion {
            get {
                return ResourceManager.GetString("pensionQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select either &apos;Profit&apos; or &apos;Loss&apos;.
        /// </summary>
        public static string profitLossRequired {
            get {
                return ResourceManager.GetString("profitLossRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] receive from net rental or royalty income (the profit after subtracting costs)?.
        /// </summary>
        public static string rentalQuestion {
            get {
                return ResourceManager.GetString("rentalQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] receive from retirement account(s)?  Include amounts received as a distribution from a retirement investment even if [NAME] isn&apos;t retired..
        /// </summary>
        public static string retirementQuestion {
            get {
                return ResourceManager.GetString("retirementQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is any of this income from a scholarship or grant used to pay for education expenses?.
        /// </summary>
        public static string schoolGrantQuestion {
            get {
                return ResourceManager.GetString("schoolGrantQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select any other type of income that [NAME] receives.
        /// </summary>
        public static string selectOtherIncomeTypes {
            get {
                return ResourceManager.GetString("selectOtherIncomeTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much net income (profits once business expenses are paid) will [NAME] receive from this self-employment this month? (If the costs for this self-employment are more than the amount [NAME] expects to earn, you may enter a negative number.).
        /// </summary>
        public static string selfEmploymentQuestion {
            get {
                return ResourceManager.GetString("selfEmploymentQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much does [NAME] receive from Social Security?  Do not include Supplemental Security Income(SSI)..
        /// </summary>
        public static string socialSecurityQuestion {
            get {
                return ResourceManager.GetString("socialSecurityQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does [NAME] pay alimony, student loan interest, or any other deductions that get reported on the front page of a federal income tax return form 1040?  This could make the cost of coverage a little lower..
        /// </summary>
        public static string taxDeductionsQuestion {
            get {
                return ResourceManager.GetString("taxDeductionsQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type of work.
        /// </summary>
        public static string typeOfWork {
            get {
                return ResourceManager.GetString("typeOfWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type of work is required.
        /// </summary>
        public static string typeOfWorkRequired {
            get {
                return ResourceManager.GetString("typeOfWorkRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is there is a date that the unemployment benefits are set to expire?.
        /// </summary>
        public static string unemploymentExpireQuestion {
            get {
                return ResourceManager.GetString("unemploymentExpireQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From what state government or former employer does [NAME] receive unemployment benefits?.
        /// </summary>
        public static string unemploymentQuestion {
            get {
                return ResourceManager.GetString("unemploymentQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source.
        /// </summary>
        public static string unemploymentSource {
            get {
                return ResourceManager.GetString("unemploymentSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unemployment Income Source is required.
        /// </summary>
        public static string unemploymentSourceRequired {
            get {
                return ResourceManager.GetString("unemploymentSourceRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a date at which the unemployment benefits are going to expire..
        /// </summary>
        public static string unEmplyExpDate {
            get {
                return ResourceManager.GetString("unEmplyExpDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Which income type do you expect to begin in a future month?.
        /// </summary>
        public static string whichFutureIncomeType {
            get {
                return ResourceManager.GetString("whichFutureIncomeType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Which income type is from this source?.
        /// </summary>
        public static string whichIncomeFromSource {
            get {
                return ResourceManager.GetString("whichIncomeFromSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Which income type do you expect to change?.
        /// </summary>
        public static string whichIncomeTypeExpectedToChange {
            get {
                return ResourceManager.GetString("whichIncomeTypeExpectedToChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Which income type do you expect will change?.
        /// </summary>
        public static string whichIncomeWillChange {
            get {
                return ResourceManager.GetString("whichIncomeWillChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work Load.
        /// </summary>
        public static string workLoad {
            get {
                return ResourceManager.GetString("workLoad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Based on what you told us, if [NAME]&apos;s income is steady each month, then it&apos;s about [AMOUNT] per year.  Is this how much you think [NAME] will receive in [COVERAGEYEAR]?.
        /// </summary>
        public static string yearlyAmountCalculated {
            get {
                return ResourceManager.GetString("yearlyAmountCalculated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Based on what you know today, how much do you think [NAME] will receive in [COVERAGEYEAR]?.
        /// </summary>
        public static string yearlyAmountManual {
            get {
                return ResourceManager.GetString("yearlyAmountManual", resourceCulture);
            }
        }
    }
}
