<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Novalys.VisualGuard.Security.VGIdentityServerClient</name>
    </assembly>
    <members>
        <member name="T:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.AccessTokenResponseJson">
            <summary>
            
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.AccessTokenResponseJson.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData">
            <summary>
            Represents VGIdentityServerClientRepositoryData
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.#ctor">
            <summary>
            VGIdentityServerClientRepositoryData
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.#ctor(System.String)">
            <summary>
            VGIdentityServerClientRepositoryData
            </summary>
            <param name="url">Url of the VGIdentityServer to connect</param>
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.#ctor(System.String,System.String,Novalys.VisualGuard.Security.VGAuthenticationMode,System.Boolean,System.Guid)">
            <summary>
            VGIdentityServerClientRepositoryData
            </summary>
            <param name="url">Url of the VGIdentityServer to connect</param>
            <param name="name">name of repository</param>
            <param name="supportedAuthenticationModes">supportedAuthenticationModes</param>
            <param name="anonymousSessionSupported">anonymousSessionSupported</param>
            <param name="applicationId">applicationId</param>
        </member>
        <member name="P:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.DisplayInformation">
            <summary>
            Gets DisplayInformation
            </summary>
        </member>
        <member name="P:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.RepositoryConnectionTypeName">
            <summary>
            RepositoryConnectionTypeName
            </summary>
            
        </member>
        <member name="P:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.TypeName">
            <summary>
            Gets type name
            </summary>
            
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.Clone">
            <summary>
            Clones the object
            </summary>
            <returns></returns>
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.Encrypt">
            <summary>
            
            </summary>
        </member>
        <member name="M:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.GetCodeCore">
            <summary>
            GetCodeCore
            </summary>
            <returns></returns>
        </member>
        <member name="P:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.Configuration.VGIdentityServerClientRepositoryData.Url">
            <summary>
            The connection string used to connect to the database containing the Visual Guard repository via VGIdentityServer
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.VGApiOperationType">
            <summary>
            This represents an enum of web api operation types.
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.VGApiOperationType.GET">
            <summary>
            GET
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.VGApiOperationType.PUT">
            <summary>
            PUT
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.VGApiOperationType.POST">
            <summary>
            POST
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Security.VGIdentityServerClient.Repository.VGApiOperationType.DELETE">
            <summary>
            DELETE
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Security.VGIdentityServerClient.VGApiParameterBindType">
            <summary>
            Represents ArgumentBindType for web api call
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Security.VGIdentityServerClient.VGApiParameterBindType.RequestBody">
            <summary>
            RequestBody
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Security.VGIdentityServerClient.VGApiParameterBindType.RequestUri">
            <summary>
            RequestUri
            </summary>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivateReflection">
            <summary>Holder for reflection information generated from Api/Controllers/Grpc/VGPrivate.proto</summary>
        </member>
        <member name="P:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivateReflection.Descriptor">
            <summary>File descriptor for Api/Controllers/Grpc/VGPrivate.proto</summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivateServiceRequest.BytesFieldNumber">
            <summary>Field number for the "bytes" field.</summary>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivateServiceResponse">
            <summary>
            The response message containing the greetings.
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivateServiceResponse.BytesFieldNumber">
            <summary>Field number for the "bytes" field.</summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestRequest.Num1FieldNumber">
            <summary>Field number for the "num1" field.</summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestRequest.Num2FieldNumber">
            <summary>Field number for the "num2" field.</summary>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestResponse">
            <summary>
            The response message containing the greetings.
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestResponse.AnsFieldNumber">
            <summary>Field number for the "ans" field.</summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesRequest.DataFieldNumber">
            <summary>Field number for the "data" field.</summary>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesResponse">
            <summary>
            The response message containing the greetings.
            </summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesResponse.DataFieldNumber">
            <summary>Field number for the "data" field.</summary>
        </member>
        <member name="F:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesResponse.ResultFieldNumber">
            <summary>Field number for the "result" field.</summary>
        </member>
        <member name="P:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.Descriptor">
            <summary>Service descriptor</summary>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestBase">
            <summary>Base class for server-side implementations of Test</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestBase.Add(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestRequest,Grpc.Core.ServerCallContext)">
            <summary>
            Add operation
            </summary>
            <param name="request">The request received from the client.</param>
            <param name="context">The context of the server-side call handler being invoked.</param>
            <returns>The response to send back to the client (wrapped by a task).</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestBase.ProcessBytes(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesRequest,Grpc.Core.ServerCallContext)">
            <summary>
            Process bytes
            </summary>
            <param name="request">The request received from the client.</param>
            <param name="context">The context of the server-side call handler being invoked.</param>
            <returns>The response to send back to the client (wrapped by a task).</returns>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient">
            <summary>Client for Test</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.#ctor(Grpc.Core.ChannelBase)">
            <summary>Creates a new client for Test</summary>
            <param name="channel">The channel to use to make remote calls.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.#ctor(Grpc.Core.CallInvoker)">
            <summary>Creates a new client for Test that uses a custom <c>CallInvoker</c>.</summary>
            <param name="callInvoker">The callInvoker to use to make remote calls.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.#ctor">
            <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.#ctor(Grpc.Core.ClientBase.ClientBaseConfiguration)">
            <summary>Protected constructor to allow creation of configured clients.</summary>
            <param name="configuration">The client configuration.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.Add(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Add operation
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
            <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
            <param name="cancellationToken">An optional token for canceling the call.</param>
            <returns>The response received from the server.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.Add(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestRequest,Grpc.Core.CallOptions)">
            <summary>
            Add operation
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="options">The options for the call.</param>
            <returns>The response received from the server.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.AddAsync(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Add operation
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
            <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
            <param name="cancellationToken">An optional token for canceling the call.</param>
            <returns>The call object.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.AddAsync(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.TestRequest,Grpc.Core.CallOptions)">
            <summary>
            Add operation
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="options">The options for the call.</param>
            <returns>The call object.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.ProcessBytes(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Process bytes
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
            <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
            <param name="cancellationToken">An optional token for canceling the call.</param>
            <returns>The response received from the server.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.ProcessBytes(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesRequest,Grpc.Core.CallOptions)">
            <summary>
            Process bytes
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="options">The options for the call.</param>
            <returns>The response received from the server.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.ProcessBytesAsync(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesRequest,Grpc.Core.Metadata,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Process bytes
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
            <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
            <param name="cancellationToken">An optional token for canceling the call.</param>
            <returns>The call object.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.ProcessBytesAsync(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.BytesRequest,Grpc.Core.CallOptions)">
            <summary>
            Process bytes
            </summary>
            <param name="request">The request to send to the server.</param>
            <param name="options">The options for the call.</param>
            <returns>The call object.</returns>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestClient.NewInstance(Grpc.Core.ClientBase.ClientBaseConfiguration)">
            <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.BindService(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestBase)">
            <summary>Creates service definition that can be registered with a server</summary>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.BindService(Grpc.Core.ServiceBinderBase,Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.Test.TestBase)">
            <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
            Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
            <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
        <member name="P:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.Descriptor">
            <summary>Service descriptor</summary>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateBase">
            <summary>Base class for server-side implementations of VGPrivate</summary>
        </member>
        <member name="T:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateClient">
            <summary>Client for VGPrivate</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateClient.#ctor(Grpc.Core.ChannelBase)">
            <summary>Creates a new client for VGPrivate</summary>
            <param name="channel">The channel to use to make remote calls.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateClient.#ctor(Grpc.Core.CallInvoker)">
            <summary>Creates a new client for VGPrivate that uses a custom <c>CallInvoker</c>.</summary>
            <param name="callInvoker">The callInvoker to use to make remote calls.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateClient.#ctor">
            <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateClient.#ctor(Grpc.Core.ClientBase.ClientBaseConfiguration)">
            <summary>Protected constructor to allow creation of configured clients.</summary>
            <param name="configuration">The client configuration.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateClient.NewInstance(Grpc.Core.ClientBase.ClientBaseConfiguration)">
            <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.BindService(Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateBase)">
            <summary>Creates service definition that can be registered with a server</summary>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
        <member name="M:Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.BindService(Grpc.Core.ServiceBinderBase,Novalys.VisualGuard.Tools.VGIdentityServer.Api.Controllers.Grpc.VGPrivate.VGPrivateBase)">
            <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
            Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
            <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
    </members>
</doc>
