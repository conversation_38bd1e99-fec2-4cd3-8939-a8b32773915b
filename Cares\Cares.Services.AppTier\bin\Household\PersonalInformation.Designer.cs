﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cares.Portal.Citizen.Resources.Household {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class PersonalInformation {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal PersonalInformation() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Cares.Portal.Citizen.Resources.Household.PersonalInformation", typeof(PersonalInformation).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add another child.
        /// </summary>
        public static string addAnotherChild {
            get {
                return ResourceManager.GetString("addAnotherChild", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We need a Social Security Number(SSN) if you want health coverage and have an SSN or can get one.  We use SSNs to check income and other information to see who is eligible for help paying for health coverage.  If [NAME] needs help getting an SSN, visit socialsecurity.gov, or call 1-************, TTY users should call 1-************..
        /// </summary>
        public static string applicantSSNStatement {
            get {
                return ResourceManager.GetString("applicantSSNStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applying for Health coverage?.
        /// </summary>
        public static string applyingForHealthCoverage {
            get {
                return ResourceManager.GetString("applyingForHealthCoverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not allowed to make changes on this page. If changes are needed then remove the household member and add him again with updated information. This can be completed from Household Summary page which will be displayed after you have reviewed the information for each household member..
        /// </summary>
        public static string DrasticChange {
            get {
                return ResourceManager.GetString("DrasticChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the exact name as shown on [NAME]&apos;s Social Security card.
        /// </summary>
        public static string enterSSCardName {
            get {
                return ResourceManager.GetString("enterSSCardName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Information.
        /// </summary>
        public static string header {
            get {
                return ResourceManager.GetString("header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Household member [HHNBR] .
        /// </summary>
        public static string householdMemberNumberHeader {
            get {
                return ResourceManager.GetString("householdMemberNumberHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How is [NAME] related to [FILER]?.
        /// </summary>
        public static string howIsPersonRelated {
            get {
                return ResourceManager.GetString("howIsPersonRelated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There must be at least one household member applying to continue.
        /// </summary>
        public static string minNbrOfHouseHoldRequired {
            get {
                return ResourceManager.GetString("minNbrOfHouseHoldRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please include yourself in the number of household along with other applicants..
        /// </summary>
        public static string moreThanOneHousehold {
            get {
                return ResourceManager.GetString("moreThanOneHousehold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is [NAME] the same name that appears on [HIS/HER] Social Security card?.
        /// </summary>
        public static string nameSameOnSSCard {
            get {
                return ResourceManager.GetString("nameSameOnSSCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate social security numbers are not allowed in an application. Please correct it..
        /// </summary>
        public static string noDuplicateSSN {
            get {
                return ResourceManager.GetString("noDuplicateSSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Providing your Social Security Number(SSN) can be helpful even if you don&apos;t want health coverage because it speeds up the application process.  We use SSNs to check income and other information to see who is eligible for help paying for health coverage.  If [NAME] needs help getting an SSN, visit socialsecurity.gov, or call 1-************, TTY users should call 1-************..
        /// </summary>
        public static string nonApplicantSSNStatement {
            get {
                return ResourceManager.GetString("nonApplicantSSNStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How many people in your family and household want health coverage? Include yourself even if you are not applying..
        /// </summary>
        public static string numberOfHHMembers {
            get {
                return ResourceManager.GetString("numberOfHHMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You will be allowed to make changes to your household composition before submission, if required..
        /// </summary>
        public static string onlineRenewalInfo {
            get {
                return ResourceManager.GetString("onlineRenewalInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Relationship.
        /// </summary>
        public static string otherRelationship {
            get {
                return ResourceManager.GetString("otherRelationship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME] is the [RELATION] .
        /// </summary>
        public static string part1IsRelationshipOf {
            get {
                return ResourceManager.GetString("part1IsRelationshipOf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  of [NAME].
        /// </summary>
        public static string part2IsRelationshipOf {
            get {
                return ResourceManager.GetString("part2IsRelationshipOf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [NAME] is the [RELATION] of [FILER]..
        /// </summary>
        public static string personIsTheRelationOf {
            get {
                return ResourceManager.GetString("personIsTheRelationOf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select relationship.
        /// </summary>
        public static string selectRelationship {
            get {
                return ResourceManager.GetString("selectRelationship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to if you are not applying for Health coverage, you can enter *********** as SSN or leave it empty..
        /// </summary>
        public static string ssnNote {
            get {
                return ResourceManager.GetString("ssnNote", resourceCulture);
            }
        }
    }
}
