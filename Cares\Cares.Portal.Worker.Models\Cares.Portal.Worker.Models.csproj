﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{94792CD6-A08C-4E5E-85A0-171CB5ACB89F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Cares.Portal.Worker.Models</RootNamespace>
    <AssemblyName>Cares.Portal.Worker.Models</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Dev|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Dev\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Test\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging|AnyCPU'">
    <OutputPath>bin\Staging\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Cares.Api.Messages, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Cares.Api.Messages\bin\Debug\Cares.Api.Messages.dll</HintPath>
    </Reference>
    <Reference Include="Cares.Data">
      <HintPath>..\Cares.Data\bin\Debug\Cares.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="netstandard" />
    <Reference Include="FluentValidation, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.8.3.0\lib\net45\FluentValidation.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation.Mvc, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.Mvc5.8.3.0\lib\net45\FluentValidation.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation.ValidatorAttribute, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.ValidatorAttribute.8.3.0\lib\net45\FluentValidation.ValidatorAttribute.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Mvc.ValidationToolkit">
      <HintPath>..\packages\MVC.ValidationToolkit\Mvc.ValidationToolkit.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.6.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.ComponentModel.Primitives, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Primitives.4.3.0\lib\net45\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\netstandard1.0\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Models\Accounting\AccountingContactPerson.cs" />
    <Compile Include="Models\Accounting\AccountingLetterHistory.cs" />
    <Compile Include="Models\Accounting\AccountingPayment.cs" />
    <Compile Include="Models\Accounting\AccountingPaymentsSearchResult.cs" />
    <Compile Include="Models\Accounting\AccountingPaymentsSearchResultsList.cs" />
    <Compile Include="Models\Accounting\AlternatePayer.cs" />
    <Compile Include="Models\Accounting\AlternatePayerAddress.cs" />
    <Compile Include="Models\Accounting\InvoiceLetterInfo.cs" />
    <Compile Include="Models\Accounting\PaymentHistoryLetterInfo.cs" />
    <Compile Include="Models\Application\ApplicationSnapshotHtmlViewModel.cs" />
    <Compile Include="Models\Application\Hub\MedicareHubResponse.cs" />
    <Compile Include="Models\Application\Income\HubVerifiedEquifaxIncome.cs" />
    <Compile Include="Models\Application\Income\HubVerifiedSsaIncome.cs" />
    <Compile Include="Models\Application\Income\IncomeListItem.cs" />
    <Compile Include="Models\Application\Income\NonMagiIncomeDetails.cs" />
    <Compile Include="Models\Application\Insurance\InsuranceDetails.cs" />
    <Compile Include="Models\Application\PersonalInfo\DHRApplicationModel.cs" />
    <Compile Include="Models\Application\PersonalInfo\DHREligibilityDetail.cs" />
    <Compile Include="Models\Application\PersonalInfo\DHRPersonDetail.cs" />
    <Compile Include="Models\Application\PersonalInfo\DhrSaveResponse.cs" />
    <Compile Include="Models\Application\PersonalInfo\DHRWorkerInformation.cs" />
    <Compile Include="Models\Application\PersonalInfo\EquifaxHubRequest.cs" />
    <Compile Include="Models\Application\PersonalInfo\FamilyDetail.cs" />
    <Compile Include="Models\Application\PersonalInfo\HubResponseDetail.cs" />
    <Compile Include="Models\Application\PersonalInfo\InsuranceInformation.cs" />
    <Compile Include="Models\Application\PersonalInfo\MedicareRequest.cs" />
    <Compile Include="Models\Application\PersonalInfo\MSPIntakeApplicationDetails.cs" />
    <Compile Include="Models\Application\PersonalInfo\MSPIntakeModel.cs" />
    <Compile Include="Models\Application\PersonalInfo\ResidencyInformation.cs" />
    <Compile Include="Models\Application\PersonalInfo\SSARequest.cs" />
    <Compile Include="Models\Application\PersonalInfo\VeteranDetails.cs" />
    <Compile Include="Models\Cola\ColaFactSheetCategoryViewModel.cs" />
    <Compile Include="Models\Cola\ColaFactSheetReadonlyViewModel.cs" />
    <Compile Include="Models\Cola\ColaFactSheetYearValueViewModel.cs" />
    <Compile Include="Models\Cola\ColaFactSheetSubCategoryViewModel.cs" />
    <Compile Include="Models\DataExchange\AMAES\ViewModel\AMAES.cs" />
    <Compile Include="Models\DataExchange\AMAES\ViewModel\AMAESCoreInfo.cs" />
    <Compile Include="Models\DataExchange\BENDEX\BENDEXRequest.cs" />
    <Compile Include="Models\DataExchange\BENDEX\ViewModel\BENDEXResponseVM.cs" />
    <Compile Include="Models\DataExchange\BENDEX\ViewModel\BENDEXVM.cs" />
    <Compile Include="Models\DataExchange\ClaimNumberDetail.cs" />
    <Compile Include="Models\DataExchange\DataExchange.cs" />
    <Compile Include="Models\DataExchange\DataExchangePerson.cs" />
    <Compile Include="Models\DataExchange\SSI\ViewModel\SDX.cs" />
    <Compile Include="Models\DataExchange\SSI\ViewModel\SDXInfo.cs" />
    <Compile Include="Models\DataExchange\SVES\SVESRequest.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESCrossReferenceAccountNumberVM.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESMBCHistoryVM.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESResponseVM.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESStandardResponseVM.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESTitleIIResponseVM.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESTitleXVIResponseVM.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESUnearnedIncomeVM.cs" />
    <Compile Include="Models\DataExchange\SVES\ViewModel\SVESVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQCoPaymentInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQCrossReferenceInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQEntitlementInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQESRDClinicalDialysisInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQGroupHealthPlanInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQMBIInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQPartBEntitlementInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQPartBThirdPartyInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQResponseVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQThirdPartyInfoVM.cs" />
    <Compile Include="Models\DataExchange\TBQ\ViewModel\TBQVM.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledLiabilityTestViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPrepaidBurialSpaceDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledAdditionalBurialFundsBudgetViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledLifeInsuranceAdditionalDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledMedicalInsuranceMonthDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledMedicalInsurancePartDBudgetViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledOtherBurialFundsDetailsViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyAutoDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyCollectibleMonthDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyMachineMonthDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyAutoDetailViewModelValidator.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPropertyMobileHomeDetailViewModel.cs" />
    <Compile Include="Models\DysFacility\ViewModel\DysFacilitiesViewModel.cs" />
    <Compile Include="Models\DysFacility\ViewModel\DysFacilityViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPropertyParcelDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledFamilyAllocationViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPropertyPreviousDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledResourceTransferMonthDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledSpouseAllocationViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledQitViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledResourceBankMonthDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledQitAndAllocationViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledResourceMonthDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ExparteApplicationInfoViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledApplicationInfoBarViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledEligEnrollAwardDenyViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledEligibilityEnrollmentViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledLiabilityDetailChangeCodeViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledSnapshotDetailsViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledSnapshotViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledTBQMedicarePartDInfoViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\FinancialInstitutionsViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ExpediteFacilityProvidersViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\FinancialInstitutionViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ExpediteFacilityProviderViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledNonMagiIncomeDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\MedicalLongTermCareInsuranceDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\PersonalNeedsAllowanceViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ReconcileFacilityProviderViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ExpediteFacilityViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\PersonInfoViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledLiabilityDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ProviderViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\RepresentativeInfoViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledLiabilityViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledVeteranPersonViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\SelectExpediteFacilityProviderViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\SpousalAllocationAmount.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\SpousalAllocationDetails.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\BudgetEntryModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\BudgetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\BurialWorksheetPolicyModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\NonIncurredWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\VACoupleWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\FamilyAllowanceWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\DeemingMSPWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\IncurredWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\PickleSSITerminatedInfo.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\VASurvivorWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\TransferPenaltyWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\PickleWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\SpousalAllocationWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\OtherFamilyMembersNoCommunitySpouseInfo.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\DisabledAdultChildWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\ElderlyDisabledWorksheetViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\SSIRetroWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\BurialWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\VAWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\VASingleWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\WidowWidowerWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\HospitalWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\IntellectualDisabilitiesWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\LoanAmortizationWorksheetModel.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\MonthlyScheduledPaymentInfo.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\ScheduledPaymentInfo.cs" />
    <Compile Include="Models\ElderlyDisabled\Worksheets\WorksheetBaseModel.cs" />
    <Compile Include="Models\Enrollment\PepInfo.cs" />
    <Compile Include="Models\Enrollment\EmailInfo.cs" />
    <Compile Include="Models\Enrollment\EnrollmentHistoryED.cs" />
    <Compile Include="Models\Enrollment\ViewModel\EligEnrollApplicantInfoViewModel.cs" />
    <Compile Include="Models\Enrollment\ViewModel\EligEnrollAwardDenyViewModel.cs" />
    <Compile Include="Models\Enrollment\ViewModel\EligEnrollIncomeViewModel.cs" />
    <Compile Include="Models\HistoricalData\HistoricalDataSearchInfoViewModel.cs" />
    <Compile Include="Models\HistoricalData\HistoricalDataSearchResultViewModel.cs" />
    <Compile Include="Models\HistoricalData\HistoricalDataSegmentsViewModel.cs" />
    <Compile Include="Models\HistoricalData\HistoricalDataViewModel.cs" />
    <Compile Include="Models\JIY\ApplicantInformation.cs" />
    <Compile Include="Models\JIY\PlacementInformation.cs" />
    <Compile Include="Models\JIY\ReleaseHistory.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingElderlyDisabledViewModel.cs" />
    <Compile Include="Models\Enrollment\ViewModel\PregnancyViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingLtcViewModel.cs" />
    <Compile Include="Models\Letters\ElderlyDisabledAddressDetails.cs" />
    <Compile Include="Models\Letters\LetterBaseViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\Address.cs" />
    <Compile Include="Models\Letters\ViewModel\Addressee.cs" />
    <Compile Include="Models\Letters\ViewModel\County.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledCOLANoticeViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledDenialDescriptionsViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledDenialLetterViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledAnnualReviewLetterViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledAnnuityFormLetter232ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledAppointmentOfRepresentative202ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledAwardLetterViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledBankAccountRelease223ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledBonaFideEffortToSell226ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledCashValueVerifaction215ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledClaimantBasicInfo.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledBonaFideReEvaluation254ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledChangeReport295ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledDisabilityEmergencyServicesDetermination256ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledDraftandFinalizedDetailViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledFairHearingSummary250ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledIncomeVerification257ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledInterpretationRequest238ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledJointOwnerStatement225ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLettersViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLiabilityChangeCodeDescriptionViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLiabilitySegmentViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLiabilityUpdateLetterViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLienForMedicalPayments220ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLienTrustAnnuityForm216ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledNoticeOfAction247ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledNoticeOfDelay208ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledNoticeofFinancialIneligibility252ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledNoticeReLienForm217ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledPatientAccount222ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledPersonalNeedsAccountVerification222RViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledPhysiciansStatementCoverLetter261ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledQualifyingIncomeTrustPacket262ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledRealtorFairMarketValue221ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledRequestForHearing249ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledRequestForInformationViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledResourceAssessmentNotice230ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLetterToEmployer233ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledResourceRebuttal253ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledReportOfInformationToSocialSecurity251ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledReviewOfEligibilityNoticeViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledSampleFormViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledSpousalAllocationAgreement227ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledStatementFromDependentRelativeCommunitySpouse240ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledStatementOfClaimantBurialFundDesignation236ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledLienInformation218ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledStatementofClaimantorOtherPerson234ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledStatementofIntenttoReturnHome214ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledTaxAssessorProperty224ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledTerminationLetterViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledTerminationNoticeViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledThirdPartyLiabilityInformationSheet287ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledTransferOfCaseRecord259ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledRecordOfContact200ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledVABenefitsVerification260ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledVerificationOfAnnuity273ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\ElderlyDisabledVerificationOfWages258ViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\LetterRecipientViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\RMAAwardLetterViewModel.cs" />
    <Compile Include="Models\Letters\ViewModel\RMATerminationLetterViewModel.cs" />
    <Compile Include="Models\Notes\PrintRecordOfContactModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\HPE\HpeLandingViewModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\ViewModel\PepInfoViewModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\ViewModel\PepDeterminersViewModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\ViewModel\PepDeterminerViewModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\ViewModel\PepLandingViewModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\ViewModel\PepProvidersViewModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\ViewModel\ProviderRepresentativeViewModel.cs" />
    <Compile Include="Models\PresumptiveEligibility\ViewModel\PepProviderViewModel.cs" />
    <Compile Include="Models\Rcc\OutOfCountryAddressRcc.cs" />
    <Compile Include="Models\Rcc\PayeeInfo.cs" />
    <Compile Include="Models\RMA\ViewModel\ApplicationRefugeeDetailViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\PersonViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RaceViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeApplicationDetailViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeApplicantViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeEligibilityEnrollmentViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeIncomeDetailViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeIncomeViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeMedicalInsuranceViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeMedicalInsuranceDetailViewModel.cs" />
    <Compile Include="Models\RMA\ViewModel\RefugeeSpouseViewModel.cs" />
    <Compile Include="Models\Shared\BaseEligibilityEnrollmentViewModel.cs" />
    <Compile Include="Models\Shared\PersonSuspension.cs" />
    <Compile Include="Models\Application\PersonUpdate.cs" />
    <Compile Include="Models\Application\Representative\HouseholdMember.cs" />
    <Compile Include="Models\Application\Representative\Sponsee.cs" />
    <Compile Include="Models\Application\Representative\Sponsor.cs" />
    <Compile Include="Models\Application\Representative\SponsorDetail.cs" />
    <Compile Include="Models\Application\ReVerifySSNAndCitizenshipRequest.cs" />
    <Compile Include="Models\Application\ReVerifySSNAndCitizenshipResponse.cs" />
    <Compile Include="Models\Application\SsiRecipientSuspension.cs" />
    <Compile Include="Models\Application\SSI\SsiInformation.cs" />
    <Compile Include="Models\Application\SSI\SsiMain.cs" />
    <Compile Include="Models\Application\Summary\Application.cs" />
    <Compile Include="Models\Application\Summary\PlasticCard\PlasticCardSearch.cs" />
    <Compile Include="Models\Application\Summary\ApplicationSummary.cs" />
    <Compile Include="Models\Application\ViewModel\ApplicationDetailViewModel.cs" />
    <Compile Include="Models\Application\ViewModel\ApplicationLivingArrangementViewModel.cs" />
    <Compile Include="Models\Application\ViewModel\ApplicationViewModel.cs" />
    <Compile Include="Models\BaseViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ElderlyDisabledSaveApplicationResponse.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ApplicationElderlyDisabledDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ApplicationResidencyInfoViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledAdditionalBurialFundsViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledLifeInsuranceDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledLifeInsuranceViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledOtherBurialFundsViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledApplicationViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledMedicalInsuranceDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledMedicalInsuranceViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledNonMagiIncomeViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledHouseholdMembersViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledHouseholdMemberViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledResourceTransferViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledResourceDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledResourceBankDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledFacilityListViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonDetailViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledFacilityViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPropertyMobileHomeViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPropertyParcelViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPropertyPreviousViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPropertyViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledRaceViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledRepresentativesViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyMachineViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyCollectibleViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledPersonalPropertyAutoViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledResourceViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledSponsorViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledVeteranViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledWorkerNumberListViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\NonMagiIncomeViewModel.cs" />
    <Compile Include="Models\Application\SSI\SsiResponse.cs" />
    <Compile Include="Models\Enrollment\DeemedIncome.cs" />
    <Compile Include="Models\Enrollment\PendingApplication.cs" />
    <Compile Include="Models\Enrollment\ViewModel\ModifyEnrollmentUnbornViewModel.cs" />
    <Compile Include="Models\Enrollment\ViewModel\ModifyEnrollmentViewModel.cs" />
    <Compile Include="Models\Enums\Enums.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingNonUSAddressViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingAddressViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingApplicationInfoMemberViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingApplicationInfoViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingChainAndCrossReferenceFieldsViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingApplicationDetailsViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingCommunicationViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingDetailsViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingChainAndCrossReferenceViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingEnrollmentViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingPaperCardViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingPersonAlertViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingPersonEnrollmentViewModel.cs" />
    <Compile Include="Models\Enrollment\SavePendingApplication.cs" />
    <Compile Include="Models\Shared\LandingNavBarViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingPhonesViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingPlasticCardViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingCreateApplicationResultViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingResultViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingPersonDetailsActionsMenuViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingPersonDetailsViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingSearchResultViewModel.cs" />
    <Compile Include="Models\Landing\ViewModel\LandingViewModel.cs" />
    <Compile Include="Models\Person\ViewModel\DependentPersonAddressViewModel.cs" />
    <Compile Include="Models\Person\ViewModel\PhoneListViewModel.cs" />
    <Compile Include="Models\Person\ViewModel\AddressViewModel.cs" />
    <Compile Include="Models\Person\ViewModel\PersonAddressesViewModel.cs" />
    <Compile Include="Models\Person\ViewModel\PersonAddressViewModel.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledContactPreferenceViewModel.cs" />
    <Compile Include="Models\Person\ViewModel\PhoneAndPersonPhoneViewModel.cs" />
    <Compile Include="Models\Shared\AddressData.cs" />
    <Compile Include="Models\Shared\AuthorizedField.cs" />
    <Compile Include="Models\Shared\OutOfCountryAddress.cs" />
    <Compile Include="Models\SideNavBar.cs" />
    <Compile Include="Models\SideNavRequest.cs" />
    <Compile Include="Models\Snapshot\AdditionalContactInformation.cs" />
    <Compile Include="Models\Snapshot\JiyFacilityInformation.cs" />
    <Compile Include="Models\Snapshot\NonUsAddressInfo.cs" />
    <Compile Include="Models\Snapshot\AddressInfo.cs" />
    <Compile Include="Models\Snapshot\ApplicationDetail.cs" />
    <Compile Include="Models\Snapshot\ApplicationIncome.cs" />
    <Compile Include="Models\Snapshot\ApplicationInformation.cs" />
    <Compile Include="Models\Snapshot\ApplicationMember.cs" />
    <Compile Include="Models\Snapshot\ApplicationRepresentative.cs" />
    <Compile Include="Models\Snapshot\ApplicationRepresentativeAssistor.cs" />
    <Compile Include="Models\Snapshot\ApplicationRepresentativeAuthRep.cs" />
    <Compile Include="Models\Snapshot\ApplicationRepresentativeAuthRepAssistorBase.cs" />
    <Compile Include="Models\Snapshot\AssistorInfoSnap.cs" />
    <Compile Include="Models\Snapshot\AuthorizedInfoSnap.cs" />
    <Compile Include="Models\Snapshot\CapitalGains.cs" />
    <Compile Include="Models\Snapshot\SSIInformation.cs" />
    <Compile Include="Models\Snapshot\DateRange.cs" />
    <Compile Include="Models\Snapshot\EligEnroll.cs" />
    <Compile Include="Models\Snapshot\EligEnrollIncomeInformation.cs" />
    <Compile Include="Models\Snapshot\IncomeBase.cs" />
    <Compile Include="Models\Snapshot\IncomeSummary.cs" />
    <Compile Include="Models\Snapshot\JobIncome.cs" />
    <Compile Include="Models\Snapshot\MSPApplicationIncome.cs" />
    <Compile Include="Models\Snapshot\OtherIncome.cs" />
    <Compile Include="Models\Snapshot\PensionRetirement.cs" />
    <Compile Include="Models\Snapshot\PersonEthnicity.cs" />
    <Compile Include="Models\Snapshot\PersonHouseholdInformation.cs" />
    <Compile Include="Models\Snapshot\PersonHubResponse.cs" />
    <Compile Include="Models\Snapshot\PersonMedicareHubResponse.cs" />
    <Compile Include="Models\Snapshot\PersonRace.cs" />
    <Compile Include="Models\Snapshot\RentalRoyaltyFarmingFishing.cs" />
    <Compile Include="Models\Snapshot\SelfEmployment.cs" />
    <Compile Include="Models\Snapshot\SponsorPhone.cs" />
    <Compile Include="Models\Snapshot\SponsorInfo.cs" />
    <Compile Include="Models\Snapshot\Unemployment.cs" />
    <Compile Include="Models\Snapshot\TaxFilerSummary.cs" />
    <Compile Include="Models\Snapshot\ViewModel\JiyFacilityInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\NonUsAddressViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AddressViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AppendixAInsuranceInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AppendixASummaryViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AppendixAViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AppendixBIncomeViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AppendixBSummaryViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AppendixBViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\ApplicationHouseholdViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\ApplicationInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\ApplicationQuickInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\ApplicationSnapshotViewModel.cs" />
    <Compile Include="Models\Snapshot\EligibilityEnrollment.cs" />
    <Compile Include="Models\Snapshot\HouseholdInfo.cs" />
    <Compile Include="Models\Snapshot\PersonBase.cs" />
    <Compile Include="Models\Snapshot\PersonDetail.cs" />
    <Compile Include="Models\Snapshot\PersonInfo.cs" />
    <Compile Include="Models\Snapshot\PersonNameBase.cs" />
    <Compile Include="Models\Snapshot\PersonSnapshot.cs" />
    <Compile Include="Models\Snapshot\PhoneInfo.cs" />
    <Compile Include="Models\Snapshot\SocialSecurityCard.cs" />
    <Compile Include="Models\Merge\SummaryOperation.cs" />
    <Compile Include="Models\Rcc\Address.cs" />
    <Compile Include="Models\Rcc\AddressRCC.cs" />
    <Compile Include="Models\Rcc\ApplicationXMLData.cs" />
    <Compile Include="Models\Rcc\Citizenship.cs" />
    <Compile Include="Models\Rcc\Enrollee.cs" />
    <Compile Include="Models\Rcc\HouseholdMember.cs" />
    <Compile Include="Models\Rcc\MetaData.cs" />
    <Compile Include="Models\Rcc\MetaDataList.cs" />
    <Compile Include="Models\Rcc\Person.cs" />
    <Compile Include="Models\Rcc\PersonAdditionalInfo.cs" />
    <Compile Include="Models\Rcc\PersonalInfo.cs" />
    <Compile Include="Models\Rcc\PersonalInformationRcc.cs" />
    <Compile Include="Models\Rcc\PersonEthnicity.cs" />
    <Compile Include="Models\Rcc\PersonEthnicityRaceSection.cs" />
    <Compile Include="Models\Rcc\PersonRace.cs" />
    <Compile Include="Models\Rcc\RequestHeader.cs" />
    <Compile Include="Models\Rcc\ResponseHeader.cs" />
    <Compile Include="Models\Rcc\RestResponseDocument.cs" />
    <Compile Include="Models\Rcc\Telephone.cs" />
    <Compile Include="Models\Shared\Income\EquifaxEmployerList.cs" />
    <Compile Include="Models\Shared\Income\EquifaxEmployerType.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AssistorViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AuthorizedRepresentativeViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AuthRepAssistorBaseViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\AppRepViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\PepDeterminerProviderViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\RMAHealthCoverageAddressViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\RMAHealthCoverageDetailViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\RMAHealthCoverageViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\RMAInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\SSIInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\ContactInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\DeemedIncomeViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\DHRInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\EligibilityEnrollmentEligibilityDetailViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\EligibilityEnrollmentEnrollmentDetailViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\EligibilityEnrollmentIncomeInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\EligibilityEnrollmentIncomeTalxInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\EligibilityEnrollmentPersonalInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\EligibilityEnrollmentViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\HealthCoverageEmployerInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\HealthCoverageBaseInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\HealthCoverageOnlineInsuranceViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\HealthCoverageOtherInsuranceInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\HealthCoverageSummaryViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\HealthCoverageViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\HubResponseViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeBasicViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeFarmFishRentalRoyaltyViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeJobViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeOtherViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomePensionRetirementViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeProfitLossBaseViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeSelfEmploymentViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeSummaryViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeUnemploymentViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\IncomeYearlyViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MedicareDateViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MedicareResponseViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MSPHubVerifiedEquifaxIncomeInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MSPHubVerifiedSSAIncomeInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MSPIncomeSummaryViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MSPIncomeTypeInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MSPMedicarePartAInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MSPSSIInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\MSPVeteranInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\PersonalInformationCitizenshipViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\PersonalInformationGeneralInfoViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\PersonalInformationMoreInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\PersonalInformationNonCitizenViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\PersonalInformationViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\PersonHubResponseViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\SponsorInfoSnapViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\SponsorPhoneViewModel.cs" />
    <Compile Include="Models\Snapshot\ViewModel\TaxFilerViewModel.cs" />
    <Compile Include="Models\Snapshot\YearlyIncome.cs" />
    <Compile Include="Models\Ssi\SdxInfo.cs" />
    <Compile Include="Models\SuspensionReason.cs" />
    <Compile Include="Models\Utility\UserAccountDetailWM.cs" />
    <Compile Include="Models\Utility\UserAccountSearchResultsListWM.cs" />
    <Compile Include="Models\Utility\UserAccountSearchResultsWM.cs" />
    <Compile Include="Models\WorkerPortalAccount\WorkerPortalAccountViewModel.cs" />
    <Compile Include="Models\WorkerPortalAccount\WorkerReminderListViewModel.cs" />
    <Compile Include="Models\WorkerPortalAccount\WorkerReminderViewModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Models\Accounting\AccountingBO.cs" />
    <Compile Include="Models\Accounting\AccountingLetters.cs" />
    <Compile Include="Models\Accounting\AccountingLettersType.cs" />
    <Compile Include="Models\Accounting\AccountingLettersSearchCriteria.cs" />
    <Compile Include="Models\Accounting\AC_Search.cs" />
    <Compile Include="Models\Accounting\AddPayment.cs" />
    <Compile Include="Models\Accounting\Adjustment.cs" />
    <Compile Include="Models\Accounting\BalanceAdjustment.cs" />
    <Compile Include="Models\Accounting\BalanceAdjustmentList.cs" />
    <Compile Include="Models\Accounting\Batch.cs" />
    <Compile Include="Models\Accounting\BatchList.cs" />
    <Compile Include="Models\Accounting\NsfPrintPopup.cs" />
    <Compile Include="Models\Accounting\Payment.cs" />
    <Compile Include="Models\Accounting\PaymentList.cs" />
    <Compile Include="Models\Accounting\Refund.cs" />
    <Compile Include="Models\Accounting\Subscriber.cs" />
    <Compile Include="Models\Accounting\SubscriberList.cs" />
    <Compile Include="Models\Application\AIAN\AiAnIncomeSource.cs" />
    <Compile Include="Models\Application\AIAN\AiAnIncomeType.cs" />
    <Compile Include="Models\Application\AIAN\AIANMemberBO.cs" />
    <Compile Include="Models\Application\AIAN\Tribe.cs" />
    <Compile Include="Models\Application\AIAN\TribeList.cs" />
    <Compile Include="Models\Application\AIAN\TribePerson.cs" />
    <Compile Include="Models\Application\Representative\AssistorAuthorizer.cs" />
    <Compile Include="Models\Application\Representative\AssistorData.cs" />
    <Compile Include="Models\Application\Representative\AuthorizedUser.cs" />
    <Compile Include="Models\Application\ContactInformation.cs" />
    <Compile Include="Models\Application\FamilyTaxFiler.cs" />
    <Compile Include="Models\Application\Hub\HubResponseList.cs" />
    <Compile Include="Models\Application\Hub\PersonHubResponseInfo.cs" />
    <Compile Include="Models\Application\Income\AmInAkNaIncome.cs" />
    <Compile Include="Models\Application\Income\AppIncomeSummary.cs" />
    <Compile Include="Models\Application\Income\ApplicationDeductions.cs" />
    <Compile Include="Models\Application\Income\ApplicationIncome.cs" />
    <Compile Include="Models\Application\Income\ChangedIncome.cs" />
    <Compile Include="Models\Application\Income\Discrepancy.cs" />
    <Compile Include="Models\Application\Income\FutureIncome.cs" />
    <Compile Include="Models\Application\Income\Income.cs" />
    <Compile Include="Models\Application\Income\IncomeSummary.cs" />
    <Compile Include="Models\Application\Income\OtherIncome.cs" />
    <Compile Include="Models\Application\Insurance\ApplicationEHCEmployee.cs" />
    <Compile Include="Models\Application\Insurance\CoverageDetail.cs" />
    <Compile Include="Models\Application\Insurance\EmpInsurance.cs" />
    <Compile Include="Models\Application\Insurance\Employer.cs" />
    <Compile Include="Models\Application\Insurance\EmployerInfo.cs" />
    <Compile Include="Models\Application\Insurance\EmployerInfoList.cs" />
    <Compile Include="Models\Application\Insurance\EmployerInformation.cs" />
    <Compile Include="Models\Application\Insurance\HealthCoverage.cs" />
    <Compile Include="Models\Application\Insurance\HealthCoverageBO.cs" />
    <Compile Include="Models\Application\Insurance\HealthCoverageMCType.cs" />
    <Compile Include="Models\Application\Insurance\HealthCovergeMCTypeList.cs" />
    <Compile Include="Models\Application\Insurance\PersonEmployerInfo.cs" />
    <Compile Include="Models\Application\Insurance\POCEmployer.cs" />
    <Compile Include="Models\Application\LookupNotes.cs" />
    <Compile Include="Models\Application\PersonalInformation.cs" />
    <Compile Include="Models\Application\PersonalInfo\IntakeSearchCriteria.cs" />
    <Compile Include="Models\Application\PersonalInfo\IntakeSearchResult.cs" />
    <Compile Include="Models\Application\PersonalInfo\IntakeSearchResultList.cs" />
    <Compile Include="Models\Application\RaceInformation.cs" />
    <Compile Include="Models\Application\Search\PerAppListSearchResult.cs" />
    <Compile Include="Models\Application\Search\PerListSearchResult.cs" />
    <Compile Include="Models\Application\Search\SearchResult.cs" />
    <Compile Include="Models\Application\SignSubmit\Incarceration.cs" />
    <Compile Include="Models\Application\SignSubmit\ReviewSign.cs" />
    <Compile Include="Models\Application\SignSubmit\SignSubmit.cs" />
    <Compile Include="Models\Application\Summary\NameAddress.cs" />
    <Compile Include="Models\Application\Summary\PlasticCardInformation.cs" />
    <Compile Include="Models\Application\Summary\PlasticCard\PaperCard.cs" />
    <Compile Include="Models\Application\Summary\PlasticCard\PlasticCard.cs" />
    <Compile Include="Models\Application\Summary\PlasticCard\PlasticCardHistory.cs" />
    <Compile Include="Models\Application\Summary\Summary.cs" />
    <Compile Include="Models\Application\Summary\TaxFilerSummary.cs" />
    <Compile Include="Models\Enrollment\EligEnroll.cs" />
    <Compile Include="Models\Enrollment\Eligibility.cs" />
    <Compile Include="Models\Enrollment\EligibilityCombineResult.cs" />
    <Compile Include="Models\Enrollment\EnrollmentHistory.cs" />
    <Compile Include="Models\Enrollment\IncomeEligibility.cs" />
    <Compile Include="Models\Enrollment\InRuleOutput.cs" />
    <Compile Include="Models\Enrollment\MagiNonMagi.cs" />
    <Compile Include="Models\Enrollment\MedTaxHousehold.cs" />
    <Compile Include="Models\Enrollment\MhxDetail.cs" />
    <Compile Include="Models\Enrollment\PersonDetail.cs" />
    <Compile Include="Models\Enrollment\PrintSubscriber.cs" />
    <Compile Include="Models\Enrollment\RulesEligibilityResult.cs" />
    <Compile Include="Models\Enrollment\RulesIncome.cs" />
    <Compile Include="Models\Enrollment\ThxDetailcs.cs" />
    <Compile Include="Models\Letters\CreateLetter.cs" />
    <Compile Include="Models\Letters\LetterHistory.cs" />
    <Compile Include="Models\Letters\LettersHistory.cs" />
    <Compile Include="Models\Letters\LetterType.cs" />
    <Compile Include="Models\Letters\LetterVariables.cs" />
    <Compile Include="Models\Letters\RenderLetter.cs" />
    <Compile Include="Models\LiveEnrollmentBO.cs" />
    <Compile Include="Models\Shared\Address.cs" />
    <Compile Include="Models\Shared\AdvancedSearch.cs" />
    <Compile Include="Models\Shared\AppDetail.cs" />
    <Compile Include="Models\Shared\ApplicationResponse.cs" />
    <Compile Include="Models\Shared\ApplicationStatus.cs" />
    <Compile Include="Models\Shared\ContactPreferences.cs" />
    <Compile Include="Models\Shared\DrasticChange.cs" />
    <Compile Include="Models\Shared\Email.cs" />
    <Compile Include="Models\Shared\ExistingPersonList.cs" />
    <Compile Include="Models\Shared\FindPerson.cs" />
    <Compile Include="Models\Shared\Income\AIANIncomeSource.cs" />
    <Compile Include="Models\Shared\Income\AIANIncomeSourceList.cs" />
    <Compile Include="Models\Shared\Income\Deduction.cs" />
    <Compile Include="Models\Shared\Income\DeductionList.cs" />
    <Compile Include="Models\Shared\Income\IncomeAmount.cs" />
    <Compile Include="Models\Shared\Income\IncomeJob.cs" />
    <Compile Include="Models\Shared\Income\IncomeTable.cs" />
    <Compile Include="Models\Shared\Income\IncomeTableList.cs" />
    <Compile Include="Models\Shared\Income\IncomeType.cs" />
    <Compile Include="Models\Shared\Income\IncomeTypeList.cs" />
    <Compile Include="Models\Shared\Income\Unemployment.cs" />
    <Compile Include="Models\Shared\Name.cs" />
    <Compile Include="Models\Shared\Notes.cs" />
    <Compile Include="Models\Shared\PaperEligibilityInfo.cs" />
    <Compile Include="Models\Shared\PersonalInformation\CitizenshipApplicationDetail.cs" />
    <Compile Include="Models\Shared\PersonalInformation\DeathInformation.cs" />
    <Compile Include="Models\Shared\PersonalInformation\Ethnicity.cs" />
    <Compile Include="Models\Shared\PersonalInformation\EthnicityDetail.cs" />
    <Compile Include="Models\Shared\PersonalInformation\EthnicityList.cs" />
    <Compile Include="Models\Shared\PersonalInformation\MoreAboutPerson.cs" />
    <Compile Include="Models\Shared\PersonalInformation\PersonDetail.cs" />
    <Compile Include="Models\Shared\PersonalInformation\Race.cs" />
    <Compile Include="Models\Shared\PersonalInformation\RaceDetail.cs" />
    <Compile Include="Models\Shared\PersonalInformation\RaceList.cs" />
    <Compile Include="Models\Shared\PersonDepLives.cs" />
    <Compile Include="Models\Shared\Phone.cs" />
    <Compile Include="Models\Shared\ProcessGroup.cs" />
    <Compile Include="Models\Shared\RetroEligibilityInfo.cs" />
    <Compile Include="Models\Shared\Role.cs" />
    <Compile Include="Models\Shared\Selection.cs" />
    <Compile Include="Models\Shared\Site.cs" />
    <Compile Include="Models\Shared\StandardAddressReponse.cs" />
    <Compile Include="Models\Shared\StandardAddressRequest.cs" />
    <Compile Include="Models\Shared\TaxFiler\Dependent.cs" />
    <Compile Include="Models\Shared\TaxFiler\LivesWith.cs" />
    <Compile Include="Models\Shared\TaxFiler\TaxFiler.cs" />
    <Compile Include="Models\Shared\TempProcessGroup.cs" />
    <Compile Include="Models\Shared\WebAccountSecurityQuestion.cs" />
    <Compile Include="Models\Utility\Login.cs" />
    <Compile Include="Models\Utility\UserAccountDetail.cs" />
    <Compile Include="Models\Utility\UserAccountSearchCriteria.cs" />
    <Compile Include="Models\Utility\UserAccountSearchResults.cs" />
    <Compile Include="Models\Utility\UserAccountSearchResultsList.cs" />
    <Compile Include="Models\Utility\UserBO.cs" />
    <Compile Include="Models\Utility\WebUserAccountDetail.cs" />
    <Compile Include="Models\Utility\WebUserAccountOperation.cs" />
    <Compile Include="Models\Utility\WebUserAccountSearchCriteria.cs" />
    <Compile Include="Models\Utility\WebUserAccountSearchResults.cs" />
    <Compile Include="Models\Utility\WebUserAccountSearchResultsList.cs" />
    <Compile Include="Models\Utility\WebUserBO.cs" />
    <Compile Include="Validators\Accounting\AccountingLettersValidator.cs" />
    <Compile Include="Validators\Accounting\AC_SearchValidator.cs" />
    <Compile Include="Validators\Accounting\AccountingLettersSearchCriteriaValidator.cs" />
    <Compile Include="Validators\Accounting\BalanceAdjustmentValidator.cs" />
    <Compile Include="Validators\Accounting\PaymentValidator.cs" />
    <Compile Include="Validators\Application\AIAN\AIANMemberBOValidator.cs" />
    <Compile Include="Validators\Application\ApplicationDetailViewModelValidator.cs" />
    <Compile Include="Validators\Application\ApplicationLivingArrangementViewModelValidator.cs" />
    <Compile Include="Validators\Application\ApplicationViewModelValidator.cs" />
    <Compile Include="Validators\Application\PersonalInformationValidator.cs" />
    <Compile Include="Validators\Application\PersonUpdateValidator.cs" />
    <Compile Include="Validators\Application\Summary\PersonalNeedsAllowanceViewModelValidator.cs" />
    <Compile Include="Validators\DysFacility\DysFacilityViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ApplicationElderlyDisabledDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ApplicationResidencyInfoViewModelValidator.cs" />
    <Compile Include="Validators\Application\AssistorAuthorizer\AssistorAuthorizerValidator.cs" />
    <Compile Include="Validators\Application\ContactInformationValidator.cs" />
    <Compile Include="Validators\Application\DHRApplicationModelValidator.cs" />
    <Compile Include="Validators\Application\FamilyTaxFilerBOValidator.cs" />
    <Compile Include="Validators\Application\IncomeValidator.cs" />
    <Compile Include="Validators\Application\Insurance\EmployerInformationValidator.cs" />
    <Compile Include="Validators\Application\Insurance\HealthCoverageBOValidator.cs" />
    <Compile Include="Validators\Application\IntakeSearchCriteriaValidator.cs" />
    <Compile Include="Validators\Application\MSPIntakeValidator.cs" />
    <Compile Include="Validators\Application\NonMagiIncomeDetailsValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledAdditionalBurialFundsBudgetViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledAdditionalBurialFundsViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledApplicationViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledContactPreferenceViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledHouseholdMembersViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledHouseholdMemberViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledLiabilityDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledLiabilityTestViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledLiabilityViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledLifeInsuranceDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledLifeInsuranceViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledMedicalInsuranceDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledMedicalInsuranceMonthDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledMedicalInsurancePartDBudgetViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledMedicalInsuranceViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledNonMagiIncomesViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledNonMagiIncomeDetailViewModelValidator .cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledNonMagiIncomeViewModelValidator.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledFormerSpouseViewModel.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledOtherBurialFundsViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPersonalPropertyCollectibleViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPersonalPropertyMachineViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPersonDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledFormerSpouseViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPropertyMobileHomeViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPropertyParcelViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledResourceBankMonthDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledResourceTransferMonthDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledVeteranDetailsViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledVeteranViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPropertyPreviousViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPropertyViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPersonalPropertyAutoViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledResourceDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledResourceTransferViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledResourceBankDetailViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPersonalPropertyViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledResourceViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledRaceViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledPersonViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledRepresentativesViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledSponsorViewModelValidator.cs" />
    <Compile Include="Models\ElderlyDisabled\ViewModel\ElderlyDisabledSpouseViewModel.cs" />
    <Compile Include="Validators\ElderlyDisabled\ElderlyDisabledSpouseViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\ExpediteFacilityViewModelValidator.cs" />
    <Compile Include="Validators\ElderlyDisabled\FinancialInstitutionViewModelValidator.cs" />
    <Compile Include="Validators\Enrollment\ElderlyDisabledEligEnrollAwardDenyViewModelValidator.cs" />
    <Compile Include="Validators\Enrollment\ElderlyDisabledEligibilityEnrollmentViewModelValidator.cs" />
    <Compile Include="Validators\Enrollment\EligEnrollAwardDenyViewModelValidator.cs" />
    <Compile Include="Validators\Pep\PepDeterminerViewModelValidator.cs" />
    <Compile Include="Validators\Pep\PepProviderViewModelValidator.cs" />
    <Compile Include="Validators\Person\PersonAddressesViewModelValidator.cs" />
    <Compile Include="Validators\Person\PersonAddressViewModelValidator.cs" />
    <Compile Include="Validators\Person\PersonalInformationValidator.cs" />
    <Compile Include="Validators\Person\PersonUpdateValidator.cs" />
    <Compile Include="Validators\Application\SsiValidator.cs" />
    <Compile Include="Validators\Application\Summary\PlasticCardInformationValidator.cs" />
    <Compile Include="Validators\Application\TaxFilerValidator.cs" />
    <Compile Include="Validators\Enrollment\EligEnrollValidator.cs" />
    <Compile Include="Validators\Person\PhoneAndPersonPhoneViewModelValidator.cs" />
    <Compile Include="Validators\Rcc\PersonalInformationRccValidator.cs" />
    <Compile Include="Validators\RMA\ApplicationRefugeeDetailViewModelValidator.cs" />
    <Compile Include="Validators\RMA\RefugeeEligibilityEnrollmentViewModelValidator.cs" />
    <Compile Include="Validators\RMA\RefugeeIncomesViewModelValidator.cs" />
    <Compile Include="Validators\RMA\RefugeeIncomeViewModelValidator.cs" />
    <Compile Include="Validators\RMA\PersonViewModelValidator.cs" />
    <Compile Include="Validators\RMA\RefugeeApplicantViewModelValidator.cs" />
    <Compile Include="Validators\RMA\RefugeeMedicalInsuranceDetailViewModelValidator.cs" />
    <Compile Include="Validators\RMA\RefugeeMedicalInsuranceViewModelValidator.cs" />
    <Compile Include="Validators\RMA\RefugeeSpouseViewModelValidator.cs" />
    <Compile Include="Validators\Shared\AddressValidator.cs" />
    <Compile Include="Validators\Shared\AddressViewModelValidator.cs" />
    <Compile Include="Validators\Shared\CitizenshipDetailValidator.cs" />
    <Compile Include="Validators\Shared\ContactPreferenceValidator.cs" />
    <Compile Include="Validators\Shared\MoreAboutPersonValidator.cs" />
    <Compile Include="Validators\Shared\NameValidator.cs" />
    <Compile Include="Validators\Shared\NotesValidator.cs" />
    <Compile Include="Validators\Shared\PersonDepLivesValidator.cs" />
    <Compile Include="Validators\Shared\PersonSuspensionValidator.cs" />
    <Compile Include="Validators\Shared\PersonDetailValidator.cs" />
    <Compile Include="Validators\Person\PersonValidator.cs" />
    <Compile Include="Validators\Shared\PhoneValidator.cs" />
    <Compile Include="Validators\Utility\LoginValidator.cs" />
    <Compile Include="Validators\Utility\UserAccountDetailValidator.cs" />
    <Compile Include="Validators\Utility\WebUserAccountOperationValidator.cs" />
    <Compile Include="Validators\Utility\WebUserAccountSearchCriteriaValidator.cs" />
    <Compile Include="Validators\WorkerPortalAccount\WorkerReminderViewModelValidator.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Cares.Api.Infrastructure\Cares.Api.Infrastructure.csproj">
      <Project>{d8f7ab2c-bb31-4fe1-b470-9ae360f5abe5}</Project>
      <Name>Cares.Api.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Infrastructure.Log\Cares.Infrastructure.Log.csproj">
      <Project>{708CF02B-DFDD-4E88-AD78-1F63A0331256}</Project>
      <Name>Cares.Infrastructure.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Infrastructure\Cares.Portal.Infrastructure.csproj">
      <Project>{ee96e415-e65c-4069-858a-3897238dcf11}</Project>
      <Name>Cares.Portal.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cares.Portal.Worker.Resources\Cares.Portal.Worker.Resources.csproj">
      <Project>{357e6d64-1a06-4a27-a3d0-926afb967eb1}</Project>
      <Name>Cares.Portal.Worker.Resources</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>